// eslint-disable typescript/no-require-imports
require('dotenv').config()
const { join } = require('node:path')
const rspack = require('@rspack/core')
const { defineConfig } = require('@rspack/cli')
const ReactRefreshPlugin = require('@rspack/plugin-react-refresh')
// const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin')
const {
  resolveJSAndTS,
  getModuleJSAndTSRules,
  plugins: { ignoreMomentLocalesPlugin },
} = require('./webpack.config.base.cjs')
const packageConfig = require('../../package.json')
const { z } = require('zod')
const { version, cartrack_meta } = packageConfig

const schema = z.object({
  NODE_ENV: z.enum([
    'development',
    'production',
    'cypress-component-test-run',
    'cypress-component-test-open',
  ]),
  SKIP_PACKAGES_TRANSPILATION: z
    .literal('true')
    .optional()
    .transform((val) => val === 'true'),
  PROD_SOURCE_MAPS_QUALITY: z
    .enum(['normal', 'highest'])
    .optional()
    .default('normal')
    .catch('normal'),
  OVERRIDE_ENDPOINT: z.string().optional(),
  PROXY_ENDPOINT: z.string().optional(),
})

// This will throw an error in case the schema is not valid
const {
  NODE_ENV,
  SKIP_PACKAGES_TRANSPILATION,
  PROD_SOURCE_MAPS_QUALITY,
  OVERRIDE_ENDPOINT,
  PROXY_ENDPOINT,
} = schema.parse(process.env)

/**
 *
 * @param {typeof NODE_ENV} env
 */
const NODE_ENV_equals = (env) => {
  // Throw if we are trying to compare with an invalid value
  const validEnv = schema.shape.NODE_ENV.parse(env)
  return NODE_ENV === validEnv
}

const isCypressEnv = process.env.CYPRESS === 'true'
const useReactRefresh = NODE_ENV_equals('development') && !isCypressEnv

const mode = NODE_ENV_equals('development') ? 'development' : 'production'

/**
 * @type {import('@rspack/core').DevTool}
 */
const devTool = (() => {
  if (NODE_ENV_equals('cypress-component-test-open')) {
    return 'eval-cheap-module-source-map' // perf reasons
  }
  if (NODE_ENV_equals('cypress-component-test-run')) {
    return 'hidden-source-map'
  }

  if (NODE_ENV_equals('development')) {
    return 'eval-source-map'
  }

  return PROD_SOURCE_MAPS_QUALITY === 'highest' ? 'source-map' : 'hidden-source-map'
})()
// eslint-disable-next-line no-console
console.log('ℹ️ Rspack configuration:devTool:', devTool)

module.exports = defineConfig({
  target: ['browserslist'],
  mode,
  entry: './src/index.tsx',
  stats: 'errors-warnings',
  output: {
    ...(NODE_ENV_equals('production') && {
      path: join(process.cwd(), 'dist/apps/fleet-web'),
      filename: 'static/js/[name].[contenthash].js',
      chunkFilename: 'static/js/[name].[contenthash].chunk.js',
      // Css
      cssFilename: 'static/css/[name].[contenthash].css',
      cssChunkFilename: 'static/css/[name].[contenthash].chunk.css',
    }),
    // On rspack 0.7.1 "HMR is not implemented for module chunk format yet". Waiting for that to be implemented before we can enable this
    // module: true,
    publicPath: '/',
  },
  lazyCompilation:
    NODE_ENV_equals('development') && process.env.DEVELOPMENT_ENV !== 'docker'
      ? {
          // Since we have an SPA, we have a single entry point so we should compile "entries" immediately.
          entries: false,
        }
      : false,
  experiments: {
    css: true,
    lazyBarrel: true,
    nativeWatcher: true,
    cache:
      NODE_ENV === 'development'
        ? {
            type: 'persistent',
            buildDependencies: [
              __filename,
              join(__dirname, '../../package.json'),
              join(__dirname, '../../pnpm-lock.yaml'),
              join(__dirname, '../../tsconfig.base.json'),
              join(__dirname, './tsconfig.app.json'),
              join(__dirname, './.env'),
            ],
            snapshot: {
              managedPaths: [join(__dirname, '../../node_modules')],
            },
          }
        : undefined,
    parallelCodeSplitting: true,
  },
  context: __dirname,
  optimization: {
    splitChunks: {
      // Chunks 'all' is recommended for modern production builds.
      // Relevant links:
      // https://github.com/webpack/webpack/discussions/18040
      chunks: 'all',
      // Greatly increase the default value to reduce amount of chunks generated.
      // The default value would create more than 150 chunks when loading fleetweb. That's way too much.
      // It's still within the allowed chunk size limit for sentry/posthog source maps uploading.
      maxSize: 4_000_000,
    },
    removeAvailableModules: true,
  },
  devtool: devTool,
  devServer: NODE_ENV_equals('development')
    ? {
        historyApiFallback: true,
        host: '0.0.0.0',
        port: 8080,
        hot: true,
        liveReload: false,
        static: __dirname,
        devMiddleware: { publicPath: '/' },
        client: { overlay: false },
        proxy:
          PROXY_ENDPOINT && !OVERRIDE_ENDPOINT
            ? [
                (() => {
                  const target = PROXY_ENDPOINT
                  // eslint-disable-next-line no-console
                  console.log('ℹ️ Rspack configuration:proxy:target', target)
                  return {
                    context: ['/jsonrpc/index.php'],
                    target,
                    changeOrigin: true, // Changes the Host header to match target
                    secure: true, // For HTTPS targets
                  }
                })(),
              ]
            : undefined,
      }
    : undefined,
  resolve: resolveJSAndTS,
  module: {
    rules: [
      ...getModuleJSAndTSRules({
        useReactRefresh,
        transpileNodeModules:
          NODE_ENV_equals('production') && !SKIP_PACKAGES_TRANSPILATION,
      }),
      {
        test: /\.scss$/,
        exclude: /node_modules/,
        use: [
          'postcss-loader',
          {
            loader: 'sass-loader',
            options: {
              // muting Sass warnings since we're not updating our legacy code for the new version.
              sassOptions: {
                quietDeps: true,
                logger: {
                  warn: () => {}, // suppress all warnings by doing nothing
                },
              },
            },
          },
        ],
        type: 'css',
      },
      {
        test: /\.css$/,
        type: 'css',
      },
      {
        test: /\.pdf$/,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]',
        },
      },
      {
        test: /\.svg$/,
        exclude: /assets/,
        type: 'asset/inline',
      },
      {
        test: /\.svg$/,
        include: /assets/,
        /** Deprecated plugin - Convert to imaginin svgo with "removeDimensions: true" */
        use: [
          {
            loader: 'svg-inline-loader',
            options: {
              removeTags: true,
              removingTags: ['title'],
            },
          },
        ],
      },
      {
        test: /\.(jpe?g|png|gif|ico|eot|woff|otf|ttf|woff2|webp|avif)$/,
        type: 'asset',
      },
      {
        test: /\.(mp3)(\?.*)?$/,
        type: 'asset/inline',
      },
    ],
  },
  plugins: [
    new rspack.CircularDependencyRspackPlugin({
      failOnError: true,
      exclude: /node_modules/,
    }),
    ignoreMomentLocalesPlugin,
    /* Uncomment to enable typescript checking errors on the rspack terminal process */
    // nodeEnv === ENVS.DEV &&
    //   new ForkTsCheckerWebpackPlugin({
    //     typescript: {
    //       configFile: 'tsconfig.app.json',
    //       memoryLimit: 6500,
    //       /** Recommended mode when used with babel loader
    //        *  Check https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/blob/master/README.md#typescript-options
    //        */
    //       mode: 'write-references',
    //       diagnosticOptions: {
    //         /* Since babel only transpiles typescript, this plugin has to cover all types of errors (both semantic and syntactic). */
    //         syntactic: true,
    //       },
    //     },
    //   }),
    // cypress-rspack-dev-server already applies this plugin so we don't do it again to prevent conflicts
    new rspack.HtmlRspackPlugin({
      template: './index.ejs',
      templateParameters: {
        appVersion: version,
        appVersionWithMetadata: cartrack_meta.version_with_metadata,
      },
    }),
    new rspack.ProvidePlugin({ process: 'process' }),
    new rspack.CopyRspackPlugin({
      patterns: [
        {
          from: 'assets/customer_styling/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/img/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/templates/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/icons/favicons/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/templates-delivery/**/*',
        },
        // Expose MSW service worker at the application root
        {
          from: './src/mockServiceWorker.js',
          to: 'mockServiceWorker.js',
        },
      ],
    }),
    new rspack.DefinePlugin({
      ENV: {
        ...(NODE_ENV_equals('production')
          ? {
              NODE_ENV: JSON.stringify('production'),
              GA_TRACKING_ID: JSON.stringify('UA-153974304-1'),
              GMAP_API_KEY: JSON.stringify(
                process.env.STAGING
                  ? process.env.STAGING_GMAP_API_KEY
                  : process.env.GMAP_API_KEY,
              ),
              SENTRY_DSN: JSON.stringify(
                'https://<EMAIL>/1444444',
              ),
            }
          : {
              NODE_ENV: JSON.stringify('development'),
              ENABLE_REDUX_DEV_MIDDLEWARE: JSON.stringify(
                process.env.ENABLE_REDUX_DEV_MIDDLEWARE,
              ),
              GMAP_API_KEY: JSON.stringify(process.env.GMAP_API_KEY),
            }),

        OVERRIDE_ENDPOINT: JSON.stringify(process.env.OVERRIDE_ENDPOINT),
        DEPLOYMENT_ENV: JSON.stringify(process.env.DEPLOYMENT_ENV || 'unspecified'),
        APP_VERSION: JSON.stringify(version),
        APP_VERSION_WITH_METADATA: JSON.stringify(cartrack_meta.version_with_metadata),
        HERE_MAPS_API_KEY: JSON.stringify(process.env.HERE_MAPS_API_KEY),
        CYPRESS_CT_ENV: JSON.stringify(
          NODE_ENV_equals('cypress-component-test-open') ||
            NODE_ENV_equals('cypress-component-test-run')
            ? NODE_ENV
            : null,
        ),

        /* EXPERIMENTAL FEATURES */
        FEAT_MIFLEET_DECIMAL_SEPARATORS: JSON.stringify(
          process.env.FEAT_MIFLEET_DECIMAL_SEPARATORS,
        ),
        NEW_LOGIN: JSON.stringify(process.env.NEW_LOGIN),
        POSTHOG_API_KEY: JSON.stringify(
          'phc_79eMUErIQvXR9nLP0PeKiWfCAEMFJrjYpmvTyTioAby',
        ),
        POSTHOG_HOST: JSON.stringify('https://eu.i.posthog.com'),
        VISION_DEFAULT_CAMERA_SETTINGS:
          process.env.VISION_DEFAULT_CAMERA_SETTINGS === 'true',
        SCORECARDS: process.env.SCORECARDS === 'true',
      },
    }),
    useReactRefresh && new ReactRefreshPlugin(),
  ].filter(Boolean),
})
