version: "3.8"

services:
  fleet-web-frontend:
    build:
      context: ../..
      dockerfile: apps/fleet-web/Dockerfile
      target: development
    ports:
      - "8080:8080"
    environment:
      - "GMAP_API_KEY=team-local-development-google-api-key"
      - "OVERRIDE_ENDPOINT=http://localhost:8081/jsonrpc/index.php"
      - "IGNORE_REDUX_DEV_MIDDLEWARE=true"
      - "NODE_ENV=development"
    stdin_open: true
