FROM node:24-alpine AS base

# Enable corepack for pnpm
RUN corepack enable

# Set working directory
WORKDIR /app

# Copy workspace files from monorepo root (build context is repo root)
COPY pnpm-lock.yaml pnpm-workspace.yaml package.json nx.json tsconfig.base.json .npmrc ./
# Copy pnpm patches directory required by patchedDependencies
COPY patches/ ./patches/
COPY apps/fleet-web/src/_third-party-libs-forks/ ./apps/fleet-web/src/_third-party-libs-forks/
COPY apps/fleet-web/package.json ./apps/fleet-web/

# Development stage
FROM base AS development

# Install all dependencies (including devDependencies)
RUN pnpm install --frozen-lockfile

# Copy frontend source code
COPY apps/fleet-web/ ./apps/fleet-web/

# create the dest folder first and then copy
RUN mkdir -p libs/karoo-ui/core
RUN mkdir -p libs/karoo-utils
COPY libs/karoo-ui/core/dist ./libs/karoo-ui/core/dist
COPY libs/karoo-utils/dist ./libs/karoo-utils/dist

EXPOSE 8080

# Start dev server
CMD ["pnpm", "nx", "run", "fleet-web:docker-serve"]
