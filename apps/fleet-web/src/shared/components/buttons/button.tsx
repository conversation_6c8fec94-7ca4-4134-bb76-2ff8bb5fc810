import { useMemo } from 'react'
import type * as React from 'react'
import { StyledActionButton } from './styled/styled-action-button'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import { StyledButtonIcon } from './styled/styled-button-icon'

type Props = {
  id?: string
  className?: string
  disabled?: boolean
  eventCategory: string
  eventAction: string
  label: string
  type?: 'button' | 'submit'
  icon?: IconProp
  width?: string
  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void
}

type StyledButtonType = typeof StyledActionButton

function ButtonComponent(Component: StyledButtonType, props: Props) {
  const buttonStyle = useMemo(() => {
    if (props.width) {
      return { width: props.width }
    }
    return {}
  }, [props.width])

  return (
    <Component
      type={props.type}
      onClick={(event) => {
        props.onClick(event)
        if (props.eventCategory && props.eventAction) {
          GA4.event({
            category: props.eventCategory,
            action: props.eventAction,
          })
        }
      }}
      className={props.className}
      disabled={props.disabled}
      style={buttonStyle}
    >
      {props.icon ? <StyledButtonIcon icon={props.icon} /> : null}
      <span>{ctIntl.formatMessage({ id: props.label })}</span>
    </Component>
  )
}

function ActionButton(props: Props) {
  return ButtonComponent(StyledActionButton, { type: 'button', ...props })
}

export { ActionButton }
