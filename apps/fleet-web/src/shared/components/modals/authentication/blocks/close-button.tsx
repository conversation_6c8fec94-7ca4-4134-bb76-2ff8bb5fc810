import styled, { type StyledComponentPropsWithRef } from 'styled-components'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import Icon from '@fleet-web/components/Icon'

const IconContainer = styled.div`
  align-items: center;
  border: 1px solid ${variables.gray40};
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  height: 35px;
  justify-content: center;
  width: 35px;

  > svg {
    height: 16px;
    width: 11px;

    > path {
      fill: ${variables.gray60};
    }
  }
`

export function CloseButton(props: StyledComponentPropsWithRef<typeof IconContainer>) {
  return (
    <IconContainer {...props}>
      <Icon icon="times" />
    </IconContainer>
  )
}
