// oxlint-disable consistent-type-definitions
// eslint-disable-next-line no-restricted-imports
import type {} from '@mui/material/themeCssVarsAugmentation'
import type {} from '@karoo-ui/core/themeAugmentation'

import type { LinearProgressProps } from '@karoo-ui/core'

import type { ColumnMenuWithCoachingDriverFilterCustomProps } from '@fleet-web/modules/coaching/CoachingEvents/components/ColumnMenuWithCoachingDriverFilter'
import type { DataGridToolbarWithLoadNewEventsButtonAndExportProps } from '@fleet-web/modules/coaching/CoachingEvents/components/DataGridToolbarWithLoadNewEventsButtonAndExport'
import type { VehicleInspectionDataGridCustomToolbarProps } from '@fleet-web/modules/maintenance/VehicleInspections/components/VehicleInspectionDataGridCustomToolbar'
import type { Props as LandscapeBottomPanelProps } from '@fleet-web/modules/map-view/LandscapeBottomPanel/Toolbar'
import type { CustomDataGridToolbarProps as CustomDataGridToolbarProps1 } from '@fleet-web/modules/reports/ReportsStatusAndManagement/ReportStatus'
import type { ActivityGridToolbarProps } from '@fleet-web/modules/tachograph/components/ActivityGridToolbar'
import type { VisionEventsCustomToolbarWithLoadNewButtonProps } from '@fleet-web/modules/vision/VisionEvents/VehiclesEventsDataGrid/VisionEventsCustomToolbarWithLoadNewButton'
import type { KarooToolbarProps } from '@fleet-web/shared/data-grid/KarooToolbar'

/** See https://next.mui.com/x/react-data-grid/components/#custom-slot-props-with-typescript */
declare module '@mui/x-data-grid-premium' {
  // Starting with mui-x 7 beta, we had to start doing this
  interface LoadingOverlayPropsOverrides extends LinearProgressProps {}

  interface ToolbarPropsOverrides
    extends DataGridToolbarWithLoadNewEventsButtonAndExportProps,
      KarooToolbarProps,
      ActivityGridToolbarProps,
      CustomDataGridToolbarProps1,
      VisionEventsCustomToolbarWithLoadNewButtonProps,
      VehicleInspectionDataGridCustomToolbarProps,
      LandscapeBottomPanelProps {}

  interface ColumnMenuPropsOverrides
    extends ColumnMenuWithCoachingDriverFilterCustomProps {}
}

// Needs exports or else typescript won't augment the module
export {}
