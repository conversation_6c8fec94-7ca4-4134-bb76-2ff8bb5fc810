// oxlint-disable consistent-type-definitions
/// <reference types="../../../cypress/support/commands.d.ts" />

/**
 * This file should contain Extension Types for already existing types.
 * e.g - Some outdated library does not have good types for a specific module. In this case we can extend that type and improve it's quality so we don't lose type safety.
 */

import type { KarooUiInternalTheme } from '@karoo-ui/core'

import type { getTheme } from '@fleet-web/duxs/client-settings-helper'
import type { ProgressSnackbarImportProps } from '@fleet-web/modules/importer/types'

/* styled-components */

declare module 'styled-components' {
  type ThemeInterface = ReturnType<typeof getTheme> & {
    karooUi: KarooUiInternalTheme
  }
  interface DefaultTheme extends ThemeInterface {}
}

declare module 'google-map-react' {
  interface RestrictionMapOption {
    restriction?: google.maps.MapRestriction | null
  }
  interface MapOptions extends RestrictionMapOption {}
}

// ----- Declarations to fix compatibility with react 18 types https://github.com/DefinitelyTyped/DefinitelyTyped/pull/56210
declare module 'recharts' {
  export interface YAxisProps {
    children?: React.ReactNode
  }

  export interface PieProps {
    children?: React.ReactNode
  }
}

declare module 'react-split' {
  export interface SplitProps {
    children?: React.ReactNode
  }
}

// -----

// Needed for our setup starting from 3.4.0 types
declare module 'luxon' {
  interface TSSettings {
    // Even though we don't have this setting on, if we don't do it, luxon types will be too strict
    throwOnInvalid: true
  }
}

export type NotistackErrorVariantPropsOwnProps = {
  icon?: React.ReactNode
}

declare module 'notistack' {
  interface VariantOverrides {
    light: true // Setting to true whitelists the variant
    backgroundImportProgress: ProgressSnackbarImportProps
    error: NotistackErrorVariantPropsOwnProps
  }
}
