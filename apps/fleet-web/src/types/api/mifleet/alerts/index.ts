import type { IconName, IconPrefix } from '@fortawesome/fontawesome-svg-core'

import type { HourMinuteDate } from '@fleet-web/util-components/connectedCtIntl'

export type CostsAlert = {
  alertConfigurationId: string
  alertId: string
  companyId: number
  documentId: number
  documentLineId: number
  driverId: string
  expirationDate: HourMinuteDate
  expirationOdometer: string
  isDismissed: boolean
  isRead: boolean
  message: string
  objectId: number
  remainingDays: number
  remainingOdometer: string
  userId: number
  warningDate: HourMinuteDate
  warningType: string
  warningTypeId: string
  // Not from backend! src/duxs/mifleet/alerts/alerts.ts
  username: string
  driver: string
  warningDescription: string
  vehicleId: string
}

export type CostsActiveAlert = {
  alertConfigurationId: string
  alertName: string
  companyId?: number
  warningTypeId: string
  warningType?: string
  isWarningPerUser?: boolean
  isWarningPerVehicle?: boolean
  isWarningPerDriver?: boolean
  isDueDaysWarning?: boolean
  isDueOdometerWarning?: boolean
  isWarningForAll: boolean
  dueRemainingDays: number
  dueRemainingOdometer?: string
  alertObjects: Array<CostsActiveAlertObjects>
  alertEmails: Array<CostsActiveAlertEmail>
  alertPhones: Array<CostsActiveAlertPhone>
  users?: Array<CostsActiveAlertUser>
  // Not from backend! Check src/api/mifleet/alerts.ts
  emailsDaysPeriod: number
  smsDaysPeriod: number
  warnedEntities?: {
    users: string
    vehicles: string
    drivers: string
  }
}

type AlertTypes =
  | 'Vehicle'
  | 'Fuel'
  | 'Driver'
  | 'Integration'
  | 'Import'
  | 'Login'
  | 'Report'

export type CostsAlertTypes = {
  [warningTypeId: string]: {
    warningTypeId: string
    description: string
    type: AlertTypes
  }
}

export type CostsActiveAlertEmail = {
  alertConfigurationEmailId?: string
  alertConfigurationId?: string
  userId?: string
  driverId?: string
  email?: string
  recurrenceDays: number | null
}

export type CostsActiveAlertPhone = {
  alertConfigurationEmailId?: number
  alertConfigurationId?: number
  userId?: string
  fleetUserId?: string
  driverId?: string
  phone?: string
  recurrenceDays: number | null
}

export type CostsActiveAlertObjects = {
  alertConfigurationObjectId: number
  alertConfigurationId: number
  userId: number
  userGroupId: string
  vehicleId: number
  vehicleGroupId: string
  driverId: string
  driverGroupId: string
  fleetUserId: string
}

export type CostsActiveAlertUser = {
  username: string
  user_id: number
  fleet_user_id: string
  cell_number: number | null
  email: string | null
}
export type CostsActiveAlertDriver = {
  driver_id: string
  short_name: string
  cell_number: number | null
  email: string | null
}

export type CostsActiveAlertVehicle = { vehicle_id: number; plate: string }

export type CostsActiveAlertDriverGroups = {
  driver_group_id: number
  driver_group: string
}

export type CostsActiveAlertVehicleGroups = {
  vehicle_group_id: number
  vehicle_group: string
}

export type CostsAlertNotification = {
  alertId: string
  alertConfigurationId: string | null
  documentId: string | null
  warningTypeId: string
  companyId: string | null
  message: string | null
  userId: string | null
  driverId: string | null
  vehicleId: string | null
  warningDate: string | null
  warningType: string | null
}

export type FleetAlertsNotifications = {
  eventTs: HourMinuteDate
  notificationContact: Array<string>
  contactType: string
  notificationMsg: string
  registration: string
  vehicleId: string
  groupDescription: string
  triggerDescription: string
  statusDescription: string
  status: string
}

export type AlertNotification = {
  timestamp: number | undefined
  time: string | undefined
  when: string | undefined
  title: string
  message: string
  truncatedMessage: string
  icon: {
    name: IconName
    prefix: IconPrefix
    color: string
  }
  alertId: string
  alertConfigurationId?: string
  documentId?: string
  type: 'fleet' | 'costs' | 'appointment'
  fleetAlertType?: string | null
  triggers?: string
}
