/** @define Button */
.But<PERSON> {
  $minWidth: 125px;
  font-family: '<PERSON><PERSON>', sans-serif;
  align-items: center;
  background: $background;
  border: 1px solid $gray40;
  border-radius: 8px;
  box-sizing: border-box;
  color: $gray60;
  cursor: pointer;
  display: flex;
  font-size: 16px;
  font-weight: $bold;
  height: 38px;
  justify-content: center;
  line-height: 16px;
  min-width: $minWidth;
  padding: 10px 15px;
  text-align: center;
  transition: all 0.2s ease-in-out;

  &--maintained-width {
    width: $minWidth;
  }

  &--full-width {
    width: 100%;
  }

  &--square {
    height: 38px;
    min-width: 38px;
    padding: 0;

    /* postcss-bem-linter: ignore */
    svg {
      margin-right: 0 !important;
    }

    /* postcss-bem-linter: ignore */
    &.Button--small {
      height: 28px !important;
      min-width: 28px !important;
      padding: 5px;
      width: 28px;
    }
  }

  &--small {
    align-items: center;
    background-color: var(--styleButtonDefaultColourStandard);
    border-color: var(--styleButtonDefaultTextColourStandard);
    border-radius: 8px;
    color: var(--styleButtonDefaultTextColourStandard);
    display: flex;
    flex-direction: row;
    font-size: 12px;
    height: 28px;
    justify-content: center;
    line-height: 16px;
    min-width: 125px;
    outline: none;
    padding: 5px 15px;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonDefaultTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background-color: var(--styleButtonDefaultColourHover);
        border-color: var(--styleButtonDefaultTextColourHover);
        color: var(--styleButtonDefaultTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonDefaultTextColourHover);
        }
      }

      &:active {
        background-color: var(--styleButtonDefaultColourActive);
        border-color: var(--styleButtonDefaultColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonDefaultTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonDefaultTextColourActive);
        }
      }
    }
  }

  &--shadow {
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.15);
  }

  /* postcss-bem-linter: ignore */
  &.rounder {
    border-radius: 8px;
  }

  &--progress {
    animation: ButtonProgress 3s ease-out infinite;
    background: linear-gradient(to right, $blue 50%, $blue60 50%) !important;
  }

  &--noClickEvent {
    border-radius: 12px;
    pointer-events: none;
  }

  &:not([disabled]) {
    &:focus {
      border-color: var(--styleButtonDefaultTextColourHover);
      outline: none;
    }

    &:active {
      background-color: var(--styleActiveButtonsColour);
      border-color: transparent;
      color: $white;
    }
  }

  &--transparent {
    background-color: transparent;
  }

  &--default {
    background-color: var(--styleButtonDefaultColourStandard);
    border-color: var(--styleButtonDefaultBorderColourStandard);
    color: var(--styleButtonDefaultTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonDefaultTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background-color: var(--styleButtonDefaultColourHover);
        border-color: var(--styleButtonDefaultTextColourHover);
        color: var(--styleButtonDefaultTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonDefaultTextColourHover);
        }
      }

      &:active {
        background-color: var(--styleButtonDefaultColourActive);
        border-color: var(--styleButtonDefaultTextColourHover);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonDefaultTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonDefaultTextColourActive);
        }
      }
    }
  }

  &--gear {
    background-color: #fff;
  }

  &--action {
    background: var(--styleButtonActionColourStandard);
    border: none;
    color: var(--styleButtonActionTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonActionTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonActionColourHover);
        color: var(--styleButtonActionTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonActionTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonActionColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonActionTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonActionTextColourActive);
        }
      }
    }
  }

  &--white {
    background-color: var(--styleButtonWhiteColourStandard);
    border-color: var(--styleButtonWhiteTextColourStandard);
    color: var(--styleButtonWhiteTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonWhiteTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background-color: var(--styleButtonWhiteColourHover);
        border-color: var(--styleButtonWhiteTextColourHover);
        color: var(--styleButtonWhiteTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonWhiteTextColourHover);
        }
      }

      &:active {
        background-color: var(--styleButtonWhiteColourActive);
        border-color: var(--styleButtonWhiteTextColourActive);
        color: var(--styleButtonWhiteTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonWhiteTextColourActive);
        }
      }
    }
  }

  &--link {
    background: var(--styleButtonLinkColourStandard);
    border: none;
    color: var(--styleButtonLinkTextColourStandard);
    display: inline-block;
    line-height: 18px;
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonLinkTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonLinkColourHover);
        color: var(--styleButtonLinkTextColourHover);
        text-decoration: underline;

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonLinkTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonLinkColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonLinkTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonLinkTextColourActive);
        }
      }
    }
  }

  &--raised {
    background: var(--styleButtonRaisedColourStandard);
    border: none;
    box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.2);
    color: var(--styleButtonRaisedTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonRaisedTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonRaisedColourHover);
        color: var(--styleButtonRaisedTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonRaisedTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonRaisedColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonRaisedTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonRaisedTextColourActive);
        }
      }
    }
  }

  &--brown {
    background-color: transparent;
    border: 1px solid #ffbd4d;
    color: #d78b09;

    svg {
      fill: #d78b09;
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: #d18707;
        border-color: transparent;
        color: white;

        /* postcss-bem-linter: ignore */
        svg {
          fill: white;
        }
      }

      &:active {
        background: #d18707;
        border-color: transparent;
        color: white;
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);

        /* postcss-bem-linter: ignore */
        svg {
          fill: white;
        }
      }
    }
  }

  &--red {
    background: var(--styleButtonRedColourStandard);
    border: 1px solid var(--styleButtonRedTextColourStandard);
    color: var(--styleButtonRedTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonRedTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonRedColourHover);
        border-color: transparent;
        color: var(--styleButtonRedTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonRedTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonRedColourActive);
        border-color: var(--styleButtonRedColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonRedTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonRedTextColourActive);
        }
      }
    }
  }

  &--green,
  &--success,
  &--solidGreen {
    background: var(--styleButtonGreenColourStandard);
    border: 1px solid var(--styleButtonGreenTextColourStandard);
    color: var(--styleButtonGreenTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonGreenTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonGreenColourHover);
        border-color: transparent;
        color: var(--styleButtonGreenTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonGreenTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonGreenColourActive);
        border-color: var(--styleButtonGreenColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonGreenTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonGreenTextColourActive);
        }
      }
    }
  }

  &--success,
  &--solidAction {
    background: var(--styleButtonActionColourStandard);
    border: 1px solid var(--styleButtonActionTextColourStandard);
    color: var(--styleButtonActionTextColourStandard);
    outline: none;

    /* postcss-bem-linter: ignore */
    svg {
      fill: var(--styleButtonActionTextColourStandard);
      margin-right: 5px;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: var(--styleButtonActionColourHover);
        border-color: transparent;
        color: var(--styleButtonActionTextColourHover);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonActionTextColourHover);
        }
      }

      &:active {
        background: var(--styleButtonActionColourActive);
        border-color: var(--styleButtonActionColourActive);
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: var(--styleButtonActionTextColourActive);

        /* postcss-bem-linter: ignore */
        svg {
          fill: var(--styleButtonActionTextColourActive);
        }
      }
    }
  }

  &:disabled,
  &--disabled {
    pointer-events: none;
    opacity: 0.4;
  }

  &--grouped + .Button--grouped {
    margin-left: 12px;
  }
}

@keyframes ButtonProgress {
  from {
    background-position: 0;
    background-size: 200% 100%;
  }

  to {
    background-position: -100%;
    background-size: 200% 100%;
  }
}
