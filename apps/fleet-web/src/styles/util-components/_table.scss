$tableRowMinHeight: 44px;
$tableRowMinHeightLarge: 57px;

.ReactTable.react-table {
  border: none;
  height: 100%;
  text-align: center;
  width: 100%;

  &:has(+ .ToggleDropdown) {
    /* 43px = space occupied by the element next to the current element (38px width + 5px margin-left of ToggleDropdown) */
    width: calc(100% - 43px);
  }

  .react-table-spinner {
    position: absolute;
    z-index: 100;
  }

  .rt-table,
  .rt-tbody {
    overflow: auto;
  }

  // Add shadow to indicate potential for extra content
  .rt-table::after {
    box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
    content: ' ';
    display: block;
    left: 2%;
    position: relative;
    width: 96%;
  }

  // Allow absolute positioned items in cells
  .rt-tbody {
    position: relative;
  }

  // Header row
  .rt-thead {
    box-shadow: none;
    font-size: 18px;
    max-height: max-content;

    @include robotoCondensedBold;
  }

  // Rows
  .rt-tr-group {
    border: none;
    font-size: 14px;
    text-align: left;
  }

  .rt-tr {
    min-height: $tableRowMinHeight;
  }

  .rt-tr.is-active {
    background-color: #f47735 !important;
    color: white !important;
  }

  // Cells
  .rt-th,
  .rt-td {
    align-items: center;
    border: none;
    display: block;
    overflow: hidden;
    position: relative;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-word;
    padding: 8px;

    p {
      margin: 0 0 5px;
    }
  }

  .rt-th {
    display: flex;
  }

  /// TextInput invalid style for table
  /// @see src/styles/util-components/_text-input.scss
  .rt-td {
    border-right: 3px solid white;
    display: flex;

    & > .is-invalid {
      min-height: 50px;
    }
  }

  &.minimal {
    .rt-th,
    .rt-td {
      border: none;
    }
  }

  // Header Cells
  .-header .rt-th {
    font-size: 1rem;
    padding: 10px 5px;
    text-align: left;
    white-space: normal;
    word-break: break-word;

    @include robotoCondensedBold;

    .rt-resizable-header-content {
      max-width: 100%;
      overflow: unset;
      white-space: normal;
    }

    &.-cursor-pointer {
      padding: 10px 20px 10px 8px;

      &::after {
        color: $gray30;
        content: '\F0DC';
        font-family: $fontAwesome;
        font-weight: 400;
        margin-left: 5px;
      }

      &.-sort-asc {
        box-shadow: inset 0 -3px 0 0 var(--styleTableColumnFontColourActive);
        color: var(--styleTableColumnFontColourActive);

        &::after {
          color: var(--styleTableColumnFontColourActive);
          content: '\F0DE';
        }

        // For headers with icons
        & svg {
          fill: var(--styleTableColumnFontColourActive);

          & g,
          & path {
            fill: var(--styleTableColumnFontColourActive);
          }
        }
      }

      &.-sort-desc {
        box-shadow: inset 0 -3px 0 0 var(--styleTableColumnFontColourActive);
        color: var(--styleTableColumnFontColourActive);

        &::after {
          color: var(--styleTableColumnFontColourActive);
          content: '\F0DD';
        }

        // For headers with icons
        & svg {
          fill: var(--styleTableColumnFontColourActive);

          & g,
          & path {
            fill: var(--styleTableColumnFontColourActive);
          }
        }
      }
    }
  }

  .-pagination {
    border-top: none;
    box-shadow: none;

    @include robotoCondensedMedium;

    input {
      -moz-appearance: none;
      -webkit-appearance: none;
      border: 1px solid #ccc;
      border-radius: 0.3em;
      box-sizing: border-box;
      color: #444;
      display: block;
      font-family: sans-serif;
      font-size: 1rem;
      line-height: 1.3;
      margin: 0 3px;
      max-width: 100%;
      padding: 0.3em;
    }

    select {
      @extend input;

      background-image: url('@fleet-web/util-components/icons/caret-down.svg');
      background-position:
        right 0.7em top 50%,
        0 0;
      background-repeat: no-repeat, repeat;
      background-size:
        1.2em auto,
        100%;
      padding: 0.3em 2em 0.3em 0.5em;
    }
  }

  .-center {
    justify-content: center;
    flex: 2;
  }

  .-previous .-btn,
  .-next .-btn {
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 8px;

    @include robotoCondensedBold;

    @media print {
      display: none;
    }

    .pagbtn {
      &.right {
        .text {
          margin-right: 50px;
        }
      }

      &.left {
        .text {
          margin-left: 50px;
        }
      }
    }
  }

  .-btn:disabled {
    .pagbtn {
      &.right {
        .text {
          margin-right: 5px;
        }
      }

      &.left {
        .text {
          margin-left: 5px;
        }
      }
    }
  }

  .-btn:not([disabled]):hover {
    background-color: #e9e9e9;
    color: rgba(0, 0, 0, 0.6);
  }

  .tabSelected {
    box-shadow: inset 0 -3px 0 0 $orange;
    color: $orange;
  }

  .tabUnselected {
    box-shadow: none !important;
    color: #000 !important;

    &.-sort-asc {
      &::after {
        color: #000 !important;
      }
    }

    &.-sort-desc {
      &::after {
        color: #000 !important;
      }
    }
  }
}

@-moz-document url-prefix() {
  .ReactTable.react-table .rt-tbody {
    scrollbar-width: none;
  }
}

/** @define Table */
.Table {
  display: flex;
  position: relative;
  width: 100%;

  &-selectedRow {
    background: $yellow20 !important;
  }

  &-options {
    height: 38px;
    position: relative;
    top: 5px;
    width: 38px;
    margin-left: 5px;

    &-inner {
      background: none;
      border: none;
      border-radius: 4px;
      box-shadow: initial;
      display: inline-block;
      line-height: 30px;

      &.is-open {
        background: white;
        border: 1px solid $gray40;
        padding: 8px;
      }
    }
  }

  &-focusedColumn {
    background: $yellow10;
    white-space: normal !important;
  }

  // postcss-bem-linter: ignore
  .-odd &-focusedColumn {
    background: $yellow20;
  }

  &.editing {
    .rt-table,
    .rt-tbody,
    .rt-td.editable {
      overflow: visible !important;
    }
  }
}

.largeRow {
  .rt-tr {
    min-height: $tableRowMinHeightLarge !important;
  }
}
