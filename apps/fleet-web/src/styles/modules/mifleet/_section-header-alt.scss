.SectionHeader {
  display: flex;

  &-column {
    border-bottom: 1px solid #999;
    flex-direction: column;
    margin-bottom: 10px;
    padding: 5px;

    &.gdpr {
      margin-bottom: 31px;
    }
  }

  &-withSub {
    border-bottom: 1px solid #999;
    flex-direction: column;
    padding-bottom: 10px;
  }

  &--settings-table {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }

  &-title {
    align-items: center;
    display: flex;
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 28px;
    font-weight: 400;
    font-weight: normal;
    line-height: 33px;
    margin: 0;

    &-badge {
      margin-left: 10px;
    }
  }

  &-row {
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;

    &-bottomSeparator {
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }
  }
}
