.TimelineTripGraph {
  &-close {
    float: right;
  }

  &-tooltip-text {
    color: white;
    font-size: 12px;
    margin: auto;
  }

  &-header {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 60px 0 40px;

    &-button {
      margin: 0 10px !important;
    }
  }

  &-chart {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 100%;
    justify-content: center;
    text-align: center;
    width: 95%;

    &-name-container-X {
      align-self: flex-end;
      color: #666;
      font-family: $roboto;
      letter-spacing: 0;
      margin: 0 20px;
      text-align: right;
    }

    &-name-container-Y {
      dominant-baseline: middle;
      fill: #666;
      font-family: $roboto;
      text-anchor: middle;
    }
  }
}
