import { Redirect, Route, Switch, useRouteMatch } from 'react-router'

import { LIST } from '@fleet-web/modules/app/components/routes/list'
import Vehicles from '@fleet-web/modules/lists/vehicles-list'
import VehicleRawData from '@fleet-web/modules/map-view/timeline/vehicle-raw-data'

export default function ListVehicles() {
  const { path } = useRouteMatch()

  return (
    <Switch>
      <Route
        exact
        path={path}
      >
        <Vehicles />
      </Route>

      <Route path={LIST.subMenusRoutes.VEHICLES.subPaths.RAW_DATA}>
        <VehicleRawData />
      </Route>

      <Route path="*">
        <Redirect to={path} />
      </Route>
    </Switch>
  )
}
