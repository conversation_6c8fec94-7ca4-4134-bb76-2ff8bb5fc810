import { useEffect, useMemo, useState } from 'react'
import { startCase } from 'lodash'
import styled, { css, keyframes } from 'styled-components'

import { DefectState, defectStates } from '@fleet-web/api/dvirs'
import useDvirDetailsQuery from '@fleet-web/api/dvirs/useDvirDetailsQuery'
import Icon from '@fleet-web/components/Icon'
import { UserFormattedLengthInKmOrMiles } from '@fleet-web/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { CloseButton } from '@fleet-web/shared/components'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import type { ExcludeStrict } from '@fleet-web/types/utils'
import {
  Button,
  FormattedUserDate,
  FormattedUserTime,
  Spinner,
} from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import GridIcon from 'assets/icons/grid.png'
import ListIcon from 'assets/icons/list.png'

import { translateDvirDetail } from '../index'
import Collapsible from './collapsible'

type Props = {
  closeDetailPanel: () => void
  vehicleDefectsHeaderId: string
}

const DvirDetail = ({ vehicleDefectsHeaderId, closeDetailPanel }: Props) => {
  const dvirDetailsQuery = useDvirDetailsQuery(vehicleDefectsHeaderId)

  return (
    <FloatingPanel>
      {(() => {
        if (dvirDetailsQuery.data) {
          return (
            <DvirDetailWithData
              data={dvirDetailsQuery.data}
              closeDetailPanel={closeDetailPanel}
            />
          )
        }
        switch (dvirDetailsQuery.status) {
          case 'pending': {
            return <Spinner />
          }
          default: {
            return (
              <TitleBlock>
                <CloseButton onClick={closeDetailPanel} />
              </TitleBlock>
            )
          }
        }
      })()}
    </FloatingPanel>
  )
}

type DisplayOption = 'grid' | 'list'

const displayOptions: Array<DisplayOption> = ['list', 'grid']

function DvirDetailWithData({
  data,
  closeDetailPanel,
}: {
  data: ExcludeStrict<ReturnType<typeof useDvirDetailsQuery>['data'], undefined>
  closeDetailPanel: () => void
}) {
  const [imageSelection, setImageSelection] = useState<Array<boolean>>([])
  const [displayOption, setDisplayOption] = useState<DisplayOption>(displayOptions[0])

  const detail = useMemo(() => translateDvirDetail(data), [data])

  const allImages = useMemo(() => {
    if (detail.photos && detail.photos.length > 0) {
      const repairSignature = detail.photos.find((photo) => photo.type === 'repair')

      const inspectionSignature = detail.photos.find(
        (photo) => photo.type === 'inspection',
      )

      const photos = detail.photos.filter((photo) => photo.type === 'defect')
      return {
        repairSignature,
        inspectionSignature,
        photos,
      }
    }
    return {
      repairSignature: undefined,
      inspectionSignature: undefined,
      photos: [],
    }
  }, [detail.photos])

  useEffect(() => {
    if (allImages.photos.length > 0) {
      setImageSelection(new Array(allImages.photos.length).fill(true))
    }
  }, [allImages.photos])

  const toggleImage = (i: number) => {
    setImageSelection((prevSelection) => {
      const newSelection = [...prevSelection]
      newSelection[i] = !imageSelection[i]
      return newSelection
    })
  }

  const renderImageButtons = () =>
    allImages.photos.map((_, imageIndex) => {
      const imgNumber = imageIndex + 1
      return (
        <ImageButton
          active={imageSelection[imageIndex]}
          key={imgNumber}
          rightMargin={imgNumber !== allImages.photos.length}
        >
          <Button
            label={imgNumber}
            square
            type="button"
            withTooltip
            tooltipMessage={`Image ${imgNumber}`}
            className="disable-focus disable-hover"
            onClick={() => toggleImage(imageIndex)}
          />
        </ImageButton>
      )
    })

  const renderHistoryItem = (item: {
    note: string
    defectStateId: DefectState
    defectState: string
    date: string
    odometer: string
    location: string
    driverName: string
    signature?: Record<string, any>
  }) => (
    <HistoryDetail defectState={item.defectStateId}>
      <FlexDiv>
        <div className="bold">{item.note}</div>
        <div>
          <Icon
            icon={
              item.defectStateId === DefectState.AwaitingRepair
                ? 'exclamation-circle'
                : 'check'
            }
            className="icon"
            style={{ width: 16, height: 16 }}
          />
          {item.defectState}
        </div>
      </FlexDiv>
      <FlexDiv>
        <div>
          <FormattedUserDate value={item.date} /> @
          <FormattedUserTime value={item.date} />
        </div>
        <div>
          <UserFormattedLengthInKmOrMiles
            valueInKm={Number.parseInt(item.odometer, 10)}
            transformValueBeforeFormatting={Math.round}
          />
        </div>
      </FlexDiv>
      {item.location && <FlexDiv>{item.location}</FlexDiv>}
      <FlexDiv>
        <div>{item.driverName}</div>
        {item.signature && (
          <img
            src={
              item.signature.url ?? `data:image/png;base64, ${item.signature.base64}`
            }
          ></img>
        )}
      </FlexDiv>
    </HistoryDetail>
  )

  const renderImages = () =>
    allImages.photos.map(
      (item, imageIndex) =>
        imageSelection[imageIndex] && (
          <StyledImg
            // eslint-disable-next-line react/no-array-index-key
            key={imageIndex}
            src={item.url ?? `data:image/png;base64, ${item.base64}`}
          ></StyledImg>
        ),
    )

  return (
    <>
      <TitleBlock>
        <Header>
          {detail.trailerRegistration
            ? `${detail.trailerRegistration} - ${ctIntl.formatMessage({
                id: detail.defectType ?? '',
              })} `
            : ctIntl.formatMessage({ id: detail.defectType ?? '' })}
        </Header>
        <CloseButton onClick={closeDetailPanel} />
      </TitleBlock>
      <StyledHr />
      <InfoBlock>
        <VehicleName>{detail.vehicleRegistration}</VehicleName>
        <Status defectState={detail.defectStateId}>{detail.defectState}</Status>
      </InfoBlock>

      <Collapsible title="History">
        {detail.inspectDate &&
          renderHistoryItem({
            note: detail.inspectNotes || 'N/A',
            defectStateId: DefectState.PassedInspection,
            defectState: ctIntl.formatMessage({
              id: defectStates[DefectState.PassedInspection],
            }),
            date: detail.inspectDate,
            odometer: detail.inspectOdometer,
            location: '',
            driverName: detail.inspectDriverName,
            signature: allImages.inspectionSignature,
          })}
        {detail.repairDate &&
          renderHistoryItem({
            note: detail.repairNotes || 'N/A',
            defectStateId: DefectState.Repaired,
            defectState: ctIntl.formatMessage({
              id: defectStates[DefectState.Repaired],
            }),
            date: detail.repairDate,
            odometer: detail.repairOdometer,
            location: '',
            driverName: detail.repairDriverName,
            signature: allImages.repairSignature,
          })}
        {detail.eventTs &&
          renderHistoryItem({
            note: detail.description || 'N/A',
            defectStateId: DefectState.AwaitingRepair,
            defectState: ctIntl.formatMessage({
              id: defectStates[DefectState.AwaitingRepair],
            }),
            date: detail.eventTs,
            odometer: detail.odometer,
            location: detail.locationDescription,
            driverName: detail.driverName,
          })}
      </Collapsible>

      <Collapsible title="Images">
        {allImages.photos && allImages.photos.length > 0 ? (
          <>
            <FlexBox>
              <ImageButtonGroup num={allImages.photos.length}>
                {renderImageButtons()}
              </ImageButtonGroup>
              <VerticalLine />
              <OptionButtons>
                {displayOptions.map((o, i) => (
                  <OptionButton
                    key={o}
                    index={i}
                    active={o === displayOption}
                  >
                    <Button
                      square
                      type="button"
                      withTooltip
                      tooltipMessage={startCase(o)}
                      className="disable-focus disable-hover"
                      iconElement={<IconImg src={o === 'list' ? ListIcon : GridIcon} />}
                      onClick={() => setDisplayOption(o)}
                    />
                  </OptionButton>
                ))}
              </OptionButtons>
            </FlexBox>
            <ImageGroup type={displayOption}>{renderImages()}</ImageGroup>
          </>
        ) : (
          <FlexDiv>{ctIntl.formatMessage({ id: 'No data available' })}</FlexDiv>
        )}
      </Collapsible>
    </>
  )
}

export default DvirDetail

const slideLeft = keyframes`
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0px);
  }
`

const FloatingPanel = styled.div`
  animation: ${slideLeft} 0.2s ease-in-out;
  background-color: #ffffff;
  box-shadow: 0 9px 14px 0 rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 18px;
  height: 100%;
  padding: 20px;
  padding-top: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 400px;
  z-index: 12;
  overflow: scroll;
`

const TitleBlock = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  padding-bottom: 35px;
`

const Header = styled.div`
  padding-top: 7px;
  color: #333333;
  font-size: 22px;
  line-height: 25px;
`

export const StyledHr = styled.div`
  border-top: 1px solid rgba(155, 155, 155, 0.4);
  height: 1px;
`

const InfoBlock = styled.div`
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
`

const VehicleName = styled.div`
  color: #333333;
  font-size: 18px;
  font-weight: bold;
  line-height: 21px;
`

const Status = styled.div<{ defectState: DefectState }>`
  padding: 6px 16px;
  font-size: 11px;
  font-weight: bold;
  line-height: 14px;
  text-align: center;
  border-radius: 12px;
  background-color: ${({ defectState }) =>
    // eslint-disable-next-line no-nested-ternary
    defectState === DefectState.Repaired
      ? '#BCD96E'
      : defectState === DefectState.PassedInspection
        ? '#2DAB33'
        : '#F58449'};
  color: #fff;
`

const HistoryDetail = styled.div<{ defectState: DefectState }>`
  img {
    max-width: 220px;
    max-height: 50px;
  }

  .bold {
    font-weight: bold;
  }

  .icon {
    font-size: 16px;
    color: ${({ defectState }) =>
      // eslint-disable-next-line no-nested-ternary
      defectState === DefectState.Repaired
        ? '#BCD96E'
        : defectState === DefectState.PassedInspection
          ? '#2DAB33'
          : '#F58449'};
    margin-right: 5px;
    margin-top: 2px;
  }
`

const FlexDiv = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  line-height: 16px;
  color: #333333;
  padding-bottom: 12px;
`
type ImageButtonProps = {
  active: boolean
  rightMargin: boolean
  key: number
}

const ImageButton = styled.div<ImageButtonProps>`
  button.disable-hover.disable-focus {
    background-color: ${(props) => (props.active ? '#6BA238' : 'white')};
    border: 1px solid #6ba238;
    color: ${(props) => (props.active ? 'white' : '#6BA238')};
    margin-right: ${(props) => (props.rightMargin ? '10px' : '0px')};
  }
`

const FlexBox = styled.div`
  display: flex;
  justify-content: flex-end;
`

const ImageButtonGroup = styled.div<{ num: number }>`
  ${(props) =>
    props.num <= 5 &&
    css`
      display: flex;
      margin-right: 10px;
    `}
  ${(props) =>
    props.num > 5 &&
    css`
      display: grid;
      grid-template-columns: repeat(5, 50px);
      grid-row-gap: ${spacing[2]};
    `}
  margin-bottom: 20px;
`
const VerticalLine = styled.div`
  height: 28px;
  margin: ${spacing[1]} ${spacing[1]} ${spacing[1]} -${spacing[1]};
  border: 1px solid #cccccc;
`

const OptionButtons = styled.div`
  display: flex;
`

const OptionButton = styled.div<{ index: number; active: boolean }>`
  ${(props) =>
    props.index !== 0 &&
    css`
      margin-left: ${spacing[2]};
    `}
  button.disable-hover.disable-focus {
    border: 1px solid #cccccc;
    ${(props) =>
      props.active &&
      css`
        background-color: #cccccc;
      `}
  }
`
const IconImg = styled.img`
  width: 20px;
`

const ImageGroup = styled.div<{ type: DisplayOption }>`
  display: grid;
  ${(props) =>
    props.type === 'grid' &&
    css`
      grid-template-columns: minmax(auto, 175px) minmax(auto, 175px);
      column-gap: 10px;
    `}
  grid-row-gap: 12px;
`

const StyledImg = styled.img`
  width: 100%;
`
