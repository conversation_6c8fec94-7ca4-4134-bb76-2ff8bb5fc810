import { useState } from 'react'
import styled from 'styled-components'

import Icon from '@fleet-web/components/Icon'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { StyledHr } from './dvir-detail'

type CollapsibleProps = {
  title: string
  children?: React.ReactNode
}

const Collapsible = ({ title, children }: CollapsibleProps) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true)
  return (
    <CollapsibleContainer>
      <CollapsibleTitle onClick={() => setIsExpanded(!isExpanded)}>
        <div className="title">{ctIntl.formatMessage({ id: title })}</div>
        <Icon
          icon="angle-down"
          className="icon"
        />
      </CollapsibleTitle>
      <StyledHr />
      <CollapsibleDetail className={isExpanded ? 'open' : 'closed'}>
        {children}
      </CollapsibleDetail>
    </CollapsibleContainer>
  )
}

export default Collapsible

const CollapsibleContainer = styled.div`
  padding-top: 20px;
`

const CollapsibleTitle = styled.div`
  display: flex;
  justify-content: space-between;
  padding-bottom: 5px;

  .title {
    color: #666666;
    font-size: 18px;
    line-height: 21px;
  }

  .icon {
    color: #666666;
    font-size: 16px;
    line-height: 16px;
  }
`

const CollapsibleDetail = styled.div`
  padding-top: 19px;
  &.closed {
    overflow: hidden;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
    transition: all 0.2s ease;
    will-change: transform;
  }

  &.open {
    max-height: 1000px;
    overflow: hidden;
    transition: all 0.2s ease;
    will-change: transform;
  }
`
