import { useMutation, useQueryClient } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { makeMutationErrorHandlerWithSnackbar } from '@fleet-web/api/helpers'
import { useSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { invalidateQueriesOnEntitiesMutation } from '@fleet-web/modules/shared/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { UpdateVehicleGroupsMapping } from './types'

function updateVehicleGroupMappingMutation(
  params: UpdateVehicleGroupsMapping.ApiFnInput,
) {
  // We change to a single vehicle group mapped to one facility only. This change will be made in FE.
  return apiCallerNoX('ct_fleet_update_vehicle_group_site_locations', {
    group_vehicle_id: params.vehicleGroupId,
    site_location_ids: params.siteLocationId ? [params.siteLocationId] : [],
  } satisfies UpdateVehicleGroupsMapping.ApiInput)
}

const useUpdateVehicleGroupMappingMutation = () => {
  const { enqueueSnackbarWithCloseAction } = useSnackbarWithCloseAction()

  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateVehicleGroupMappingMutation,
    onSuccess() {
      invalidateQueriesOnEntitiesMutation(queryClient, {
        entities: 'all_standard',
      })
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'vehicleGroupsMapping.edit.success',
        }),
        { variant: 'success' },
      )
    },
    ...makeMutationErrorHandlerWithSnackbar({
      customErrorMessage: ctIntl.formatMessage({
        id: 'vehicleGroupsMapping.edit.error',
      }),
    }),
  })
}

export default useUpdateVehicleGroupMappingMutation
