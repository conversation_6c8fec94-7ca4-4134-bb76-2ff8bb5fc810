import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { makeQueryErrorHandlerWithToast } from '@fleet-web/api/helpers'
import { safeParseFromZodSchema } from '@fleet-web/util-functions/zod-utils'

import {
  vehicleGroupMappingIdSchema,
  type FetchVehicleGroupsMapping,
  type VehicleGroupMappingId,
} from './types'

export const fetchVehicleGroupsMapping = async () => {
  const res: FetchVehicleGroupsMapping.ApiOutput = await apiCallerNoX(
    'ct_fleet_fetch_vehicle_group_site_locations',
  )

  const vehicleGroupsMapping = res.ct_fleet_fetch_vehicle_group_site_locations.map(
    (g) => ({
      vehicleGroupId: safeParseFromZodSchema(
        vehicleGroupMappingIdSchema,
        g.vehicle_group_id,
        {
          defaultValue: ({ input }) => String(input) as VehicleGroupMappingId,
        },
      ),
      vehicleGroupName: g.vehicle_group_name,
      // We change to a single vehicle group mapped to one facility only. This change will be made in FE, so we only get the first value here.
      siteLocation:
        g.site_locations.length > 0
          ? {
              siteLocationId: g.site_locations[0].site_location_id,
              siteLocationName: g.site_locations[0].site_location_name,
            }
          : null,
    }),
  )

  const vehicleGroupsWithoutLocations = res.ct_fleet_fetch_vehicle_groups
    .filter(
      (g) =>
        !res.ct_fleet_fetch_vehicle_group_site_locations.some(
          (mappingGroup) => mappingGroup.vehicle_group_id === g.id,
        ),
    )
    .map((g) => ({
      vehicleGroupId: safeParseFromZodSchema(vehicleGroupMappingIdSchema, g.id, {
        defaultValue: ({ input }) => String(input) as VehicleGroupMappingId,
      }),
      vehicleGroupName: g.name,
      siteLocation: null,
    }))

  return {
    vehicleGroupsMapping: [...vehicleGroupsMapping, ...vehicleGroupsWithoutLocations],
    locations: res.ct_fleet_fetch_site_locations.map((l) => ({
      id: l.site_location_id,
      name: l.site_location_name,
    })),
    vehicleGroups: res.ct_fleet_fetch_vehicle_groups.map((g) => ({
      id: safeParseFromZodSchema(vehicleGroupMappingIdSchema, g.id, {
        defaultValue: ({ input }) => String(input) as VehicleGroupMappingId,
      }),
      name: g.name,
    })),
  }
}

const createKey = () => ['list/vehicleGroupsMapping'] as const

export default function useVehicleGroupsMappingQuery() {
  return useQuery({
    queryKey: createKey(),
    queryFn: fetchVehicleGroupsMapping,
    ...makeQueryErrorHandlerWithToast(),
  })
}
