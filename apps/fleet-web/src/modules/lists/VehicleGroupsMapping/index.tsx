import { useMemo, useState } from 'react'
import {
  Box,
  DataGrid,
  GridActionsCellItem,
  LinearProgress,
  OverflowTypography,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowParams,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/EditOutlined'
import { match } from 'ts-pattern'

import DataStatePlaceholder from '@fleet-web/components/_data/Placeholder'
import {
  getFacilitiesTranslatorFn,
  getVehicleGroupLabel,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { FetchVehicleGroupsMapping } from './api/types'
import useVehicleGroupsMappingQuery from './api/useVehicleGroupsMappingQuery'
import VehicleGroupMappingEditDrawer from './VehicleGroupMappingEditDrawer'

type VehicleGroupMappingType =
  FetchVehicleGroupsMapping.Return['vehicleGroupsMapping'][number]

const VehicleGroupMapping = () => {
  const [editingVehicleGroupMapping, setEditingVehicleGroupMapping] = useState<
    VehicleGroupMappingType | undefined
  >(undefined)
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const { groupsLabel, groupLabel } = useTypedSelector(getVehicleGroupLabel)

  const vehicleGroupsMappingQuery = useVehicleGroupsMappingQuery()

  const columnHelper = useDataGridColumnHelper<VehicleGroupMappingType>({
    filterMode: 'client',
  })

  const getRowId = useCallbackBranded(
    (row: VehicleGroupMappingType) => row.vehicleGroupId,
    [],
  )

  const columns = useMemo(
    (): Array<GridColDef<VehicleGroupMappingType>> => [
      columnHelper.string((_, row) => row.vehicleGroupName, {
        field: 'vehicleGroupName',
        headerName: ctIntl.formatMessage({
          id: groupLabel,
        }),
        flex: 1,
      }),
      columnHelper.string(
        (_, row) => (row.siteLocation ? row.siteLocation.siteLocationName : ''),
        {
          field: 'siteLocations',
          headerName: translateFacilitiesTerm('Facility'),
          flex: 1,
          renderCell: ({ value }) => (
            <OverflowTypography
              typographyProps={{
                variant: 'body2',
              }}
            >
              {value}
            </OverflowTypography>
          ),
        },
      ),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'center',
        getActions: ({ row }) => [
          <Tooltip
            key="edit-vehicle-groups-mapping"
            title={ctIntl.formatMessage({ id: 'Edit' })}
          >
            <GridActionsCellItem
              label={ctIntl.formatMessage({ id: 'Edit' })}
              icon={<EditIcon />}
              onClick={() => {
                setEditingVehicleGroupMapping(row)
              }}
            />
          </Tooltip>,
        ],
      },
    ],
    [columnHelper, groupLabel, translateFacilitiesTerm],
  )

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        height: '100%',
        width: '100%',
        p: 2,
        gap: 2,
      }}
    >
      {!!editingVehicleGroupMapping && (
        <VehicleGroupMappingEditDrawer
          onClose={() => {
            setEditingVehicleGroupMapping(undefined)
          }}
          editingVehicleGroupMapping={editingVehicleGroupMapping}
          locations={vehicleGroupsMappingQuery.data?.locations ?? []}
        />
      )}
      <Box>
        <Typography variant="h5">
          {ctIntl.formatMessage({
            id: `${groupsLabel} ${translateFacilitiesTerm('Facility')} Mapping`,
          })}
        </Typography>
      </Box>
      {match(vehicleGroupsMappingQuery)
        .with({ status: 'success' }, { status: 'pending' }, ({ data, fetchStatus }) => (
          <UserDataGridWithSavedSettingsOnIDB
            Component={DataGrid}
            dataGridId="vehicle-groups-mapping"
            getRowId={getRowId}
            disableVirtualization
            disableRowSelectionOnClick
            onRowClick={({ row }: GridRowParams<VehicleGroupMappingType>) => {
              setEditingVehicleGroupMapping(row)
            }}
            loading={fetchStatus === 'fetching'}
            autoPageSize
            slots={{
              toolbar: KarooToolbar,
              loadingOverlay: LinearProgress,
              noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
            }}
            slotProps={{
              toolbar: KarooToolbar.createProps({
                slots: {
                  searchFilter: { show: true },
                  filterButton: { show: true },
                  settingsButton: { show: true },
                },
              }),
            }}
            columns={columns}
            rows={data?.vehicleGroupsMapping ?? []}
            pagination
          />
        ))
        .with({ status: 'error' }, () => <DataStatePlaceholder type={'error'} />)
        .exhaustive()}
    </Box>
  )
}

export default VehicleGroupMapping
