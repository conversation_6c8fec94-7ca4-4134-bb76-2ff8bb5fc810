import { useMemo, useState } from 'react'
import {
  DataGridAsTabItem,
  getDataGridClientModeIncludedSelectedRowIds,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridToolbarExport,
  LinearProgress,
  OverflowTypography,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import ViewIcon from '@mui/icons-material/Visibility'
import { useIntl } from 'react-intl'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import type { FetchGeofencesListResolved } from '@fleet-web/api/geofences'
import type { GeofenceId } from '@fleet-web/api/types'
import ConfirmationModal from '@fleet-web/components/_modals/Confirmation'
import { getEditableGeofences, getLoading } from '@fleet-web/duxs/geofences'
import { getGeofences } from '@fleet-web/duxs/geofences-selector'
import {
  getSettings_UNSAFE,
  getUserPositionAddressStateGetter,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { getVehiclesById } from '@fleet-web/duxs/vehicles'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { UserFormattedPositionAddress } from '@fleet-web/modules/components/connected/UserFormattedPositionAddress'
import { EditGeoTabs } from '@fleet-web/modules/map-view/FleetMapView/Geofence/schema'
import { getGeofenceDetailsModalMainPath } from '@fleet-web/modules/map-view/FleetMapView/Geofence/utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { DataGridDeleteButtonWithCounter } from '@fleet-web/shared/data-grid/DataGridDeleteButtonWithCounter'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { ctToast } from '@fleet-web/util-components'
import { CellTextWithMore } from '@fleet-web/util-components/CellTextWithMore'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useDeleteGeofencesMutation } from './api/api'

type DataGridRow = FetchGeofencesListResolved['geofences'][number]

export default function GeofenceList() {
  const { formatList } = useIntl()

  const history = useHistory()
  const gridApiRef = useGridApiRef()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  const geofenceList = useTypedSelector(getGeofences)
  const vehiclesList = useTypedSelector(getVehiclesById)
  const isLoading = useTypedSelector(getLoading)
  const editableGeofences = useTypedSelector(getEditableGeofences)
  const getUserPositionAddressState = useTypedSelector(
    getUserPositionAddressStateGetter,
  )
  const { geofencesDeleteGeofence } = useTypedSelector(getSettings_UNSAFE)

  const deleteGeofencesMutation = useDeleteGeofencesMutation()

  const [deleteModal, setDeleteModal] = useState<{
    data: Array<{ id: GeofenceId; name: string }>
  } | null>(null)

  const [selectedRowIds, setSelectedRowIds] = useState<ReadonlySet<GeofenceId>>(
    new Set(),
  )

  const handleSelectionModelChange = (selectionModel: GridRowSelectionModel) => {
    setSelectedRowIds(
      getDataGridClientModeIncludedSelectedRowIds(
        selectionModel as GridRowSelectionModel<GeofenceId>,
        { gridApiRef },
      ),
    )
  }

  const handleConfirm = () => {
    if (deleteModal) {
      const { data } = deleteModal
      const deletedIds = data.map((item) => item.id)
      deleteGeofencesMutation.mutate(
        { ids: deletedIds },
        {
          onSuccess: () => {
            ctToast.fire(
              'success',
              data.length > 1
                ? ctIntl.formatMessage({
                    id: 'Geofences were successfully deleted.',
                  })
                : ctIntl.formatMessage({ id: 'The geofence ' }) +
                    `"${data[0].name}" ` +
                    ctIntl.formatMessage({ id: 'was successfully deleted.' }),
            )
            setDeleteModal(null)
            setSelectedRowIds(
              new Set(
                Array.from(selectedRowIds).filter((id) => !deletedIds.includes(id)),
              ),
            )
          },
        },
      )
    }
  }

  // an id:name map to fetch geofence name fast
  const geofenceIdNameMaps: Record<GeofenceId, string> = useMemo(
    () =>
      geofenceList.reduce(
        (acc, cur) => ({
          ...acc,
          [cur.id]: cur.name,
        }),
        {},
      ),
    [geofenceList],
  )

  const editableGeofenceIdNameMaps: Record<GeofenceId, string> = useMemo(
    () =>
      editableGeofences.reduce(
        (acc, cur) => ({
          ...acc,
          [cur.id]: cur.name,
        }),
        {},
      ),
    [editableGeofences],
  )

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      { ...GRID_CHECKBOX_SELECTION_COL_DEF, hideable: false },
      columnHelper.string((_, row) => row.name, {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Name' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.owner, {
        field: 'owner',
        headerName: ctIntl.formatMessage({ id: 'Owner' }),
        flex: 1,
      }),
      columnHelper.string(
        (_, row) =>
          match(
            getUserPositionAddressState({
              address: row.address,
              gpsFixType: null,
            }),
          )
            .with('EMPTY', () => '')
            .with({ visibility: 'PRIVATE' }, () =>
              ctIntl.formatMessage({ id: 'Privacy Enabled' }),
            )
            .with(
              { visibility: 'PUBLIC' },
              ({ processedDescriptionText }) => processedDescriptionText,
            )
            .exhaustive(),
        {
          field: 'address',
          headerName: ctIntl.formatMessage({ id: 'Location' }),
          flex: 1,
          renderCell: ({ row }) => (
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              <UserFormattedPositionAddress
                gpsFixType={null}
                address={row.address}
              />
            </OverflowTypography>
          ),
        },
      ),
      columnHelper.string(
        (_, row) =>
          row.isAllVehicles
            ? ctIntl.formatMessage({ id: 'All' })
            : formatList(
                row.vehicleIds.reduce((acc, curr) => {
                  const vehicle = vehiclesList.get(curr)
                  if (vehicle) {
                    acc.push(vehicle.name)
                  }

                  return acc
                }, [] as Array<string>),
              ),

        {
          field: 'vehicles',
          headerName: ctIntl.formatMessage({ id: 'Vehicles' }),
          flex: 1,
          renderCell: ({ row, value }) =>
            row.isAllVehicles ? (
              ctIntl.formatMessage({ id: 'All' })
            ) : (
              <CellTextWithMore value={value ?? ''} />
            ),
          minWidth: 250,
        },
      ),
      columnHelper.string(
        (_, row) =>
          row.isAllGroups
            ? ctIntl.formatMessage({ id: 'All' })
            : formatList(row.groups.map((g) => g.name)),
        {
          field: 'groups',
          headerName: ctIntl.formatMessage({ id: 'Groups' }),
          flex: 1,
          renderCell: ({ row, value }) =>
            row.isAllGroups ? (
              ctIntl.formatMessage({ id: 'All' })
            ) : (
              <CellTextWithMore value={value ?? ''} />
            ),
          minWidth: 250,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        flex: 1,
      }),
      columnHelper.dateTime({
        field: 'lastUpdated',
        headerName: ctIntl.formatMessage({ id: 'Last Update' }),
        valueGetter: (_, row) => row.lastUpdated,
        flex: 1,
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        align: 'right',
        getActions: ({ row: { name, id } }) => [
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Edit' })}
            arrow
            key="edit"
            placement="top"
          >
            <GridActionsCellItem
              data-testid={`Geofence-Edit-${id}`}
              label={ctIntl.formatMessage({ id: 'Edit' })}
              icon={<EditIcon />}
              onClick={() => {
                history.push(
                  getGeofenceDetailsModalMainPath(history.location, {
                    geo: 'edit',
                    id: id as GeofenceId,
                    tab: EditGeoTabs.Details,
                  }),
                )
              }}
              disabled={(() =>
                !Object.prototype.hasOwnProperty.call(
                  editableGeofenceIdNameMaps,
                  id,
                ))()}
            />
          </Tooltip>,
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Delete' })}
            arrow
            key="delete"
            placement="top"
          >
            <GridActionsCellItem
              data-testid={`Geofence-Delete-${id}`}
              label={ctIntl.formatMessage({ id: 'Delete' })}
              icon={<DeleteIcon />}
              onClick={() => setDeleteModal({ data: [{ id, name }] })}
              disabled={(() =>
                !geofencesDeleteGeofence ||
                !Object.prototype.hasOwnProperty.call(
                  editableGeofenceIdNameMaps,
                  id,
                ))()}
            />
          </Tooltip>,
          <Tooltip
            title={ctIntl.formatMessage({ id: 'View Activity' })}
            arrow
            key="viewActivity"
            placement="top"
          >
            <GridActionsCellItem
              data-testid={`Geofence-View-${id}`}
              label={ctIntl.formatMessage({ id: 'View Activity' })}
              icon={<ViewIcon />}
              onClick={() =>
                history.push(
                  getGeofenceDetailsModalMainPath(history.location, {
                    geo: 'edit',
                    id: id as GeofenceId,
                  }),
                )
              }
            />
          </Tooltip>,
        ],
      },
    ],
    [
      columnHelper,
      getUserPositionAddressState,
      formatList,
      vehiclesList,
      history,
      editableGeofenceIdNameMaps,
      geofencesDeleteGeofence,
    ],
  )

  return (
    <>
      {deleteModal && (
        <ConfirmationModal
          open
          data-testid="Geofence-DeleteModal"
          onClose={() => setDeleteModal(null)}
          onConfirm={handleConfirm}
          title="Confirm"
          isLoading={deleteGeofencesMutation.isPending}
        >
          {ctIntl.formatMessage({
            id:
              deleteModal.data.length > 1
                ? 'Are you sure you want to delete selected geofences?'
                : 'Are you sure you want to delete the geofence?',
          })}
        </ConfirmationModal>
      )}
      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        dataGridId="GeofenceListTable"
        data-testid="GeofenceListTable"
        Component={DataGridAsTabItem}
        apiRef={gridApiRef}
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{ pagination: { paginationModel: { pageSize: 25, page: 0 } } }}
        disableRowSelectionOnClick
        rows={geofenceList}
        columns={columns}
        checkboxSelection={geofencesDeleteGeofence}
        rowSelectionModel={selectedRowIds}
        onRowSelectionModelChange={handleSelectionModelChange}
        onRowClick={(params) => {
          history.push(
            getGeofenceDetailsModalMainPath(history.location, {
              geo: 'edit',
              id: params.id as GeofenceId,
            }),
          )
        }}
        isRowSelectable={({ row }) =>
          Object.prototype.hasOwnProperty.call(editableGeofenceIdNameMaps, row.id)
        }
        loading={isLoading}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <>
                  <GridToolbarExport
                    csvOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Geofence List' }),
                    }}
                    excelOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Geofence List' }),
                    }}
                    // TODO: need to enable it when print export stable
                    printOptions={{ disableToolbarButton: true }}
                  />
                  <DataGridDeleteButtonWithCounter
                    data-testid="GeofenceListTable-DeleteMultipleButton"
                    count={selectedRowIds.size}
                    disabled={!geofencesDeleteGeofence}
                    ButtonProps={{
                      size: 'small',
                      onClick: () => {
                        setDeleteModal({
                          data: Array.from(selectedRowIds).map((id) => ({
                            id,
                            name: geofenceIdNameMaps[id],
                          })),
                        })
                      },
                    }}
                  />
                </>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
      />
    </>
  )
}
