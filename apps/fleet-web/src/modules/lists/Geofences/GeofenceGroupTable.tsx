import { useMemo, useState } from 'react'
import {
  DataGridAsTabItem,
  getDataGridClientModeIncludedSelectedRowIds,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridToolbarExport,
  LinearProgress,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import { useHistory } from 'react-router'

import type { FetchGeofencesListResolved } from '@fleet-web/api/geofences'
import type { GeofenceGroupId, GeofenceId } from '@fleet-web/api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import ConfirmationModal from '@fleet-web/components/_modals/Confirmation'
import { getGeofenceGroups, getLoading } from '@fleet-web/duxs/geofences'
import { getGeofences } from '@fleet-web/duxs/geofences-selector'
import { LIST } from '@fleet-web/modules/app/components/routes/list'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { DataGridDeleteButtonWithCounter } from '@fleet-web/shared/data-grid/DataGridDeleteButtonWithCounter'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { ctToast } from '@fleet-web/util-components'
import { CellTextWithMore } from '@fleet-web/util-components/CellTextWithMore'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useDeleteGeofenceGroupMutation } from './api/api'
import { geofenceGroupDetailSearchParamsSchema } from './GroupDetail/GeofenceGroupDetail'

type DataGridRow = FetchGeofencesListResolved['groups'][number]

export default function GeofenceGroupTable() {
  const history = useHistory()

  const isLoading = useTypedSelector(getLoading)
  const groups = useTypedSelector(getGeofenceGroups)
  const geofenceList = useTypedSelector(getGeofences)
  const gridApiRef = useGridApiRef()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  const [selectedRowIds, setSelectedRowIds] = useState<ReadonlySet<GeofenceGroupId>>(
    new Set(),
  )

  const [deleteModal, setDeleteModal] = useState<{
    data: Array<{ id: GeofenceGroupId; name: string }>
  } | null>(null)

  const deleteGeofenceGroupMutation = useDeleteGeofenceGroupMutation()

  const handleSelectionModelChange = (selectionModel: GridRowSelectionModel) =>
    setSelectedRowIds(
      getDataGridClientModeIncludedSelectedRowIds(
        selectionModel as GridRowSelectionModel<GeofenceGroupId>,
        { gridApiRef },
      ),
    )

  const handleConfirm = () => {
    if (deleteModal) {
      const { data } = deleteModal
      const deletedIds = data.map((item) => item.id)
      deleteGeofenceGroupMutation.mutate(
        { groupIds: deletedIds },
        {
          onSuccess: () => {
            ctToast.fire(
              'success',
              data.length > 1
                ? ctIntl.formatMessage({
                    id: 'Geofence groups were successfully deleted.',
                  })
                : ctIntl.formatMessage({ id: 'The geofence group ' }) +
                    `"${data[0].name}" ` +
                    ctIntl.formatMessage({ id: 'was successfully deleted.' }),
            )
            setDeleteModal(null)
            setSelectedRowIds(
              new Set(
                Array.from(selectedRowIds).filter((id) => !deletedIds.includes(id)),
              ),
            )
          },
        },
      )
    }
  }

  // an id:name map to fetch geofence name fast
  const geofenceIdNameMaps: Record<GeofenceId, string> = useMemo(
    () =>
      geofenceList.reduce(
        (acc, cur) => ({
          ...acc,
          [cur.id]: cur.name,
        }),
        {},
      ),
    [geofenceList],
  )

  // an id:name map to fetch geofence group name fast
  const geofenceGroupIdNameMaps: Record<GeofenceGroupId, string> = useMemo(
    () =>
      groups.reduce(
        (acc, cur) => ({
          ...acc,
          [cur.id]: cur.name,
        }),
        {},
      ),
    [groups],
  )

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
        hideable: false,
      },
      columnHelper.string((_, row) => row.name, {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Name' }),
        flex: 1,
      }),
      columnHelper.number((_, row) => row.itemIds.length, {
        field: 'number',
        headerName: ctIntl.formatMessage({ id: 'Number of Geofences' }),
        flex: 1,
        align: 'left',
        headerAlign: 'left',
      }),
      columnHelper.string(
        (_, row) =>
          row.itemIds.length === geofenceList.length
            ? ctIntl.formatMessage({ id: 'All' })
            : row.itemIds.map((id) => geofenceIdNameMaps[id]).join(', '),
        {
          field: 'geofences',
          headerName: ctIntl.formatMessage({ id: 'Geofences' }),
          flex: 3,
          renderCell: ({ row, value }) =>
            row.itemIds.length === geofenceList.length ? (
              ctIntl.formatMessage({ id: 'All' })
            ) : (
              <CellTextWithMore value={value ?? ''} />
            ),
        },
      ),
      columnHelper.string((_, row) => row.description ?? '', {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        flex: 2,
      }),
      {
        type: 'actions',
        field: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => [
          <Tooltip
            key="edit"
            title={ctIntl.formatMessage({ id: 'Edit' })}
            arrow
            placement="top"
            disableInteractive
          >
            <GridActionsCellItem
              data-testid={`GeofenceGroup-Edit-${row.id}`}
              icon={<EditIcon />}
              label={ctIntl.formatMessage({ id: 'Edit' })}
              onClick={() => {
                history.push(
                  `${
                    LIST.subMenusRoutes.GEOFENCES.subPaths.GROUP
                  }?${buildRouteQueryStringKeepingExistingSearchParams({
                    location: history.location,
                    schema: geofenceGroupDetailSearchParamsSchema,
                    searchParams: {
                      type: 'edit',
                      id: row.id,
                    },
                  })}`,
                )
              }}
            />
          </Tooltip>,
          <Tooltip
            key="remove"
            title={ctIntl.formatMessage({ id: 'Remove' })}
            arrow
            placement="top"
            disableInteractive
          >
            <GridActionsCellItem
              data-testid={`GeofenceGroup-Remove-${row.id}`}
              icon={<DeleteIcon />}
              label={ctIntl.formatMessage({ id: 'Remove' })}
              onClick={() => setDeleteModal({ data: [{ id: row.id, name: row.name }] })}
            />
          </Tooltip>,
        ],
      },
    ],
    [columnHelper, geofenceIdNameMaps, geofenceList.length, history],
  )

  return (
    <>
      {deleteModal && (
        <ConfirmationModal
          open
          data-testid="GeofenceGroup-DeleteModal"
          onClose={() => setDeleteModal(null)}
          onConfirm={handleConfirm}
          title="Confirm"
          isLoading={deleteGeofenceGroupMutation.isPending}
        >
          {ctIntl.formatMessage({
            id:
              deleteModal.data.length > 1
                ? 'Are you sure you want to delete selected geofence groups?'
                : 'Are you sure you want to delete this geofence group?',
          })}
        </ConfirmationModal>
      )}
      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        Component={DataGridAsTabItem}
        dataGridId="GeofenceGroupTableDataGrid"
        data-testid="GeofenceGroupTable"
        apiRef={gridApiRef}
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
        }}
        rows={groups}
        columns={columns}
        checkboxSelection
        rowSelectionModel={selectedRowIds}
        onRowSelectionModelChange={handleSelectionModelChange}
        loading={isLoading}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <>
                  <GridToolbarExport
                    data-testid="GeofenceGroupTable-Export"
                    // TODO: need to enable it when print export stable
                    csvOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Geofence Group' }),
                    }}
                    excelOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Geofence Group' }),
                    }}
                    printOptions={{ disableToolbarButton: true }}
                  />
                  <DataGridDeleteButtonWithCounter
                    data-testid="GeofenceGroupTable-Delete"
                    count={selectedRowIds.size}
                    ButtonProps={{
                      size: 'small',
                      onClick: () => {
                        setDeleteModal({
                          data: Array.from(selectedRowIds).map((id) => ({
                            id,
                            name: geofenceGroupIdNameMaps[id],
                          })),
                        })
                      },
                    }}
                  />
                </>
              ),
            },
          }),
          basePagination: { material: { showFirstButton: true, showLastButton: true } },
        }}
      />
    </>
  )
}
