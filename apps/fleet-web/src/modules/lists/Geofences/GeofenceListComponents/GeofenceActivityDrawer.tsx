import { useMemo, useState } from 'react'
import {
  Box,
  DataGrid,
  GridLogicOperator,
  LinearProgress,
  Typography,
  useCallbackBranded,
  useGridApiRef,
  type GridColDef,
  type GridEventListener,
  type GridFilterModel,
  type GridSortDirection,
  type GridSortModel,
} from '@karoo-ui/core'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'
import { z } from 'zod'

import { geofenceIdSchema, type GeofenceId } from '@fleet-web/api/types'
import { DrawerCloseButton } from '@fleet-web/components/DrawerCloseButton'
import { getGeofences } from '@fleet-web/duxs/geofences-selector'
import { useEventHandler } from '@fleet-web/hooks/useEventHandler'
import { useValidatedSearchParams } from '@fleet-web/hooks/useValidatedSearchParams'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ServerInfiniteLoadingPaginationContextProvider } from '@fleet-web/shared/data-grid/ServerInfiniteLoadingPaginationContextProvider'
import { downloadBasicTableDataAsSheetFile } from '@fleet-web/shared/data-grid/utils'
import { ctToast } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { Array_filterMap } from '@fleet-web/util-functions/performance-critical-utils'

import {
  fetchGeofencesActivityEvents,
  useGeofencesActivityEvents,
  type GeofencesActivityEventsQueryParams,
  type UseGeofencesActivityEventsReturnedData,
} from '../api/api'
import {
  fetchSingleGeofenceActivityEventsFilterModelSchema,
  type FetchGeofencesActivityEvents,
  type FetchSingleGeofenceActivityEventsFilterModelSchemaSelf,
} from '../api/types'
import {
  GeofencesActivityTablesDataGridToolbar,
  useGeofencesActivityTableCommonColDefsAndMappers,
  type GeofencesActivityTablesDataGridToolbarProps,
} from '../geofences-activity-utils'
import GeofenceListDrawer from './GeofenceListDrawer'

type DataGridRow = FetchGeofencesActivityEvents.ParsedEvent

type DataGridSortModel =
  FetchGeofencesActivityEvents.ApiInput['serverRequestModel']['sort']

type DataGridFilterModel = FetchSingleGeofenceActivityEventsFilterModelSchemaSelf

type SortableColumnId = (typeof sortableColumnIds)[keyof typeof sortableColumnIds]
type FilterableColumnId = (typeof filterableColumnIds)[keyof typeof filterableColumnIds]

const columnsIds = {
  vehicle: 'vehicle',
  time: 'timestamp',
  eventType: 'eventType',
  permanenceTime: 'permanenceTime',
} as const

const sortableColumnIds = R.pick(columnsIds, ['time'])
const sortableColumnIdsArray = Object.values(sortableColumnIds) satisfies Array<
  DataGridSortModel[number]['field']
>

const filterableColumnIds = R.pick(columnsIds, [
  'time',
  'vehicle',
  'eventType',
  'permanenceTime',
])

const filterableColumnIdsArray = Object.values(filterableColumnIds) satisfies Array<
  DataGridFilterModel['items'][number]['field']
>

const POLLING_REFETCH_INTERVAL = 30_000

type Props = {
  onClose: () => void
}

export const geofenceActivityDrawerSearchParamsSchema = z.object({
  geofenceId: geofenceIdSchema,
})

type DispatchedEvent = {
  type: 'clicked_export_menu_item'
  item: 'csv' | 'excel'
}

export function GeofenceActivityDrawer({ onClose }: Props) {
  const validatedParams = useValidatedSearchParams(
    () => geofenceActivityDrawerSearchParamsSchema,
  )

  return (
    <>
      {match(validatedParams)
        .with({ status: 'invalid' }, () => null)
        .with({ status: 'valid' }, ({ data: { geofenceId } }) => (
          <Content
            onClose={onClose}
            geofenceID={geofenceId}
          />
        ))
        .exhaustive()}
    </>
  )
}

function Content({
  geofenceID,
  onClose,
}: {
  geofenceID: GeofenceId
  onClose: () => void
}) {
  const geofenceList = useTypedSelector(getGeofences)

  const gridApiRef = useGridApiRef()

  const [sortModel, setSortModel] = useState<DataGridSortModel>([
    { field: columnsIds.time, sort: 'desc' },
  ])
  const [filterModel, setFilterModel] = useState<DataGridFilterModel>({
    items: [],
    logicOperator: GridLogicOperator.And,
  })
  const [exportQueryFetchStatus, setExportQueryFetchStatus] = useState<
    'fetching' | 'idle'
  >('idle')

  const {
    colDefs: { permanenceTimeColDef, eventTypeColDef, timeColDef, vehicleColDef },
    serverFilterMappers: {
      mapPermanenceTimeFilterToServerFilter,
      mapEventTypeFilterToServerFilter,
      mapTimeFilterToServerFilter,
      mapVehicleFilterToServerFilter,
    },
  } = useGeofencesActivityTableCommonColDefsAndMappers()

  const queryParams = useMemo(
    () =>
      ({
        serverModel: {
          sort: sortModel,
          pageSize: 100,
          filter: {
            logicOperator: filterModel.logicOperator,
            items: Array_filterMap(filterModel.items, (item, { RemoveSymbol }) =>
              match(item)
                .with({ field: columnsIds.vehicle }, (filterItem) => {
                  const serverFilter = mapVehicleFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.eventType }, (filterItem) => {
                  const serverFilter = mapEventTypeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.time }, (filterItem) => {
                  const serverFilter = mapTimeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.permanenceTime }, (filterItem) => {
                  const serverFilter = mapPermanenceTimeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .exhaustive(),
            ),
          },
        },
        geofenceId: geofenceID,
      }) satisfies GeofencesActivityEventsQueryParams,
    [
      sortModel,
      filterModel.logicOperator,
      filterModel.items,
      geofenceID,
      mapVehicleFilterToServerFilter,
      mapEventTypeFilterToServerFilter,
      mapTimeFilterToServerFilter,
      mapPermanenceTimeFilterToServerFilter,
    ],
  )

  const geofencesActivityEventsQuery = useGeofencesActivityEvents({
    params: queryParams,
    refetchInterval: POLLING_REFETCH_INTERVAL,
  })

  const selectedGeofenceName = useMemo(() => {
    const selectedGeofence = geofenceList.find((i) => i.id === geofenceID)

    return selectedGeofence?.name || ''
  }, [geofenceID, geofenceList])

  const dispatch = useEventHandler((event: DispatchedEvent) => {
    const state = {}

    match([state, event])
      .with([P._, { type: 'clicked_export_menu_item' }], async ([, { item }]) => {
        try {
          setExportQueryFetchStatus('fetching')

          const maxExportableRows = 10000
          const eventsToExport = await fetchGeofencesActivityEvents({
            geofenceId: geofenceID,
            serverModel: {
              filter: queryParams.serverModel.filter,
              sort: queryParams.serverModel.sort,
              pagination: { cursor: 'start', pageSize: maxExportableRows },
            },
          })

          // Make sure the types are the same as the rows sent to the DataGrid
          const rowsToExport: typeof rows = eventsToExport.rows

          await downloadBasicTableDataAsSheetFile({
            fileName: ctIntl.formatMessage(
              { id: 'geofence.single.activity.export.fileName' },
              {
                values: {
                  limitNumber: maxExportableRows,
                  geofenceName: selectedGeofenceName,
                },
              },
            ),
            fileExtension: match(item)
              .with('csv', () => 'csv' as const)
              .with('excel', () => 'xlsx' as const)
              .exhaustive(),
            gridApiRef,
            rowsToExport,
          })
        } catch {
          ctToast.fire('error', 'Something went wrong, please try again')
        } finally {
          setExportQueryFetchStatus('idle')
        }
      })
      .exhaustive()
  })

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const base: Array<GridColDef<DataGridRow>> = [
      vehicleColDef({ field: columnsIds.vehicle }),
      eventTypeColDef({ field: columnsIds.eventType }),
      timeColDef({ field: columnsIds.time }),
      permanenceTimeColDef({ field: columnsIds.permanenceTime }),
    ]

    return base.map((column) => ({
      ...column,
      sortable: sortableColumnIdsArray.includes(column.field as SortableColumnId),
      filterable: filterableColumnIdsArray.includes(column.field as FilterableColumnId),
    }))
  }, [eventTypeColDef, permanenceTimeColDef, timeColDef, vehicleColDef])

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filter model is invalid (which is what we want)

      fetchSingleGeofenceActivityEventsFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const onDataGridRowsScrollEnd: GridEventListener<'rowsScrollEnd'> = () => {
    geofencesActivityEventsQuery.fetchNextPage()
  }

  const onSortModelChange = async (newSortModel_: GridSortModel) => {
    type TypeSortItem = {
      field: SortableColumnId
      sort: GridSortDirection
    }
    const newSortModel = newSortModel_ as Array<TypeSortItem>

    const isStartTimeField = (item: TypeSortItem) => item.field === columnsIds.time

    setSortModel(() => [
      {
        field: columnsIds.time,
        sort: newSortModel.find((item) => isStartTimeField(item))?.sort ?? 'asc',
      },
    ])
  }

  const rows = useMemo(
    (): ReadonlyArray<DataGridRow> =>
      geofencesActivityEventsQuery.data?.pages.flatMap((page) => page.rows) ?? [],
    [geofencesActivityEventsQuery.data?.pages],
  )

  return (
    <GeofenceListDrawer onClose={onClose}>
      <Box
        sx={{
          px: 3,
          display: 'flex',
          flexDirection: 'column',
          flexFlow: 'column',
          height: '100%',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography
            mb={1}
            variant="h5"
          >
            {selectedGeofenceName}
          </Typography>
          <DrawerCloseButton onClick={onClose} />
        </Box>
        <Typography
          variant="h5"
          sx={{ opacity: 0.6, mb: 3 }}
        >
          {ctIntl.formatMessage({ id: 'geofence.activity.geofenceActivity' })}
        </Typography>

        <ServerInfiniteLoadingPaginationContextProvider
          loadedPages={geofencesActivityEventsQuery.data?.pages}
          pageSize={queryParams.serverModel.pageSize}
          getPageMetaData={useCallbackBranded(
            (page: UseGeofencesActivityEventsReturnedData['pages'][number]) => ({
              nextCursor: page.serverModelPageInfo.nextCursorRow,
              rowsCount: page.rows.length,
            }),
            [],
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB
            apiRef={gridApiRef}
            hideFooterPagination
            filterDebounceMs={500}
            Component={DataGrid}
            dataGridId="GeofenceDetailActivity"
            loading={
              geofencesActivityEventsQuery.fetchStatus === 'fetching' ||
              exportQueryFetchStatus === 'fetching'
            }
            disableRowSelectionOnClick
            onRowsScrollEnd={onDataGridRowsScrollEnd}
            filterMode="server"
            sortingMode="server"
            sortModel={sortModel}
            onSortModelChange={onSortModelChange}
            filterModel={filterModel}
            onFilterModelChange={onFilterModelChange}
            rows={rows}
            columns={columns}
            slots={{
              toolbar: GeofencesActivityTablesDataGridToolbar,
              loadingOverlay: LinearProgress,
              footerRowCount:
                ServerInfiniteLoadingPaginationContextProvider.UnknownFooterRowCount,
            }}
            slotProps={{
              toolbar: {
                isExporting: exportQueryFetchStatus === 'fetching',
                onExportMenuItemClick: (item) => {
                  dispatch({ type: 'clicked_export_menu_item', item })
                },
              } satisfies GeofencesActivityTablesDataGridToolbarProps,
            }}
          />
        </ServerInfiniteLoadingPaginationContextProvider>
      </Box>
    </GeofenceListDrawer>
  )
}
