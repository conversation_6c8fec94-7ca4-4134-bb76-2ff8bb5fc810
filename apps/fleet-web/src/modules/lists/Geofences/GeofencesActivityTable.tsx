import { useMemo, useState } from 'react'
import {
  DataGridAsTabItem,
  GridLogicOperator,
  LinearProgress,
  useCallbackBranded,
  useGridApiRef,
  type GridColDef,
  type GridEventListener,
  type GridFilterModel,
  type GridSortDirection,
  type GridSortModel,
} from '@karoo-ui/core'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'

import { useEventHandler } from '@fleet-web/hooks/useEventHandler'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { mapFilterItemToServerFilter_String } from '@fleet-web/shared/data-grid/server-client/utils'
import { ServerInfiniteLoadingPaginationContextProvider } from '@fleet-web/shared/data-grid/ServerInfiniteLoadingPaginationContextProvider'
import {
  createDataGridTextColumn,
  downloadBasicTableDataAsSheetFile,
} from '@fleet-web/shared/data-grid/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { ctToast } from '@fleet-web/util-components/ctToast'
import { Array_filterMap } from '@fleet-web/util-functions/performance-critical-utils'

import {
  fetchGeofencesActivityEvents,
  useGeofencesActivityEvents,
  type GeofencesActivityEventsQueryParams,
  type UseGeofencesActivityEventsReturnedData,
} from './api/api'
import {
  fetchMultipleGeofencesActivityEventsFilterModelSchema,
  type FetchGeofencesActivityEvents,
  type FetchMultipleGeofencesActivityEventsFilterModelSchemaSelf,
} from './api/types'
import {
  GeofencesActivityTablesDataGridToolbar,
  useGeofencesActivityTableCommonColDefsAndMappers,
} from './geofences-activity-utils'

type DataGridRow = FetchGeofencesActivityEvents.ParsedEvent

type DataGridSortModel =
  FetchGeofencesActivityEvents.ApiInput['serverRequestModel']['sort']

type DataGridFilterModel = FetchMultipleGeofencesActivityEventsFilterModelSchemaSelf

type SortableColumnId = (typeof sortableColumnIds)[keyof typeof sortableColumnIds]
type FilterableColumnId = (typeof filterableColumnIds)[keyof typeof filterableColumnIds]

const columnsIds = {
  time: 'timestamp',
  geofence: 'geofence',
  vehicle: 'vehicle',
  eventType: 'eventType',
  permanenceTime: 'permanenceTime',
} as const

const sortableColumnIds = R.pick(columnsIds, ['time'])
const sortableColumnIdsArray = Object.values(sortableColumnIds) satisfies Array<
  DataGridSortModel[number]['field']
>

const filterableColumnIds = R.pick(columnsIds, [
  'time',
  'geofence',
  'vehicle',
  'eventType',
  'permanenceTime',
])

const filterableColumnIdsArray = Object.values(filterableColumnIds) satisfies Array<
  DataGridFilterModel['items'][number]['field']
>

const POLLING_REFETCH_INTERVAL = 30_000

type DispatchedEvent = {
  type: 'clicked_export_menu_item'
  item: 'csv' | 'excel'
}

export function GeofencesActivityTable() {
  const gridApiRef = useGridApiRef()
  const [sortModel, setSortModel] = useState<DataGridSortModel>([
    { field: columnsIds.time, sort: 'desc' },
  ])
  const [filterModel, setFilterModel] = useState<DataGridFilterModel>({
    items: [],
    logicOperator: GridLogicOperator.And,
  })
  const [exportQueryFetchStatus, setExportQueryFetchStatus] = useState<
    'fetching' | 'idle'
  >('idle')

  const {
    colDefs: { permanenceTimeColDef, eventTypeColDef, timeColDef, vehicleColDef },
    serverFilterMappers: {
      mapVehicleFilterToServerFilter,
      mapEventTypeFilterToServerFilter,
      mapTimeFilterToServerFilter,
      mapPermanenceTimeFilterToServerFilter,
    },
  } = useGeofencesActivityTableCommonColDefsAndMappers()

  const queryParams = useMemo(
    () =>
      ({
        serverModel: {
          sort: sortModel,
          pageSize: 100,
          filter: {
            logicOperator: filterModel.logicOperator,
            items: Array_filterMap(filterModel.items, (item, { RemoveSymbol }) =>
              match(item)
                .with({ field: columnsIds.vehicle }, (filterItem) => {
                  const serverFilter = mapVehicleFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.geofence }, ({ value, operator }) => {
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'geofence_name',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.eventType }, (filterItem) => {
                  const serverFilter = mapEventTypeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.time }, (filterItem) => {
                  const serverFilter = mapTimeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .with({ field: columnsIds.permanenceTime }, (filterItem) => {
                  const serverFilter = mapPermanenceTimeFilterToServerFilter(filterItem)
                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                })
                .exhaustive(),
            ),
          },
        },
        geofenceId: null, // We want all geofences activity
      }) satisfies GeofencesActivityEventsQueryParams,
    [
      filterModel.items,
      filterModel.logicOperator,
      mapEventTypeFilterToServerFilter,
      mapPermanenceTimeFilterToServerFilter,
      mapTimeFilterToServerFilter,
      mapVehicleFilterToServerFilter,
      sortModel,
    ],
  )

  const geofencesActivityEventsQuery = useGeofencesActivityEvents({
    params: queryParams,
    refetchInterval: POLLING_REFETCH_INTERVAL,
  })

  const dispatch = useEventHandler((event: DispatchedEvent) => {
    const state = {}

    match([state, event] as const)
      .with([P._, { type: 'clicked_export_menu_item' }], async ([, { item }]) => {
        try {
          setExportQueryFetchStatus('fetching')

          const maxExportableRows = 10000
          const eventsToExport = await fetchGeofencesActivityEvents({
            geofenceId: null,
            serverModel: {
              filter: queryParams.serverModel.filter,
              sort: queryParams.serverModel.sort,
              pagination: { cursor: 'start', pageSize: maxExportableRows },
            },
          })

          // Make sure the types are the same as the rows sent to the DataGrid
          const rowsToExport: typeof rows = eventsToExport.rows

          await downloadBasicTableDataAsSheetFile({
            fileName: ctIntl.formatMessage(
              { id: 'geofences.activity.export.fileName' },
              {
                values: {
                  limitNumber: maxExportableRows,
                },
              },
            ),
            fileExtension: match(item)
              .with('csv', () => 'csv' as const)
              .with('excel', () => 'xlsx' as const)
              .exhaustive(),
            gridApiRef,
            rowsToExport,
          })
        } catch {
          ctToast.fire('error', 'Something went wrong, please try again')
        } finally {
          setExportQueryFetchStatus('idle')
        }
      })
      .exhaustive()
  })

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const base: Array<GridColDef<DataGridRow>> = [
      vehicleColDef({ field: columnsIds.vehicle }),
      eventTypeColDef({ field: columnsIds.eventType }),
      createDataGridTextColumn({
        field: columnsIds.geofence,
        headerNameMsg: { id: 'Geofence' },
        valueGetter: (_, row) => row.geofenceName,
        flex: 2,
      }),
      timeColDef({ field: columnsIds.time }),
      permanenceTimeColDef({ field: columnsIds.permanenceTime }),
    ]

    return base.map((column) => ({
      ...column,
      sortable: sortableColumnIdsArray.includes(column.field as SortableColumnId),
      filterable: filterableColumnIdsArray.includes(column.field as FilterableColumnId),
    }))
  }, [eventTypeColDef, permanenceTimeColDef, timeColDef, vehicleColDef])

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filter model is invalid (which is what we want)

      fetchMultipleGeofencesActivityEventsFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const onDataGridRowsScrollEnd: GridEventListener<'rowsScrollEnd'> = () => {
    geofencesActivityEventsQuery.fetchNextPage()
  }

  const onSortModelChange = async (newSortModel_: GridSortModel) => {
    type TypeSortItem = {
      field: SortableColumnId
      sort: GridSortDirection
    }
    const newSortModel = newSortModel_ as Array<TypeSortItem>

    const isStartTimeField = (item: TypeSortItem) => item.field === columnsIds.time

    setSortModel(() => [
      {
        field: columnsIds.time,
        sort: newSortModel.find((item) => isStartTimeField(item))?.sort ?? 'asc',
      },
    ])
  }

  const rows = useMemo(
    (): ReadonlyArray<DataGridRow> =>
      geofencesActivityEventsQuery.data?.pages.flatMap((page) => page.rows) ?? [],
    [geofencesActivityEventsQuery.data?.pages],
  )

  return (
    <ServerInfiniteLoadingPaginationContextProvider
      loadedPages={geofencesActivityEventsQuery.data?.pages}
      pageSize={queryParams.serverModel.pageSize}
      getPageMetaData={useCallbackBranded(
        (page: UseGeofencesActivityEventsReturnedData['pages'][number]) => ({
          nextCursor: page.serverModelPageInfo.nextCursorRow,
          rowsCount: page.rows.length,
        }),
        [],
      )}
    >
      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        apiRef={gridApiRef}
        data-testid="GeofenceGroupActivity"
        hideFooterPagination
        filterDebounceMs={500}
        Component={DataGridAsTabItem}
        dataGridId="GeofenceActivityTableDataGrid"
        loading={
          geofencesActivityEventsQuery.fetchStatus === 'fetching' ||
          exportQueryFetchStatus === 'fetching'
        }
        disableRowSelectionOnClick
        onRowsScrollEnd={onDataGridRowsScrollEnd}
        filterMode="server"
        sortingMode="server"
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        filterModel={filterModel}
        onFilterModelChange={onFilterModelChange}
        rows={rows}
        columns={columns}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          footerRowCount:
            ServerInfiniteLoadingPaginationContextProvider.UnknownFooterRowCount,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <GeofencesActivityTablesDataGridToolbar
                  ReGeofencesList
                  isExporting={exportQueryFetchStatus === 'fetching'}
                  onExportMenuItemClick={(item) => {
                    dispatch({ type: 'clicked_export_menu_item', item })
                  }}
                />
              ),
            },
          }),
        }}
      />
    </ServerInfiniteLoadingPaginationContextProvider>
  )
}
