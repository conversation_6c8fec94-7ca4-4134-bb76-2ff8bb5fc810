import type { Dispatch } from '@reduxjs/toolkit'
import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
  type QueryClient,
  type QueryKey,
  type SetDataOptions,
  type Updater,
} from '@tanstack/react-query'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { ReadonlyDeep } from 'type-fest'
import { put } from 'typed-redux-saga'
import { z } from 'zod'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import geofencesApi from '@fleet-web/api/geofences'
import {
  makeMutationErrorHandlerWithToast,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import {
  geofenceVisitIdSchema,
  type GeofenceGroupId,
  type GeofenceId,
} from '@fleet-web/api/types'
import { useSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { fetchListGeofencesAction } from '@fleet-web/duxs/geofences'
import { invalidateQueriesOnEntitiesMutation } from '@fleet-web/modules/shared/utils'
import { useAppDispatch } from '@fleet-web/redux-hooks'
import type { PromiseResolvedType } from '@fleet-web/types'
import { isBusinessApiError } from '@fleet-web/types/api/BusinessApiError'
import type { ExcludeStrict } from '@fleet-web/types/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { minutesToMs, toImmutable } from '@fleet-web/util-functions/functional-utils'
import { Array_filterMap } from '@fleet-web/util-functions/performance-critical-utils'
import {
  createQuery,
  type BetterUndefinedInitialDataInfiniteOptions,
} from '@fleet-web/util-functions/react-query-utils'

import type { SaveGeofenceForm } from '../types'
import type { FetchGeofencesActivityEvents } from './types'

export const invalidateQueriesAffectedByGeofencesAndGroupsChanges = ({
  queryClient,
  dispatch,
}: {
  queryClient: QueryClient
  dispatch: Dispatch
}) => {
  dispatch(fetchListGeofencesAction(true))
  invalidateQueriesOnEntitiesMutation(queryClient, { entities: ['geofence'] })
}

/**
 * Useful to call in a saga
 * IMPORTANT: __Needs__ to be in sync with the normal invalidate function
 */
export function* invalidateQueriesAffectedByGeofencesAndGroupsChangesSaga({
  queryClient,
}: {
  queryClient: QueryClient
}) {
  yield* put(fetchListGeofencesAction(true))
  invalidateQueriesOnEntitiesMutation(queryClient, { entities: ['geofence'] })
}

export const useCreateGeofenceGroupMutation = () => {
  const { enqueueSnackbarWithCloseAction } = useSnackbarWithCloseAction()
  return useMutation({
    mutationFn: (props: { name: string; description: string | null }) =>
      geofencesApi.createGeofenceGroup(props),
    ...makeMutationErrorHandlerWithToast({
      nonStandardErrorHandler: (error) => {
        match(error)
          .when(
            isBusinessApiError({
              errorId: z.literal('ERR_CREATE_GEOFENCE_GROUP_DUPLICATED_NAME'),
            }),
            () => {
              enqueueSnackbarWithCloseAction(
                ctIntl.formatMessage({
                  id: 'ERR_CREATE_GEOFENCE_GROUP_DUPLICATED_NAME',
                }),
                {
                  variant: 'error',
                },
              )
            },
          )
          .otherwise(() => null)
      },
    }),
  })
}

export const useUpdateGeofenceGroupMutation = () =>
  useMutation({
    mutationFn: (group: {
      name: string
      id: GeofenceGroupId
      description: string | null
    }) => geofencesApi.updateGeofenceGroup(group),
    ...makeMutationErrorHandlerWithToast(),
  })

export const useUpdateGeofenceGroupItemsMutation = () => {
  const dispatch = useAppDispatch()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (props: { groupId: GeofenceGroupId; selectedIds: Array<string> }) =>
      geofencesApi.updateGeofenceGroupItems({
        groupId: props.groupId,
        itemIds: props.selectedIds,
      }),
    onSuccess: () => {
      // fetch the geofences and groups with cache after update items
      invalidateQueriesAffectedByGeofencesAndGroupsChanges({ dispatch, queryClient })
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

export const useDeleteGeofenceGroupMutation = () => {
  const dispatch = useAppDispatch()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (props: { groupIds: Array<GeofenceGroupId> }) =>
      geofencesApi.deleteGeofenceGroup(props.groupIds),
    onSuccess: () => {
      // fetch the geofences and groups with cache after delete success
      invalidateQueriesAffectedByGeofencesAndGroupsChanges({ dispatch, queryClient })
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

export type SaveGeofenceResult = PromiseResolvedType<typeof geofencesApi.saveGeofence>

export const useSaveGeofenceMutation = ({
  onSuccess,
}: {
  onSuccess: (data: SaveGeofenceResult, variables: SaveGeofenceForm) => void
}) => {
  const dispatch = useAppDispatch()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (props: SaveGeofenceForm) => geofencesApi.saveGeofence(props),
    onSuccess: (data, variables) => {
      if (data.isValid) {
        invalidateQueriesAffectedByGeofencesAndGroupsChanges({ dispatch, queryClient })
      }
      // FIXME: this props onSuccess should be put in the method mutate,
      // but it has some problem to be triggered, we need to find the reason for that
      onSuccess(data, variables)
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

export const useDeleteGeofencesMutation = () => {
  const dispatch = useAppDispatch()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (props: { ids: Array<GeofenceId> }) =>
      geofencesApi.deleteGeofences(props.ids),
    onSuccess: () => {
      // fetch the geofences and groups with cache after delete success
      invalidateQueriesAffectedByGeofencesAndGroupsChanges({ dispatch, queryClient })
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

/********************************************** Fetch Geofence Activities ********************************************/

export const geofencesActivityEventsQueryKey = (
  params:
    | {
        serverModel: Pick<
          FetchGeofencesActivityEventsParams['serverModel'],
          'sort' | 'filter'
        >
      }
    | undefined,
) => {
  const baseKey = ['geofencesActivityEvents'] as const

  return params ? [...baseKey, params] : baseKey
}

type FetchGeofencesActivityEventsParams = Parameters<
  typeof fetchGeofencesActivityEvents
>[0]

export async function fetchGeofencesActivityEvents({
  serverModel: serverModelParam,
  geofenceId,
}: {
  serverModel: FetchGeofencesActivityEvents.ApiInput['serverRequestModel']
  geofenceId: GeofenceId | null
}) {
  const apiParams: FetchGeofencesActivityEvents.ApiInput = {
    serverRequestModel: {
      pagination: {
        cursor: serverModelParam.pagination.cursor,
        pageSize: serverModelParam.pagination.pageSize,
      },
      sort: serverModelParam.sort,
      filter: serverModelParam.filter,
    },
    geofence_id: geofenceId,
  }

  const rawData = await apiCallerNoX<FetchGeofencesActivityEvents.ApiOutput>(
    'ct_fleet_get_geofences_activities',
    apiParams,
  )

  const parsedData = Array_filterMap(rawData.rows, (rawEvent, { RemoveSymbol }) => {
    const idParseResult = geofenceVisitIdSchema.safeParse(rawEvent.id)

    if (idParseResult.success === false) {
      return RemoveSymbol
    }

    return {
      id: idParseResult.data,
      geofenceId: rawEvent.geofence_id,
      geofenceName: rawEvent.geofence_name,
      vehicleId: rawEvent.vehicle_id,
      vehicleRegistration: rawEvent.registration,
      typeMetaData: (() => {
        if (rawEvent.event === 'enter') {
          return {
            type: 'entered',
            enterTimestamp: new Date(rawEvent.timestamp),
          }
        }

        return {
          type: 'left',
          exitTimestamp: new Date(rawEvent.timestamp),
          visitDurationInSeconds: Number(rawEvent.visit_duration),
        }
      })(),
    } satisfies FetchGeofencesActivityEvents.ParsedEvent
  })

  return toImmutable({
    rows: parsedData,
    serverModelPageInfo: {
      nextCursorRow: rawData.serverModel.pageInfo.nextCursorRow,
    },
  })
}

export const geofencesActivityEventsQuery = ({
  params,
  queryClient,
}: {
  params: {
    serverModel: Pick<
      FetchGeofencesActivityEventsParams['serverModel'],
      'sort' | 'filter'
    > & {
      pageSize: number
    }
    geofenceId: FetchGeofencesActivityEventsParams['geofenceId']
  }
  queryClient: QueryClient
}) => {
  const queryKey = geofencesActivityEventsQueryKey(params)

  const findIndexofPageParam = <
    PageParam extends GeofencesActivityEventsQueryPageParam,
  >(
    pageParams: ReadonlyArray<PageParam>,
    pageParam: PageParam,
  ) =>
    pageParams.findIndex((param): boolean => {
      if (pageParam === null || param === null) {
        // This is actual needed because the first page param will be undefined
        return param === pageParam
      }
      return (
        pageParam.nextCursorRow.enter_timestamp ===
          param.nextCursorRow.enter_timestamp &&
        pageParam.nextCursorRow.exit_timestamp === param.nextCursorRow.exit_timestamp &&
        pageParam.nextCursorRow.id === param.nextCursorRow.id &&
        pageParam.pageSize === param.pageSize
      )
    })

  const query = {
    queryKey,
    staleTime: minutesToMs(0.5),
    gcTime: 15,
    retry: 1,
    initialPageParam: null,
    getNextPageParam: (lastPage) => {
      if (R.isNullish(lastPage.serverModelPageInfo.nextCursorRow)) {
        // Return null to tell react-query there is no next page available
        return null
      }
      const { nextCursorRow } = lastPage.serverModelPageInfo

      return {
        nextCursorRow,
        pageSize: params.serverModel.pageSize,
      }
    },
    queryFn: (metaData): Promise<GeofenceActivityEventsResolvedType> => {
      const pageParam = metaData.pageParam

      const previousData = getData(queryClient)

      if (previousData && previousData.pages.length > 0) {
        const dataIndex = findIndexofPageParam(previousData.pageParams, pageParam)

        if (dataIndex !== -1) {
          const pageData = previousData.pages[dataIndex]

          // Unless we are on the actual last available page, we want to just resolve with the cache we already have
          // The "past" data won't change so we don't have to bother fetch it again
          if (!R.isNullish(pageData.serverModelPageInfo.nextCursorRow)) {
            return Promise.resolve(pageData)
          }
        }
      }

      return fetchGeofencesActivityEvents({
        serverModel: {
          filter: params.serverModel.filter,
          sort: params.serverModel.sort,
          pagination:
            pageParam === null
              ? { cursor: 'start', pageSize: params.serverModel.pageSize }
              : {
                  cursor: pageParam.nextCursorRow,
                  pageSize: pageParam.pageSize,
                },
        },
        geofenceId: params.geofenceId,
      })
    },
    ...makeQueryErrorHandlerWithToast(),
  } satisfies BetterUndefinedInitialDataInfiniteOptions<
    GeofenceActivityEventsResolvedType,
    Error,
    GeofencesActivityEventsQueryPageParam
  >

  type ImmutableTData = ReadonlyDeep<PromiseResolvedType<typeof query.queryFn>>
  type QueryInfiniteData = {
    pages: ReadonlyArray<ImmutableTData>
    pageParams: ReadonlyArray<GeofencesActivityEventsQueryPageParam>
  }

  const getData = (
    queryClient: QueryClient,
    {
      queryKey = query.queryKey,
    }: {
      queryKey?: QueryKey
    } = {},
  ) => queryClient.getQueryData<QueryInfiniteData>(queryKey)

  const setData = (
    queryClient: QueryClient,
    {
      queryKey: queryKeyParam = queryKey,
      updater,
      options,
    }: {
      queryKey?: QueryKey
      updater: Updater<QueryInfiniteData | undefined, QueryInfiniteData | undefined>
      options?: SetDataOptions
    },
  ) => queryClient.setQueryData(queryKeyParam, updater, options)

  return { ...query, getData, setData }
}

type GeofencesActivityEventsQueryPageParam = {
  nextCursorRow: Record<string, unknown>
  pageSize: number
} | null

export type GeofencesActivityEventsQueryParams = Parameters<
  typeof geofencesActivityEventsQuery
>[0]['params']

type GeofenceActivityEventsResolvedType = PromiseResolvedType<
  typeof fetchGeofencesActivityEvents
>

export function useGeofencesActivityEvents({
  params,
  refetchInterval,
}: {
  params: GeofencesActivityEventsQueryParams
  refetchInterval: number
}) {
  const queryClient = useQueryClient()
  return useInfiniteQuery({
    ...geofencesActivityEventsQuery({ params, queryClient }),
    refetchInterval: (query) => {
      const { data } = query.state
      const pages = data?.pages ?? []

      const timeSort = params.serverModel.sort.find(
        (sort) => sort.field === 'timestamp',
      )

      const timeSortOrder = timeSort?.sort ?? 'asc'

      if (timeSortOrder === 'desc') {
        // Has its own refetch logic
        return false
      }

      if (pages.length === 0) {
        return refetchInterval
      }

      const lastPage = pages[pages.length - 1]

      return R.isNullish(lastPage.serverModelPageInfo.nextCursorRow)
        ? refetchInterval
        : false
    },
  })
}

export type UseGeofencesActivityEventsReturnedData = ExcludeStrict<
  ReturnType<typeof useGeofencesActivityEvents>['data'],
  undefined
>

/********************************************** Fetch Geofence Details ********************************************/

export const createGeofenceDetailsQueryKey = ({
  geofenceId,
}: {
  geofenceId: GeofenceId
}) => ['geofenceDetails', geofenceId] as const

export const geofenceDetailsQuery = ({ geofenceId }: { geofenceId: GeofenceId }) =>
  createQuery({
    queryKey: createGeofenceDetailsQueryKey({ geofenceId }),
    queryFn: () => geofencesApi.fetchGeofenceDetails({ geofenceId }),
    ...makeQueryErrorHandlerWithToast(),
  })

export default function useGeofenceDetailsQuery({
  geofenceId,
}: {
  geofenceId: GeofenceId
}) {
  return useQuery(geofenceDetailsQuery({ geofenceId }))
}
