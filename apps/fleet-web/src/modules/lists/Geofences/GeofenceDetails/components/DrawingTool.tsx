import { Button, Stack, styled, Tooltip } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

const DrawingTool = styled(
  ({
    tooltipLabel,
    onClick,
    children,
    color,
    variant,
    disabled = false,
  }: {
    tooltipLabel: string
    onClick: React.MouseEventHandler<HTMLButtonElement>
    children: JSX.Element
    color: 'primary' | 'error' | 'secondary'
    variant: 'text' | 'outlined' | 'contained'
    disabled?: boolean
  }) => (
    <Tooltip
      arrow
      placement="top"
      title={ctIntl.formatMessage({ id: tooltipLabel })}
    >
      <Stack
        alignContent="center"
        justifyContent="center"
      >
        <StyledButton
          data-testid={`GeofenceDetailModal-DrawingControl-${tooltipLabel}`}
          size="small"
          color={color}
          variant={variant}
          onClick={onClick}
          disabled={disabled}
        >
          {children}
        </StyledButton>
      </Stack>
    </Tooltip>
  ),
)()

export default DrawingTool

const StyledButton = styled(Button)(({ theme }) =>
  theme.unstable_sx({
    '&.MuiButton-outlinedSecondary, &.MuiButton-containedPrimary': {
      minWidth: 'fit-content',
      borderColor: '#BDBDBD',
    },
    '&.MuiButton-outlinedSecondary': {
      background: 'white',
    },
    '&.MuiButton-outlinedError': {
      minWidth: 'fit-content',
    },
  }),
)
