import type { Dispatch } from '@reduxjs/toolkit'

import type { AppState } from '@fleet-web/root-reducer'
import { MapTypeId } from '@fleet-web/types/extended/google-maps'

type State = {
  mapTypeId: google.maps.MapTypeId
  showUserGeofence: boolean
  showSystemGeofence: boolean
}

export type MapControlsAction =
  | {
      type: 'map_geofence_update_mapTypeId'
      param: google.maps.MapTypeId
    }
  | {
      type: 'map_geofence_update_showUserGeofence'
      param: boolean
    }
  | {
      type: 'map_geofence_update_showSystemGeofence'
      param: boolean
    }

export type MapControlsDispatch = Dispatch<MapControlsAction>

const initialState: State = {
  mapTypeId: MapTypeId.ROADMAP,
  showUserGeofence: false,
  showSystemGeofence: false,
}

function geofenceMapControlsReducer(
  state: State = initialState,
  action: MapControlsAction,
): State {
  switch (action.type) {
    case 'map_geofence_update_mapTypeId': {
      return {
        ...state,
        mapTypeId: action.param,
      }
    }
    case 'map_geofence_update_showUserGeofence': {
      return {
        ...state,
        showUserGeofence: action.param,
      }
    }
    case 'map_geofence_update_showSystemGeofence': {
      return {
        ...state,
        showSystemGeofence: action.param,
      }
    }
    default: {
      return state
    }
  }
}

export { geofenceMapControlsReducer }

export const getMapTypeId = (state: AppState) => state.geofenceMapControls.mapTypeId
export const getShowUserGeofence = (state: AppState) =>
  state.geofenceMapControls.showUserGeofence
export const getShowSystemGeofence = (state: AppState) =>
  state.geofenceMapControls.showSystemGeofence
