import { useRef, useState } from 'react'
import { debounce, isEmpty, isEqual } from 'lodash'
import { Box } from '@karoo-ui/core'
import type L from 'leaflet'

import type { AppGeofence } from '@fleet-web/api/geofences'
import Map from '@fleet-web/components/_map/_openstreetmap/Map'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { useEventHandler } from '@fleet-web/hooks/useEventHandler'
import { useTypedSelector } from '@fleet-web/redux-hooks'
// import { setNonGoogleMapTypeId } from '@fleet-web/duxs/map'
import type { mapOpenLayersProviderConfig } from '@fleet-web/util-components/map/open-layers/base-map-config'
import { OpenLayerGeofences } from '@fleet-web/util-components/map/open-layers/layers'
import MapControls from '@fleet-web/util-components/map/shared/map-controls'
import { useMapAreaTheme } from '@fleet-web/util-components/map/shared/MapAreaThemeContext'
import type { MapProviderMetaData } from '@fleet-web/util-components/map/shared/types'

import type { GeofenceDetailsMapState, StateCoords } from '../types'
import DrawingControl from './DrawingControl'

type Props = {
  color: string
  editing: boolean
  geometryInWgs: StateCoords
  renderingGeofencesInWgs: Array<AppGeofence>
  mapApiProviderId: keyof typeof mapOpenLayersProviderConfig
  fullscreen: boolean
  minZoom: number
  maxZoom: number
  mapState: GeofenceDetailsMapState
  mapProviderMetaData: MapProviderMetaData
  mapTypeId: google.maps.MapTypeId
  isCreateMode: boolean
  onCoordsChange: (points: StateCoords) => void
  onChangeMapTypeId: (mapTypeId: google.maps.MapTypeId) => void
  setMapState: React.Dispatch<React.SetStateAction<GeofenceDetailsMapState>>
  onChangeMapCenterZoom: (lat: number, lng: number, zoom: number) => void
  onMapLoaded: (map: L.Map) => void
}

const OpenLayersMapArea = ({
  color,
  geometryInWgs,
  renderingGeofencesInWgs,
  mapApiProviderId,
  mapTypeId,
  fullscreen,
  editing,
  mapState,
  minZoom,
  maxZoom,
  mapProviderMetaData,
  isCreateMode,
  onCoordsChange,
  onChangeMapTypeId,
  setMapState,
  onChangeMapCenterZoom,
  onMapLoaded,
}: Props) => {
  const { hereApiCode } = useTypedSelector(getSettings_UNSAFE)

  const mapContainerRef = useRef<HTMLDivElement>(null)

  const [mapInstance, setMapInstance] = useState<L.Map | undefined>(undefined)

  const handleMapStateChange = useEventHandler(
    debounce((event: { target: L.Map }) => {
      const mapBounds = event.target.getBounds()
      const nw = mapBounds.getNorthWest()
      const ne = mapBounds.getNorthEast()
      const sw = mapBounds.getSouthWest()
      const se = mapBounds.getSouthEast()
      const size = event.target.getSize()

      const obj = {
        bounds: { nw, ne, sw, se },
        center: event.target.getCenter(),
        size: { width: size.x, height: size.y },
        zoom: event.target.getZoom(),
      }

      if (
        !mapState ||
        !isEqual(obj.center, mapState.center) ||
        !isEqual(obj.bounds, mapState?.bounds) ||
        obj.zoom !== mapState.zoom
      ) {
        setMapState({ center: obj.center, zoom: obj.zoom, bounds: obj.bounds })
      }
    }, 100),
  )

  const handleMapLoaded = (map: L.Map) => {
    setMapInstance(map)
    onMapLoaded(map)
  }

  const renderGeofences = () => (
    <OpenLayerGeofences
      geofencesInWgs={renderingGeofencesInWgs}
      mapApiProviderId={mapApiProviderId}
      preferences={{
        showGeofences: true,
        showGeofenceLabels: true,
      }}
      onGeofenceContextMenu={() => {}}
    />
  )

  const theme = useMapAreaTheme()

  return (
    <Box
      sx={{ ...theme.container, height: '100%' }}
      ref={mapContainerRef}
      data-testid="GeofenceDrawer-OpenMapContainer"
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          left: 0,
          top: 0,
          margin: 0,
          padding: 0,
          position: 'absolute',
          overflow: 'hidden',
          zIndex: 0,
        }}
      >
        <Map
          key={1}
          options={{
            center: mapState.center,
            zoom: mapState.zoom,
            minZoom,
            maxZoom,
          }}
          onMapChange={handleMapStateChange}
          editable
          onMapLoaded={handleMapLoaded}
          config={{ apiKey: hereApiCode }}
          mapApiProviderId={mapApiProviderId}
          mapScheme={mapTypeId}
        >
          {renderGeofences()}
        </Map>
      </Box>

      <MapControls
        mapProviderSelectionUI={
          mapProviderMetaData.selectionUI === 'do_not_show'
            ? 'do_not_show'
            : {
                currentMapProvider: mapProviderMetaData.currentMapProvider,
                onClick: mapProviderMetaData.selectionUI.onClick,
              }
        }
        mapTypeId={mapTypeId}
        center={mapState.center}
        zoom={mapState.zoom}
        minZoom={minZoom}
        maxZoom={maxZoom}
        fullscreenUI={{
          isFullscreen: fullscreen,
          onEnterButtonClick: () => mapContainerRef.current?.requestFullscreen(),
        }}
        showMapTypeControls
        onChangeMapTypeId={onChangeMapTypeId}
        onChangeMapCenterZoom={onChangeMapCenterZoom}
      />
      {mapInstance && (
        <DrawingControl
          fillColor={color}
          isEditing={editing}
          mapInstance={mapInstance}
          onCoordsChange={onCoordsChange}
          mapApiProviderId={mapApiProviderId}
          coordsInWgs={isEmpty(geometryInWgs) ? [] : geometryInWgs}
          isCreateMode={isCreateMode}
        />
      )}
    </Box>
  )
}

export default OpenLayersMapArea
