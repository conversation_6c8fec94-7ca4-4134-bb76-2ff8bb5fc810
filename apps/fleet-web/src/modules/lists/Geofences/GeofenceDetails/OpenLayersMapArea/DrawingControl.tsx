import { useEffect, useState } from 'react'
import { Stack } from '@karoo-ui/core'
import ClearIcon from '@mui/icons-material/Clear'
import SquareIcon from '@mui/icons-material/CropDin'
import EditIcon from '@mui/icons-material/Edit'
import CircleIcon from '@mui/icons-material/PanoramaFishEye'
import UndoIcon from '@mui/icons-material/Undo'
import L from 'leaflet'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import { MapApiProvider } from '@fleet-web/api/user/types'
import { defaultShapeOptions } from '@fleet-web/components/_map/_openstreetmap/_controls/DrawingControl/constants'
import { getCircleGeodesicCoords } from '@fleet-web/components/_map/_openstreetmap/_controls/DrawingControl/DrawingUtils'
import type { OSM } from '@fleet-web/components/_map/_openstreetmap/types'
import { useEffectEvent, useEventHandler } from '@fleet-web/hooks/useEventHandler'
import type { FixMeAny } from '@fleet-web/types'
import IntlTypography from '@fleet-web/util-components/IntlTypography'
import { useMapAreaTheme } from '@fleet-web/util-components/map/shared/MapAreaThemeContext'
import type { MapApiProvider_Leaflet } from '@fleet-web/util-components/map/shared/types'
import { gcj2wgsIfInChinaWithoutTaiwan } from '@fleet-web/util-functions/china-map-utils'

import DrawingTool from '../components/DrawingTool'
import { convertAutoNaviCoords2Gcj, type ToolType } from '../types'

type DrawingControlProps = {
  mapInstance: L.Map
  fillColor: string
  isEditing: boolean
  coordsInWgs: Array<L.LatLngLiteral> | undefined
  mapApiProviderId: MapApiProvider_Leaflet
  onCoordsChange: (coords: Array<L.LatLngLiteral>) => void
  isCreateMode: boolean
}

type ShapeLeafletEvent<
  Shape extends L.Polygon | L.Circle | L.Rectangle = L.Polygon | L.Circle | L.Rectangle,
> = Except<L.LeafletEvent, 'target' | 'layer'> & {
  target: Shape
  layer: Shape
}

type ShapeTypeClass = L.Polygon | L.Circle | L.Rectangle

const DrawingControl = ({
  mapInstance,
  coordsInWgs,
  mapApiProviderId,
  isEditing,
  fillColor,
  onCoordsChange,
  isCreateMode,
}: DrawingControlProps) => {
  const [baseShape, setBaseShape] = useState<ShapeTypeClass | undefined>(undefined)
  const [toolType, setToolType] = useState<ToolType | null>('polygon')
  const [lastPointsInWgs, setLastPointsInWgs] = useState<Array<Array<L.LatLngLiteral>>>(
    [],
  )
  const [shouldShowInstruction, setShouldShowInstruction] = useState(false)
  const theme = useMapAreaTheme()

  const onMount = useEffectEvent(() => {
    mapInstance.doubleClickZoom.disable()
    onStartDrawingShape({ pointsInWgs: coordsInWgs })
  })
  useEffect(() => {
    onMount()
  }, [])

  const onFinishDrawing = useEventHandler((event: KeyboardEvent) => {
    if (!baseShape) {
      return
    }
    if (event.code === 'Enter') {
      baseShape.editor.cancelDrawing()
    }
  })

  useEffect(() => {
    window.addEventListener('keyup', onFinishDrawing)
    return () => {
      window.removeEventListener('keyup', onFinishDrawing)
    }
  }, [onFinishDrawing])

  // update the fill color
  useEffect(() => {
    if (baseShape) {
      baseShape.setStyle({ fillColor: fillColor })
    }
  }, [fillColor, baseShape])

  const convertCoords2Wgs = (coords: Array<L.LatLngLiteral>) =>
    mapApiProviderId === MapApiProvider.AUTO_NAVI_CHINESE
      ? coords.map((coord) =>
          gcj2wgsIfInChinaWithoutTaiwan({ gcjLat: coord.lat, gcjLng: coord.lng }),
        )
      : coords

  const onDraggingEnd = useEventHandler((event: ShapeLeafletEvent) => {
    onUpdatingPoints(event)
  })

  const onDrawingEnd = useEventHandler((event: ShapeLeafletEvent) => {
    if (toolType === 'polygon') {
      const newPoints =
        'getLatLngs' in event.target && resolveLatLngs(event.target.getLatLngs()[0])

      if (newPoints) {
        onCoordsChange(convertCoords2Wgs(newPoints))
        setLastPointsInWgs(resolveLastPoints(coordsInWgs))
        setShouldShowInstruction(false)
        setToolType(null)
      }
      return
    }

    let newPoints: Array<L.LatLngLiteral> = []
    if (toolType === 'circle' && event.layer instanceof L.Circle) {
      const origin = event.layer.getLatLng()
      const radius = event.layer.getRadius()
      newPoints = getCircleGeodesicCoords(origin, radius) // These are the points that make up the circle
    } else if (toolType === 'rectangle' && 'getLatLngs' in event.target) {
      newPoints = resolveLatLngs(event.target.getLatLngs()[0])
    }

    // Redraw circle/reactangle as polygon to enable vertex editing
    // NOTE: the points from the event in autonavi China is gcj format
    const newPointsInWgs = convertCoords2Wgs(newPoints)
    onStartDrawingShape({ pointsInWgs: newPointsInWgs })
    onCoordsChange(newPointsInWgs)
    setToolType(null)
    setLastPointsInWgs(resolveLastPoints(coordsInWgs))
    return
  })

  const onDrawingMousedown = useEventHandler((eventAny: ShapeLeafletEvent) => {
    if (
      !coordsInWgs ||
      coordsInWgs.length > 0 ||
      toolType === 'circle' ||
      toolType === 'rectangle'
    ) {
      return
    }

    const event = eventAny as ShapeLeafletEvent<L.Polygon>
    const drawnPoints = resolveLatLngs(event.target.getLatLngs()[0])

    if (drawnPoints.length > 0 && toolType === 'polygon' && isCreateMode) {
      setShouldShowInstruction(true)
    }
    return
  })

  const onDeletingPoint = useEventHandler((event: ShapeLeafletEvent) => {
    onUpdatingPoints(event)
  })

  useEffect(
    () => () => {
      if (baseShape) {
        baseShape.off('editable:drawing:end', onDrawingEnd, false)
        baseShape.off('editable:drawing:mousedown', onDrawingMousedown, false)
        baseShape.off('editable:dragend', onDraggingEnd, false)
        baseShape.off('editable:vertex:dragend', onDraggingEnd, false)
        baseShape.off('editable:vertex:deleted', onDeletingPoint, false)
      }
    },
    [baseShape, onDeletingPoint, onDraggingEnd, onDrawingEnd, onDrawingMousedown],
  )

  const onChangeToolType = (newToolType: ToolType) => {
    onStartDrawingShape({ toolType: newToolType })
    setToolType(newToolType)
    onCoordsChange([])
    setLastPointsInWgs(resolveLastPoints(coordsInWgs))
  }

  const onClearAllPolygon = () => {
    baseShape?.remove()
    onCoordsChange([])
    setLastPointsInWgs(resolveLastPoints(coordsInWgs))

    onStartDrawingShape({})
  }

  const onUpdatingPoints = (event: L.LeafletEvent) => {
    if (!('getLatLngs' in event.target)) {
      return
    }
    onCoordsChange(convertCoords2Wgs(resolveLatLngs(event.target.getLatLngs()[0])))
    setLastPointsInWgs(resolveLastPoints(coordsInWgs))
  }

  const onStartDrawingShape = useEventHandler(
    ({
      pointsInWgs = [],
      toolType = 'polygon',
    }: {
      pointsInWgs?: Array<L.LatLngLiteral>
      toolType?: ToolType
    }) => {
      const shapeOptions = {
        ...defaultShapeOptions,
        draggable: isEditing,
        fillColor: fillColor,
      } satisfies L.PolylineOptions & { draggable?: boolean }

      if (baseShape) {
        baseShape.remove()
      }

      let newShape: ShapeTypeClass

      if (pointsInWgs && pointsInWgs.length > 0) {
        // NOTE: the points displayed in the map should based on the map
        newShape = L.polygon(
          convertAutoNaviCoords2Gcj(pointsInWgs, mapApiProviderId),
          shapeOptions,
        ).addTo(mapInstance)
        // isCentered && onCenterToPolygon(newShape)
      } else {
        newShape = match(toolType)
          .returnType<ShapeTypeClass>()
          .with('circle', () =>
            mapInstance.editTools.startCircle(undefined, {
              ...shapeOptions,
              radius: 0,
            }),
          )
          .with('polygon', () =>
            mapInstance.editTools.startPolygon(undefined, shapeOptions),
          )
          .with('rectangle', () =>
            mapInstance.editTools.startRectangle(undefined, shapeOptions),
          )
          .exhaustive()
      }

      setBaseShape(newShape)

      onStartEventsSubscription(shapeOptions, newShape)
    },
  )

  const onStartEventsSubscription = (
    shapeOptions: OSM.ShapeProps,
    newBaseShape: ShapeTypeClass,
  ) => {
    if (newBaseShape) {
      if (isEditing) {
        newBaseShape.enableEdit?.()
      } else {
        newBaseShape.disableEdit?.()
      }

      newBaseShape.on('editable:drawing:end', onDrawingEnd, false)
      newBaseShape.on('editable:drawing:mousedown', onDrawingMousedown, false)
      newBaseShape.on('editable:dragend', onDraggingEnd, false)
      newBaseShape.on('editable:vertex:dragend', onDraggingEnd, false)
      newBaseShape.on('editable:vertex:deleted', onDeletingPoint, false)

      // Set style to guide lines
      if (newBaseShape.editor) {
        // Apparently "tools" is not a public API and we should not be using it
        const shapeEditorTools = (newBaseShape.editor as FixMeAny).tools
        shapeEditorTools.forwardLineGuide.setStyle(shapeOptions)
        shapeEditorTools.backwardLineGuide.setStyle(shapeOptions)
      }
    }
  }

  const onUndoLastClick = () => {
    const newPoints = lastPointsInWgs.slice(-1)[0]

    onStartDrawingShape({ pointsInWgs: newPoints })
    onCoordsChange(newPoints)
    setLastPointsInWgs(lastPointsInWgs.slice(0, -1))
  }

  const resolveLastPoints = (
    newPoints: Array<L.LatLngLiteral> | null | undefined,
  ): Array<Array<L.LatLngLiteral>> =>
    newPoints && newPoints.length > 0
      ? [...lastPointsInWgs, newPoints]
      : [...lastPointsInWgs]

  const resolveLatLngs = (latlngs: FixMeAny): Array<L.LatLngLiteral> =>
    latlngs.map((latlng: FixMeAny) => ({ lat: latlng.lat, lng: latlng.lng }))

  return isEditing ? (
    <Stack
      data-testid="Geofence-OpenMap-DrawingControl"
      sx={{
        backgroundColor: 'white',
        position: 'absolute',
        gap: 1,
        p: 2,
        top: 0,
        borderBottomLeftRadius: '4px',
        borderBottomRightRadius: '4px',
        transform: 'translateX(-50%)',
        left: '50%',
        width: '366px',
      }}
    >
      <Stack
        justifyContent="center"
        direction="row"
        gap={3}
        sx={{
          '& button': {
            height: theme.drawingControls.buttonSize,
            width: theme.drawingControls.buttonSize,
          },
          '& svg': {
            fontSize: theme.drawingControls.iconSize,
          },
        }}
      >
        <Stack
          gap={1}
          direction="row"
        >
          <DrawingTool
            color="secondary"
            variant="outlined"
            tooltipLabel="Undo"
            onClick={onUndoLastClick}
            disabled={!lastPointsInWgs || lastPointsInWgs.length === 0}
          >
            <UndoIcon />
          </DrawingTool>
          <DrawingTool
            color="error"
            variant="outlined"
            tooltipLabel="Clear"
            disabled={coordsInWgs?.length === 0}
            onClick={onClearAllPolygon}
          >
            <ClearIcon />
          </DrawingTool>
        </Stack>

        <Stack
          gap={1}
          direction="row"
        >
          <DrawingTool
            tooltipLabel="Circle"
            onClick={() => onChangeToolType('circle')}
            color={toolType === 'circle' ? 'primary' : 'secondary'}
            variant={toolType === 'circle' ? 'contained' : 'outlined'}
          >
            <CircleIcon />
          </DrawingTool>
          <DrawingTool
            tooltipLabel="Square"
            onClick={() => onChangeToolType('rectangle')}
            color={toolType === 'rectangle' ? 'primary' : 'secondary'}
            variant={toolType === 'rectangle' ? 'contained' : 'outlined'}
          >
            <SquareIcon />
          </DrawingTool>
          <DrawingTool
            tooltipLabel="Free form"
            onClick={() => onChangeToolType('polygon')}
            color={toolType === 'polygon' ? 'primary' : 'secondary'}
            variant={toolType === 'polygon' ? 'contained' : 'outlined'}
          >
            <EditIcon />
          </DrawingTool>
        </Stack>
      </Stack>
      {shouldShowInstruction && (
        <Stack
          data-testid="OpenMap-DrawingControl-Instruction"
          direction="column"
          textAlign="center"
        >
          <IntlTypography
            variant="caption"
            msgProps={{ id: 'list.geofence.create.instruction' }}
          />
        </Stack>
      )}
    </Stack>
  ) : null
}

export default DrawingControl
