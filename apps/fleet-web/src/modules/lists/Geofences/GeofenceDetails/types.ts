import type { ChangeEventValue } from 'google-map-react'
import type { SetOptional } from 'type-fest'

import { MapApiProvider } from '@fleet-web/api/user/types'
import type { getFocusedVehicle } from '@fleet-web/duxs/vehicles'
import type { MapApiProvider_Leaflet } from '@fleet-web/util-components/map/shared/types'
import { wgs2gcjIfInChinaWithoutTaiwan } from '@fleet-web/util-functions/china-map-utils'

export type GeofenceDetailsDrawerHistoryProps = {
  mapAssets?: {
    showVehicles: boolean
    focusedVehicle: ReturnType<typeof getFocusedVehicle>
  }
  mapState?: {
    center: {
      lat: number
      lng: number
    }
    zoom: number
  }
}

export type GeofenceDetailsMapState =
  | ChangeEventValue
  | SetOptional<ChangeEventValue, 'bounds' | 'marginBounds' | 'size'>

export type StateCoords = Array<{
  lat: number
  lng: number
}>

export type ToolType = 'circle' | 'polygon' | 'rectangle'

export const convertAutoNaviCoords2Gcj = (
  coords: Array<L.LatLngLiteral>,
  mapApiProviderId: MapApiProvider_Leaflet,
) =>
  mapApiProviderId === MapApiProvider.AUTO_NAVI_CHINESE
    ? coords.map((coord) => wgs2gcjIfInChinaWithoutTaiwan(coord))
    : coords
