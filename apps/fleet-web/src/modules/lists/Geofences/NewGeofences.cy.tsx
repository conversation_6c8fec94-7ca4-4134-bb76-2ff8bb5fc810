/* eslint-disable no-param-reassign */
/// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { createMemoryHistory } from 'history'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type {
  Ct_fleet_delete_geofences,
  Ct_fleet_update_geofence_group,
  SaveGeofence,
} from '@fleet-web/api/geofences/types'
import type { GeofenceGroupId, GeofenceId } from '@fleet-web/api/types'
import { MapApiProvider } from '@fleet-web/api/user/types'
import { duxsMocks } from '@fleet-web/cypress-ct/mocks/duxs'
import { endpointsMocks, vehiclesMock } from '@fleet-web/cypress-ct/mocks/endpoints'
import {
  geofenceEndpointMocks,
  geofenceGroupsMock,
} from '@fleet-web/cypress-ct/mocks/endpoints/geofence'
import {
  cyExpect,
  exhaustiveEndpointCallCheck,
  matchWithMethod,
  mountWithProviders,
  runTestsInOrderWithLoggingAndSetupOnce,
} from '@fleet-web/cypress-ct/utils'
import { LIST } from '@fleet-web/modules/app/components/routes/list'
import type { FixMeAny } from '@fleet-web/types'
import { toImmutable } from '@fleet-web/util-functions/functional-utils'

import Geofences from '.'

const basePath = LIST.subMenusRoutes.GEOFENCES.path
const newName = 'new name'
const newDesp = 'new description'

const globalHistory = createMemoryHistory({
  initialEntries: [basePath],
})

const mountGeofenceMain = (options?: {
  geofencesImportGeofences?: boolean
  geofencesAddGroup?: boolean
  geofencesAddGeofence?: boolean
  geofencesDeleteGeofence?: boolean
}) => {
  mountWithProviders(<Geofences />, {
    history: globalHistory,
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({
          mapApiProvider: MapApiProvider.GOOGLE,
          availableMapApiProviders: [
            MapApiProvider.GOOGLE,
            MapApiProvider.AUTO_NAVI_CHINESE,
          ],
          geofencesImportGeofences: options?.geofencesImportGeofences ?? true,
          geofencesAddGroup: options?.geofencesImportGeofences ?? true,
          geofencesAddGeofence: options?.geofencesImportGeofences ?? true,
          geofencesDeleteGeofence: options?.geofencesImportGeofences ?? true,
        }).mockState,
        geofences: duxsMocks.geofences,
        fleetMapView: duxsMocks.fleetMapViewDetailsPanel,
        drivers: duxsMocks.drivers({}),
        vehicles: duxsMocks.vehicles({
          vehicles: vehiclesMock,
        }),
      },
    },
  })

  // Wait for the api
  cy.wait('@ct_fleet_get_geofence_list')
}

const geofenceGroupsObj =
  geofenceEndpointMocks.ct_fleet_get_geofence_list().body.result
    .ct_fleet_get_geofence_groups

const geofenceGroupsArr = R.keys(geofenceGroupsObj).map((key) => geofenceGroupsObj[key])

const geofencesObj =
  geofenceEndpointMocks.ct_fleet_get_geofence_list().body.result.ct_fleet_get_geofence

const geofencesArr = toImmutable(R.keys(geofencesObj).map((key) => geofencesObj[key]))

// Based on isAllGroups logic. Check geofences parser
const geofencesThatAreInAllGroupsAutomatically = geofencesArr.filter((geofence) => {
  if (!geofence.geofence_groups) {
    return false
  }
  return (
    geofence.geofence_groups.length > 0 &&
    geofence.geofence_groups.length === geofenceGroupsArr.length
  )
})

function clickGroupTab() {
  mountGeofenceMain()
  cy.findByTestId('GeofencesMain-GridContainerTab-group').click()
}

const ToolbarDeleteButton = () => cy.findByTestId('datagrid-delete-button-with-counter')

const GridContent = () => cy.get('.MuiDataGrid-virtualScrollerRenderZone').children()

const mockVehicles = endpointsMocks.ct_fleet_get_vehiclelist_v3().successReply

const checkVehicleObjectsWhenAllVehicles = (
  vehiclesObject: SaveGeofence.ApiInput['vehiclesObject'],
) => {
  cyExpect(vehiclesObject.allFlag, 'vehiclesObject.allFlag').toEq(true)
}

const handleApiCalls = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_prelogin_data' }, () =>
      req.reply({ delay: 50, body: { id: 10, result: {} } }),
    )
    .with(matchWithMethod(req, 'ct_fleet_get_geofence_list'), () => {
      req.reply(geofenceEndpointMocks.ct_fleet_get_geofence_list())
    })
    .with(matchWithMethod(req, 'ct_fleet_get_vehiclelist_v3'), () =>
      req.reply(mockVehicles),
    )
    .with({ method: 'ct_fleet_get_vehicle_positions' }, () => {
      const mockVehicleId1 =
        mockVehicles.body.result.ct_fleet_get_vehiclelist[0].vehicle_id
      req.reply(
        endpointsMocks.ct_fleet_get_vehicle_positions({
          mockVehicleId: mockVehicleId1,
        }).successReply,
      )
    })
    .otherwise(exhaustiveEndpointCallCheck)

beforeEach(() => {
  cy.intercept('POST', '/jsonrpc/public/index.php', handleApiCalls)
})

describe('Geofences Table', () => {
  it('show the geofences table', () => {
    mountGeofenceMain()

    // header buttons
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').should('exist')
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').should(
      'contain.text',
      'Add Geofence',
    )
    // table exists
    cy.findByTestId('GeofenceListTable').should('exist')

    // check columns and rows
    GridContent()
      .should('have.length', geofencesArr.length)
      .first()
      .findAllByRole('gridcell')
      .should('have.length', 9)

    // the geofence with all groups should show the text All in the Group column
    cy.get('[data-id="3c"] > [data-field="groups"]').should(($el) => {
      const text = $el.text()
      expect(text).to.include('Group Name 323')
      expect(text).to.include('Group Name 324')
    })
    cy.get('[data-id="5e"] > [data-field="groups"]').should('have.text', 'All')

    // the geofence with all vehicles should show the text All in the Vehicles column
    cy.get('[data-id="3c"] > [data-field="vehicles"]').should('not.have.text')
    cy.get('[data-id="5e"] > [data-field="vehicles"]').should('have.text', 'All')

    // the delete button in toolbar should be disabled
    ToolbarDeleteButton().should('be.disabled')
  })

  it('select row will update the number of delete button', () => {
    mountGeofenceMain()
    // click the first row checkbox
    GridContent().first().find('.MuiDataGrid-cellCheckbox').first().click()
    // the delete button in toolbar should has number
    ToolbarDeleteButton().should('not.be.disabled')
    ToolbarDeleteButton().find('div').first().should('contain.text', '1')
  })

  it('click delete button will delete the geofence with confirmation', () => {
    mountGeofenceMain()

    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<Ct_fleet_delete_geofences.ApiParams>(
            req,
            'ct_fleet_delete_geofences',
          ),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              geofence_ids: [geofencesArr[0].geofence_id],
              x: 'x',
            })

            req.alias = 'deleteGeofence'
            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    // click the delete button of first row
    cy.findByTestId(`Geofence-Delete-${geofencesArr[0].geofence_id}`).click()
    // delete confirmation modal
    cy.findByTestId('Geofence-DeleteModal').should('exist')
    // click the confirm button
    cy.findByTestId('ConfirmationModal-confirm-button').click()

    cy.wait('@deleteGeofence')
    // close the confirm modal
    cy.findByTestId('Geofence-DeleteModal').should('not.exist')
  })

  // TODO
  it('click view button will open the geofence group activity page', () => {})
})

describe('Geofences Table without permissions', () => {
  it('disable the buttons', () => {
    mountGeofenceMain({
      geofencesAddGeofence: false,
      geofencesDeleteGeofence: false,
      geofencesAddGroup: false,
      geofencesImportGeofences: false,
    })

    // header buttons
    cy.findByTestId('GeofencesHeaderButton-ImportGeofence').should(
      'have.class',
      'Mui-disabled',
    )
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').should(
      'have.class',
      'Mui-disabled',
    )
    cy.findByTestId('GeofencesHeaderButton-AddGeofenceGroup').should(
      'have.class',
      'Mui-disabled',
    )

    // the delete button in toolbar should be disabled
    ToolbarDeleteButton().should('be.disabled')
    // Selection checkbox should not exist
    GridContent().first().find('.MuiDataGrid-cellCheckbox').should('not.exist')
  })
})

describe('Geofence Group Table', () => {
  it('show the geofences group table', () => {
    clickGroupTab()
    // header buttons
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').should('exist')
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').should(
      'contain.text',
      'Add Geofence',
    )
    // table exists
    cy.findByTestId('GeofenceGroupTable').should('exist')

    // check columns and rows
    GridContent()
      .should('have.length', geofenceGroupsArr.length)
      .first()
      .findAllByRole('gridcell')
      .should('have.length', 6)

    // the delete button in toolbar should be disabled
    cy.findByTestId('datagrid-delete-button-with-counter').should('be.disabled')
  })

  it('when the contained geofences is too many, it will display in the tooltip when hovered', () => {
    clickGroupTab()

    GridContent()
      .first()
      .findAllByRole('gridcell')
      .eq(2)
      .should('contain.text', geofenceGroupsArr[0].geofences.length)

    GridContent().first().children().eq(3).trigger('mouseover')

    // TODO
  })

  it('select row will update the number of delete button', () => {
    clickGroupTab()

    // click the first row checkbox
    GridContent().first().find('.MuiDataGrid-cellCheckbox').first().click()

    // the delete button in toolbar should has number
    ToolbarDeleteButton().should('not.be.disabled')
    ToolbarDeleteButton().find('div').first().should('contain.text', '1')
  })

  it('click delete button will delete the group with confirmation', () => {
    clickGroupTab()
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<FixMeAny>(req, 'ct_fleet_delete_client_group_vehicle_driver'),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              groupVehicleDriverIds: [geofenceGroupsArr[0].group_geofence_id],
              type: 'geofence',
            })
            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    // click the delete button of first row
    cy.findByTestId(
      `GeofenceGroup-Remove-${geofenceGroupsArr[0].group_geofence_id}`,
    ).click()

    // delete confirmation modal
    cy.findByTestId('GeofenceGroup-DeleteModal').should('exist')

    // click the confirm button
    cy.findByTestId('ConfirmationModal-confirm-button').click()

    // close the confirm modal
    cy.findByTestId('GeofenceGroup-DeleteModal').should('not.exist')
  })
})

describe('Geofence Group Edit Drawer', () => {
  const selectedGeofenceGroup = geofenceGroupsArr[0]
  const selectedFirstGeofenceId = selectedGeofenceGroup.geofences[0] as GeofenceId
  const selectedGeofencesSize = selectedGeofenceGroup.geofences.length

  beforeEach(() => {
    clickGroupTab()

    // click the edit button of first row
    cy.findByTestId(
      `GeofenceGroup-Edit-${geofenceGroupsArr[0].group_geofence_id}`,
    ).click()
    cy.findByTestId('GeofenceGroupDetailDrawer').should('exist')
  })

  it('click edit button will open the geofence group detail page', () => {
    // header
    cy.findByTestId('GeofenceGroupDrawer-Header')
      .find('h5')
      .should('contain.text', selectedGeofenceGroup.name)
    cy.findByTestId('GeofenceGroupDrawer-Header').find('button').should('not.be.empty')

    // description filled
    cy.findByTestId('GeofenceGroupDrawer-NameDescription')
      .find('input')
      .should('contain.value', selectedGeofenceGroup.description)

    // geofence list
    cy.findByTestId('GeofenceGroupDrawer-GenfencesTitle').should(
      'contain.text',
      `Geofences (${selectedGeofencesSize})`,
    )
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences')
      .children()
      .should('have.length', selectedGeofencesSize)
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('.MuiDataGrid-selectedRowCount')
      .should('contain.text', `${selectedGeofencesSize} rows selected`)

    // check the geofence list
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences-0').should(
      'contain.text',
      geofencesArr.find((geofence) => geofence.geofence_id === selectedFirstGeofenceId)
        ?.geofence_name,
    )

    // cancel button
    cy.findByTestId('GeofenceGroupDrawer-CancelButton').click()
    cy.findByTestId('GeofenceGroupDetailDrawer').should('not.exist')
  })

  it('update group detail', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<FixMeAny>(req, 'ct_fleet_rename_client_group_name'),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              groupData: {
                description: newDesp,
                group_geofence_id: selectedGeofenceGroup.group_geofence_id,
                name: newName,
              },
              x: 'x',
              type: 'geofence',
            })
            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .with(
          matchWithMethod<Ct_fleet_update_geofence_group.ApiParams>(
            req,
            'ct_fleet_update_geofence_group',
          ),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              geofence_groups_id: selectedGeofenceGroup.group_geofence_id,
              geofence_id_array: selectedGeofenceGroup.geofences.slice(1),
              x: 'x',
            })

            req.alias = 'saveGeofenceGroup'
            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    // click header edit button
    cy.findByTestId('GeofenceGroupDrawer-Header').find('button').click()

    cy.findByTestId('GeofenceGroupDrawer-Header')
      .find('input')
      .type(`{selectall}{backspace}${newName}`)

    // description filled
    cy.findByTestId('GeofenceGroupDrawer-NameDescription')
      .find('input')
      .type(`{selectall}{backspace}${newDesp}`)

    // delete the first selected  geofence
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences-0').find('svg').click()

    // geofence list should be updated
    cy.findByTestId('GeofenceGroupDrawer-GenfencesTitle').should(
      'contain.text',
      `Geofences (${selectedGeofencesSize - 1})`,
    )
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences')
      .children()
      .should('have.length', selectedGeofencesSize - 1)
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('.MuiDataGrid-selectedRowCount')
      .should('contain.text', `${selectedGeofencesSize - 1} rows selected`)

    // save
    cy.findByTestId('GeofenceGroupDrawer-SaveButton').click()
    cy.wait('@saveGeofenceGroup')

    cy.findByTestId('GeofenceGroupDetailDrawer').should('not.exist')
  })
})

describe('Geofence Group Add Drawer', () => {
  beforeEach(() => {
    mountGeofenceMain()
    cy.findByTestId('GeofencesMain-GridContainerTab-group').click()

    // click the add geofence group button
    cy.findByTestId('GeofencesHeaderButton-AddGeofenceGroup').click()
    cy.findByTestId('GeofenceGroupDetailDrawer').should('exist')
  })

  it('click the add group button to trigger drawer', () => {
    // url
    cyExpect(globalHistory.entries[globalHistory.entries.length - 1].search).toEqual(
      '?tab=group&type=add',
    )
    // header
    cy.findByTestId('GeofenceGroupDrawer-Header')
      .find('h5')
      .first()
      .should('contain.text', 'Add Geofence Group')

    // geofence list
    cy.findByTestId('GeofenceGroupDrawer-GenfencesTitle').should(
      'contain.text',
      'Geofences (1)',
    )
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences')
      .should('exist')
      .children()
      .should('have.length', 1)
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('.MuiDataGrid-selectedRowCount')
      .should('contain.text', `1 row selected`)
    // cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
    //   .find('.MuiTablePagination-displayedRows')
    //   .should('contain.text', '1–2 of 5')

    // cancel button
    cy.findByTestId('GeofenceGroupDrawer-CancelButton').click()
    cy.findByTestId('GeofenceGroupDetailDrawer').should('not.exist')
  })

  it('fill the data to create group', () => {
    const newGroupId = 'new_group_id' as GeofenceGroupId
    const searchText = 'b'

    const selectedGeofences = R.uniqueBy(
      [
        ...geofencesArr.filter((geofence) =>
          geofence.geofence_name.toLocaleLowerCase().includes(searchText),
        ),
        ...geofencesThatAreInAllGroupsAutomatically,
      ],
      (geofence) => geofence.geofence_id,
    )
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<FixMeAny>(req, 'ct_fleet_create_client_vehicle_driver_group'),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              vehicleGroupData: {
                name: newName,
                group_driver_id: '',
                group_vehicle_id: '',
                group_geofence_id: '',
                group_status_id: '',
                description: newDesp,
              },
              type: 'geofence',
              x: 'x',
            })
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: {
                  groupData: [
                    {
                      group_geofence_id: newGroupId,
                      description: newDesp,
                      name: newName,
                    },
                  ],
                },
              },
            })
          },
        )
        .with(
          matchWithMethod<Ct_fleet_update_geofence_group.ApiParams>(
            req,
            'ct_fleet_update_geofence_group',
          ),
          ({ params }) => {
            cyExpect(params).toDeepEqual({
              geofence_groups_id: newGroupId,
              geofence_id_array: selectedGeofences
                .map((geofence) => geofence.geofence_id)
                .reverse(),
              x: 'x',
            })

            req.alias = 'saveGroup'
            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    // fill name and description
    cy.findByTestId('GeofenceGroupDrawer-NameDescription')
      .find('input')
      .first()
      .type(newName)
    cy.findByTestId('GeofenceGroupDrawer-NameDescription')
      .find('input')
      .last()
      .type(newDesp)

    // filter geofences
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .findByRole('toolbar')
      .findByRole('button')
      .click()

    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .findByRole('toolbar')
      .findByRole('searchbox')
      .type(searchText)

    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('.MuiTablePagination-displayedRows')
      .should('contain.text', '1–2 of 2')

    // select geofence
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('input[type="checkbox"]')
      .check()

    // geofence list should be updated
    cy.findByTestId('GeofenceGroupDrawer-GenfencesTitle').should(
      'contain.text',
      `Geofences (${selectedGeofences.length})`,
    )
    cy.findByTestId('GeofenceGroupDrawer-SelectedGeofences')
      .children()
      .should('have.length', selectedGeofences.length)
    cy.findByTestId('GeofenceGroupDrawer-GeofenceDataGrid')
      .find('.MuiDataGrid-selectedRowCount')
      .should('contain.text', `${selectedGeofences.length} rows selected`)

    // save
    cy.findByTestId('GeofenceGroupDrawer-SaveButton').click()
    cy.wait('@saveGroup')

    // close the drawer
    cy.findByTestId('GeofenceGroupDetailDrawer').should('not.exist')
  })
})

// TODO: fix tests flakiness
describe.skip('Geofence Add Drawer', () => {
  const openGeofenceDetailModalWhileOnGeofencesList = () => {
    cy.findByTestId('GeofencesHeaderButton-AddGeofence').click()
    cy.findByTestId('GeofenceDetailModal').should('exist')
  }

  const setup = () => {
    mountGeofenceMain()
    cy.findByTestId('GeofencesMain-GridContainerTab-list').click()

    // click the add geofence button
    openGeofenceDetailModalWhileOnGeofencesList()
  }

  runTestsInOrderWithLoggingAndSetupOnce('geofence add drawer tests', {
    setupOnceBeforeTests: setup,
    tests: [
      {
        name: 'click the add geofence button to trigger drawer',
        fn: () => {
          // header
          cy.findByTestId('GeofenceDetailModal-Header').should(
            'contain.text',
            'Add Geofence',
          )

          // geofence groups
          cy.findByTestId('GeofenceDetailModal-GeofenceGroups')
            .find('.MuiChip-root')
            .should('have.length', 0)

          // vehicles
          // Verify that no options are selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('not.exist')

          // Open the Autocomplete dropdown by clicking on the input field
          cy.findByTestId('GeofenceDetailModal-Vehicles').click()

          // Select the option for 'All Vehicles'
          cy.get('li').contains('All Vehicles').click()

          // Verify that only the option with the value 'all' is selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('have.length', 1)
            .and('contain', 'All Vehicles')

          // place search
          cy.findByTestId('GeofenceDetailModal-PlaceSearch').should('exist')

          // map controls
          cy.findByTestId('General-MapControls').should('exist')

          // default google map
          cy.findByTestId('General-MapControls-Google').should('exist')
          cy.findByTestId('MapType-Roadmap').should('exist')
          cy.findByTestId('MapType-Hybrid').should('exist')
          cy.findByTestId('MapType-Satellite').should('exist')

          // drawer controls
          cy.findByTestId('GoogleMap-DrawingControl').should('exist')
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').should(
            'be.disabled',
          )
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Clear').should(
            'be.disabled',
          )
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Circle').should('exist')
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Square').should('exist')
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Free form')
            .should('exist')
            .should('have.class', 'MuiButton-containedPrimary')

          // click the open map with autonavi
          cy.findByTestId('General-MapControls-AutoNavi Chinese')
            .should('exist')
            .click()
          cy.findByTestId('MapType-Roadmap').should('exist')
          cy.findByTestId('MapType-Hybrid').should('not.exist')
          cy.findByTestId('MapType-Satellite').should('exist')

          cy.findByTestId('Geofence-OpenMap-DrawingControl').should('exist')

          // cancel button
          cy.findByTestId('GeofenceDetailModal-CancelButton').click()
          return cy.findByTestId('GeofenceDetailModal').should('not.exist')
        },
      },
      {
        name: 'create geofence in open layer map',
        fn: () => {
          openGeofenceDetailModalWhileOnGeofencesList()

          // before draw geofence, the input should be disabled
          cy.findByTestId('GeofenceDetailModal-Name')
            .find('input')
            .should('be.disabled')
          cy.findByTestId('GeofenceDetailModal-Desp')
            .find('input')
            .should('be.disabled')

          // click the open layer map
          cy.findByTestId('General-MapControls-AutoNavi Chinese')
            .should('exist')
            .click()

          // draw the polygon
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Free form').should(
            'have.class',
            'MuiButton-containedPrimary',
          )
          cy.findByTestId('OpenMap-DrawingControl-Instruction').should('not.exist')
          cy.findByTestId('GeofenceDrawer-OpenMapContainer').click(50, 50)
          cy.findByTestId('OpenMap-DrawingControl-Instruction').should('not.exist')
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(500)
          cy.findByTestId('GeofenceDrawer-OpenMapContainer').click(100, 100)
          // the instruction shows
          cy.findByTestId('OpenMap-DrawingControl-Instruction').should('exist')
          cy.findByTestId('GeofenceDrawer-OpenMapContainer').dblclick(50, 100)
          cy.findByTestId('OpenMap-DrawingControl-Instruction').should('not.exist')

          // marked
          cy.findByText('Area marked').should('exist')
          cy.findByTestId('GeofenceDetailModal-Name')
            .find('input')
            .should('not.be.disabled')
            .type(newName)
          cy.findByTestId('GeofenceDetailModal-Desp')
            .find('input')
            .should('not.be.disabled')
            .type(newDesp)

          // clear it
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Free form').should(
            'not.have.class',
            'MuiButton-containedPrimary',
          )
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Clear')
            .should('have.css', 'color', 'rgb(244, 67, 54)')
            .click()

          // the undo button should not be disabled
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').should(
            'not.be.disabled',
          )

          // FIXME: unable to draw rectangle and circle in open layer map since have no idea how to drag and drop
          // // Rectangle
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Square').click()
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Square').should(
          //   'have.class',
          //   'MuiButton-containedPrimary',
          // )
          // cy.findByTestId('GeofenceDrawer-OpenMapContainer')
          //   .trigger('mousedown', { which: 1, clientX: 50, clientY: 50 })
          //   .wait(500)
          // cy.findByTestId('OpenMap-DrawingControl-Instruction').should('not.exist')
          // cy.findByTestId('GeofenceDrawer-OpenMapContainer')
          //   .trigger('mousemove', { which: 1, clientX: 100, clientY: 100 })
          //   .wait(500)
          // cy.findByTestId('GeofenceDrawer-OpenMapContainer')
          //   .trigger('mouseup', { which: 1 })
          //   .wait(500)
          // // cy.findByTestId('GeofenceDrawer-OpenMapContainer').click(100, 100).wait(500)
          // cy.get('circle').should('have.length', 4) // 4 points

          // // click Circle directly without clicking clear button
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Circle').click()
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Circle').should(
          //   'have.class',
          //   'MuiButton-containedPrimary',
          // )
          // cy.findByTestId('GeofenceDrawer-OpenMapContainer').click(200, 200).wait(500)
          // cy.findByTestId('OpenMap-DrawingControl-Instruction').should('not.exist')
          // cy.findByTestId('GeofenceDrawer-OpenMapContainer').click(250, 250).wait(500)
          // cy.get('circle').should('have.length', 36)

          // // undo
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').click()
          // cy.get('circle').should('have.length', 4)
          // cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').click()
          // cy.get('circle').should('have.length', 3)

          // select geofence groups, click dropdown button
          cy.findByTestId('GeofenceDetailModal-GeofenceGroups')
            .find('input')
            .type('all{downArrow}{enter}')

          // Verify that no options are selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('not.exist')

          // Open the Autocomplete dropdown by clicking on the input field
          cy.findByTestId('GeofenceDetailModal-Vehicles').click()

          // Select the option for 'All Vehicles'
          cy.get('li').contains('All Vehicles').click()

          // Verify that only the option with the value 'all' is selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('have.length', 1)
            .and('contain', 'All Vehicles')

          // submit
          cy.findByTestId('GeofenceDetailModal-CancelButton').click()
          cy.findByTestId('DiscardModal-confirm-button').click()

          return cy.findByTestId('GeofenceDetailModal').should('not.exist')
        },
      },
      {
        name: 'fill the data to create geofence with google map',
        fn: () => {
          openGeofenceDetailModalWhileOnGeofencesList()

          const newGeom =
            'POLYGON((-0.************** 0.326841489086852,-0.3350830078125 0.2581778372370411,-0.************** 0.2581778372370411,-0.************** 0.326841489086852))'
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<SaveGeofence.ApiInput>(req, 'ct_fleet_save_geofence'),
                ({ params }) => {
                  const { vehiclesObject, geom, ...restParams } = params

                  cyExpect(geom.split(',').length, 'geom length').toEq(4)

                  cyExpect(vehiclesObject.allFlag, 'vehiclesObject.allFlag').toEq(true)

                  cyExpect(restParams).toDeepEqual({
                    x: 'x',
                    geofence_id: 'new',
                    name: newName,
                    description: newDesp,
                    // geom: newGeom,
                    groups: {
                      allFlag: true,
                      groups: Object.values(geofenceGroupsMock).map(
                        (group) => group.group_geofence_id,
                      ),
                    },
                    colour: '#666666',
                  })

                  req.alias = 'createGeofence'
                  req.reply({
                    delay: 50,
                    body: {
                      id: 10,
                      result: {
                        validate_geofence: {
                          is_valid: true,
                          updated_geom: newGeom,
                        },
                        ct_fleet_save_geofence: [
                          { count: '0', geofence_id: 'newgeofenceId' },
                        ],
                      } satisfies SaveGeofence.ApiOutput,
                    },
                  })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          // before draw geofence, the input should be disabled
          cy.findByTestId('GeofenceDetailModal-Name')
            .find('input')
            .should('be.disabled')
          cy.findByTestId('GeofenceDetailModal-Desp')
            .find('input')
            .should('be.disabled')

          // draw the polygon
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Free form').should(
            'have.class',
            'MuiButton-containedPrimary',
          )
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(400)

          // FIXME: instruction
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(50, 50)
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(400)
          // cy.findByTestId('GoogleMap-DrawingControl-Instruction').should('not.exist')
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(100, 100)
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(400)
          // // the instruction shows
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').dblclick(50, 100)

          // marked
          cy.findByText('Area marked').should('exist')
          cy.findByTestId('GeofenceDetailModal-Name')
            .find('input')
            .should('not.be.disabled')
            .type(newName)
          cy.findByTestId('GeofenceDetailModal-Desp')
            .find('input')
            .should('not.be.disabled')
            .type(newDesp)

          // clear it
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Free form').should(
            'not.have.class',
            'MuiButton-containedPrimary',
          )
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Clear')
            .should('have.css', 'color', 'rgb(244, 67, 54)')
            .click()

          // the undo button should not be disabled
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').should(
            'not.be.disabled',
          )

          // Rectangle
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Square').click()
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Square').should(
            'have.class',
            'MuiButton-containedPrimary',
          )
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(50, 50)
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(500)
          cy.findByTestId('GoogleMap-DrawingControl-Instruction').should('not.exist')
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(100, 100)
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(500)
          // FIXME: unable to test the overlay points
          // cy.get('circle').should('have.length', 4) // 4 points

          // click Circle directly without clicking clear button
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Circle').click()
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Circle').should(
            'have.class',
            'MuiButton-containedPrimary',
          )
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(200, 200)
          // eslint-disable-next-line cypress/no-unnecessary-waiting
          cy.wait(500)
          cy.findByTestId('GoogleMap-DrawingControl-Instruction').should('not.exist')
          cy.findByTestId('GeofenceDrawer-GoogleMapContainer').click(250, 250)

          // undo
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').should(
            'not.be.disabled',
          )
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').click()
          cy.findByTestId('GeofenceDetailModal-DrawingControl-Undo').click()

          // select geofence groups, click dropdown button
          cy.findByTestId('GeofenceDetailModal-GeofenceGroups')
            .find('input')
            .type('all{downArrow}{enter}')

          // Verify that no options are selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('not.exist')

          // Open the Autocomplete dropdown by clicking on the input field
          cy.findByTestId('GeofenceDetailModal-Vehicles').click()

          // Select the option for 'All Vehicles'
          cy.get('li').contains('All Vehicles').click()

          // Verify that only the option with the value 'all' is selected
          cy.findByTestId('GeofenceDetailModal-Vehicles')
            .find('.MuiChip-root')
            .should('have.length', 1)
            .and('contain', 'All Vehicles')

          // submit
          cy.findByTestId('GeofenceDetailModal-SaveButton').click()

          cy.wait('@createGeofence')

          return cy.findByTestId('GeofenceDetailModal').should('not.exist')
        },
      },
    ],
  })
})

// TODO: fix tests flakiness
describe.skip('Geofence Edit Drawer', () => {
  const selectedGeofence = geofencesArr[0]
  const selectCount = 4
  const resultLimit = 3
  const selectGroupActions = new Array(selectCount).fill('{downArrow}{enter}').join('')
  const selectedGeofenceGroupIds = geofenceGroupsArr
    .slice(0, selectCount)
    .map((group) => group.group_geofence_id)

  const newGeom =
    'POLYGON((103.89643907546997 1.3297627503891585,103.88652563095093 1.3297627503891585,103.88652563095093 1.3207743907512048,103.89643907546997 1.3207743907512048,103.89643907546997 1.3297627503891585))'

  const setup = () => {
    mountGeofenceMain()
    cy.findByTestId('GeofencesMain-GridContainerTab-group').click()
    cy.findByTestId('GeofencesMain-GridContainerTab-list').click()

    // click the edit button of first row
    cy.findByTestId(`Geofence-Edit-${geofencesArr[0].geofence_id}`).click()
    cy.findByTestId('GeofenceDetailModal').should('exist')
  }

  it('click edit button will open the geofence detail page', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<SaveGeofence.ApiInput>(req, 'ct_fleet_save_geofence'),
          ({ params }) => {
            const { vehiclesObject, ...restParams } = params
            checkVehicleObjectsWhenAllVehicles(vehiclesObject)

            cyExpect(restParams).toDeepEqual({
              x: 'x',
              geofence_id: selectedGeofence.geofence_id,
              name: newName,
              description: newDesp,
              geom: selectedGeofence.the_geom,
              groups: {
                allFlag: false,
                groups: selectedGeofenceGroupIds,
              },
              colour: '#ffbd4d',
            })

            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: {
                  validate_geofence: { is_valid: true, updated_geom: newGeom },
                  ct_fleet_save_geofence: [
                    { count: '0', geofence_id: selectedGeofence.geofence_id },
                  ],
                } satisfies SaveGeofence.ApiOutput,
              },
            })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })
    setup()

    // header
    cy.findByTestId('GeofenceDetailModal-Header').should(
      'contain.text',
      'Edit Geofence',
    )

    // name
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .should('contain.value', selectedGeofence.geofence_name)
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .type(`{selectall}{backspace}${newName}`)

    // description filled
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .should('contain.value', selectedGeofence.description)
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .type(`{selectall}{backspace}${newDesp}`)

    // geofence list
    cy.findByTestId('GeofenceDetailModal-GeofenceGroups')
      .find('.MuiChip-root')
      .should('have.length', 0)

    // vehicles
    cy.findByTestId('GeofenceDetailModal-Vehicles')
      .find('.MuiChip-root')
      .find('span')
      .should('contain.text', selectedGeofence.vehicles_all_flag && 'All')

    // select geofence groups, click dropdown button, select 4 groups
    cy.findByTestId('GeofenceDetailModal-GeofenceGroups')
      .find('input')
      .type(`3${selectGroupActions}`)

    // the selected group should be in the input with chips and extra number
    for (const [index, groupId] of selectedGeofenceGroupIds.entries()) {
      if (index < resultLimit) {
        cy.findByTestId(`Autocomplete-Chip-${groupId}`).should('exist')
      } else {
        cy.findByTestId(`Autocomplete-Chip-${groupId}`).should('not.exist')
      }
    }
    cy.findByTestId(`Autocomplete-Chip-${selectedGeofenceGroupIds[0]}`)
      .parent()
      .should('contain.text', ` +${selectCount - resultLimit}`)

    // submit
    cy.findByTestId('GeofenceDetailModal-SaveButton').click()
    cy.wait('@ct_fleet_save_geofence')

    // close the drawer
    cy.findByTestId('GeofenceDetailModal').should('not.exist')
  })

  it('save geofence with invalid geometries, would show the confirmation modal and click cancel button', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<SaveGeofence.ApiInput>(req, 'ct_fleet_save_geofence'),
          ({ params }) => {
            const { vehiclesObject, ...restParams } = params
            checkVehicleObjectsWhenAllVehicles(vehiclesObject)

            cyExpect(restParams).toDeepEqual({
              x: 'x',
              geofence_id: selectedGeofence.geofence_id,
              name: newName,
              description: newDesp,
              geom: selectedGeofence.the_geom,
              groups: { allFlag: false, groups: [] },
              colour: '#ffbd4d',
            })

            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: {
                  validate_geofence: { is_valid: false, updated_geom: newGeom },
                  ct_fleet_save_geofence: [
                    { count: '0', geofence_id: selectedGeofence.geofence_id },
                  ],
                } satisfies SaveGeofence.ApiOutput,
              },
            })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })
    setup()

    // header
    cy.findByTestId('GeofenceDetailModal-Header').should(
      'contain.text',
      'Edit Geofence',
    )

    // name
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .should('contain.value', selectedGeofence.geofence_name)
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .type(`{selectall}{backspace}${newName}`)

    // description filled
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .should('contain.value', selectedGeofence.description)
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .type(`{selectall}{backspace}${newDesp}`)

    // submit
    cy.findByTestId('GeofenceDetailModal-SaveButton').click()
    cy.wait('@ct_fleet_save_geofence')

    // show the confirmation modal
    cy.findByTestId('GeofenceDetailModal-ImproveConfirmationModal').should('exist')

    // click cancel button
    cy.findByTestId('ConfirmationModal-cancel-button').click()

    // close the drawer
    cy.findByTestId('GeofenceDetailModal-ImproveConfirmationModal').should('not.exist')
    cy.findByTestId('GeofenceDetailModal').should('not.exist')
  })

  it('save geofence with invalid geometries, would show the confirmation modal and click confirm button', () => {
    let saveTimes = 0

    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<SaveGeofence.ApiInput>(req, 'ct_fleet_save_geofence'),
          ({ params }) => {
            const { vehiclesObject, ...restParams } = params
            checkVehicleObjectsWhenAllVehicles(vehiclesObject)

            cyExpect(restParams).toDeepEqual({
              x: 'x',
              geofence_id: selectedGeofence.geofence_id,
              name: newName,
              description: newDesp,
              geom: saveTimes === 0 ? selectedGeofence.the_geom : newGeom,
              groups: { allFlag: false, groups: [] },
              colour: '#ffbd4d',
            })

            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: {
                  validate_geofence: {
                    is_valid: saveTimes === 0 ? false : true,
                    updated_geom: newGeom,
                  },
                  ct_fleet_save_geofence: [
                    { count: '0', geofence_id: selectedGeofence.geofence_id },
                  ],
                } satisfies SaveGeofence.ApiOutput,
              },
            })
            saveTimes += 1
          },
        )
        .otherwise(() => handleApiCalls(req))
    })
    setup()

    // header
    cy.findByTestId('GeofenceDetailModal-Header').should(
      'contain.text',
      'Edit Geofence',
    )

    // name
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .should('contain.value', selectedGeofence.geofence_name)
    cy.findByTestId('GeofenceDetailModal-Name')
      .find('input')
      .type(`{selectall}{backspace}${newName}`)

    // description filled
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .should('contain.value', selectedGeofence.description)
    cy.findByTestId('GeofenceDetailModal-Desp')
      .find('input')
      .type(`{selectall}{backspace}${newDesp}`)

    // submit
    cy.findByTestId('GeofenceDetailModal-SaveButton').click()
    cy.wait('@ct_fleet_save_geofence')

    // show the confirmation modal
    cy.findByTestId('GeofenceDetailModal-ImproveConfirmationModal').should('exist')

    // click the confirm button
    cy.findByTestId('ConfirmationModal-confirm-button').click()

    // close the drawer
    cy.findByTestId('GeofenceDetailModal-ImproveConfirmationModal').should('not.exist')
    cy.findByTestId('GeofenceDetailModal').should('not.exist')
  })
})
