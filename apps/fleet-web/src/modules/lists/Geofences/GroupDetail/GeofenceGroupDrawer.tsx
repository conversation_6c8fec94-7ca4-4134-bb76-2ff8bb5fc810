import { useMemo } from 'react'
import {
  Box,
  Button,
  Chip,
  CircularProgressDelayedAbsolute,
  DataGrid,
  DataGridToolbar,
  getDataGridClientModeIncludedSelectedRowIds,
  GridToolbarQuickFilter,
  Typography,
  use<PERSON><PERSON>backBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { useForm, useWatch } from 'react-hook-form'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import type { FetchGeofencesListResolved } from '@fleet-web/api/geofences'
import type { GeofenceGroupId, GeofenceId } from '@fleet-web/api/types'
import { getGeofenceGroups, getLoading } from '@fleet-web/duxs/geofences'
import { getGeofences } from '@fleet-web/duxs/geofences-selector'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import EditableTextFieldControlled from '@fleet-web/shared/react-hook-form/EditableTextFieldControlled'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import IntlTypography from '@fleet-web/util-components/IntlTypography'

import {
  geofenceGroupFormSchema,
  MAX_LENGTH,
  type GeofenceGroupFormSchema,
} from '../types'
import { Body, BodySection, DetailsDrawer, Header } from './util'

type Props = {
  onClose: () => void
  onHandleSubmit: (
    data: GeofenceGroupFormSchema & { groupId?: GeofenceGroupId },
  ) => void
  id: GeofenceGroupId | undefined
}

type DataGridRow = FetchGeofencesListResolved['geofences'][number]

export default function GeofenceGroupDrawer(props: Props) {
  const isLoading = useTypedSelector(getLoading)

  return (
    <DetailsDrawer
      onClose={props.onClose}
      data-testid="GeofenceGroupDetailDrawer"
    >
      {isLoading ? (
        <CircularProgressDelayedAbsolute />
      ) : (
        <GeofenceGroupDrawerContent {...props} />
      )}
    </DetailsDrawer>
  )
}

function GeofenceGroupDrawerContent({ onClose, onHandleSubmit, id }: Props) {
  const geofenceList = useTypedSelector(getGeofences)
  const selectedGroup = useTypedSelector((state) =>
    id ? getGeofenceGroups(state).find((group) => group.id === id) : null,
  )
  const gridApiRef = useGridApiRef()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  const FormDefaultValues = useMemo(
    () => ({
      name: '',
      description: '',
      itemIds: geofenceList.filter((item) => item.isAllGroups).map((group) => group.id),
    }),
    [geofenceList],
  )

  // Form
  const {
    control,
    setValue: setFormValue,
    formState: { isValid },
    handleSubmit,
  } = useForm<GeofenceGroupFormSchema>({
    resolver: zodResolverV4(geofenceGroupFormSchema),
    mode: 'onChange',
    defaultValues: selectedGroup ? selectedGroup : FormDefaultValues,
  })

  const selectedRowIds = useWatch({ name: 'itemIds', control })
  const selectedRowIdsSet = useMemo(() => new Set(selectedRowIds), [selectedRowIds])

  const geofenceIdMaps: Record<GeofenceId, (typeof geofenceList)[number]> = useMemo(
    () =>
      geofenceList.reduce(
        (acc, cur) => ({
          ...acc,
          [cur.id]: cur,
        }),
        {},
      ),
    [geofenceList],
  )

  const onSubmit = handleSubmit((data: GeofenceGroupFormSchema) =>
    onHandleSubmit({ ...data, ...(id ? { groupId: id } : {}) }),
  )

  const columns = useMemo(
    () => [
      columnHelper.string((_, row) => row.name, {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Geofence Name' }),
        flex: 1,
        minWidth: 380,
      }),
    ],
    [columnHelper],
  )

  const handleSelectionModelChange = (selectionModal: GridRowSelectionModel) => {
    const selectedIds = getDataGridClientModeIncludedSelectedRowIds(
      selectionModal as GridRowSelectionModel<GeofenceId>,
      { gridApiRef },
    )
    setFormValue('itemIds', [...selectedIds], { shouldValidate: false })
  }

  const getRowHeight = useCallbackBranded(() => 'auto', [])

  return (
    <>
      <Header data-testid="GeofenceGroupDrawer-Header">
        {id ? (
          <EditableTextFieldControlled
            ControllerProps={{ name: 'name', control }}
            inputProps={{ maxLength: MAX_LENGTH }}
          />
        ) : (
          <IntlTypography
            variant="h5"
            msgProps={{ id: 'Add Geofence Group' }}
          />
        )}
      </Header>
      <Body>
        <BodySection data-testid="GeofenceGroupDrawer-NameDescription">
          <IntlTypography msgProps={{ id: 'Group Details' }} />
          {!id && (
            <TextFieldControlled
              ControllerProps={{ name: 'name', control }}
              label={ctIntl.formatMessage({ id: 'Name' })}
              required
              inputProps={{ maxLength: MAX_LENGTH }}
            />
          )}
          <TextFieldControlled
            ControllerProps={{ name: 'description', control }}
            label={ctIntl.formatMessage({ id: 'Description' })}
            inputProps={{ maxLength: MAX_LENGTH }}
          />
        </BodySection>
        <BodySection sx={{ height: '100%' }}>
          <Typography data-testid="GeofenceGroupDrawer-GenfencesTitle">
            {ctIntl.formatMessage({ id: 'Geofences' })} ({selectedRowIds.length})
          </Typography>
          {selectedRowIds.length > 0 && (
            <Box
              sx={{
                gap: 1,
                display: 'flex',
                flexWrap: 'wrap',
                maxHeight: '100px',
                overflowY: 'auto',
              }}
              data-testid="GeofenceGroupDrawer-SelectedGeofences"
            >
              {selectedRowIds.map((selected, index) => (
                <Chip
                  key={selected}
                  data-testid={`GeofenceGroupDrawer-SelectedGeofences-${index}`}
                  label={geofenceIdMaps[selected].name}
                  onDelete={
                    geofenceIdMaps[selected].isAllGroups
                      ? undefined
                      : () =>
                          setFormValue(
                            'itemIds',
                            selectedRowIds.filter((name) => name !== selected),
                            { shouldValidate: false },
                          )
                  }
                />
              ))}
            </Box>
          )}
          <UserDataGridWithSavedSettingsOnIDB
            data-testid="GeofenceGroupDrawer-GeofenceDataGrid"
            dataGridId="GroupAddGeofencDataGrid"
            Component={DataGrid}
            apiRef={gridApiRef}
            autoPageSize
            pagination
            getRowHeight={getRowHeight}
            disableRowSelectionOnClick
            rows={geofenceList}
            columns={columns}
            checkboxSelection
            rowSelectionModel={selectedRowIdsSet}
            onRowSelectionModelChange={handleSelectionModelChange}
            keepNonExistentRowsSelected
            isRowSelectable={(params) => !params.row.isAllGroups}
            slots={{ toolbar: CustomToolbar }}
          />
        </BodySection>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            data-testid="GeofenceGroupDrawer-CancelButton"
            variant="outlined"
            onClick={onClose}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            data-testid="GeofenceGroupDrawer-SaveButton"
            variant="contained"
            onClick={onSubmit}
            disabled={!isValid}
          >
            {ctIntl.formatMessage({ id: 'Save' })}
          </Button>
        </Box>
      </Body>{' '}
    </>
  )
}

function CustomToolbar() {
  return (
    <DataGridToolbar>
      <GridToolbarQuickFilter
        placeholder={ctIntl.formatMessage({ id: 'Search' })}
        debounceMs={100}
        sx={{
          padding: '10px 16px',
          '.MuiInput-root': {
            padding: '4px 6px',
            border: '1px solid rgb(0, 0, 0, 0.23)',
            borderRadius: '4px',
          },
        }}
      />
    </DataGridToolbar>
  )
}
