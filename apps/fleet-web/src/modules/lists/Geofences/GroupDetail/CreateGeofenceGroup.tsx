import { ctToast } from '@fleet-web/util-components'

import {
  useCreateGeofenceGroupMutation,
  useUpdateGeofenceGroupItemsMutation,
} from '../api/api'
import GeofenceGroupDrawer from './GeofenceGroupDrawer'

export default function CreateGeofenceGroup({ onClose }: { onClose: () => void }) {
  // NOTE: the reason we use two apis to create geofence group is the create one does not
  // accept the geofenceIds array, so we need to use the update one to do the job.
  const createGeofenceGroupMutation = useCreateGeofenceGroupMutation()
  const updateGeofenceGroupItemsMutation = useUpdateGeofenceGroupItemsMutation()

  return (
    <GeofenceGroupDrawer
      id={undefined}
      onClose={onClose}
      onHandleSubmit={(data) =>
        createGeofenceGroupMutation.mutate(
          { name: data.name, description: data.description },
          {
            onSuccess: (createdGeofenceGroupId) => {
              // after create the group, attach the geofences to the group
              updateGeofenceGroupItemsMutation.mutate(
                {
                  groupId: createdGeofenceGroupId,
                  selectedIds: data.itemIds,
                },
                {
                  onSuccess: () => {
                    ctToast.fire('success', '{name} was successfully created', {
                      formatMessageOptions: {
                        values: { name: data.name },
                      },
                    })
                    onClose()
                  },
                },
              )
            },
          },
        )
      }
    />
  )
}
