import { match } from 'ts-pattern'
import { z } from 'zod'

import type { GeofenceGroupId } from '@fleet-web/api/types'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { useValidatedSearchParams } from '@fleet-web/hooks/useValidatedSearchParams'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import CreateGeofenceGroup from './CreateGeofenceGroup'
import EditGeofenceGroup from './EditGeofenceGroup'

type Props = {
  onClose: () => void
}

export const geofenceGroupDetailSearchParamsSchema = z.discriminatedUnion('type', [
  z.object({ type: z.literal('add') }),
  z.object({ type: z.literal('edit'), id: z.string() }),
])

export function GeofenceGroupDetail({ onClose }: Props) {
  const { geofencesAddGroup } = useTypedSelector(getSettings_UNSAFE)
  const validatedParams = useValidatedSearchParams(
    () => geofenceGroupDetailSearchParamsSchema,
  )

  return (
    <>
      {match(validatedParams)
        .with({ status: 'invalid' }, () => null)
        .with({ status: 'valid', data: { type: 'add' } }, () =>
          geofencesAddGroup ? <CreateGeofenceGroup onClose={onClose} /> : null,
        )
        .with({ status: 'valid', data: { type: 'edit' } }, ({ data: { id } }) => (
          <EditGeofenceGroup
            onClose={onClose}
            id={id as GeofenceGroupId}
          />
        ))
        .exhaustive()}
    </>
  )
}
