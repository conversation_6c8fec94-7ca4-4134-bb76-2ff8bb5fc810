import { useEffect, useState } from 'react'

import type { GeofenceGroupId } from '@fleet-web/api/types'
import { getGeofenceGroups, getLoading } from '@fleet-web/duxs/geofences'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctToast } from '@fleet-web/util-components'

import {
  useUpdateGeofenceGroupItemsMutation,
  useUpdateGeofenceGroupMutation,
} from '../api/api'
import GeofenceGroupDrawer from './GeofenceGroupDrawer'

export default function EditGeofenceGroup(props: {
  onClose: () => void
  id: GeofenceGroupId
}) {
  const { id, onClose } = props
  // NOTE: the reason we use two apis to create geofence group is the create one does not
  // accept the geofenceIds array, so we need to use the update one to do the job.
  const updateGeofenceGroupMutation = useUpdateGeofenceGroupMutation()
  const updateGeofenceGroupItemsMutation = useUpdateGeofenceGroupItemsMutation()

  const isLoading = useTypedSelector(getLoading)
  const geofenceGroups = useTypedSelector(getGeofenceGroups)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  // if the group does not exist, do not open the drawer
  useEffect(() => {
    if (!isLoading) {
      const selectedGroup = geofenceGroups.find((group) => group.id === id)
      if (selectedGroup) {
        setIsDrawerOpen(true)
      } else {
        ctToast.fire('error', 'Geofence group does not exist', {
          formatMessageOptions: { values: { id } },
        })
        onClose()
      }
    }
  }, [geofenceGroups, id, isLoading, onClose])

  return isDrawerOpen ? (
    <GeofenceGroupDrawer
      {...props}
      onHandleSubmit={(data) =>
        updateGeofenceGroupMutation.mutate(
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          { name: data.name, id: data.groupId!, description: data.description },
          {
            onSuccess: () => {
              updateGeofenceGroupItemsMutation.mutate(
                {
                  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                  groupId: data.groupId!,
                  selectedIds: data.itemIds,
                },
                {
                  onSuccess: () => {
                    ctToast.fire('success', '{name} was successfully updated', {
                      formatMessageOptions: {
                        values: { name: data.name },
                      },
                    })
                    props.onClose()
                  },
                },
              )
            },
          },
        )
      }
    />
  ) : null
}
