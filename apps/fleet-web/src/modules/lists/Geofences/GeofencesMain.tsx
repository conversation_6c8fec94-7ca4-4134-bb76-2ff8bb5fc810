import { useEffect } from 'react'
import { Button, ContainerWithTabsForDataGrid } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import UploadIcon from '@mui/icons-material/Upload'
import type { Location } from 'history'
import { Link as RouterLink, useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

//TODO remove file
// import { geofenceDetailsSearchParamsSchema } from './GeofenceDetails'
import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import PageHeader from '@fleet-web/components/_containers/PageHeader'
import PageWithMainTableContainer from '@fleet-web/components/_containers/PageWithMainTable'
import { fetchListGeofencesAction } from '@fleet-web/duxs/geofences'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { useValidatedSearchParams } from '@fleet-web/hooks/useValidatedSearchParams'
import { LIST } from '@fleet-web/modules/app/components/routes/list'
import { getImporterDrawerMainPath } from '@fleet-web/modules/app/GlobalDrawers/Importer'
import { getGeofenceDetailsModalMainPath } from '@fleet-web/modules/map-view/FleetMapView/Geofence/utils'
import { useAppDispatch, useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import GeofenceGroupTable from './GeofenceGroupTable'
import GeofenceList from './GeofenceList'
import { GeofencesActivityTable } from './GeofencesActivityTable'
import { geofenceGroupDetailSearchParamsSchema } from './GroupDetail/GeofenceGroupDetail'
import {
  geofencesSearchParamsSchema,
  geofencesTableTabs,
  type GeofencesSearchParams,
  type GeofenceTableTab,
} from './types'

const makeGridsContainerTabs = (): Array<{ label: string; value: GeofenceTableTab }> =>
  geofencesTableTabs.map((tab) => {
    switch (tab) {
      case 'activity': {
        return { label: ctIntl.formatMessage({ id: 'Activity' }), value: 'activity' }
      }
      case 'group': {
        return { label: ctIntl.formatMessage({ id: 'Group' }), value: 'group' }
      }
      case 'list': {
        return { label: ctIntl.formatMessage({ id: 'List' }), value: 'list' }
      }
    }
  })

export function GeofencesMain() {
  const dispatch = useAppDispatch()
  const history = useHistory()
  const validatedParams = useValidatedSearchParams(() => geofencesSearchParamsSchema)
  const { geofencesImportGeofences, geofencesAddGroup, geofencesAddGeofence } =
    useTypedSelector(getSettings_UNSAFE)

  const selectedGridContainerTab: GeofenceTableTab =
    validatedParams.status === 'valid' ? validatedParams.data.tab : 'list'

  const setSelectedGridContainerTab = (tab: GeofenceTableTab) => {
    const searchParams = new URLSearchParams(history.location.search)
    searchParams.set('tab' satisfies keyof GeofencesSearchParams, tab)

    history.replace({
      pathname: history.location.pathname,
      search: searchParams.toString(),
    })
  }

  useEffect(() => {
    dispatch(fetchListGeofencesAction())
  }, [dispatch])

  return (
    <PageWithMainTableContainer>
      <PageHeader>
        <PageHeader.Title>{ctIntl.formatMessage({ id: 'Geofences' })}</PageHeader.Title>
        <PageHeader.ButtonsContainer>
          <Button
            size="small"
            variant="outlined"
            color="secondary"
            component={RouterLink}
            to={getImporterDrawerMainPath(
              { pathname: window.location.pathname, state: {} } as Location,
              { tab: 'GEOFENCES' },
            )}
            startIcon={<UploadIcon />}
            disabled={!geofencesImportGeofences}
            data-testid="GeofencesHeaderButton-ImportGeofence"
          >
            {ctIntl.formatMessage({ id: 'Import Geofences' })}
          </Button>

          <Button
            size="small"
            data-testid="GeofencesHeaderButton-AddGeofenceGroup"
            variant="outlined"
            color="secondary"
            component={RouterLink}
            to={`${
              LIST.subMenusRoutes.GEOFENCES.subPaths.GROUP
            }?${buildRouteQueryStringKeepingExistingSearchParams({
              location: history.location,
              schema: geofenceGroupDetailSearchParamsSchema,
              searchParams: {
                type: 'add',
              },
            })}`}
            startIcon={<AddIcon />}
            disabled={!geofencesAddGroup}
          >
            {ctIntl.formatMessage({ id: 'Add Group' })}
          </Button>
          <Button
            size="small"
            data-testid="GeofencesHeaderButton-AddGeofence"
            variant="contained"
            component={RouterLink}
            to={getGeofenceDetailsModalMainPath(history.location, {
              geo: 'add',
              initialMapData: 'none',
            })}
            startIcon={<AddIcon />}
            disabled={!geofencesAddGeofence}
          >
            {ctIntl.formatMessage({ id: 'Add Geofence' })}
          </Button>
        </PageHeader.ButtonsContainer>
      </PageHeader>
      <ContainerWithTabsForDataGrid
        renderTabs={() => (
          <ContainerWithTabsForDataGrid.Tabs
            value={selectedGridContainerTab}
            onChange={(_e, newValue: GeofenceTableTab) =>
              setSelectedGridContainerTab(newValue)
            }
          >
            {makeGridsContainerTabs().map(({ label, value }) => (
              <ContainerWithTabsForDataGrid.Tab
                data-testid={`GeofencesMain-GridContainerTab-${value}`}
                key={value}
                label={label}
                value={value}
              />
            ))}
          </ContainerWithTabsForDataGrid.Tabs>
        )}
      >
        {match(selectedGridContainerTab)
          .with('list', () => <GeofenceList />)
          .with('group', () => <GeofenceGroupTable />)
          .with('activity', () => <GeofencesActivityTable />)
          .exhaustive()}
      </ContainerWithTabsForDataGrid>
    </PageWithMainTableContainer>
  )
}
