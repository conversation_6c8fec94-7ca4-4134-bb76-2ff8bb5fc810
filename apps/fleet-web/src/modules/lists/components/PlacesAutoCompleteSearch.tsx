import {
  Autocomplete,
  InputAdornment,
  ListItem,
  TextField,
  type AutocompleteChangeReason,
  type AutocompleteInputChangeReason,
  type KarooUiInternalTheme,
  type SxProps,
} from '@karoo-ui/core'
import SearchIcon from '@mui/icons-material/Search'
import * as R from 'remeda'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export type PlacesAutocompleteOption = { id: string; label: string }

export default function PlacesAutoCompleteSearch({
  options,
  inputValue,
  onInputChange,
  onChange,
  disabled,
  sx = [],
}: {
  sx?: SxProps<KarooUiInternalTheme>
  options: Array<PlacesAutocompleteOption>
  inputValue: string
  onInputChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: string,
    reason: AutocompleteInputChangeReason,
  ) => void
  onChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: string | PlacesAutocompleteOption | null,
    reason: AutocompleteChangeReason,
  ) => void
  disabled: boolean
}) {
  return (
    <Autocomplete
      data-testid="GeofenceDetailModal-PlaceSearch"
      sx={[
        {
          '& .MuiInputBase-root': {
            pr: '8px !important',
          },
        },
        ...(R.isArray(sx) ? sx : [sx]),
      ]}
      onChange={onChange}
      options={options}
      renderInput={(params) => (
        <TextField
          {...params}
          label={ctIntl.formatMessage({ id: 'Search Location' })}
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <InputAdornment position="end">
                  <SearchIcon sx={{ color: 'rgba(0,0,0,0.54)' }} />
                </InputAdornment>
              ),
            },
          }}
        />
      )}
      onInputChange={onInputChange}
      inputValue={inputValue}
      renderOption={(params, option) => (
        <ListItem
          {...params}
          divider
          key={option.id}
        >
          {option.label}
        </ListItem>
      )}
      freeSolo
      disabled={disabled}
      noOptionsText={<></>}
    />
  )
}
