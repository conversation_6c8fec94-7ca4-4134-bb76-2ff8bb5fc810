import { useMemo, useState } from 'react'
import { debounce } from 'lodash'

import { hereFindPlaces, type HereApiParsedPlace } from '@fleet-web/api/here-places'
import {
  getHereMapsLanguageCodeFromUserLocale,
  getMapZoomOptions,
  getSettings_UNSAFE,
} from '@fleet-web/duxs/user'
import { createMapZoomConfig } from '@fleet-web/modules/shared/map-utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import type { GeofenceDetailsMapState } from '../Geofences/GeofenceDetails/types'
import type { POIDetailsMapState } from '../Landmarks/types'
import PlacesAutoCompleteSearch from './PlacesAutoCompleteSearch'

type Props = {
  setMapState: React.Dispatch<
    React.SetStateAction<GeofenceDetailsMapState | POIDetailsMapState>
  >
  mapCenter: {
    lat: number
    lng: number
  }
}

const DEBOUNCE_TIMEOUT = 500

const hereFindPlacesDebounce = debounce(hereFindPlaces, DEBOUNCE_TIMEOUT)

export default function OpenPlacesAutoCompleteSearch({
  setMapState,
  mapCenter,
}: Props) {
  const { minZoom, maxZoom } = useTypedSelector(getMapZoomOptions)
  const { hereApiCode, countryCodeAlpha3: ISOCountryCode } =
    useTypedSelector(getSettings_UNSAFE)
  const hereMapsLanguageCode = useTypedSelector(getHereMapsLanguageCodeFromUserLocale)

  const [searchValue, setSearchValue] = useState('')
  const [findPlacesResults, setFindPlacesResults] = useState<
    Array<HereApiParsedPlace & { key: string; id: string }>
  >([])

  const { mapMaxZoom } = useMemo(
    () => createMapZoomConfig({ minZoom, maxZoom }),
    [minZoom, maxZoom],
  )

  const handlePlacesInputDropdownInputChange = (value: string) => {
    hereFindPlacesDebounce({
      query: value,
      countryCode: ISOCountryCode,
      locale: hereMapsLanguageCode,
      successCB: handleFindPlacesResponse,
      errorCB: console.error,
      hereApiCode,
      mapCenter,
    })
  }

  const handleFindPlacesResponse = (places: Array<HereApiParsedPlace>) => {
    setFindPlacesResults(
      places.map((p) => ({
        ...p,
        key: p.id,
        value: p.id,
      })),
    )
  }

  const placesSearchOptions = useMemo(
    () =>
      findPlacesResults.map((prediction) => ({
        label: prediction.name,
        id: prediction.key,
      })),
    [findPlacesResults],
  )

  const handleAddressSelection = ({ address }: { address: string }) => {
    const result = findPlacesResults.find((p) => p.name === address)
    if (result) {
      const { lat, lng } = result
      setMapState((current) => ({
        ...current,
        center: { lat, lng },
        zoom: mapMaxZoom,
      }))
    }
  }

  return (
    <PlacesAutoCompleteSearch
      sx={{
        mt: 1,
      }}
      inputValue={searchValue}
      onInputChange={(_e, value, reason) => {
        if (reason === 'clear' || !value) {
          //nothing to do for now
        }
        setSearchValue(value)
        handlePlacesInputDropdownInputChange(value)
      }}
      onChange={(_e, newValue, reason) => {
        if (reason === 'clear') {
          return
        }
        if (newValue) {
          handleAddressSelection({
            address: typeof newValue === 'string' ? newValue : newValue.label,
          })
        }
      }}
      disabled={false}
      options={placesSearchOptions}
    />
  )
}
