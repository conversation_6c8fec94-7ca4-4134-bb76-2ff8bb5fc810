import { PureComponent } from 'react'
import { isNil, mapValues, reduce } from 'lodash'
import { push } from 'connected-react-router'
import type * as H from 'history'
import { injectIntl, type IntlShape } from 'react-intl'
import { connect } from 'react-redux'
import styled from 'styled-components'

import { getLocation } from '@fleet-web/duxs/global-location'
import { getSettings } from '@fleet-web/duxs/user'
import type { AppState } from '@fleet-web/root-reducer'
import { ActionButton } from '@fleet-web/shared/components/buttons/button'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { FixMeAny } from '@fleet-web/types'
import {
  AdvancedTable,
  Button,
  DatePicker,
  InputDropdown,
  Modal,
  SearchBar,
  Spinner,
  Stats,
} from '@fleet-web/util-components'
import { formatSimpleMessage } from '@fleet-web/util-functions/intl-utils'
import { searchMatchSorter } from '@fleet-web/util-functions/search-utils'

import GroupTiles from './groups-tiles'
import { ListContext } from './list-context'

type State = Record<string, any>

type Props = {
  intl: IntlShape
  history: H.History

  renderTable?: (props: { filteredItems: Array<any> }) => JSX.Element

  // Data
  items: Array<any>
  itemGroups?: any
  allCapsResourceName?: any
  columns: any
  resourceName: any
  resourceNamePlural?: any
  path?: any
  loading: any
  showTableWhenLoading?: any
  // Configuration
  addGroupPath?: any
  assignItemName?: any
  addItemPath?: any
  searchFilters?: any
  assignItemPath?: any
  advancedTableProps?: any
  assignItemOptions?: any
  className?: any
  headerClassName?: any
  importPath?: any
  importLabel?: any
  paginationFunc?: any
  getPaginationArgs?: any
  filterFunc?: any
  dropdownFilters?: any
  flagUserSettings?: any
  userSettings: any
  location: any
  disableAdd?: any
  disableFetchOnMount?: any
  maxDisplayedFilters?: any
  modal?: any
  datePickerPosition?: any
  datePickerProps?: any
  onRowClick?: any
  onAddItemClick?: any
  datePickerClassName?: any
  datePickerLabel?: any
  dateSearchProp?: any
  searchBarClassName?: any
  searchBarId?: any
  searchInputFocus?: any
  showAddItemButton?: any
  showStatsData?: any
  noSearch?: any
  noRange?: any
  renderInfo?: any
  renderExtraButtons?: any
  forceRenderExtraButtons?: any
  style?: any
  onFilterChangedCallback?: any
  onDateChange?: any
  onItemsChange?: any
  calculateFaultColumnWidth?: any
  handleSubRowFilter?: any
  clearExpanded?: any
  hasMultipleDelete?: any
  isMultipleDeleteDisabled?: any
  onDeleteMultiple?: any
  hasClearFilters?: any
  totalPages?: any
  hasClearDatePicker?: any
  manualSearchChange?: any
  // Hoc
  push: any
  disableDefaultStat?: any
  extraStats?: any
  // ACL
  enableAddGroup?: any
  enableImport?: any
}

class List extends PureComponent<Props, State> {
  static defaultProps = {
    itemGroups: [],
    allCapsResourceName: '',
    path: '',
    addGroupPath: '',
    addItemPath: '',
    assignItemName: '',
    assignItemPath: '',
    assignItemOptions: [],
    className: '',
    headerClassName: '',
    importPath: '',
    importLabel: '',
    dropdownFilters: [],
    flagUserSettings: null,
    paginationFunc: null,
    getPaginationArgs: null,
    filterFunc: null,
    disableAdd: false,
    maxDisplayedFilters: null,
    modal: null,
    datePickerClassName: 'col-xs-3',
    datePickerLabel: '',
    datePickerPosition: null,
    datePickerProps: null,
    advancedTableProps: {},
    onRowClick: null,
    onAddItemClick: null,
    dateSearchProp: null,
    noSearch: false,
    noRange: false,
    searchBarClassName: 'col-xs',
    searchInputFocus: false,
    showAddItemButton: true,
    showStatsData: true,
    renderInfo: null,
    renderExtraButtons: null,
    forceRenderExtraButtons: false,
    disableFetchOnMount: false,
    style: {},
    onFilterChangedCallback: null,
    onDateChange: null,
    onItemsChange: null,
    resourceNamePlural: '',
    calculateFaultColumnWidth: null,
    disableDefaultStat: false,
    extraStats: {},
    enableAddGroup: true,
    enableImport: true,
    handleSubRowFilter: null,
    clearExpanded: null,
    hasMultipleDelete: false,
    isMultipleDeleteDisabled: true,
    onDeleteMultiple: null,
    hasClearFilters: false,
    totalPages: null,
    showTableWhenLoading: false,
    hasClearDatePicker: false,
    manualSearchChange: () => {},
  }

  constructor(props: Props) {
    super(props)

    this.state = {
      tablePage: 0,
      searchInputState: '',
      dropdownFilterStates:
        (this.props.location.state && this.props.location.state.dropdownFilterStates) ||
        this.props.dropdownFilters.map(
          (d: FixMeAny) => d.options[d.defaultIndex || 0] || {},
        ),
      isModalOpen: false,
      modalChildProps: null,
      showMoreFilters: false,
    }
  }

  getFilteredItems = () => {
    const {
      state: { searchInputState: keyword, dropdownFilterStates },
      props: {
        handleSubRowFilter,
        items,
        onItemsChange,
        advancedTableProps: { manual },
        searchFilters,
      },
    } = this

    let filteredItems = items

    // sorting, filtering and pagination in server side
    if (manual) {
      return filteredItems
    }

    if (keyword || dropdownFilterStates.length > 0) {
      if (dropdownFilterStates.length > 0) {
        for (const { predicate } of this.state.dropdownFilterStates) {
          if (predicate !== true) {
            filteredItems = filteredItems.filter((item) => {
              if (typeof predicate === 'function') return predicate(item)
              for (const [k, v] of Object.entries(predicate)) {
                if (typeof v === 'string' && item[k] && item[k].includes(v)) return true
                if (item[k] !== v) return false
              }

              return true
            })
          }
        }
      }

      const lookupProps =
        searchFilters ??
        // @ts-expect-error deprecated file
        this.props.columns.map(({ accessor }) =>
          // eslint-disable-next-line no-nested-ternary
          typeof accessor === 'string'
            ? accessor || ''
            : typeof accessor === 'function'
              ? accessor
              : '',
        )

      filteredItems = searchMatchSorter(filteredItems, keyword, lookupProps)
    }

    if (handleSubRowFilter) {
      filteredItems = handleSubRowFilter(items, filteredItems, keyword)
    }

    if (typeof onItemsChange === 'function') {
      onItemsChange(filteredItems)
    }

    return handleSubRowFilter
      ? filteredItems.filter((x) => x.isDisplayedOnMainRow === true)
      : filteredItems
  }

  handleAssignDropdownChange = (item: FixMeAny) =>
    this.props.push({
      pathname: `${this.props.assignItemPath}/${item}`,
      state: {
        selectedItems: this.props.advancedTableProps.selectedIds,
      },
    })

  handleFilterChange = (value: FixMeAny, id: FixMeAny, category: FixMeAny) => {
    const idx = id.replace('ListDropdown', '')
    const dropdownFilterStates = this.props.dropdownFilters.map(
      (d: FixMeAny, i: FixMeAny) =>
        d.options.find((o: FixMeAny) =>
          o.value ? o.value === value : o.name === value,
        ) || this.state.dropdownFilterStates[i],
    )

    if (this.props.onFilterChangedCallback) {
      this.props.onFilterChangedCallback(
        dropdownFilterStates[0].predicate.status ?? dropdownFilterStates[0].tableName,
      )
    }

    if (this.props.filterFunc) {
      this.props.filterFunc(dropdownFilterStates)
    }

    if (this.props.clearExpanded) {
      this.props.clearExpanded(dropdownFilterStates)
    }

    this.setState(
      { dropdownFilterStates },
      // eslint-disable-next-line no-nested-ternary
      this.props.dropdownFilters[idx].noUpdate
        ? undefined
        : this.props.noRange
          ? (this.handlePagination as FixMeAny)
          : undefined,
    )

    GA4.event({
      category,
      action: 'Search Filter',
    })
  }

  handleModalClose = () => {
    this.setState({ isModalOpen: false })
  }

  handlePagination = (dateRangeOrDate: FixMeAny, origin = 'onChange') => {
    const { from, to } = this.props.noRange
      ? {
          from: dateRangeOrDate || this.state.from,
          to: dateRangeOrDate || this.state.to,
        }
      : dateRangeOrDate || this.state

    if (origin === 'onChange' && this.props.onDateChange) {
      this.props.onDateChange({ from, to })
    }

    if (this.props.clearExpanded) {
      this.props.clearExpanded()
    }

    this.setState(
      { from, to },
      () =>
        this.props.paginationFunc &&
        this.props.paginationFunc(
          ...(this.props.getPaginationArgs
            ? this.props.getPaginationArgs(from, to, this.state.dropdownFilterStates)
            : [from, to]),
        ),
    )
  }
  // @ts-expect-error deprecated file
  handleRowClick = (id, rowInfo, category) => {
    const { modal, onRowClick, path } = this.props

    GA4.event({
      category,
      action: 'Item Click',
    })

    if (modal) {
      this.setState({
        isModalOpen: true,
        modalChildProps: reduce(
          modal.childPropsAccessors,
          (acc, value, key) => {
            // @ts-expect-error deprecated file
            acc[key] = rowInfo.original[value]
            return acc
          },
          {},
        ),
      })
      return
    }

    if (!isNil(onRowClick)) {
      onRowClick(id, rowInfo)
      return
    }

    if (path !== '') {
      this.props.push({
        pathname: `${this.props.path}/${id}`,
        state: {
          ...this.props.location.state,
          history: this.props.path,
        },
      })
    }
  }

  handleSearchInputChange = (e: FixMeAny) => {
    const nextSearchInputState = e.target.value
    this.props.manualSearchChange(nextSearchInputState)
    this.setState({
      searchInputState: nextSearchInputState,
    })
  }

  handleMoreFiltersClick = () => {
    const { showMoreFilters } = this.state
    this.setState({
      showMoreFilters: !showMoreFilters,
    })
  }

  parseIntlFilterOptions = (filters: Array<any>) =>
    filters.map((filterValue, filterKey) =>
      // @ts-expect-error deprecated file
      filterKey === 'options'
        ? mapValues(filterValue, (v) =>
            this.props.intl.formatMessage({
              id: v,
              defaultMessage: v,
            }),
          )
        : filterValue,
    )

  composeRangePlaceholder = (label: FixMeAny) =>
    label ? { from: `${label} From`, to: `${label} To` } : { from: 'From', to: 'To' }

  renderDatePicker() {
    const {
      datePickerClassName,
      datePickerLabel,
      datePickerProps = {},
      disableFetchOnMount,
      noRange,
      hasClearDatePicker,
    } = this.props

    return (
      <>
        <DatePicker
          {...datePickerProps}
          placeholder={noRange ? 'Date' : this.composeRangePlaceholder(datePickerLabel)}
          value={
            // eslint-disable-next-line no-nested-ternary
            noRange
              ? this.state.to || datePickerProps.initialValue
              : hasClearDatePicker && this.state.from === null
                ? null
                : {
                    from:
                      this.state.from ||
                      (datePickerProps.initialValue &&
                        datePickerProps.initialValue.from),
                    to:
                      this.state.to ||
                      (datePickerProps.initialValue && datePickerProps.initialValue.to),
                  }
          }
          extraClassNames={{
            containerClassNames: datePickerClassName,
          }}
          toMonth={null}
          isRange={!noRange}
          onMount={
            disableFetchOnMount
              ? undefined
              : (value: FixMeAny) => this.handlePagination(value, 'onMount')
          }
          onChange={(value: FixMeAny) => this.handlePagination(value)}
        />
        {hasClearDatePicker && this.state.from !== null && (
          <Button
            square
            icon={['fas', 'eraser']}
            onClick={() => this.handlePagination({ from: null, to: null })}
          />
        )}
      </>
    )
  }

  handleFlagItem = (flag: FixMeAny) => {
    const { userSettings, flagUserSettings } = this.props

    if (!flag || !flagUserSettings) return true

    const flagItem = userSettings[flagUserSettings[flag]]

    return isNil(flagItem) ? true : flagItem
  }

  handleMultipleDelete = () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    this.props.onDeleteMultiple && this.props.onDeleteMultiple()
  }

  shouldShowClearFilters = () => !!this.state.searchInputState

  clearFilters = () => {
    this.setState({
      searchInputState: '',
    })
  }

  // eslint-disable-next-line complexity
  render() {
    const {
      state: { searchInputState: keyword },
      props: {
        addGroupPath,
        addItemPath,
        advancedTableProps,
        assignItemName,
        assignItemOptions,
        className,
        columns,
        datePickerPosition,
        disableAdd,
        dropdownFilters,
        intl,
        itemGroups,
        headerClassName,
        importPath,
        importLabel,
        loading,
        showTableWhenLoading,
        maxDisplayedFilters,
        modal,
        noSearch,
        onAddItemClick,
        onRowClick,
        path,
        paginationFunc,
        resourceName,
        resourceNamePlural,
        renderExtraButtons,
        forceRenderExtraButtons,
        renderInfo,
        searchBarClassName,
        searchBarId,
        searchInputFocus,
        showAddItemButton,
        showStatsData,
        style,
        disableDefaultStat,
        extraStats,
        enableAddGroup,
        enableImport,
        hasMultipleDelete,
        isMultipleDeleteDisabled,
        renderTable,
        hasClearFilters,
      },
    } = this

    const filteredItemGroups = keyword
      ? searchMatchSorter(itemGroups, keyword)
      : itemGroups

    const filteredItems = this.getFilteredItems()

    const createEdit = this.handleFlagItem('addItem')

    const shouldRenderExtraButtons = this.handleFlagItem('renderExtraButtons')

    const resourceNameWithPlural =
      resourceNamePlural ||
      // eslint-disable-next-line no-nested-ternary
      (resourceName.endsWith('s')
        ? resourceName
        : resourceName
          ? `${resourceName}s`
          : '')

    const statsData = disableDefaultStat
      ? []
      : [
          {
            key: intl.formatMessage({
              id: `Total ${resourceNameWithPlural}`,
            }),
            value: filteredItems.length,
          },
        ]

    if (extraStats) {
      for (const key of Object.keys(extraStats)) {
        statsData.push({
          key: intl.formatMessage({ id: key }),
          value: extraStats[key],
        })
      }
    }

    if (addGroupPath) {
      statsData.push({
        key: intl.formatMessage({
          id: 'Total Groups',
          defaultMessage: 'Total Groups',
        }),
        value: filteredItemGroups.length,
      })
    }

    let basicFilters = []
    let moreFilters = []
    if (maxDisplayedFilters !== null && dropdownFilters.length > maxDisplayedFilters) {
      basicFilters = dropdownFilters.slice(0, maxDisplayedFilters)
      moreFilters = dropdownFilters.slice(maxDisplayedFilters)
    } else {
      basicFilters = dropdownFilters
    }

    const parseIntlBasicFilters = this.parseIntlFilterOptions(basicFilters)
    const parseIntlMoreFilters = this.parseIntlFilterOptions(moreFilters)

    return (
      <ListContext.Consumer>
        {({ category }) => (
          <div className={`List ${className}`}>
            <div className={`row ${headerClassName}`}>
              {noSearch ? null : (
                <SearchBar
                  id={searchBarId}
                  className={searchBarClassName}
                  focus={searchInputFocus}
                  forceOriginalValue
                  onClick={() => {
                    GA4.event({
                      category,
                      action: 'Search Click',
                    })
                  }}
                  onChange={this.handleSearchInputChange}
                  value={this.state.searchInputState}
                />
              )}

              {parseIntlBasicFilters.map((d, i) => {
                const { value: selectedValue, name } = this.state.dropdownFilterStates[
                  i
                ] || {
                  value: undefined,
                  name: undefined,
                }
                return (
                  <InputDropdown
                    key={d.name}
                    placeholder={d.name}
                    extraClassNames={{
                      containerClassNames: d.className || 'col-xs-2',
                    }}
                    onChange={(value: FixMeAny, id: FixMeAny) =>
                      this.handleFilterChange(value, id, category)
                    }
                    activeOption={selectedValue || name}
                    options={d.options}
                    id={'ListDropdown' + i}
                    useValueAsKey
                    defaultSelectedIndex={0}
                    disableInput={d.disableInput === undefined ? true : d.disableInput}
                  />
                )
              })}
              {paginationFunc &&
                datePickerPosition === 'basic-filters' &&
                this.renderDatePicker()}

              {hasClearFilters && this.shouldShowClearFilters() && (
                <Button
                  square
                  icon={['fas', 'eraser']}
                  onClick={this.clearFilters}
                />
              )}
              {parseIntlMoreFilters.length > 0 && (
                <Button
                  label={this.state.showMoreFilters ? 'Less Filters' : 'More Filters'}
                  icon={this.state.showMoreFilters ? 'caret-up' : 'caret-down'}
                  onClick={this.handleMoreFiltersClick}
                />
              )}
              {paginationFunc && !datePickerPosition && this.renderDatePicker()}
              {showStatsData && (
                <Stats
                  className="col-xs"
                  data={statsData}
                />
              )}
              {hasMultipleDelete && (
                <Button
                  red
                  grouped
                  icon="trash-alt"
                  label="Delete Selected"
                  disabled={isMultipleDeleteDisabled}
                  onClick={this.handleMultipleDelete}
                />
              )}
              {importPath && (
                <Button
                  grouped
                  icon="upload"
                  label={importLabel ? importLabel : `Import ${resourceName}`}
                  to={importPath}
                  onClick={() => {
                    GA4.event({
                      category,
                      action: 'Import Click',
                    })
                  }}
                  disabled={!enableImport}
                />
              )}
              {renderExtraButtons &&
                (shouldRenderExtraButtons || forceRenderExtraButtons) &&
                renderExtraButtons()}
              {addGroupPath && (
                <StyledRightButton
                  className="Button Button--grouped Button--action"
                  eventCategory={category}
                  eventAction="Add Group"
                  icon="plus"
                  label={formatSimpleMessage('Add Group')}
                  onClick={() => {
                    this.props.history.push(addGroupPath)
                  }}
                  disabled={!enableAddGroup}
                />
              )}
              {(showAddItemButton || (addItemPath && createEdit)) && (
                <Button
                  grouped
                  label={`Add ${resourceName}`}
                  icon="plus"
                  action
                  to={addItemPath}
                  disabled={disableAdd}
                  onClick={(e) => {
                    GA4.event({
                      category,
                      action: 'Add New',
                    })
                    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                    onAddItemClick && onAddItemClick(e)
                  }}
                />
              )}
              {assignItemName && (
                <InputDropdown
                  placeholder={`Assign ${assignItemName}`}
                  iconName="plus"
                  className="List-addItemButton"
                  options={assignItemOptions}
                  onChange={this.handleAssignDropdownChange}
                  disabled={Object.keys(advancedTableProps.selectedIds).length === 0}
                />
              )}
            </div>
            {parseIntlMoreFilters.length > 0 && this.state.showMoreFilters && (
              <div className="row List-more-filters">
                {paginationFunc &&
                  datePickerPosition === 'more-filters' &&
                  this.renderDatePicker()}
                {parseIntlMoreFilters.map((d, i) => (
                  <InputDropdown
                    key={d.name}
                    placeholder={d.name}
                    extraClassNames={{
                      containerClassNames: d.className || 'col-xs-2',
                    }}
                    onChange={this.handleFilterChange}
                    activeOption={
                      this.state.dropdownFilterStates[i + maxDisplayedFilters].value ||
                      this.state.dropdownFilterStates[i + maxDisplayedFilters].name
                    }
                    options={d.options}
                    id={'ListDropdown' + (i + maxDisplayedFilters)}
                    useValueAsKey
                    defaultSelectedIndex={0}
                    disableInput={d.disableInput === undefined ? true : d.disableInput}
                  />
                ))}
              </div>
            )}
            {addGroupPath && filteredItemGroups.length > 0 ? (
              <GroupTiles groups={filteredItemGroups} />
            ) : null}
            {renderInfo && renderInfo(filteredItems, this.props, this.state)}
            <div
              className={`row List-table List-table--${resourceName.toLowerCase()} is-paginatable`}
            >
              {/* eslint-disable-next-line no-nested-ternary */}
              {loading && !showTableWhenLoading ? (
                <Spinner />
              ) : renderTable ? (
                renderTable({ filteredItems })
              ) : (
                <AdvancedTable
                  style={style}
                  data={filteredItems}
                  columns={columns}
                  onRowClick={
                    createEdit && (modal || onRowClick || path)
                      ? (id: FixMeAny, rowInfo: FixMeAny) =>
                          this.handleRowClick(id, rowInfo, category)
                      : null
                  }
                  loading={loading}
                  calculateFaultColumnWidth={this.props.calculateFaultColumnWidth}
                  onPageChange={(page: FixMeAny) => {
                    if (page > this.state.tablePage) {
                      GA4.event({
                        category,
                        action: 'Button Next',
                      })
                    } else {
                      GA4.event({
                        category,
                        action: 'Button Previous',
                      })
                    }
                    this.setState({ tablePage: page })
                  }}
                  {...advancedTableProps}
                />
              )}
            </div>
            {modal && (
              <Modal
                {...modal.props}
                isOpen={this.state.isModalOpen}
                onClose={this.handleModalClose}
              >
                {this.state.isModalOpen && (
                  <modal.child {...this.state.modalChildProps} />
                )}
              </Modal>
            )}
          </div>
        )}
      </ListContext.Consumer>
    )
  }
}

function mapStateToProps(state: AppState) {
  return {
    userSettings: getSettings(state),
    location: getLocation(state),
  }
}

const mapDispatchToProps = {
  push,
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(List as FixMeAny)) as FixMeAny

const StyledRightButton = styled(ActionButton)`
  padding: 10px 15px;
`
