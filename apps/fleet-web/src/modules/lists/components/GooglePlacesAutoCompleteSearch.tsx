import { useMemo } from 'react'

import {
  getGoogleMapsLanguageCodeFromUserLocale,
  getMapZoomOptions,
} from '@fleet-web/duxs/user'
import {
  getGoogleLatLngFromGeocode,
  getGoogleMapsCachedGeocodeFirstResult,
  useUserGoogleLazyPlacesAutocomplete,
} from '@fleet-web/hooks/useUserGooglePlacesAutocomplete'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import type { GeofenceDetailsMapState } from '../Geofences/GeofenceDetails/types'
import type { POIDetailsMapState } from '../Landmarks/types'
import PlacesAutoCompleteSearch, {
  type PlacesAutocompleteOption,
} from './PlacesAutoCompleteSearch'

type Props = {
  setMapState: React.Dispatch<
    React.SetStateAction<GeofenceDetailsMapState | POIDetailsMapState>
  >
}

export default function GooglePlacesAutoCompleteSearch({ setMapState }: Props) {
  const {
    ready,
    value: searchValue,
    suggestions,
    setValue: setSearchValue,
  } = useUserGoogleLazyPlacesAutocomplete({
    debounce: 500,
    defaultValue: '',
  })

  const { maxZoom } = useTypedSelector(getMapZoomOptions)
  const googleMapsLanguageCode = useTypedSelector(
    getGoogleMapsLanguageCodeFromUserLocale,
  )

  const handleAddressSelection = async ({ address }: { address: string }) => {
    const result = await getGoogleMapsCachedGeocodeFirstResult({
      address,
      language: googleMapsLanguageCode,
    })
    const { lat, lng } = getGoogleLatLngFromGeocode(result)
    setMapState((current) => ({
      ...current,
      center: { lat, lng },
      zoom: maxZoom,
    }))
  }

  const placesSearchOptions = useMemo(
    () =>
      suggestions.data.map(
        (prediction): PlacesAutocompleteOption => ({
          label: prediction.description,
          id: prediction.place_id,
        }),
      ),
    [suggestions.data],
  )

  return (
    <PlacesAutoCompleteSearch
      sx={{ mt: 1 }}
      inputValue={searchValue}
      onInputChange={(_e, value, reason) => {
        if (reason === 'clear' || !value) {
          //nothing to do for now
        }
        setSearchValue(value)
      }}
      onChange={(_e, newValue, reason) => {
        if (reason === 'clear') {
          return
        }
        if (newValue) {
          handleAddressSelection({
            address: typeof newValue === 'string' ? newValue : newValue.label,
          })
        }
      }}
      disabled={!ready}
      options={placesSearchOptions}
    />
  )
}
