import styled from 'styled-components'

import Icon from '@fleet-web/components/Icon'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const Container = styled.div`
  display: flex;
  cursor: pointer;
  height: 60px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 1px 2px 4px 0 rgba(0, 0, 0, 0.15);
`

const LeftBlock = styled(
  ({
    className,
    folderIcon,
    subClassNames = {},
  }: {
    className?: string
    subClassNames?: { icon?: string }
    folderIcon: 'open' | 'closed'
  }) => (
    <div className={className}>
      <Icon
        icon={folderIcon === 'closed' ? 'folder' : 'folder-open'}
        className={`icon ${subClassNames.icon || ''}`}
      />
    </div>
  ),
)`
  align-items: center;
  background-color: #eee;
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
  display: flex;
  justify-content: center;
  padding: 0 15px;
  width: 45px;
  text-align: center;
  user-select: none;

  & .icon {
    color: ${variables.gray60};
    font-size: 16px;
  }
`

const RightBlock = styled.div`
  background-color: white;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
  padding: 12px 10px;
  width: calc(100% - 45px);
`

const Info = styled(
  ({
    className,
    itemsCount,
    label,
  }: {
    className?: string
    itemsCount?: number
    label: string
  }) => (
    <div className={className}>
      {itemsCount !== undefined && (
        <span>
          {ctIntl.formatMessage({
            id: label,
          })}{' '}
          {itemsCount}
        </span>
      )}
    </div>
  ),
)`
  ${(props) => props.theme.typography.mixins.robotoMedium};

  color: ${variables.gray};
  font-size: 12px;
`

const Name = styled.h2`
  ${(props) => props.theme.typography.mixins.robotoCondensedMedium};

  color: #333;
  font-size: 16px;
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`

export const GroupTile = { Container, Name, LeftBlock, Info, RightBlock }
