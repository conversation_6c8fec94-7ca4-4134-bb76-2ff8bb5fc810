import { useEffect, useMemo, useState } from 'react'
import { isNil } from 'lodash'
import type { ChangeEventValue, Coords } from 'google-map-react'

import useTimelineEventsRawQuery, {
  type FetchTimelineEventsRawQuery,
} from '@fleet-web/api/timeline/useTimelineEventsRawQuery'
import useTimelineEventsUIQuery from '@fleet-web/api/timeline/useTimelineEventsUIQuery'
import type { VehicleId } from '@fleet-web/api/types'
import { MapApiProvider } from '@fleet-web/api/user/types'
import useVehicleDetailsQuery from '@fleet-web/api/vehicles/useVehicleDetailsQuery'
import { MapThemeContextProvider } from '@fleet-web/components/context/MapThemeContext'
import { getMapZoomOptions, getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { UserGoogleBaseMapWithChinaSupport } from '@fleet-web/modules/components/connected/UserGoogleBaseMap'
import PathComponents from '@fleet-web/modules/map-view/map/path-components/path-components'
import { getBoundsFrom } from '@fleet-web/modules/shared/map-utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import { Spinner } from '@fleet-web/util-components'
import VehicleMarker from '@fleet-web/util-components/map/google/layers/vehicle-marker'
import { MAP_DEFAULT_ZOOM } from '@fleet-web/util-functions/constants'
import { isValidGPSData } from '@fleet-web/util-functions/map-utils'

import type { TripMarkerData } from '../map/types'

type Props = {
  params: {
    vehicleId: VehicleId
    startTs: Date
    endTs: Date
  }
  mapTypeId: google.maps.MapTypeId
}

const VehicleTripMapPage = ({
  params: { vehicleId, startTs, endTs },
  mapTypeId,
}: Props) => {
  const { defaultMapLat, defaultMapLon, defaultMapZoom } =
    useTypedSelector(getSettings_UNSAFE)
  const { minZoom, maxZoom } = useTypedSelector(getMapZoomOptions)

  const [mapInstance, setMapInstance] = useState<{
    state: ChangeEventValue | null
    object: MapsExtended.MapObject | null
    zoom: number
    center: Coords
  }>({
    state: null,
    object: null,
    zoom: (defaultMapZoom as number) || MAP_DEFAULT_ZOOM,
    center: {
      lat: (defaultMapLat as number) || 0,
      lng: (defaultMapLon as number) || 0,
    },
  })

  const timelineEventsUIQuery = useTimelineEventsUIQuery({
    vehicleId,
    startTs,
    endTs,
  })

  const timelineEventsRawQuery = useTimelineEventsRawQuery(
    {
      vehicleId,
      startTs,
      endTs,
    },
    {
      enabled: timelineEventsUIQuery.data?.trips !== undefined,
    },
  )

  const vehicleDetailsQuery = useVehicleDetailsQuery({
    vehicleId,
  })

  useEffect(() => {
    if (mapInstance.state !== null && mapInstance.object !== null) {
      const coords: Array<{
        lat: number
        lng: number
      }> = []

      if (timelineEventsRawQuery.status === 'success') {
        for (const event of timelineEventsRawQuery.data.events)
          coords.push({
            lat: event.lat,
            lng: event.lng,
          })
      }

      if (timelineEventsUIQuery.status === 'success') {
        coords.push(timelineEventsUIQuery.data.lastPosition)
      }

      const { center, zoom } = getBoundsFrom({
        pathItems: coords,
        mapSize: mapInstance.state.size,
        mapObject: mapInstance.object,
      })

      setMapInstance((prevState) => ({
        ...prevState,
        center,
        zoom,
      }))
    }
  }, [
    mapInstance.state,
    mapInstance.object,
    timelineEventsRawQuery.data,
    timelineEventsRawQuery.status,
    timelineEventsUIQuery.data,
    timelineEventsUIQuery.status,
  ])

  const mapElements = useMemo(() => {
    if (
      mapInstance.object !== null &&
      timelineEventsUIQuery.status === 'success' &&
      vehicleDetailsQuery.status === 'success'
    ) {
      const vehicle = vehicleDetailsQuery.data?.fleetVehicle
      const lastPosition = timelineEventsUIQuery.data.lastPosition
      const timelineTrips: TripMarkerData = []
      let events: FetchTimelineEventsRawQuery.Return['events'] = []
      let statusClassName = 'ignition-off'

      if (timelineEventsRawQuery.status === 'success') {
        const lastEvent =
          timelineEventsRawQuery.data.events[
            timelineEventsRawQuery.data.events.length - 1
          ]

        if (lastEvent !== undefined) {
          statusClassName = lastEvent.statusClassName
        }

        if (timelineEventsUIQuery.data.trips !== undefined) {
          for (const trip of timelineEventsUIQuery.data.trips) {
            timelineTrips.push({
              ...trip,
              events: timelineEventsRawQuery.data.events.filter(
                (event) =>
                  isValidGPSData(event.lat, event.lng) &&
                  event.time >= trip.startTimeMS &&
                  event.time <= trip.endTimeMS,
              ),
              formattedTime: {
                start: '',
                end: '',
              },
              totals: [],
            })
          }

          events = timelineEventsRawQuery.data.events
        }
      }

      return [
        <PathComponents
          key="path-components"
          mapProps={{
            mapApiProviderId: MapApiProvider.GOOGLE,
            mapObject: mapInstance.object,
            mapTypeId,
          }}
          focusedVehicle={vehicleId}
          events={events}
          selectedTripEvents={[]}
          timelineTripsMarkerData={timelineTrips}
          currentLayerVisibility={{
            harshEvents: true,
            showTripLines: true,
            harshEventsSpeeding: true,
          }}
          zoom={mapInstance.zoom}
          onEventMarkerClick={undefined}
          useSVREvents={false}
        />,
        isNil(vehicle) ? null : (
          <VehicleMarker
            key="vehicleMarker"
            map={mapInstance.object.map}
            vehicle={vehicle}
            mapTypeId={mapTypeId}
            positionInWgs84={lastPosition}
            statusClassName={statusClassName}
            isFocused={false}
            isHovered={false}
            showLabel={false}
            zoom={mapInstance.zoom}
          />
        ),
      ]
    }

    return null
  }, [
    mapInstance.object,
    mapInstance.zoom,
    timelineEventsRawQuery.data,
    timelineEventsRawQuery.status,
    timelineEventsUIQuery.data,
    timelineEventsUIQuery.status,
    vehicleDetailsQuery.data,
    vehicleDetailsQuery.status,
    vehicleId,
    mapTypeId,
  ])

  const shouldDisplayLoader =
    timelineEventsRawQuery.status === 'pending' ||
    timelineEventsUIQuery.status === 'pending' ||
    vehicleDetailsQuery.status === 'pending'

  if (shouldDisplayLoader) {
    return <Spinner absolute />
  }

  return (
    <MapThemeContextProvider mapTypeId={mapTypeId}>
      <UserGoogleBaseMapWithChinaSupport
        mapTypeId={mapTypeId}
        zoom={mapInstance.zoom}
        center={mapInstance.center}
        onChange={(mapState) =>
          setMapInstance((prevState) => ({
            ...prevState,
            state: mapState,
          }))
        }
        onGoogleApiLoaded={(mapObject) =>
          setMapInstance((prevState) => ({
            ...prevState,
            object: mapObject,
          }))
        }
        options={{
          minZoom,
          maxZoom,
          disableDefaultUI: true,
        }}
        yesIWantToUseGoogleMapApiInternals
      >
        {mapElements}
      </UserGoogleBaseMapWithChinaSupport>
    </MapThemeContextProvider>
  )
}

export default VehicleTripMapPage
