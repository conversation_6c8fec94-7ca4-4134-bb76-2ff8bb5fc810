import {
  Box,
  Button,
  Stack,
  styled,
  Typography,
  type TypographyProps,
} from '@karoo-ui/core'
import SearchOffIcon from '@mui/icons-material/SearchOff'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export const NoResults = () => (
  <ListContainer>
    <SearchOffIcon sx={{ color: '#FF9800', fontSize: '28px' }} />
    <StyledSearchTitle>
      {ctIntl.formatMessage({ id: 'No results found' })}
    </StyledSearchTitle>
    <SearchMsg>
      {ctIntl.formatMessage({ id: 'You may want to try using different keywords' })}
    </SearchMsg>
  </ListContainer>
)

export const ListContainer = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    alignItems: 'center',
    gap: 1,
    p: 2,
  }),
)

const StyledSearchTitle = styled(Typography)({
  color: 'rgba(0, 0, 0, 0.87)',
  fontSize: '16px',
})

export const SearchMsg = styled(Typography)({
  color: 'rgba(0, 0, 0, 0.6)',
  fontSize: '12px',
})

export const GrayInfo = (props: TypographyProps) => (
  <Typography
    component="span"
    variant="subtitle2"
    sx={(theme) => ({
      color: theme.palette.secondary.light,
    })}
    {...props}
  />
)

export const ResetButton = styled(Button)(({ theme }) =>
  theme.unstable_sx({
    fontSize: '13px',
    px: 1,
    py: 0,
  }),
)

export const Panel = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    gap: 2,
    padding: 2,
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
  }),
)

export const Header = styled(Stack)({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
})

export const CircleIconWrap = styled(Stack, {
  shouldForwardProp: (prop) => prop !== 'disabled',
})<{ disabled?: boolean }>(({ theme, disabled }) =>
  theme.unstable_sx({
    pointerEvents: disabled ? 'none' : 'auto',
    alignItems: 'center',
    gap: 1,
    '&:hover': {
      cursor: 'pointer',
      '.places-icon': {
        background: '#F47735',
      },
      '.places-svg': {
        color: '#fff',
      },
    },
  }),
)
export const CircleIcon = styled(Box)({
  width: '48px',
  height: '48px',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  borderRadius: '100px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
})

export const CircleIconLabel = styled(Typography)({
  color: 'rgba(0, 0, 0, 0.60)',
  textAlign: 'center',
})

export const getCheckCount = <K extends string>(filters: Record<K, boolean>): number =>
  (Object.values(filters) as Array<boolean>).reduce(
    (count, value) => count + (value ? 1 : 0),
    0,
  )

export const filterCountValue = (filterCount: number) =>
  filterCount > 0 ? ` (${filterCount})` : ''
