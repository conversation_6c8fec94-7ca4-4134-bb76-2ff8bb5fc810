import {
  UserGoogleBaseMapWithChinaSupport,
  type UserGoogleBaseMapWithChinaSupportProps,
} from '@fleet-web/modules/components/connected/UserGoogleBaseMap'

type Props = UserGoogleBaseMapWithChinaSupportProps
function BaseMap({ bootstrapURLKeys, ...props }: Props) {
  return (
    <UserGoogleBaseMapWithChinaSupport
      bootstrapURLKeys={{
        libraries: ['places'],
        ...bootstrapURLKeys,
      }}
      yesIWantToUseGoogleMapApiInternals
      resetBoundsOnResize
      {...props}
    />
  )
}

export default BaseMap
