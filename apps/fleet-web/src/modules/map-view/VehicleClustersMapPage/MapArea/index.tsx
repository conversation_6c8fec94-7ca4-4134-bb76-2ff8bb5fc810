import { forwardRef, useMemo } from 'react'
import { throttle } from 'lodash'
import { Box } from '@karoo-ui/core'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import OpenInNewIcon from '@mui/icons-material/OpenInNew'
// import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
// import FollowUpIconButton from '@fleet-web/components/_map/_controls/FollowUpIconButton'
import type { ChangeEventValue, Coords } from 'google-map-react'
import type { Points } from 'points-cluster'
import screenfull from 'screenfull'

import ReVehicleMarkerInfo from '@fleet-web/components/_map/_markers/ReVehicle/Label'
import { MapThemeContextProvider } from '@fleet-web/components/context/MapThemeContext'
import { getDefaultLayerVisibility } from '@fleet-web/duxs/map-timeline'
import {
  getIsSomeMapVehicleLabelAvailable,
  getMapVehicleLabels,
  getMapZoomOptions,
} from '@fleet-web/duxs/user'
import { getVehicles } from '@fleet-web/duxs/vehicles'
import type { EventHandlerBranded } from '@fleet-web/hooks/useEventHandler'
import { useUserFormattedClock } from '@fleet-web/modules/components/connected/UserFormattedClock'
import { useUserFormatLengthInKmOrMiles } from '@fleet-web/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import { VehicleCluster } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { VehicleMarkerBase } from '@fleet-web/util-components/map/google/layers/vehicle-marker-base'
import MapPositionControls from '@fleet-web/util-components/map/shared/group-buttons/map-position-controls'
import MapTypeControls from '@fleet-web/util-components/map/shared/group-buttons/map-type-controls'
import {
  MapButtonGroup,
  MapControlsWrap,
  MapIconButton,
  MapTopControlsWrap,
} from '@fleet-web/util-components/map/shared/group-buttons/utils'

import type { NormalizedVehiclePoint } from '../../VehicleCluster/types'
import type { MapReadonlyInstance } from '../types'
import BaseMap from './BaseMap'
import useVehiclesMapData from './useVehiclesMapData'

type Props = {
  mapState: {
    zoom: number
    center: Coords
    mapTypeId: google.maps.MapTypeId
    isFollowingVehicles: boolean
  }
  mapReadonlyInstance: MapReadonlyInstance
  height: number | string
  vehicles: Array<NormalizedVehiclePoint>

  clickedMapTypeButton: (mapTypeId: google.maps.MapTypeId) => void
  clickedZoomOutButton: () => void
  clickedZoomInButton: () => void
  clickedFollowUpButton: () => void
  onDragEnd: () => void
  throttledOnWheel: EventHandlerBranded<[]>
  onMapGoogleApiLoaded: (map: MapsExtended.MapObject) => void
  onMapChange: (mapState: ChangeEventValue) => void
  clickedVehicleCluster: (info: { points: Points }) => void
  viewFullScreenNewTab: () => void
  viewFullScreen: () => void
}

const MapArea = forwardRef<HTMLDivElement, Props>(
  (
    {
      height,
      vehicles,
      mapState,
      mapReadonlyInstance,
      clickedMapTypeButton,
      clickedZoomOutButton,
      clickedZoomInButton,
      onMapGoogleApiLoaded,
      onMapChange,
      clickedVehicleCluster,
      clickedFollowUpButton,
      onDragEnd,
      throttledOnWheel: propThrottledOnWheel,
      viewFullScreenNewTab,
      viewFullScreen,
    }: Props,
    ref,
  ) => {
    const vehiclesMapData = useVehiclesMapData({
      vehicles,
      mapReadonlyInstanceState: mapReadonlyInstance.state,
    })

    const { minZoom, maxZoom } = useTypedSelector(getMapZoomOptions)
    const isSomeMapVehicleLabelAvailable = useTypedSelector(
      getIsSomeMapVehicleLabelAvailable,
    )
    const mapVehicleLabels = useTypedSelector(getMapVehicleLabels)
    const { livePositionLabels } = useTypedSelector(getDefaultLayerVisibility)
    const mapVehicles = useTypedSelector(getVehicles)
    const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()
    const { formatClock } = useUserFormattedClock()

    const handleMapClusterClick = (
      event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    ) => {
      const cluster = vehiclesMapData?.clusters.find(
        (c) => c.id === event.currentTarget.id,
      )
      clickedVehicleCluster({ points: cluster?.vehiclePoints ?? [] })
    }

    const throttledOnWheel = useMemo(
      () =>
        throttle(() => {
          propThrottledOnWheel()
        }, 1000),
      [propThrottledOnWheel],
    )

    const vehicleDetailsLookup = useMemo(() => {
      const vehicleIdsLookup = Object.fromEntries(vehicles.map((v) => [v.id, true]))
      return Object.fromEntries(
        mapVehicles.filter((v) => vehicleIdsLookup[v.id]).map((v) => [v.id, v]),
      )
    }, [mapVehicles, vehicles])

    const renderVehicleClusters = () => {
      if (mapReadonlyInstance.object !== null && vehiclesMapData?.clusters) {
        return vehiclesMapData.clusters.map((c) => {
          if (!mapReadonlyInstance.object) {
            return null
          }
          const mapObject = mapReadonlyInstance.object

          if (c.numPoints === 1) {
            const { vehicle } = c.vehiclePoints[0]

            const vehicleDetails = vehicleDetailsLookup[vehicle.id]

            return (
              <VehicleMarkerBase
                key={vehicle.id}
                map={mapObject.map}
                mapTypeId={mapState.mapTypeId}
                positionInWgs84={vehicle}
                statusClassName={vehicle.statusClassName}
                vehicleGpsFixType={vehicle.gpsFixType}
                vehicleId={vehicle.id}
                vehicleType={vehicle.type}
              >
                {({ renderBearing, renderMarkerContent }) => (
                  <>
                    {renderBearing({ bearing: vehicle.bearing })}
                    {renderMarkerContent({
                      children: (
                        <>
                          {livePositionLabels && isSomeMapVehicleLabelAvailable && (
                            <ReVehicleMarkerInfo
                              vehicleLabels={{
                                registration: mapVehicleLabels.registration
                                  ? { value: vehicle.registration }
                                  : false,
                                ...(vehicleDetails
                                  ? {
                                      name: mapVehicleLabels.name
                                        ? { value: vehicleDetails.name }
                                        : false,
                                      description: mapVehicleLabels.description
                                        ? { value: vehicleDetails.description }
                                        : false,
                                      description1: mapVehicleLabels.description1
                                        ? { value: vehicleDetails.description1 }
                                        : false,
                                      description2: mapVehicleLabels.description2
                                        ? { value: vehicleDetails.description2 }
                                        : false,
                                      driver: mapVehicleLabels.driver
                                        ? {
                                            defaultDriver: vehicleDetails.defaultDriver,
                                            driverName: vehicleDetails.driverName,
                                          }
                                        : false,
                                      location: mapVehicleLabels.location
                                        ? {
                                            gpsFixType: vehicle.gpsFixType,
                                            positionDescription:
                                              vehicleDetails.positionDescription,
                                          }
                                        : false,
                                      odometer: mapVehicleLabels.odometer
                                        ? {
                                            value: vehicleDetails.odometer,
                                            formatLengthInKmOrMiles,
                                          }
                                        : false,
                                      unitRawClock: mapVehicleLabels.unitRawClock
                                        ? {
                                            value: vehicleDetails.unitRawClock,
                                            formatClock,
                                          }
                                        : false,
                                    }
                                  : {}),
                              }}
                            />
                          )}
                        </>
                      ),
                    })}
                  </>
                )}
              </VehicleMarkerBase>
            )
          }

          return (
            <VehicleCluster
              key={c.id}
              id={c.id}
              map={mapObject.map}
              mapTypeId={mapState.mapTypeId}
              positionInWgs84={{
                lat: c.lat,
                lng: c.lng,
              }}
              useIconColor={false}
              numPoints={c.numPoints}
              points={c.vehiclePoints}
              onVehicleClusterClick={handleMapClusterClick}
            />
          )
        })
      }
      return null
    }

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2.5,
          overflow: 'auto', // allows for this area to overflow independently from left panel (this way left panel acts like a fixed container)
          position: 'relative',
          height,
          width: '100%',
        }}
        onWheel={throttledOnWheel}
        ref={ref}
      >
        <MapThemeContextProvider mapTypeId={mapState.mapTypeId}>
          <BaseMap
            mapTypeId={mapState.mapTypeId}
            zoom={mapState.zoom}
            center={mapState.center}
            onChange={(obj) => onMapChange(obj)}
            onGoogleApiLoaded={(obj) => onMapGoogleApiLoaded(obj)}
            options={{
              minZoom,
              maxZoom,
              disableDefaultUI: true,
            }}
            onDragEnd={onDragEnd}
          >
            {renderVehicleClusters()}
          </BaseMap>
        </MapThemeContextProvider>
        <MapTopControlsWrap>
          <MapButtonGroup orientation="horizontal">
            <MapIconButton
              active={screenfull && screenfull.isFullscreen}
              title={ctIntl.formatMessage({ id: 'Fullscreen View' })}
              onClick={() => viewFullScreen()}
            >
              <FullscreenIcon fontSize="small" />
            </MapIconButton>

            <MapIconButton
              active={false}
              title={ctIntl.formatMessage({ id: 'Fullscreen in a new tab' })}
              onClick={() => viewFullScreenNewTab()}
            >
              <OpenInNewIcon fontSize="small" />
            </MapIconButton>
          </MapButtonGroup>
        </MapTopControlsWrap>
        <MapControlsWrap sx={{ right: 10 }}>
          <MapTypeControls
            activeTypeId={mapState.mapTypeId}
            toggleMapType={(typeId) => clickedMapTypeButton(typeId)}
          />
          <MapPositionControls
            toggleActiveState={{ location: mapState.isFollowingVehicles }}
            toggleCurrentLocation={() => clickedFollowUpButton()}
            toggleZoomIn={() => clickedZoomInButton()}
            toggleZoomOut={() => clickedZoomOutButton()}
          />
        </MapControlsWrap>
      </Box>
    )
  },
)

export default MapArea
