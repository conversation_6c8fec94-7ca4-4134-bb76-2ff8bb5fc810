import { useMemo } from 'react'
import { isNil } from 'lodash'
import type { ChangeEventValue } from 'google-map-react'
import supercluster, { type Cluster } from 'points-cluster'

import {
  MAP_CLUSTER_MAX_ZOOM,
  MAP_CLUSTER_MIN_ZOOM,
  MAP_CLUSTER_RADIUS,
} from '@fleet-web/util-functions'

import type { NormalizedVehiclePoint } from '../../VehicleCluster/types'

const mapVehicleCluster = (c: Cluster<Array<NormalizedVehiclePoint>>) => ({
  id: `${c.numPoints}_${c.points[0].id}`,
  vehiclePoints: c.points.map((p) => ({
    lat: p.lat,
    lng: p.lng,
    vehicle: {
      id: p.id,
      bearing: p.bearing,
      lat: p.lat,
      lng: p.lng,
      statusClassName: p.statusClassName,
      gpsFixType: p.gpsFixType,
      type: p.type,
      registration: p.registration,
    },
  })),
  lat: c.wy,
  lng: c.wx,
  numPoints: c.numPoints,
})

export default function useVehiclesMapData({
  vehicles,
  mapReadonlyInstanceState,
}: {
  vehicles: Array<NormalizedVehiclePoint>
  mapReadonlyInstanceState: ChangeEventValue | null
}) {
  return useMemo(() => {
    if (isNil(mapReadonlyInstanceState)) {
      return null
    }

    const vehicleClusterGenerator = supercluster(vehicles, {
      minZoom: MAP_CLUSTER_MIN_ZOOM,
      maxZoom: MAP_CLUSTER_MAX_ZOOM,
      radius: MAP_CLUSTER_RADIUS,
    })

    const clusters = vehicleClusterGenerator({
      bounds: mapReadonlyInstanceState.bounds,
      zoom: mapReadonlyInstanceState.zoom,
    })

    return {
      clusters: clusters.map((c) => mapVehicleCluster(c)),
    }
  }, [mapReadonlyInstanceState, vehicles])
}
