import { createAction } from '@reduxjs/toolkit'
import type { <PERSON><PERSON><PERSON> } from 'google-map-react'
import type * as H from 'history'

import type { GeofenceId, LandmarkId, VehicleId } from '@fleet-web/api/types'
import type { VisionActiveVehicle } from '@fleet-web/duxs/live-vision'
import type {
  MapCarpoolVehicleFilterName,
  MapDriverFilter,
  MapState,
  MapVehicleFilterName,
} from '@fleet-web/duxs/map-types'
import type { TimelineResetState } from '@fleet-web/duxs/timeline'
import type { UserAvailableMapApiProvider } from '@fleet-web/duxs/user-sensitive-selectors'
import type { ResolvedPlaceSearchItem } from '@fleet-web/modules/map-view/types'

import type { UseVisionCheckUserCreditData } from '../vision/VisionDataUsage/api/ui-types'
import type { FocusedVehicle } from './FleetMapView/DetailsPanel/ui-types'
import type { FullscreenMapNavigationState } from './fullscreen-map/slice'
import type { MapFleetBottomPanelState } from './MapFleetProvider'
import type { VehicleClusterId } from './VehicleCluster/api/types'

export const prefix = 'mapView/'

export const clickedMapContextMenuWhatsNearby = createAction(
  prefix + 'clickedMapContextMenuWhatsNearby',
)

export const clickedMapContextMenuStreetView = createAction(
  prefix + 'clickedMapContextMenuStreetView',
)

export const clickedNearbyVehiclesPanelCloseButton = createAction(
  prefix + 'clickedNearbyVehiclesPanelCloseButton',
)

export const clickedLeftPanelPlace = createAction<ResolvedPlaceSearchItem>(
  prefix + 'clickedLeftPanelPlace',
)

export const clickedLeftPanelCompareTripsButton = createAction(
  prefix + 'clickedLeftPanelCompareTripsButton',
)

export const clickedLeftPanelCompareTripsBackButton = createAction(
  prefix + 'clickedLeftPanelCompareTripsBackButton',
)

export const clickedVehicleMarker = createAction<{
  vehicleId: VehicleId
}>(prefix + 'clickedVehicleMarker')

export const clickedLeftPanelVehicleOnlineHowenCameraIcon = createAction<{
  activeVehicle: VisionActiveVehicle
  visionCheckUserCreditData: UseVisionCheckUserCreditData
}>(prefix + 'clickedLeftPanelVehicleOnlineHowenCameraIcon')

export const leftPanel_itemPopover_vehicleStatusSection_onCameraIconClick =
  createAction<{
    activeVehicle: VisionActiveVehicle
    visionCheckUserCreditData: UseVisionCheckUserCreditData
  }>(prefix + 'leftPanel_itemPopover_vehicleStatusSection_onCameraIconClick')

export const refocusVehicle = createAction<TimelineResetState['timelineEventsRaw']>(
  prefix + 'refocusVehicle',
)

export const refocusVehicleTrip = createAction<TimelineResetState['timelineEventsRaw']>(
  prefix + 'refocusVehicleTrip',
)

export const setIsStreetViewVisible = createAction<boolean>(
  prefix + 'setIsStreetViewVisible',
)

export const clickedLeftPanelGeofence = createAction<{
  geofence: { id: GeofenceId; geometry: ReadonlyArray<Coords> }
}>(prefix + 'clickedLeftPanelGeofence')

export const clickedLeftPanelLandmarkViewOnMap = createAction<{
  landmark: {
    lat: number
    lng: number
    id: LandmarkId
  }
}>(prefix + 'clickedLeftPanelLandmarkViewOnMap')

export const onVehicleMarkerMouseEnter = createAction<{ vehicleId: VehicleId }>(
  'onVehicleMarkerMouseEnter',
)

export const onVehicleMarkerMouseLeave = createAction<{ vehicleId: VehicleId }>(
  'onVehicleMarkerMouseLeave',
)

export const onLeftPanelVehicleFilterChange = createAction<{
  filterName: MapVehicleFilterName
  filterChecked: boolean
}>(prefix + 'onLeftPanelVehicleFilterChange')

export const onLeftPanelDriverFilterChange = createAction<{
  filterName: MapDriverFilter
}>(prefix + 'onLeftPanelDriverFilterChange')

export const onLeftPanelResetVehicleFilterChange = createAction(
  prefix + 'onLeftPanelResetVehicleFilterChange',
)

export const onLeftPanelResetDriverFilterChange = createAction(
  prefix + 'onLeftPanelResetDriverFilterChange',
)

export const onLeftPanelResetAllVehicleFiltersButtonClick = createAction(
  prefix + 'onLeftPanelResetAllVehicleFiltersButtonClick',
)

export const onLeftPanelCarpoolVehicleFilterChange = createAction<{
  filterName: MapCarpoolVehicleFilterName
  filterChecked: boolean
}>(prefix + 'onLeftPanelCarpoolVehicleFilterChange')

export const onLeftPanelResetCarpoolVehicleFilter = createAction(
  prefix + 'onLeftPanelResetCarpoolVehicleFilter',
)

export const onLeftPanelSearchBoxClear = createAction(
  prefix + 'onLeftPanelSearchBoxClear',
)

export const onVehicleClusterChipClick = createAction<{
  clusterId: VehicleClusterId
  initialCenter: Coords
  initialZoom: number
}>(prefix + 'onVehicleClusterChipClick')

export const selectVehicleCluster = createAction<{
  clusterId: VehicleClusterId
  initialCenter: Coords
  initialZoom: number
}>(prefix + 'selectVehicleCluster')

export const setVehicleClusterPanelInitialOpenState = createAction<{
  clusterId: VehicleClusterId
  initialCenter: Coords
  initialZoom: number
}>(prefix + 'setVehicleClusterPanelInitialOpenState')

export const setVehicleClusterPanelClosedState = createAction(
  prefix + 'setVehicleClusterPanelClosedState',
)

export const onVehicleClusterAddSuccess = createAction<{
  clusterId: VehicleClusterId
  initialCenter: Coords
  initialZoom: number
}>(prefix + 'onVehicleClusterAddSuccess')

export const onVehicleClusterPanelCloseButtonClick = createAction(
  prefix + 'onVehicleClusterPanelCloseButtonClick',
)

export const clickedLiveVisionRightPanelCloseButton = createAction(
  prefix + 'clickedLiveVisionRightPanelCloseButton',
)

export const clickedClearPlaceSearchButton = createAction(
  prefix + 'clickedClearPlaceSearchButton',
)

export const onMapProviderUIClick = createAction<{
  clickedMapProvider: UserAvailableMapApiProvider
  currentMapProvider: UserAvailableMapApiProvider
  mapTypeId: google.maps.MapTypeId
}>(prefix + 'onMapProviderUIClick')

export const onGoogleMapTypeIdChange = createAction<{
  selectedMapTypeId: google.maps.MapTypeId
}>(prefix + 'onGoogleMapTypeIdChange')

/**
 * Should only be used in map pages
 */
export const focusVehicle = createAction<{
  vehicle: Pick<FocusedVehicle, 'latitude' | 'longitude' | 'id'>
  dateTimeRange:
    | {
        from: Date
        to: Date
      }
    | 'default'
  bottomPanelInitialSectionType?: MapFleetBottomPanelState['sectionType']
  tripId?: string
}>('FOCUS_VEHICLE')

export const clickedMapFullscreenButton = createAction<{
  fullscreenMapState: FullscreenMapNavigationState | undefined
  prePathname: string
  history: H.History
  fullscreenPath: string | undefined
}>('clickedMapFullscreenButton')

export const onMapTypeFromUrlChange = createAction<{
  mapTypeFromUrl: string
}>('onMapTypeFromUrlChange')

export const jumpToTime = createAction<{
  nextProgress: MapState['timelineProgress']
  nextActiveEventIndex: MapState['activeEventIndex']
  nextCoords: { lat: number; lng: number } | null
}>('jumpToTime')

export const zoomToEvents = createAction<{
  events: Array<Record<string, unknown>>
  maxZoom: number
}>(prefix + 'zoomToEvents')

export const setNonGoogleMapTypeId = createAction<google.maps.MapTypeId>(
  prefix + 'setNonGoogleMapTypeId',
)
