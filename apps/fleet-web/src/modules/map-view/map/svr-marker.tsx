import { useMemo } from 'react'

import {
  AdvancedMarkerHeadless,
  getAdvancedMarkerRenderedPosition,
} from '@fleet-web/components/_map/AdvancedMarkerHeadless'
import { getActiveTimelineEventByActivity } from '@fleet-web/duxs/timeline'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { FixMeAny } from '@fleet-web/types'
import { latLng2World, world2Screen } from '@fleet-web/util-functions'

function polarToCartesian(
  centerX: number,
  centerY: number,
  radius: number,
  angleInDegrees: number,
) {
  const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180

  return {
    x: centerX + radius * Math.cos(angleInRadians),
    y: centerY + radius * Math.sin(angleInRadians),
  }
}

function getArcPath(
  { x, y }: { x: number; y: number },
  radius: number,
  bearing: number,
  beamWidth: number,
) {
  const start = polarToCartesian(x, y, radius, bearing + beamWidth / 2)
  const end = polarToCartesian(x, y, radius, bearing - beamWidth / 2)

  const largeArcFlag = bearing + beamWidth - bearing <= 180 ? '0' : '1'

  return (
    `M ${radius} ${radius} ` +
    `L ${start.x} ${start.y} ` +
    `A ${radius} ${radius} 0 ${largeArcFlag} 0 ${end.x} ${end.y} z`
  )
}

function getLngOffsetDeg({ lat }: { lat: number }, m: number) {
  return (m / (6378137 * Math.cos((Math.PI * lat) / 180))) * (180 / Math.PI)
}

function resolveSvgPath({
  renderedPosition,
  bearing,
  radius: radiusMeters,
  width,
  zoom,
}: {
  renderedPosition: { lat: number; lng: number }
  bearing: number
  radius: number
  width: number
  zoom: number
}) {
  const center = world2Screen(latLng2World(renderedPosition), zoom)
  const offsetCenter = world2Screen(
    latLng2World({
      lat: renderedPosition.lat,
      lng:
        renderedPosition.lng +
        getLngOffsetDeg({ lat: renderedPosition.lat }, radiusMeters),
    }),
    zoom,
  )
  const radiusPx = offsetCenter.x - center.x
  return {
    points: getArcPath({ x: radiusPx, y: radiusPx }, radiusPx, bearing, width),
    radius: Math.max(radiusPx, 10),
  }
}

type ArcPathProps = {
  event: {
    id: string
    bearing: number
    radius?: number | null
    width?: number | null | undefined
  }
  zoom: number
  isFocused: boolean
  positionInWgs84: { lat: number; lng: number }
  mapTypeId: google.maps.MapTypeId
  map: google.maps.Map
}

function ArcPath({
  event,
  zoom,
  isFocused,
  positionInWgs84,
  mapTypeId,
  map,
}: ArcPathProps) {
  const { points, radius } = useMemo(
    () =>
      resolveSvgPath({
        renderedPosition: getAdvancedMarkerRenderedPosition({
          positionInWgs84,
          mapTypeId,
        }),
        bearing: event.bearing,
        radius: event.radius as FixMeAny,
        width: event.width as FixMeAny,
        zoom,
      }),
    [event, zoom, positionInWgs84, mapTypeId],
  )

  const style = {
    position: 'relative',
    left: -radius,
    top: -radius,
  } as const
  return (
    <AdvancedMarkerHeadless
      positionInWgs84={positionInWgs84}
      mapTypeId={mapTypeId}
      map={map}
    >
      <svg
        width={radius * 2}
        height={radius * 2}
        style={style}
      >
        <path
          d={points}
          className={`Arc-path ${isFocused ? 'is-focused' : ''}`}
        />
      </svg>
      <div className={`Arc-path-dot ${isFocused ? 'is-focused' : ''}`} />
    </AdvancedMarkerHeadless>
  )
}

type InternalSVRMarkerProps = {
  positionInWgs84: { lat: number; lng: number }
  event: ArcPathProps['event'] & {
    positionType?: string | null
  }
  zoom: number
  mapTypeId: google.maps.MapTypeId
  map: google.maps.Map
}

export const SVRMarker = ({
  event,
  zoom,
  positionInWgs84,
  mapTypeId,
  map,
}: InternalSVRMarkerProps) => {
  const activeTimelineEvent = useTypedSelector(getActiveTimelineEventByActivity)
  const activeEventId = activeTimelineEvent.data?.id
  const isEventFocused = activeEventId !== undefined && activeEventId === event.id

  return event.positionType === 'ARC' ? (
    <ArcPath
      event={event}
      positionInWgs84={positionInWgs84}
      zoom={zoom}
      isFocused={isEventFocused}
      map={map}
      mapTypeId={mapTypeId}
    />
  ) : (
    <SVRGPSMarker
      positionInWgs84={positionInWgs84}
      isFocused={isEventFocused}
      mapTypeId={mapTypeId}
      map={map}
    />
  )
}

export type SVRGPSMarkerProps = {
  positionInWgs84: { lat: number; lng: number }
  isFocused: boolean
  mapTypeId: google.maps.MapTypeId
  map: google.maps.Map
}
export const SVRGPSMarker = ({
  positionInWgs84,
  isFocused,
  mapTypeId,
  map,
}: SVRGPSMarkerProps) => (
  <AdvancedMarkerHeadless
    positionInWgs84={positionInWgs84}
    mapTypeId={mapTypeId}
    map={map}
  >
    <div className={`Arc-circle ${isFocused ? 'is-focused' : ''}`} />
  </AdvancedMarkerHeadless>
)
