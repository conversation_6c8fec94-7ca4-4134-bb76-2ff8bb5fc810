import type * as React from 'react'
import type { Coord<PERSON> } from 'google-map-react'
import { connect } from 'react-redux'
import { generatePath, type RouteComponentProps } from 'react-router-dom'
import * as R from 'remeda'

import { MapApiProvider } from '@fleet-web/api/user/types'
import { deleteGeofence } from '@fleet-web/duxs/geofences'
import {
  changeMapCenterZoom,
  getCenter,
  getZoom,
  setFocusedPoint,
} from '@fleet-web/duxs/map'
import { getCurrentLayerVisibility } from '@fleet-web/duxs/map-timeline'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
// import { buildRouteQueryString } from '@fleet-web/api/utils'
import {
  getGeofencesAddGeofenceSetting,
  getLandmarksAddPOISetting,
  type UserAvailableMapApiProvider,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { getFocusedVehicle } from '@fleet-web/duxs/vehicles'
import { LIST } from '@fleet-web/modules/app/components/routes/list'
import { clickedMapContextMenuWhatsNearby } from '@fleet-web/modules/map-view/actions'
import type { AppState } from '@fleet-web/root-reducer'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import BaseContextMenu from '@fleet-web/util-components/map/shared/context-menu'
import {
  contextMenuOptions,
  type ContextMenuOptions,
} from '@fleet-web/util-components/map/shared/context-menu-utils/data'
import { getWgs84ValueFromRenderedCoordsOnMap } from '@fleet-web/util-functions/china-map-utils'

import type { GeofenceId } from '../../../api/types'
import { getGeofenceDetailsModalMainPath } from '../FleetMapView/Geofence/utils'
import { getPOIDetailsModalMainPath } from '../FleetMapView/POI/utils'

type Position = {
  x: number
  y: number
  lat: number
  lng: number
}

type Props = {
  mapApiProviderId: MapApiProvider
  mapTypeId: google.maps.MapTypeId
  showDirections?: boolean
  contextMenuElement: {
    type: string
    id: string
    position: Position | null
  } | null
  mapRef: React.RefObject<HTMLDivElement>
  mapsApi: MapsExtended.MapObject['maps']
  position: Position
  onCloseContextMenu: () => void
  onMeasureDistance: (lat: number, lng: number) => void
  onShowGPSCoordinates: (position: Position) => void
  onDirectionsChange?: (
    direction: 'start' | 'end' | 'clean',
    position?: Position,
  ) => void
  history: RouteComponentProps['history']
  center: Coords
  zoom: number
} & ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps

function ContextMenu({
  mapApiProviderId,
  mapTypeId,
  showDirections = true,
  contextMenuElement,
  history,
  landmarksAddPOI,
  geofencesAddGeofence,
  geofencesDeleteGeofence,
  onCloseContextMenu,
  onDirectionsChange,
  onMeasureDistance,
  onShowGPSCoordinates,
  position,
  changeMapCenterZoom,
  mapRef,
  mapsApi,
  setFocusedPoint,
  clickedMapContextMenuWhatsNearby,
  deleteGeofence,
  currentLayerVisibility,
  zoom,
  center,
  focusedVehicle,
}: Props) {
  const mapAssets = {
    focusedVehicle,
    showGeofences: currentLayerVisibility.userGeofences,
    showVehicles: currentLayerVisibility.livePositions,
  }

  const mapState = { center, zoom }
  const getMenuElements = () => {
    const element =
      contextMenuElement && contextMenuElement.type
        ? contextMenuElement
        : { type: null }

    switch (element.type) {
      case 'geofence': {
        return {
          items: [
            {
              icon: 'info',
              message: 'More Info',
              handleOnClick: () => {
                history.push(
                  getGeofenceDetailsModalMainPath(history.location, {
                    geo: 'edit',
                    id: element.id as GeofenceId,
                  }),
                  { mapAssets, mapState },
                )
              },
            },
            {
              icon: 'bullseye',
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
            {
              icon: 'trash',
              message: 'Delete Geofence',
              handleOnClick: () => handleDeleteGeofence(element.id),
              disabled: !geofencesDeleteGeofence,
            },
          ],
        }
      }
      case 'landmark': {
        return {
          withStreetView: mapApiProviderId === MapApiProvider.GOOGLE,
          items: [
            {
              icon: 'info',
              message: 'More Info',
              handleOnClick: () => {
                history.push(
                  getPOIDetailsModalMainPath(history.location, {
                    poi: 'edit',
                    id: element.id as GeofenceId,
                  }),
                )
              },
            },
            {
              icon: 'bullseye',
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
          ],
        }
      }
      case 'vehicle': {
        return {
          withStreetView: mapApiProviderId === MapApiProvider.GOOGLE,
          items: [
            {
              icon: 'info',
              message: 'More Info',
              handleOnClick: () =>
                history.push({
                  pathname: generatePath(
                    LIST.subMenusRoutes.VEHICLES.subPaths.DETAILS,
                    {
                      vehicleId: element.id,
                    },
                  ),
                  state: {
                    history: history.location.pathname,
                  },
                }),
            },
            {
              icon: 'bullseye',
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
          ],
        }
      }
      default: {
        const items: ContextMenuOptions = (
          [
            ...(showDirections && onDirectionsChange
              ? [
                  {
                    ...contextMenuOptions.directions.start,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('start', position)
                    },
                  },
                  {
                    ...contextMenuOptions.directions.finish,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('end', position)
                    },
                  },
                  {
                    ...contextMenuOptions.directions.clean,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('clean', position)
                    },
                  },
                ]
              : []),
            landmarksAddPOI
              ? {
                  icon: 'map-marker-alt',
                  message: 'Add Landmark',
                  handleOnClick: () => handleCreateLandmark(),
                }
              : null,
            geofencesAddGeofence
              ? {
                  icon: 'object-ungroup',
                  message: 'Add Geofence',
                  handleOnClick: () => handleCreateGeofence(),
                }
              : null,
            {
              icon: 'bullseye',
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
            {
              ...contextMenuOptions.distanceMeasuring,
              handleOnClick: () => onMeasureDistance(position.lat, position.lng),
            },
            {
              ...contextMenuOptions.gpsCoordinates,
              handleOnClick: () => {
                onCloseContextMenu()
                onShowGPSCoordinates(position)
              },
            },
          ] satisfies Array<ContextMenuOptions[number] | null>
        ).filter((v) => R.isNonNullish(v))

        if (mapApiProviderId !== MapApiProvider.GOOGLE) {
          items.push(
            contextMenuOptions.getStreetViewInNewTab({
              latLng: position,
              onClick: () => onCloseContextMenu(),
            }),
          )
        }

        return { items }
      }
    }
  }

  const handleCreateGeofence = () => {
    setFocusedPoint(position)
    history.push(
      getGeofenceDetailsModalMainPath(history.location, {
        geo: 'add',
        initialMapData: 'none',
      }),
      {
        mapAssets,
        mapState,
      },
    )
  }

  const handleCreateLandmark = () => {
    setFocusedPoint(position)

    const coordsInWgs84 = getWgs84ValueFromRenderedCoordsOnMap({
      renderedCoordsInWgsOrGcj: position,
      mapApiProviderId,
      mapTypeId,
    })

    history.push(
      getPOIDetailsModalMainPath(history.location, {
        poi: 'add',
        initialMapData: {
          zoom: zoom,
          coordsInWgs84,
          mapApiProvider: mapApiProviderId as UserAvailableMapApiProvider,
          mapTypeId: mapTypeId,
        },
      }),
    )
  }

  const handleNearby = () => {
    const { lat, lng } = position
    onCloseContextMenu()
    setFocusedPoint(position)
    clickedMapContextMenuWhatsNearby()
    changeMapCenterZoom(lat, lng, undefined, 'handleNearby')
  }

  const handleDeleteGeofence = (id: string) => {
    deleteGeofence(id as GeofenceId, null, 'map')
    onCloseContextMenu()
  }

  return (
    <BaseContextMenu
      menuElements={getMenuElements()}
      {...{
        mapRef,
        mapsApi,
        contextMenuElement,
        onCloseContextMenu,
        position,
      }}
    />
  )
}

const mapStateToProps = (state: AppState) => {
  const { geofencesDeleteGeofence } = getSettings_UNSAFE(state)
  return {
    center: getCenter(state),
    zoom: getZoom(state),
    landmarksAddPOI: getLandmarksAddPOISetting(state),
    geofencesAddGeofence: getGeofencesAddGeofenceSetting(state),
    geofencesDeleteGeofence,
    currentLayerVisibility: getCurrentLayerVisibility(state),
    focusedVehicle: getFocusedVehicle(state),
  }
}

const mapDispatchToProps = {
  changeMapCenterZoom,
  setFocusedPoint,
  clickedMapContextMenuWhatsNearby,
  deleteGeofence,
}

export default connect(mapStateToProps, mapDispatchToProps)(ContextMenu)
