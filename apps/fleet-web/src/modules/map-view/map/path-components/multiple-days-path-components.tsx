import type { BasicEvent } from '@fleet-web/api/timeline/types'
import tripPathLineOSM from '@fleet-web/components/_map/_openstreetmap/_layers/PathLine/Trip'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import type { ExcludeStrict } from '@fleet-web/types/utils'
import type { MapApiProvider_Leaflet } from '@fleet-web/util-components/map/shared/types'

type Props = {
  mapObject: MapsExtended.MapObject
  eventsCoords: ReadonlyArray<ExcludeStrict<BasicEvent['coords'], null>>
  mapTypeId: google.maps.MapTypeId
}

export const multipleDaysOSMPathComponents = ({
  eventsCoords,
  mapProvider,
}: Pick<Props, 'eventsCoords'> & {
  mapProvider: MapApiProvider_Leaflet
}) =>
  tripPathLineOSM({
    vertices: eventsCoords,
    mapProvider,
  })
