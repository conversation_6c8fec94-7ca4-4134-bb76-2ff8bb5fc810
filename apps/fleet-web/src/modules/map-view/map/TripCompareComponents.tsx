import { isEmpty } from 'lodash'

import { MapApiProvider } from '@fleet-web/api/user/types'
import tripPathLineOSM from '@fleet-web/components/_map/_openstreetmap/_layers/PathLine/Trip'
import { TripPolyline } from '@fleet-web/components/_map/Polyline/Trip'
import type { Trip } from '@fleet-web/duxs/trip-compare'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import type { OpenLayersMapApiProvider } from '@fleet-web/util-components/map/open-layers/base-map-config'
import type { MapApiProvider_Leaflet } from '@fleet-web/util-components/map/shared/types'
import { filterOutInvalidCoordinates } from '@fleet-web/util-functions'

import type { TripMarkerData } from './types'

type GoogleMapProps = {
  mapApiProviderId: (typeof MapApiProvider)['GOOGLE']
  mapObject: MapsExtended.MapObject
  mapTypeId: google.maps.MapTypeId
}

type HereMapProps = {
  mapApiProviderId: OpenLayersMapApiProvider
}

type Props = {
  comparedTrips: ReadonlyArray<Trip>
  hoveredComparedTripIndex: number | null | undefined
  selectedComparedTrip: Trip | null
  timelineTripsMarkerData: TripMarkerData
  mapProps: GoogleMapProps | HereMapProps
}

export function TripCompareComponents({
  hoveredComparedTripIndex,
  selectedComparedTrip,
  comparedTrips,
  timelineTripsMarkerData,
  mapProps,
}: Props) {
  const renderOpenLayersTripLines = ({
    hovered,
    selected,
    pathVertices,
    trip,
    mapProviderLeaflet,
  }: {
    hovered: boolean
    selected: boolean
    pathVertices: Array<google.maps.LatLngLiteral>
    trip: Trip
    mapProviderLeaflet: MapApiProvider_Leaflet
  }) =>
    tripPathLineOSM({
      color: trip.color || 'gray',
      bringToBack: !hovered && !selected,
      bringToFront: hovered,
      opacity: hovered || selected ? 1 : 0.8,
      vertices: pathVertices,
      trips: timelineTripsMarkerData,
      mapProvider: mapProviderLeaflet,
    })

  return comparedTrips.map((trip, i) => {
    if (isEmpty(trip.events)) {
      return null
    }

    const pathVertices = filterOutInvalidCoordinates(trip.events)
    const hovered = i === hoveredComparedTripIndex
    const selected = trip === selectedComparedTrip
    return mapProps.mapApiProviderId === MapApiProvider.GOOGLE ? (
      <TripPolyline
        key={trip.key}
        mapObject={mapProps.mapObject}
        pathInWgs84={pathVertices}
        strokeColor={trip.color || 'gray'}
        // eslint-disable-next-line no-nested-ternary
        zIndex={hovered ? 100 : selected ? 50 : 10}
        strokeOpacity={hovered || selected ? 1 : 0.8}
        trips={timelineTripsMarkerData}
        mapTypeId={mapProps.mapTypeId}
      />
    ) : (
      renderOpenLayersTripLines({
        hovered,
        selected,
        pathVertices,
        trip,
        mapProviderLeaflet: mapProps.mapApiProviderId,
      })
    )
  })
}
