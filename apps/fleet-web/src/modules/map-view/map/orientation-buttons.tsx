import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { FixMeAny, ValueOf } from '@fleet-web/types'
import { IconButton } from '@fleet-web/util-components'
import { VIEW_MODE } from '@fleet-web/util-functions'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

const icons = {
  landscape: require('../../../../assets/svg/landscape_view.svg'),
  portrait: require('../../../../assets/svg/portrait_view.svg'),
}

type Props = {
  changeViewMode: (mode: ValueOf<typeof VIEW_MODE>) => void
  viewMode: ValueOf<typeof VIEW_MODE>
}

const OrientationButtons = ({ changeViewMode, viewMode }: Props) => {
  const renderOrientationIcon = (icon: FixMeAny, isActive: boolean) => (
    <div
      className={`Map-viewIcon Map-viewIcon-innerIcon${isActive ? '-isActive' : ''}`}
      {...makeSanitizedInnerHtmlProp({ dirtyHtml: icon })}
    />
  )

  const isLandscape = viewMode === VIEW_MODE.landscape
  const isPortrait = viewMode === VIEW_MODE.portrait

  return (
    <>
      <IconButton
        tooltipMessage="Landscape View"
        iconHtml={renderOrientationIcon(icons.landscape, isLandscape)}
        id="landscape"
        className="Map-viewIcon Map-viewIcon"
        onClick={() => {
          GA4.event({
            category: 'Map',
            action: 'Landscape View Click',
          })
          changeViewMode(VIEW_MODE.landscape)
        }}
        active={isLandscape}
      />
      <IconButton
        tooltipMessage="Portrait View"
        iconHtml={renderOrientationIcon(icons.portrait, isPortrait)}
        id="portrait"
        className="Map-viewIcon Map-viewIcon"
        onClick={() => {
          GA4.event({
            category: 'Map',
            action: 'Portrait View Click',
          })
          changeViewMode(VIEW_MODE.portrait)
        }}
        active={isPortrait}
      />
    </>
  )
}

export default OrientationButtons
