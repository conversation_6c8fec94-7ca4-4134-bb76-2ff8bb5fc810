import type { Except } from 'type-fest'
import { z } from 'zod'

import type {
  FetchTimelineEventsUI,
  TimelineEvent,
} from '@fleet-web/api/timeline/types'

const _statusClassNameArray = [
  'driving-manual',
  'driving',
  'excessive-idling',
  'excessive-rpm',
  'harsh-accel',
  'harsh-acceleration',
  'harsh-braking',
  'harsh-turning',
  'idling',
  'ignition-off',
  'ignition-on',
  'maintenance',
  'max-speed',
  'moving-ignition-off',
  'no-signal ns-with-time',
  'no-signal',
  'off-duty',
  'on-duty',
  'pc',
  'sleeper-berth',
  'speeding',
  'stationary',
  'ym',
] as const
export type EventStatusClassName = (typeof _statusClassNameArray)[number]
export type ComputedEventStatusForUI =
  | EventStatusClassName
  | 'vision'
  | 'generic_sensor'
/**
 * We use a Set instead of z.enum because it's faster to parse in a loop
 */
export const eventStatusClassNameSchema: ReadonlySet<
  (typeof _statusClassNameArray)[number]
> = new Set(_statusClassNameArray)

export const vehicleStatusClassNameSchema = z.string().or(z.enum(_statusClassNameArray))

export type TripMarkerData = Array<
  FetchTimelineEventsUI.ParsedApiTrip & {
    formattedTime: {
      start: string
      end: string
    }
    totals: Array<{
      label: string
      value: number | string
    }>
    events: Array<
      Except<TimelineEvent, 'lat' | 'lng'> & {
        lat: number
        lng: number
        latitude: number
        longitude: number
      }
    >
  }
>

export type MenuElements = {
  withStreetView?: boolean
  items: Array<{
    icon: React.ReactNode
    disabled?: boolean
    message: string
    handleOnClick?: React.MouseEventHandler<HTMLDivElement>
  }>
}
