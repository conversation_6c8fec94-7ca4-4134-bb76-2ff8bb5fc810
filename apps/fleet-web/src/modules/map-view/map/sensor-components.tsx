import type { VehicleId } from '@fleet-web/api/types'
import type { FixMeAny } from '@fleet-web/types'
import { Array_map } from '@fleet-web/util-functions/performance-critical-utils'

import type { MappedSensorCluster } from './map'
import SensorMarker, { type SensorMarkerProps } from './sensor-marker'
import type { GetSensorMarkerEventLabel } from './sensor-marker/types'

type Props = {
  vehicleId: VehicleId
  sensorClusters: Array<MappedSensorCluster>
  onSensorClick: (event: Record<string, FixMeAny>) => void
  onSensorClusterClick: React.MouseEventHandler<HTMLDivElement>
  getSensorMarkerEventLabel: GetSensorMarkerEventLabel
}

function sensorComponents({
  vehicleId,
  sensorClusters,
  onSensorClick,
  onSensorClusterClick,
  getSensorMarkerEventLabel,
}: Props) {
  return Array_map(sensorClusters, (c) => {
    const {
      event: { timestampDate, eventTypeIcon },
    } = c.sensorPoints[0]

    const commonProps = {
      id: c.id,
      lat: c.lat,
      lng: c.lng,
      timestampDate,
      vehicleId: vehicleId,
    } satisfies Partial<SensorMarkerProps> & { lat: number; lng: number }

    if (eventTypeIcon.toLowerCase().includes('no match')) {
      return null
    }

    if (c.numPoints > 1) {
      return (
        <SensorMarker
          key={c.id}
          {...commonProps}
          events={c.sensorPoints}
          numPoints={c.numPoints}
          onSensorClick={onSensorClick}
          onSensorClusterClick={onSensorClusterClick}
          icon={eventTypeIcon}
          getEventLabel={getSensorMarkerEventLabel}
        />
      )
    }

    return (
      <SensorMarker
        key={c.id}
        {...commonProps}
        events={c.sensorPoints}
        icon={eventTypeIcon}
        getEventLabel={getSensorMarkerEventLabel}
      />
    )
  })
}

export default sensorComponents
