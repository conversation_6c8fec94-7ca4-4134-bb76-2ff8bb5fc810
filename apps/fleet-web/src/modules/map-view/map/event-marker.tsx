import { memo, useState } from 'react'
import { match } from 'ts-pattern'

import { AdvancedMarkerHeadless } from '@fleet-web/components/_map/AdvancedMarkerHeadless'
import { useMapTheme } from '@fleet-web/components/context/MapThemeContext'
import type {
  TimelineEvent,
  TimelineEventWithRoadSpeed,
} from '@fleet-web/duxs/timeline'
import { getSettings_UNSAFE, getSpeedUnit } from '@fleet-web/duxs/user'
import { useVehiclesGroupedEventTypesQuery } from '@fleet-web/modules/api/useEventTypes'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import {
  EventMarker as BaseEventMarker,
  EventMarkerInfo as BaseEventMarkerInfo,
} from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { formatDistance } from '@fleet-web/util-components/formatted-distance'
import { getEventMarkerAttributes, STATUS_TO_MESSAGE } from '@fleet-web/util-functions'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import fairEnd from 'assets/icons/fare-end.svg'
import fairStart from 'assets/icons/fare-start.svg'

import {
  EVENT_MARKER_TOOLTIP_Z_INDEX,
  EVENT_MARKER_Z_INDEX,
} from '../FleetMapView/map-z-indexes'

const eventTypeIdToSvg = {
  '159': fairStart,
  '160': fairEnd,
}

type Props = {
  positionInWgs84: google.maps.LatLngLiteral
  event: TimelineEventWithRoadSpeed | TimelineEvent
  onEventMarkerClick: ((id: string) => void) | undefined
  TooltipComponent?: React.ComponentType<{
    event: TimelineEventWithRoadSpeed | TimelineEvent
    isHighlighted: boolean
  }>
  alwaysShowTooltip?: boolean
  mapTypeId: google.maps.MapTypeId
  map: google.maps.Map
}

export const EventMarker = memo(function EventMarker({
  onEventMarkerClick,
  event,
  TooltipComponent,
  alwaysShowTooltip,
  mapTypeId,
  map,
  positionInWgs84,
}: Props) {
  const [isHovered, setIsHovered] = useState(false)
  const speedUnit = useTypedSelector(getSpeedUnit)
  const { distanceInMiles } = useTypedSelector(getSettings_UNSAFE)
  const mapTheme = useMapTheme()
  const vehiclesGroupedEventTypesQuery = useVehiclesGroupedEventTypesQuery()

  if (!vehiclesGroupedEventTypesQuery.data) {
    return null
  }

  const { getEventLabel } = vehiclesGroupedEventTypesQuery.data

  const formattedSpeed = formatDistance(event.speed ?? 0, {
    round: true,
    isMiles: distanceInMiles,
  })
  const { bearingStyle, inferredStatus } = getEventMarkerAttributes(event)

  const showTooltip = isHovered || !!alwaysShowTooltip

  if (event.altClassName in eventTypeIdToSvg) {
    const altClassName = event.altClassName as keyof typeof eventTypeIdToSvg
    return (
      <div
        style={bearingStyle}
        {...makeSanitizedInnerHtmlProp({
          dirtyHtml: eventTypeIdToSvg[altClassName],
        })}
        className="EventMarker EventMarker-special"
      />
    )
  }

  const eventLabel = match(inferredStatus)
    .with('vision', 'generic_sensor', () => getEventLabel(event))
    .otherwise(() =>
      ctIntl.formatMessage({
        id: STATUS_TO_MESSAGE[inferredStatus] ?? '',
      }),
    )

  return (
    <AdvancedMarkerHeadless
      mapTypeId={mapTypeId}
      map={map}
      positionInWgs84={positionInWgs84}
      zIndex={EVENT_MARKER_Z_INDEX}
      onMouseEnter={() => {
        setIsHovered(true)
      }}
      onMouseLeave={() => {
        setIsHovered(false)
      }}
    >
      <div
        id={event.id}
        data-testid="event-marker"
        onClick={(e) => {
          GA4.event({
            category: 'Map',
            action: 'Event Marker Click',
          })

          onEventMarkerClick?.(e.currentTarget.id)
        }}
      >
        <BaseEventMarker
          style={bearingStyle}
          status={inferredStatus}
          mapTheme={mapTheme}
        />
        {showTooltip && (
          <AdvancedMarkerHeadless
            mapTypeId={mapTypeId}
            map={map}
            positionInWgs84={positionInWgs84}
            zIndex={EVENT_MARKER_TOOLTIP_Z_INDEX}
          >
            {TooltipComponent ? (
              <TooltipComponent
                event={event}
                isHighlighted={!!alwaysShowTooltip}
              />
            ) : (
              <BaseEventMarkerInfo
                eventClassName={inferredStatus}
                eventLabel={eventLabel}
                eventDetail={event}
                speed={{
                  value: formattedSpeed,
                  unit: speedUnit,
                }}
              />
            )}
          </AdvancedMarkerHeadless>
        )}
      </div>
    </AdvancedMarkerHeadless>
  )
})
