import { useEffect, type ComponentProps, type ComponentType } from 'react'
import { isEmpty } from 'lodash'
import moment from 'moment-timezone'
import { connect } from 'react-redux'
import type { RouteComponentProps } from 'react-router-dom'
import type { Except } from 'type-fest'

import type { FetchTimelineEventsRawResolved } from '@fleet-web/api/timeline'
import type { VehicleId } from '@fleet-web/api/types'
import FocusedLandmarkMarker from '@fleet-web/components/_map/_openstreetmap/_markers/FocusedLandmarkMarker'
import OpenLayerMarker from '@fleet-web/components/_map/_openstreetmap/_markers/Marker'
import { fetchMapGeofencesAction, getAllMapGeofences } from '@fleet-web/duxs/geofences'
import { fetchLandmarks } from '@fleet-web/duxs/landmarks'
import {
  getFocusedGeofence,
  getFocusedLandmark,
  getFocusedPoint,
  getMapLandmarksWithoutFocusedLandmark,
  getMapVehicles,
  getTimelineProgress,
} from '@fleet-web/duxs/map'
import { getIsComparingTrips } from '@fleet-web/duxs/map-timeline'
import {
  getActiveTimelineEventByActivity,
  getMultipleDaysPathComponentsFilteredEventsCoords,
  getSelectedTrip,
  getTimelineEventsByActivityAndMapType,
  getTimelineTripsMarkerData,
} from '@fleet-web/duxs/timeline'
import { getTrafficAlerts } from '@fleet-web/duxs/traffic-alerts'
import {
  getComparedTrips,
  getHoveredComparedTripIndex,
  getSelectedComparedTripIndex,
  getSelectedCompareTripActiveEvent,
} from '@fleet-web/duxs/trip-compare'
import { getMapZoomOptions } from '@fleet-web/duxs/user'
import {
  useEffectEvent,
  useEventHandlerBranded,
} from '@fleet-web/hooks/useEventHandler'
import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'
import type { ExtractStrict } from '@fleet-web/types/utils'
import type { OnVehicleHoverHandler } from '@fleet-web/util-components/map/google/layers/vehicle-components'
import OpenLayerBaseMap from '@fleet-web/util-components/map/open-layers/base-map-open-layers'
import {
  OpenLayerFocusedGeofence,
  OpenLayerGeofences,
  OpenLayerLandmarks,
  OpenLayersVehicles,
  OpenLayerTrafficAlerts,
} from '@fleet-web/util-components/map/open-layers/layers'
import mapEvents, {
  type MapEventsInjectedProps,
  type MapEventsWrappedComponentRequiredProps,
} from '@fleet-web/util-components/map/shared/map-events'
import type { MapViewContainerInjectedProps } from '@fleet-web/util-components/map/shared/mapViewContainer'
import type { MapProviderMetaDataOpenLayers } from '@fleet-web/util-components/map/shared/types'
import { getDateDiffByDays, zoomToBounds } from '@fleet-web/util-functions'
import { is2DimensionalArray } from '@fleet-web/util-functions/array-utils'

import ContextMenu from './context-menu'
import OrientationButtons from './orientation-buttons'
import { multipleDaysOSMPathComponents } from './path-components/multiple-days-path-components'
import PathComponents from './path-components/path-components'
import { TripCompareComponents } from './TripCompareComponents'

type Props = {
  timelineStartTime: number
  preferences: Record<string, FixMeAny>
  isLandmarkEnable?: boolean
  isDrawingMap?: boolean
  fullscreenState?: {
    selectedType: 'vehicle'
    focusedItemStartDate: Date | null
  }
  onVehicleHover?: OnVehicleHoverHandler
  onVehicleMarkerClick: (vehicleId: VehicleId) => void
  onEventMarkerClick: (
    id: string,
    events: FetchTimelineEventsRawResolved['events'],
  ) => void
  onFullscreenEscape?: () => void
  onFullscreenClick?: () => void
  mapProviderMetaData: MapProviderMetaDataOpenLayers
  mapTypeId: google.maps.MapTypeId
  focusVehiclesOnMapMountAndVehiclesSizeChange: boolean
} & Pick<MapViewContainerInjectedProps, 'focusedItem' | 'mapType' | 'mapHeight'> &
  Pick<
    RouteComponentProps<void, any, { pathname: FixMeAny } | undefined | null>,
    'location' | 'history'
  > &
  Pick<ComponentProps<typeof OrientationButtons>, 'changeViewMode' | 'viewMode'> &
  ReduxProps &
  MapEventsInjectedProps

const MapOpenLayers = ({
  changeMapCenterZoom,
  timelineStartTime,
  fetchMapGeofencesAction,
  fetchLandmarks,
  setVehicleFocusedLayerVisibility,
  setLayerVisibility,
  setMapState,
  focusedItem,
  fullscreenState,
  focusedPoint,
  geofencesInWgs = [],
  landmarks = [],
  location,
  mapState,
  preferences: { useVehicleIconColor },
  mapType,
  vehicles = [],
  zoom,
  onContextMenuClick,
  onFullscreenEscape,
  onFollowFocusedItemClick,
  onMapClick,
  onFullscreenClick,
  onVehicleMarkerClick,
  contextMenuElement,
  contextMenuCoords,
  activeTimelineEvent,
  currentLayerVisibility,
  isComparingTrips,
  onEventMarkerClick: onEventMarkerClickProp,
  selectedTripEvents = [],
  timelineEventsByActivity,
  timelineTripsMarkerData,
  multipleDaysPathComponentsEventsCoords,
  changeViewMode,
  history,
  mapRef,
  onCloseContextMenu,
  onMeasureDistance,
  onShowGPSCoordinates,
  viewMode,
  trafficAlerts = [],
  timelineProgress,
  activeCompareEvent,
  comparedTrips,
  center,
  hoveredComparedTripIndex,
  selectedComparedTrip,
  followFocusedItem,
  isDrawingMap,
  isMeasuring,
  isLandmarkEnable = true,
  onVehicleHover = {
    onMouseEnter: () => {},
    onMouseLeave: () => {},
  },
  mapZoomOptions,
  mapProviderMetaData,
  mapTypeId,
  focusVehiclesOnMapMountAndVehiclesSizeChange,
  focusedLandmark,
  focusedGeofence,
}: Props) => {
  const {
    alerts: showTrafficAlerts,
    geofences: showGeofences,
    geofenceLabels: showGeofenceLabels,
    landmarks: showLandmarks,
    pointsOfInterestLabels: showLandmarkLabels,
    livePositions: showVehicles,
    livePositionClusters: showClusters,
    livePositionLabels: showVehicleLabels,
    livePositionShowWhileAVehicleIsSelected: showVehiclesWhileAVehicleIsSelected,
    maps: showMaps,
  } = currentLayerVisibility
  const { pathname: prevPathName } = location.state ?? {}
  const showDefaultMarkers = isLandmarkEnable && !isComparingTrips

  const mapApiProviderId = mapProviderMetaData.currentMapProvider

  useEffect(() => {
    fetchMapGeofencesAction()
    fetchLandmarks()
  }, [fetchMapGeofencesAction, fetchLandmarks])

  const onVehiclesSizeChange = useEffectEvent(() => {
    // Kind of sub optimal solution, but this way we can optionally avoid this behaviour
    if (!focusVehiclesOnMapMountAndVehiclesSizeChange) {
      return
    }
    if (mapState && !focusedItem && !focusedPoint) {
      const filteredVehicles = vehicles.map(({ latitude, longitude }) => ({
        latitude,
        longitude,
      }))

      changeMapCenterZoom(
        ...([
          ...zoomToBounds(filteredVehicles, mapState.size, {
            maxZoom: mapZoomOptions.maxZoom,
          }),
          'newVehicleList',
        ] as const),
      )
    }
  })

  const vehiclesSize = vehicles.length
  useEffect(() => {
    if (vehiclesSize > 0) {
      onVehiclesSizeChange()
    }
  }, [vehiclesSize])

  const handleGeofenceContextMenuClick = (
    id: string,
    position: Parameters<typeof onContextMenuClick>[2],
    geofenceType: 'user' | 'new' | 'system',
  ) => {
    if (geofenceType === 'user') {
      onContextMenuClick('geofence', id, position)
    } else {
      onContextMenuClick('', '', position)
    }
  }

  const handleLandmarkContextMenuClick = (
    id: string,
    position: Parameters<typeof onContextMenuClick>[2],
  ) => {
    onContextMenuClick('landmark', id, position)
  }

  const handleVehicleHover = ({ vehicleId }: { vehicleId: VehicleId }) => {
    if (vehicleId !== undefined) {
      onVehicleHover.onMouseEnter({ vehicleId })
    }
  }

  const handleVehicleContextMenuClick = (
    id: string,
    position: Parameters<typeof onContextMenuClick>[2],
  ) => {
    onContextMenuClick('vehicle', id, position)
  }

  const renderVehicles = () => {
    // Get first timeline event and today's date
    const todayDate = moment().format()
    const selectedTimelineDate = moment(timelineStartTime).format()
    const dateDiff = getDateDiffByDays(todayDate, selectedTimelineDate)

    const showGhostVehicle =
      timelineEventsByActivity.type === 'daily' &&
      timelineEventsByActivity.events.length > 0 &&
      dateDiff === 0 &&
      timelineProgress !== 0 &&
      timelineProgress !== 1

    const preferences = {
      showVehicles: !isEmpty(focusedItem)
        ? showVehiclesWhileAVehicleIsSelected
        : showVehicles,
      showVehicleClusters: showClusters,
      showVehicleLabels,
      useVehicleIconColor,
    }

    return (
      <OpenLayersVehicles
        mapApiProvider={mapProviderMetaData.currentMapProvider}
        data={vehicles}
        onHover={{
          onMouseOver: handleVehicleHover,
          onMouseOut: onVehicleHover.onMouseLeave,
        }}
        onVehicleContextMenuClick={handleVehicleContextMenuClick}
        preferences={preferences}
        activeCompareEvent={activeCompareEvent}
        activeTimelineEvent={activeTimelineEvent.data}
        isComparingTrips={isComparingTrips}
        showGhostVehicle={showGhostVehicle}
        showDefaultMarkers={showDefaultMarkers}
        onVehicleMarkerClick={onVehicleMarkerClick}
      />
    )
  }

  const renderGeofences = () =>
    showDefaultMarkers &&
    !focusedGeofence && (
      <OpenLayerGeofences
        geofencesInWgs={geofencesInWgs}
        mapApiProviderId={mapApiProviderId}
        onGeofenceContextMenu={handleGeofenceContextMenuClick}
        preferences={{ showGeofences, showGeofenceLabels }}
      />
    )

  const renderFocusedGeofence = () =>
    showDefaultMarkers &&
    focusedGeofence && (
      <OpenLayerFocusedGeofence
        geofenceInWgs={focusedGeofence}
        mapApiProviderId={mapApiProviderId}
      />
    )

  const renderLandmarks = () =>
    showDefaultMarkers && (
      <OpenLayerLandmarks
        mapProvider={mapApiProviderId}
        data={landmarks}
        onLandmarkContextMenu={handleLandmarkContextMenuClick}
        preferences={{
          showLandmarks,
          showLandmarkClusters: showClusters,
          showLandmarkLabels,
        }}
      />
    )

  const renderFocusedLandmark = () =>
    !!focusedLandmark && (
      <FocusedLandmarkMarker
        key={focusedLandmark.id}
        positionInWgs84={{
          lat: Number(focusedLandmark.lat),
          lng: Number(focusedLandmark.lng),
        }}
        colorName={focusedLandmark.colorName}
        mapProvider={mapApiProviderId}
        focusedLandmark={focusedLandmark}
        drawRadius={{
          radius: focusedLandmark.radius,
          realZoom: zoom,
        }}
      />
    )

  const renderPointMarker = () =>
    focusedPoint && (
      <OpenLayerMarker
        className="PointMarker"
        positionInWgs84={focusedPoint}
        mapProvider={mapApiProviderId}
      />
    )

  const renderTrafficAlerts = () =>
    showMaps && (
      <OpenLayerTrafficAlerts
        data={trafficAlerts}
        preferences={{ showTrafficAlerts }}
        mapProvider={mapApiProviderId}
      />
    )

  const handleEventMarkerClick = useEventHandlerBranded((id: string) => {
    const dailyEvents = timelineEventsByActivity.events as ExtractStrict<
      typeof timelineEventsByActivity,
      { type: 'daily' }
    >['events']
    onEventMarkerClickProp(id, dailyEvents)
  })

  const renderPath = () => {
    const focusedVehicle = focusedItem

    if (!isComparingTrips) {
      if (timelineEventsByActivity.type === 'daily') {
        const useSVREvents = !!(focusedVehicle && mapType === 'svr-units')
        if (is2DimensionalArray(selectedTripEvents)) {
          return selectedTripEvents.map((tripEvents) => (
            <PathComponents
              key={tripEvents.at(0)?.id}
              mapProps={{ mapApiProviderId }}
              focusedVehicle={focusedVehicle}
              // events: memoizedTimelineEventsByActivity,
              events={timelineEventsByActivity.events}
              selectedTripEvents={tripEvents}
              timelineTripsMarkerData={timelineTripsMarkerData}
              currentLayerVisibility={currentLayerVisibility}
              zoom={zoom}
              onEventMarkerClick={handleEventMarkerClick}
              useSVREvents={useSVREvents}
            />
          ))
        } else {
          return (
            <PathComponents
              mapProps={{ mapApiProviderId }}
              focusedVehicle={focusedVehicle}
              // events: memoizedTimelineEventsByActivity,
              events={timelineEventsByActivity.events}
              selectedTripEvents={selectedTripEvents}
              timelineTripsMarkerData={timelineTripsMarkerData}
              currentLayerVisibility={currentLayerVisibility}
              zoom={zoom}
              onEventMarkerClick={handleEventMarkerClick}
              useSVREvents={useSVREvents}
            />
          )
        }
      } else {
        return multipleDaysOSMPathComponents({
          eventsCoords: multipleDaysPathComponentsEventsCoords,
          mapProvider: mapApiProviderId,
        })
      }
    } else {
      return null
    }
  }

  const renderTripCompare = () =>
    isComparingTrips && (
      <TripCompareComponents
        key="trip-compare-components"
        comparedTrips={comparedTrips}
        hoveredComparedTripIndex={hoveredComparedTripIndex}
        selectedComparedTrip={selectedComparedTrip}
        timelineTripsMarkerData={timelineTripsMarkerData}
        mapProps={{ mapApiProviderId }}
      />
    )

  const renderContextMenu = () => {
    if (contextMenuElement === null || contextMenuCoords === null) {
      return null
    }

    const getContextMenuPosition = () => {
      const { x, y } = contextMenuCoords

      switch (contextMenuElement.type) {
        case 'vehicle': {
          return { ...contextMenuCoords, x: x + 8, y: y + 12 }
        }
        case 'landmark': {
          return { ...contextMenuCoords, x: x + 8, y: y + 16 }
        }
        default: {
          return contextMenuCoords
        }
      }
    }

    return (
      <ContextMenu
        {...{
          contextMenuElement,
          history,
          mapApiProviderId,
          mapTypeId,
          mapRef,
          onCloseContextMenu,
          onMeasureDistance,
          onShowGPSCoordinates,
        }}
        mapsApi={null as FixMeAny}
        position={getContextMenuPosition()}
        showDirections={false}
      />
    )
  }

  return (
    <>
      <OpenLayerBaseMap
        {...{
          center,
          zoom,
          currentLayerVisibility,
          focusedItem,
          followFocusedItem,
          setVehicleFocusedLayerVisibility,
          setLayerVisibility,
          setMapState,
          onMeasureDistance,
          onFollowFocusedItemClick,
          onMapClick,
          onFullscreenClick,
          onFullscreenEscape,
          onContextMenuClick,
          changeMapCenterZoom,
          isComparingTrips,
          isDrawingMap,
          isMeasuring,
          mapState,
          history,
          location,
        }}
        mapProviderMetaData={mapProviderMetaData}
        onMoveStart={onCloseContextMenu}
        fullscreenPath="/map/fullscreen"
        fullscreenState={
          fullscreenState
            ? {
                focusedItem,
                focusedItemStartDate: fullscreenState.focusedItemStartDate,
                type: mapType,
                selectedType: fullscreenState.selectedType,
              }
            : undefined
        }
        hasFocusedItem={!isEmpty(focusedItem)}
        showVisionOptions={!!focusedItem?.['isCamera']}
        showLayersMenu={activeTimelineEvent.type === 'daily'}
        showMapViewTray={activeTimelineEvent.type === 'daily'}
        changeViewMode={changeViewMode}
        {...((mapType === 'fleet' || prevPathName === '/map/fleet') &&
          activeTimelineEvent.type === 'daily' && {
            viewTrayControls: (
              <OrientationButtons
                changeViewMode={changeViewMode}
                viewMode={viewMode}
              />
            ),
          })}
      >
        {/* Map Layers */}
        {renderVehicles()}
        {renderGeofences()}
        {renderFocusedGeofence()}
        {renderLandmarks()}
        {renderFocusedLandmark()}
        {renderPointMarker()}
        {renderTrafficAlerts()}
        {renderTripCompare()}
        {renderPath()}
      </OpenLayerBaseMap>

      {/* Other Components */}
      {renderContextMenu()}
    </>
  )
}

const mapStateToProps = (state: AppState) => {
  const comparedTrips = getComparedTrips(state)
  const selectedComparedTripIndex = getSelectedComparedTripIndex(state)
  const selectedComparedTrip =
    selectedComparedTripIndex === null ? null : comparedTrips[selectedComparedTripIndex]

  return {
    focusedPoint: getFocusedPoint(state),
    geofencesInWgs: getAllMapGeofences(state),
    focusedGeofence: getFocusedGeofence(state),
    landmarks: getMapLandmarksWithoutFocusedLandmark(state),
    focusedLandmark: getFocusedLandmark(state),
    trafficAlerts: getTrafficAlerts(state),
    selectedTripEvents: getSelectedTrip(state)?.events,
    vehicles: getMapVehicles(state),
    multipleDaysPathComponentsEventsCoords:
      getMultipleDaysPathComponentsFilteredEventsCoords(state),

    // Timeline
    activeTimelineEvent: getActiveTimelineEventByActivity(state),
    timelineProgress: getTimelineProgress(state),
    timelineEventsByActivity: getTimelineEventsByActivityAndMapType(state),
    timelineTripsMarkerData: getTimelineTripsMarkerData(state),

    // Trip Compare
    comparedTrips,
    selectedComparedTrip,
    activeCompareEvent: getSelectedCompareTripActiveEvent(state),
    hoveredComparedTripIndex: getHoveredComparedTripIndex(state),
    isComparingTrips: getIsComparingTrips(state),
    mapZoomOptions: getMapZoomOptions(state),
  }
}

const mapDispatchToProps = {
  fetchMapGeofencesAction,
  fetchLandmarks,
}

type ReduxProps = ReturnType<typeof mapStateToProps> & typeof mapDispatchToProps

const EnhancedMapOpenLayers: FixMeAny = connect(
  mapStateToProps,
  mapDispatchToProps,
)(mapEvents(MapOpenLayers as FixMeAny) as FixMeAny)

export default EnhancedMapOpenLayers as ComponentType<
  Except<
    ComponentProps<typeof MapOpenLayers>,
    keyof MapEventsInjectedProps | keyof ReduxProps
  > &
    MapEventsWrappedComponentRequiredProps
>
