import { useMemo } from 'react'
import { flatten } from 'lodash'
import type { Except } from 'type-fest'

import type { TerminalEventTypeCode } from '@fleet-web/api/types'
import type { TranslatedDescription } from '@fleet-web/api/user/types'
import { getVehicleFocusedLayerVisibility } from '@fleet-web/duxs/map-timeline'
import {
  getLayerGroups,
  getSelectedTrip,
  getTimelineEventsRaw,
} from '@fleet-web/duxs/timeline'
import { getFocusedVehicle } from '@fleet-web/duxs/vehicles'
import {
  useVehiclesGroupedEventTypesQuery,
  type VehicleVisionGroupedEventTypeUniqueKey,
} from '@fleet-web/modules/api/useEventTypes'
import { useTypedSelector } from '@fleet-web/redux-hooks'

export type VisionMapLayerOptions = {
  vision: {
    labelMeta: { msgId: string }
    children: ReadonlyArray<{
      key: VehicleVisionGroupedEventTypeUniqueKey
      labelMeta: { translatedLabel: string }
    }>
  }
}

export type UseVehicleVisionMapLayersOptionsMeta =
  | 'loading'
  | {
      vehiclesGroupedVisionEventTypesMap: Map<
        VehicleVisionGroupedEventTypeUniqueKey,
        {
          translatedDescription: TranslatedDescription
          eventTypeCodes: Set<TerminalEventTypeCode> // May contain one or more
        }
      >
      visionLayerMenuOptions: VisionMapLayerOptions['vision']
    }
export function useVehicleVisionMapLayersOptionsMeta(): UseVehicleVisionMapLayersOptionsMeta {
  const vehiclesGroupedEventTypesQuery = useVehiclesGroupedEventTypesQuery()

  const meta = useMemo((): UseVehicleVisionMapLayersOptionsMeta => {
    if (!vehiclesGroupedEventTypesQuery.data) {
      return 'loading' as const
    }

    const { vehiclesGroupedVisionEventTypesMap } = vehiclesGroupedEventTypesQuery.data
    const sortedChildren = Array.from(vehiclesGroupedVisionEventTypesMap.entries())
      .map(([key, value]) => ({
        key: key,
        labelMeta: { translatedLabel: value.translatedDescription },
      }))
      .sort((a, b) =>
        a.labelMeta.translatedLabel.localeCompare(b.labelMeta.translatedLabel),
      )

    return {
      visionLayerMenuOptions: {
        labelMeta: { msgId: 'map.layersMenu.vision' },
        children: sortedChildren,
      },
      vehiclesGroupedVisionEventTypesMap,
    }
  }, [vehiclesGroupedEventTypesQuery.data])

  return meta
}

export function useFocusedVehicleSensorMarkerEvents() {
  const layerGroups = useTypedSelector(getLayerGroups)
  const events = useTypedSelector(getTimelineEventsRaw)
  const selectedTrip = useTypedSelector(getSelectedTrip)
  const currentLayerVisibility = useTypedSelector(getVehicleFocusedLayerVisibility)
  const focusedVehicle = useTypedSelector(getFocusedVehicle)
  const visionLayerMenuOptionsMeta = useVehicleVisionMapLayersOptionsMeta()

  // This makes it so that the useMemo runs less often. We only need to know if the vehicle is focused.
  // Not run the memo every time a prop of focusedVehicle changes.
  const isFocusedVehicleDefined = !!focusedVehicle

  const sensorEvents = useMemo(() => {
    if (!isFocusedVehicleDefined) {
      return { events: [] }
    }
    type SelectedSensorKey =
      | TerminalEventTypeCode
      | 'indicators'
      | 'privacy'
      | 'PTO'
      | 'rightIndicator'
      | 'leftIndicator'
      | 'privacyStart'
      | 'privacyEnd'
      | 'pto-active'
      | 'pto-deactive'
    const selectedSensors = new Set<SelectedSensorKey>()

    if (currentLayerVisibility.vision && visionLayerMenuOptionsMeta !== 'loading') {
      const { vehiclesGroupedVisionEventTypesMap, visionLayerMenuOptions } =
        visionLayerMenuOptionsMeta
      for (const child of visionLayerMenuOptions.children) {
        if (!currentLayerVisibility[child.key]) {
          continue
        }
        const visionGroupedEventType = vehiclesGroupedVisionEventTypesMap.get(child.key)
        if (visionGroupedEventType) {
          for (const evenTypeCode of visionGroupedEventType.eventTypeCodes) {
            // When selecting a vision event type, it might be a group underneath.
            // As such, we need to add all the children of the group to the selected sensors.
            selectedSensors.add(evenTypeCode)
          }
        }
      }
    }

    Object.keys(currentLayerVisibility)
      .filter(
        (key) =>
          currentLayerVisibility.sensors &&
          layerGroups.sensors.children.some((type) => type.key === key),
      )
      // eslint-disable-next-line
      // eslint-disable-next-line unicorn/no-array-for-each
      .forEach((key_) => {
        const key = key_ as keyof typeof currentLayerVisibility
        if (currentLayerVisibility[key]) {
          selectedSensors.add(key as SelectedSensorKey)
        }
      })

    if (selectedSensors.has('indicators')) {
      selectedSensors.add('leftIndicator').add('rightIndicator')
    }

    if (selectedSensors.has('privacy')) {
      selectedSensors.add('privacyStart').add('privacyEnd')
    }

    if (selectedSensors.has('PTO')) {
      selectedSensors.add('pto-active').add('pto-deactive')
    }

    const sensorEvents = selectedTrip === null ? events : flatten(selectedTrip.events)

    type SensorEventWithEventTypeIconSet = Except<
      (typeof sensorEvents)[number],
      'eventTypeIcon'
    > & { eventTypeIcon: string }

    const filteredSensorEvents: Array<SensorEventWithEventTypeIconSet> = []

    for (const sensorEvent of sensorEvents) {
      if (sensorEvent.eventTypeIcon && selectedSensors.has(sensorEvent.eventTypeIcon)) {
        filteredSensorEvents.push({
          ...sensorEvent,
          eventTypeIcon: sensorEvent.eventTypeIcon,
        })
        continue
      }

      const firstActionWithASelectedSensorIcon = sensorEvent.actions.find((action) =>
        selectedSensors.has(action.icon),
      )
      if (firstActionWithASelectedSensorIcon !== undefined) {
        filteredSensorEvents.push({
          ...sensorEvent,
          eventTypeIcon: firstActionWithASelectedSensorIcon.icon,
          eventTypeDescription: firstActionWithASelectedSensorIcon.description,
        })
      }
    }

    return { events: filteredSensorEvents }
  }, [
    currentLayerVisibility,
    events,
    isFocusedVehicleDefined,
    layerGroups.sensors.children,
    selectedTrip,
    visionLayerMenuOptionsMeta,
  ])

  return sensorEvents
}
