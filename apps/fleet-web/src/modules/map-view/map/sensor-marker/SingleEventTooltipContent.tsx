import { useMemo } from 'react'
import { Chip, Stack, Typography } from '@karoo-ui/core'
import { DateTime } from 'luxon'

import type { VehicleId } from '@fleet-web/api/types'
import { getVisionSetting } from '@fleet-web/duxs/user'
import type { MappedSensorCluster } from '@fleet-web/modules/map-view/map/map'
import {
  getEventMap,
  type EventType,
} from '@fleet-web/modules/vision/CameraFootageModal/components/CamerasVideoControl/utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import EventCameraFootageBox from './EventCameraFootageBox'

type SinglePointTooltipContentProps = {
  eventsTitle: string
  chipLabel: string
  sensorEvent: MappedSensorCluster['sensorPoints'][number]
  timestampDate: Date
  vehicleId: VehicleId
}

const SingleEventTooltipContent = ({
  vehicleId,
  eventsTitle,
  chipLabel,
  sensorEvent,
  timestampDate,
}: SinglePointTooltipContentProps) => {
  const visionSetting = useTypedSelector(getVisionSetting)
  const sourceType = sensorEvent.event.actions[0]?.sourceType

  const formattedTime = useMemo(
    () => DateTime.fromJSDate(timestampDate).toFormat('tt'),
    [timestampDate],
  )

  const eventType: EventType = sourceType === 'video' ? 'vision' : 'sensor'

  return (
    <Stack
      gap={0.5}
      sx={{ alignItems: 'center', justifyContent: 'center' }}
    >
      <Typography variant="caption">{eventsTitle}</Typography>
      <Chip
        label={chipLabel}
        sx={(theme) => ({
          backgroundColor: getEventMap(eventType, theme).color,
          color: 'common.white',
        })}
      />
      <Typography variant="caption">{formattedTime}</Typography>

      {visionSetting && (
        <EventCameraFootageBox
          vehicleId={vehicleId}
          eventTime={sensorEvent.event.timestampDate}
        />
      )}
    </Stack>
  )
}

export default SingleEventTooltipContent
