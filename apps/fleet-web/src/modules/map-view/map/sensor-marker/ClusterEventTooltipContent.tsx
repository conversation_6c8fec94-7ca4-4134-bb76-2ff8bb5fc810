import { useEffect, useMemo, useState } from 'react'
import { Box, Chip, CircularProgress, IconButton, Typography } from '@karoo-ui/core'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { VehicleId } from '@fleet-web/api/types'
import { getVisionSetting } from '@fleet-web/duxs/user'
import type { EventHandlerBranded } from '@fleet-web/hooks/useEventHandler'
import type { MappedSensorCluster } from '@fleet-web/modules/map-view/map/map'
import type { FootageBlock } from '@fleet-web/modules/vision/CameraFootageModal/api/useVisionFootageBlocksQuery'
import {
  getEventMap,
  type EventType,
} from '@fleet-web/modules/vision/CameraFootageModal/components/CamerasVideoControl/utils'
import { getCameraFootageModalMainPath } from '@fleet-web/modules/vision/CameraFootageModal/utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import type { GetSensorMarkerEventLabel } from './types'
import useEventFootage from './useEventFootage'

type ClusterEventTooltipContentProps = {
  vehicleId: VehicleId
  events: MappedSensorCluster['sensorPoints']
  eventsTitle: string
  onSensorClusterEventClick: EventHandlerBranded<
    [event: MappedSensorCluster['sensorPoints'][number]['event']]
  >
  getEventLabel: GetSensorMarkerEventLabel
}

const ClusterEventTooltipContent = ({
  vehicleId,
  events,
  eventsTitle,
  onSensorClusterEventClick,
  getEventLabel,
}: ClusterEventTooltipContentProps) => {
  const visionSetting = useTypedSelector(getVisionSetting)
  // Track if any button needs space
  const [showButtonColumn, setShowButtonColumn] = useState(false)

  const updateButtonColumnVisibility = (shouldTakeUpSpace: boolean) => {
    if (shouldTakeUpSpace) {
      setShowButtonColumn(true)
    }
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 0.5,
        }}
      >
        <Typography variant="caption">{eventsTitle}</Typography>
      </Box>

      <Box
        sx={{
          maxHeight: 150, // ~ 5 events showing
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        {events.map(({ event }) => {
          const eventType: EventType =
            event.actions[0].sourceType === 'video' ? 'vision' : 'sensor'
          return (
            <Box
              key={event.id}
              sx={(theme) => ({
                display: 'grid',
                gridTemplateColumns: showButtonColumn
                  ? 'minmax(0, 1fr) auto 24px'
                  : 'minmax(0, 1fr) auto 0px',
                columnGap: 1,
                alignItems: 'center',
                py: 0.5,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: rgba(theme.palette.common.white, 0.1),
                },
              })}
              onClick={() => onSensorClusterEventClick(event)}
            >
              <Box>
                <Chip
                  sx={(theme) => ({
                    backgroundColor: getEventMap(eventType, theme).color,
                    color: 'common.white',
                    width: '100%',
                  })}
                  label={getEventLabel(event)}
                />
              </Box>
              <Typography
                variant="caption"
                sx={{ whiteSpace: 'nowrap' }}
              >
                {DateTime.fromJSDate(event.timestampDate).toFormat('tt')}
              </Typography>
              <Box sx={{ p: 0 }}>
                {visionSetting && (
                  <ViewEventFootageButton
                    vehicleId={vehicleId}
                    eventTime={event.timestampDate}
                    onStatusUpdate={updateButtonColumnVisibility}
                  />
                )}
              </Box>
            </Box>
          )
        })}
      </Box>
    </Box>
  )
}

export default ClusterEventTooltipContent

type ViewEventFootageButtonProps = {
  eventTime: Date
  vehicleId: VehicleId
  onStatusUpdate: (shouldTakeUpSpace: boolean) => void
}

const ViewEventFootageButton = ({
  eventTime: eventTimeByDate,
  vehicleId,
  onStatusUpdate,
}: ViewEventFootageButtonProps) => {
  const history = useHistory()
  const eventTime = useMemo(
    () => DateTime.fromJSDate(eventTimeByDate),
    [eventTimeByDate],
  )

  const { footage, status } = useEventFootage({
    eventTime,
    vehicleId,
  })

  const [wasPending] = useState(status === 'pending')

  // Notify parent component if this button needs space in the grid
  useEffect(() => {
    const shouldTakeUpSpace = status === 'pending' || wasPending || !!footage
    if (shouldTakeUpSpace) {
      onStatusUpdate(true)
    }
  }, [status, footage, wasPending, onStatusUpdate])

  const handleViewFootageIconClick = (footage: FootageBlock) => {
    history.push(
      getCameraFootageModalMainPath(history.location, {
        selectedVehicleId: vehicleId,
        selectedDate: eventTime.toISO(),
        initialSelectedFootage: {
          startTime: DateTime.fromJSDate(footage.start).toISO(),
          camera: footage.camera as `${number}`,
        },
      }),
    )
  }

  return (
    <>
      {match(status)
        .with('pending', () => <CircularProgress size={20} />)
        .with('error', () => null)
        .with('success', () => (
          <>
            {!footage ? (
              wasPending && <Box sx={{ width: 20, height: 20 }} />
            ) : (
              <IconButton
                size="small"
                sx={{ p: 0 }}
                onClick={(e) => {
                  e.stopPropagation()
                  handleViewFootageIconClick(footage)
                }}
                color="primary"
              >
                <RemoveRedEyeOutlinedIcon sx={{ color: 'common.white' }} />
              </IconButton>
            )}
          </>
        ))
        .exhaustive()}
    </>
  )
}
