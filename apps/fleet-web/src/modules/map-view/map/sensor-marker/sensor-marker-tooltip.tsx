import { useMemo } from 'react'
import { Box, Tooltip } from '@karoo-ui/core'

import type { VehicleId } from '@fleet-web/api/types'
import type { EventHandlerBranded } from '@fleet-web/hooks/useEventHandler'
import type { MappedSensorCluster } from '@fleet-web/modules/map-view/map/map'

import ClusterEventTooltipContent from './ClusterEventTooltipContent'
import SingleEventTooltipContent from './SingleEventTooltipContent'
import type { GetSensorMarkerEventLabel } from './types'

type Props = {
  events: MappedSensorCluster['sensorPoints']
  eventsTitle: string
  getEventLabel: GetSensorMarkerEventLabel
  numPoints: number
  timestampDate: Date
  vehicleId: VehicleId
  children: React.ReactElement
  onSensorClusterEventClick: EventHandlerBranded<
    [event: MappedSensorCluster['sensorPoints'][number]['event']]
  >
}

const SensorMarkerTooltip = ({
  events,
  eventsTitle,
  children,
  getEventLabel,
  numPoints,
  timestampDate,
  vehicleId,
  onSensorClusterEventClick,
}: Props) => {
  const tooltipContent = useMemo(
    () => (
      <Box
        sx={{
          p: 0.5,
          borderRadius: 1,
          color: 'white',
          textAlign: 'center',
          fontSize: '10px',
        }}
      >
        {numPoints > 1 ? (
          <ClusterEventTooltipContent
            vehicleId={vehicleId}
            events={events}
            eventsTitle={eventsTitle}
            onSensorClusterEventClick={onSensorClusterEventClick}
            getEventLabel={getEventLabel}
          />
        ) : (
          <SingleEventTooltipContent
            eventsTitle={eventsTitle}
            chipLabel={getEventLabel(events[0].event)}
            sensorEvent={events[0]}
            timestampDate={timestampDate}
            vehicleId={vehicleId}
          />
        )}
      </Box>
    ),
    [
      numPoints,
      eventsTitle,
      events,
      getEventLabel,
      timestampDate,
      vehicleId,
      onSensorClusterEventClick,
    ],
  )

  return (
    <Tooltip
      title={tooltipContent}
      placement="top"
      arrow
    >
      {children}
    </Tooltip>
  )
}

export default SensorMarkerTooltip
