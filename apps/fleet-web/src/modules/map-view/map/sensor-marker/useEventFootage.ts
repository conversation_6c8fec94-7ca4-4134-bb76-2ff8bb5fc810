import { useMemo } from 'react'
import type { DateTime } from 'luxon'

import type { VehicleId } from '@fleet-web/api/types'
import useVisionFootageBlocksQuery from '@fleet-web/modules/vision/CameraFootageModal/api/useVisionFootageBlocksQuery'

type Params = {
  vehicleId: VehicleId
  eventTime: DateTime
}

export default function useEventFootage({ vehicleId, eventTime }: Params) {
  // Here actually we can use the eventTime, but we use the start date and end date
  // to prevent calling the API too much, since trips and other events will need to fetch footage as well
  const selectedDateTsRange = useMemo(
    () => ({
      startTs: eventTime.startOf('day').toJSDate(),
      endTs: eventTime.endOf('day').toJSDate(),
    }),
    [eventTime],
  )

  const query = useVisionFootageBlocksQuery({
    vehicleId,
    startTs: selectedDateTsRange.startTs,
    endTs: selectedDateTsRange.endTs,
  })

  const { data, status } = query

  // Find the footage block that contains the eventTime
  const footage = useMemo(() => {
    if (data?.footage?.array) {
      const eventDate = eventTime.toJSDate()
      return data.footage.array.find(
        (f) => f.start <= eventDate && f.end >= eventDate && !!f.url,
      )
    }
    return undefined
  }, [data, eventTime])

  return {
    footage: footage ?? null,
    status,
  }
}
