import { useMemo, useState } from 'react'
import {
  Box,
  CircularProgress,
  IconButton,
  Stack,
  styled,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import PlayCircleFilledWhiteOutlinedIcon from '@mui/icons-material/PlayCircleFilledWhiteOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { VehicleId } from '@fleet-web/api/types'
import type { FootageBlock } from '@fleet-web/modules/vision/CameraFootageModal/api/useVisionFootageBlocksQuery'
import { getCameraFootageModalMainPath } from '@fleet-web/modules/vision/CameraFootageModal/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import useEventFootage from './useEventFootage'

type Props = {
  eventTime: Date
  vehicleId: VehicleId
}

const VIDEO_BOX_DIMENSIONS = {
  width: 143,
  height: 80,
}

const EventCameraFootageBox = ({
  eventTime: eventTimeByDate,
  vehicleId,
  ...rest
}: Props) => {
  const eventTime = useMemo(
    () => DateTime.fromJSDate(eventTimeByDate),
    [eventTimeByDate],
  )

  const { footage, status } = useEventFootage({ eventTime, vehicleId })

  return match(status)
    .with('pending', () => (
      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
        <CircularProgress size={24} />
      </Box>
    ))
    .with('error', () => null)
    .with('success', () => (
      <EventCameraFootage
        eventTime={eventTime}
        footage={footage}
        vehicleId={vehicleId}
        {...rest}
      />
    ))
    .exhaustive()
}

type EventCameraFootageProps = {
  eventTime: DateTime
  vehicleId: VehicleId
  footage: FootageBlock | null
}

const EventCameraFootage = ({
  eventTime,
  vehicleId,
  footage,
}: EventCameraFootageProps) => {
  const history = useHistory()
  const [isHover, setIsHover] = useState(false)

  const videoTimeLabel =
    footage?.start && footage?.end
      ? `${DateTime.fromJSDate(footage.start).toLocaleString(
          DateTime.TIME_WITH_SECONDS,
        )} - ${DateTime.fromJSDate(footage.end).toLocaleString(
          DateTime.TIME_WITH_SECONDS,
        )}`
      : null

  const snapshotUrl = footage?.image_url ?? null
  const hasVideo = !!footage

  const handleCameraFootageBoxClick = () => {
    if (hasVideo) {
      history.push(
        getCameraFootageModalMainPath(history.location, {
          selectedVehicleId: vehicleId,
          selectedDate: eventTime.toISO(),
          initialSelectedFootage: {
            startTime: DateTime.fromJSDate(footage.start).toISO(),
            camera: footage.camera as `${number}`,
          },
        }),
      )
    }
  }

  if (!hasVideo) {
    return null
  }

  return (
    <Container
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      isHover={isHover}
      onClick={(e) => {
        e.stopPropagation()
        handleCameraFootageBoxClick()
      }}
    >
      {snapshotUrl ? (
        <SnapshotBackground style={{ backgroundImage: `url(${snapshotUrl})` }} />
      ) : (
        <NoSnapshotContainer>
          <Typography variant="caption">
            {ctIntl.formatMessage({ id: 'Video has no preview' })}
          </Typography>
        </NoSnapshotContainer>
      )}
      {videoTimeLabel && (
        <BottomOverlay>
          <Typography
            variant="caption"
            sx={(theme) => ({
              color: theme.palette.common.white,
            })}
          >
            {videoTimeLabel}
          </Typography>
        </BottomOverlay>
      )}
      {isHover && (
        <>
          <HoverOverlay />
          <CenterOverlay>
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View footage' })}
              arrow
            >
              <IconButton sx={(theme) => ({ color: theme.palette.common.white })}>
                <PlayCircleFilledWhiteOutlinedIcon
                  sx={{ width: '32px', height: '32px' }}
                />
              </IconButton>
            </Tooltip>
          </CenterOverlay>
        </>
      )}
    </Container>
  )
}

export default EventCameraFootageBox

const Container = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isHover',
})<{ isHover: boolean }>(({ theme, isHover }) =>
  theme.unstable_sx({
    position: 'relative',
    minWidth: `${VIDEO_BOX_DIMENSIONS.width}px`,
    width: `${VIDEO_BOX_DIMENSIONS.width}px`,
    height: `${VIDEO_BOX_DIMENSIONS.height}px`,
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: 0.5,
    overflow: 'hidden',
    boxShadow: isHover
      ? `0px 0px 6px 1px ${rgba(theme.palette.primary.main, 0.3)}`
      : 'none',
    transition: 'box-shadow 0.3s ease',
    backgroundColor: theme.palette.grey[600],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  }),
)

const SnapshotBackground = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
})

const BottomOverlay = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    position: 'absolute',
    padding: '2px 4px',
    backgroundColor: rgba(theme.palette.common.black, 0.6),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    right: '2px',
    bottom: '2px',
    borderRadius: 1,
  }),
)

const CenterOverlay = styled(Box)(() => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 2,
}))

const HoverOverlay = styled(Box)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: rgba(0, 0, 0, 0.4),
  zIndex: 1,
}))

const NoSnapshotContainer = styled(Stack)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: 'center',
  alignItems: 'center',
  textAlign: 'center',
  gap: '2px',
}))
