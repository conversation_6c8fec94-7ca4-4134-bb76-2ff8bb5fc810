import { memo, useMemo } from 'react'
import type * as React from 'react'
import { styled } from '@karoo-ui/core'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type { VehicleId } from '@fleet-web/api/types'
import { useEventHandlerBranded } from '@fleet-web/hooks/useEventHandler'
import type { MappedSensorCluster } from '@fleet-web/modules/map-view/map/map'
import { AlertsBadge } from '@fleet-web/util-components'
import { mapActionIconToSvgString } from '@fleet-web/util-components/action-icon-matcher'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import SensorMarkerTooltip from './sensor-marker-tooltip'
import type { GetSensorMarkerEventLabel } from './types'

export type SensorMarkerProps = {
  id: string
  events: MappedSensorCluster['sensorPoints'] | undefined
  vehicleId: VehicleId
  icon: string
  getEventLabel: GetSensorMarkerEventLabel
  timestampDate: Date
  onSensorClick?: (event: MappedSensorCluster['sensorPoints'][number]['event']) => void
  onSensorClusterClick?: React.MouseEventHandler<HTMLDivElement>
  numPoints?: number
}

function SensorMarker({
  id,
  icon,
  vehicleId,
  getEventLabel,
  events = [],
  timestampDate,
  onSensorClick: onSensorClickProp,
  onSensorClusterClick = () => null,
  numPoints = 0,
}: SensorMarkerProps) {
  const sensorEvents = useMemo(
    () =>
      // Sort Sensor Events
      !R.isEmpty(events) ? R.sortBy(events, (e) => e.event.time) : [],

    [events],
  )

  const eventsTitle = useMemo(() => {
    const count = sensorEvents.length

    const sourceTypes = new Set(sensorEvents.map((e) => e.event.actions[0].sourceType))

    if (sourceTypes.size === 1) {
      const [type] = sourceTypes

      return match(type)
        .with('video', () =>
          ctIntl.formatMessage(
            { id: 'map.sensorMarkerTooltip.title.visionEvent' },
            { values: { count } },
          ),
        )
        .with('sensor', () =>
          ctIntl.formatMessage(
            { id: 'map.sensorMarkerTooltip.title.sensorEvent' },
            { values: { count } },
          ),
        )
        .otherwise(() =>
          ctIntl.formatMessage(
            { id: 'map.sensorMarkerTooltip.title.genericEvent' },
            { values: { count } },
          ),
        )
    }

    return ctIntl.formatMessage(
      { id: 'map.sensorMarkerTooltip.title.genericEvent' },
      { values: { count } },
    )
  }, [sensorEvents])

  const markerIcon = mapActionIconToSvgString(icon, {
    variant: 'marker',
  })

  const onSensorClick = useEventHandlerBranded(
    (event: MappedSensorCluster['sensorPoints'][number]['event']) => {
      onSensorClickProp?.(event)
    },
  )

  const typeDescriptionSplit = events[0].event.eventTypeDescription?.split(';')
  const groupActionMeta =
    typeDescriptionSplit && typeDescriptionSplit.length > 1
      ? { count: typeDescriptionSplit.length }
      : null

  return (
    <SensorMarkerTooltip
      eventsTitle={eventsTitle}
      getEventLabel={getEventLabel}
      numPoints={numPoints}
      events={sensorEvents}
      timestampDate={timestampDate}
      vehicleId={vehicleId}
      onSensorClusterEventClick={onSensorClick}
    >
      <div
        id={id}
        className="SensorMarker"
        onClick={onSensorClusterClick}
      >
        {markerIcon ? (
          <Marker
            table={false}
            {...makeSanitizedInnerHtmlProp({ dirtyHtml: markerIcon })}
          />
        ) : null}
        {groupActionMeta && (
          <MarkerActionCount>{groupActionMeta.count}</MarkerActionCount>
        )}
        {numPoints > 1 && (
          <AlertsBadge
            className="SensorMarker-count"
            count={numPoints}
          />
        )}
      </div>
    </SensorMarkerTooltip>
  )
}

export default memo(SensorMarker)

const Marker = styled('div')<{ table: boolean }>(({ table }) => ({
  height: 'auto',
  margin: 'auto',
  position: 'relative',
  transform: `translateY(${table ? '0px' : '-20px'})`,
  width: '30px',
  zIndex: 10,
  '& svg': {
    height: 'inherit',
  },
}))

const MarkerActionCount = styled('div')(({ theme }) => ({
  position: 'absolute',
  right: '-18px',
  top: '-5px',
  width: '23px',
  height: '23px',
  borderRadius: '5px',
  backgroundColor: theme.palette.primary.main,
  color: 'white',
  zIndex: 10,
}))
