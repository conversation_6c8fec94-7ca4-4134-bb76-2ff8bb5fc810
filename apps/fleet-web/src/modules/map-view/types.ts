import type { ReadonlyDeep } from 'type-fest'

import type { getItems, getSortedLeftPanelExtras } from '@fleet-web/duxs/map'
import type { StandardPlaceDetails } from '@fleet-web/shared/google-places'
import type { ExtractStrict } from '@fleet-web/types/utils'

export type MapGooglePlacePrediction = {
  type: 'google_place_prediction'
  placeId: string
  name: string
  address: string
}

export type MapPlaceResult = StandardPlaceDetails | MapGooglePlacePrediction

export type MapSearchResult =
  | ReturnType<typeof getItems>[number]
  | ReturnType<typeof getSortedLeftPanelExtras>[number]

export type ResolvedSearchItems = ReadonlyDeep<
  | Array<MapSearchResult>
  | Array<MapPlaceResult>
  | [
      {
        address: null
      },
    ]
  | [
      | {
          lat: `${number}`
          lng: `${number}`
        }
      | {
          lat: `${number}`
          lng: `${number}`
          type: 'place'
          name?: string
          id: string | undefined
          address?: string
          photo: string
          icon: string
        },
    ]
>

export type ResolvedPlaceSearchItem = ExtractStrict<
  ResolvedSearchItems[number],
  { type: 'place' }
>

export type ResolvedPlaceOrGooglePredictionSearchItem = ExtractStrict<
  ResolvedSearchItems[number],
  { type: 'place' | 'google_place_prediction' }
>
