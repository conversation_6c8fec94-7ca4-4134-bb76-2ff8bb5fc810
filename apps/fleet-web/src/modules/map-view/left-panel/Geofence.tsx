import { MenuItem, MenuList } from '@karoo-ui/core'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router'

import SVGIcon from '@fleet-web/components/Icon/SVGIcon'
import { getFocusedGeofenceId } from '@fleet-web/duxs/map'
import { UserFormattedPositionAddress } from '@fleet-web/modules/components/connected/UserFormattedPositionAddress'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { ExtractStrict } from '@fleet-web/types/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import GeolocationIcon from 'assets/svg/geolocation.svg'

import { clickedLeftPanelGeofence } from '../actions'
import { getGeofenceDetailsModalMainPath } from '../FleetMapView/Geofence/utils'
import { useMapViewSearchContext } from '../MapViewSearchProvider'
import type { ResolvedSearchItems } from '../types'
import {
  CellIcon,
  CellInfo,
  CellListEntry,
  CellListEntryTooltipMenu,
  CellTitle,
  CellWrap,
} from './shared/utils'

type Props = {
  item: ExtractStrict<ResolvedSearchItems[number], { type: 'geofence' }>
  onMouseEnter: ({ id, type }: { id: string; type: string }) => void
  onMouseLeave: () => void
}

function Geofence({ item, onMouseEnter, onMouseLeave }: Props) {
  const history = useHistory()
  const { highlightText } = useMapViewSearchContext()
  const dispatch = useDispatch()
  const focusedGeofenceId = useTypedSelector(getFocusedGeofenceId)

  const { id, color, name, address, description, type } = item
  return (
    <CellListEntryTooltipMenu
      title={
        <MenuList>
          <MenuItem
            onClick={() => dispatch(clickedLeftPanelGeofence({ geofence: item }))}
          >
            {ctIntl.formatMessage({ id: 'map.poi.viewOnMap' })}
          </MenuItem>
          <MenuItem
            onClick={() => {
              history.push(
                getGeofenceDetailsModalMainPath(history.location, {
                  geo: 'edit',
                  id: id,
                }),
              )
              dispatch(clickedLeftPanelGeofence({ geofence: item }))
            }}
          >
            {ctIntl.formatMessage({ id: 'map.geofence.viewDetails' })}
          </MenuItem>
        </MenuList>
      }
    >
      <CellListEntry
        onClick={() => dispatch(clickedLeftPanelGeofence({ geofence: item }))}
        onMouseEnter={() => onMouseEnter({ id, type })}
        onMouseLeave={onMouseLeave}
        selected={id === focusedGeofenceId}
      >
        <CellIcon bgColor={color}>
          <SVGIcon
            height="20"
            svg={GeolocationIcon}
          />
        </CellIcon>
        <CellWrap>
          <CellTitle>{highlightText(name)}</CellTitle>
          <CellInfo>
            <UserFormattedPositionAddress
              address={address}
              gpsFixType={null}
            />
          </CellInfo>
          {description && <CellInfo>{highlightText(description)}</CellInfo>}
        </CellWrap>
      </CellListEntry>
    </CellListEntryTooltipMenu>
  )
}

export default Geofence
