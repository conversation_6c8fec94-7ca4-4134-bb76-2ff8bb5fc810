import { useMemo, useState } from 'react'
import {
  ClickAwayListener,
  FormControl,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Select,
  Typography,
} from '@karoo-ui/core'
import SearchIcon from '@mui/icons-material/Search'

import type { MapFleetSearchSchema } from '@fleet-web/modules/map-view/MapFleetProvider'
import type {
  ResolvedPlaceOrGooglePredictionSearchItem,
  ResolvedSearchItems,
} from '@fleet-web/modules/map-view/types'
import type { ExtractStrict } from '@fleet-web/types/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import PlacesSearchResultsPopover from './PlacesSearchResultsPopover'

type Props<T> = {
  searchTextValue: string
  searchTypeValue: MapFleetSearchSchema['searchType']
  onTextChange: (event: { target: { value: string } }) => void
  onTypeChange: (type: T) => void
  items: ResolvedSearchItems
  onFocus?: () => void
  options: Array<{
    name: string
    renderValue: string
    value: T
  }>
  handlePlaceClick: (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    placeOrGooglePrediction: ResolvedPlaceOrGooglePredictionSearchItem,
  ) => void
  isFetchingPlacesResultsInPopover: boolean
  endAdornmentBeforeSelect: React.ReactNode
}

function LeftPanelMultiSearchInput<T>({
  searchTextValue,
  searchTypeValue,
  onTextChange,
  onTypeChange,
  onFocus,
  items,
  options,
  handlePlaceClick,
  isFetchingPlacesResultsInPopover,
  endAdornmentBeforeSelect,
}: Props<T>) {
  const [formControlEl, setFormControlEl] = useState<HTMLDivElement | null>(null)
  const [isFocusedOnPlaceSearch, setIsFocusedOnPlaceSearch] = useState<boolean>(false)
  const optionValue = useMemo(
    () => options.find((item) => item.value === searchTypeValue) || options[0],
    [options, searchTypeValue],
  )
  const isPlaceSearch = optionValue.value === 'placeSearch'

  return (
    <ClickAwayListener
      onClickAway={() => {
        setIsFocusedOnPlaceSearch(false)
      }}
    >
      <FormControl
        fullWidth
        variant="outlined"
        size="small"
        ref={setFormControlEl}
      >
        <OutlinedInput
          placeholder={ctIntl.formatMessage({
            id: optionValue.name,
          })}
          sx={{
            paddingRight: 0,
            '& input::placeholder': {
              fontSize: '12px',
            },
          }}
          value={searchTextValue}
          onChange={onTextChange}
          onFocus={() => {
            onFocus?.()
            if (isPlaceSearch) {
              setIsFocusedOnPlaceSearch(true)
            }
          }}
          startAdornment={
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          }
          endAdornment={
            <InputAdornment position="end">
              {endAdornmentBeforeSelect}
              <Select
                size="small"
                sx={{
                  '.MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                }}
                value={optionValue.value}
                onChange={(e) => {
                  onTypeChange(e.target.value as T)
                }}
                renderValue={(value) => (
                  <Typography
                    sx={{
                      color: (theme) => theme.palette.primary.main,
                    }}
                  >
                    {ctIntl.formatMessage({
                      id:
                        options.find((option) => option.value === value)?.renderValue ??
                        '',
                    })}
                  </Typography>
                )}
              >
                {options.map((option) => (
                  <MenuItem
                    key={option.value as React.Key}
                    value={option.value as string | number}
                  >
                    {ctIntl.formatMessage({ id: option.name })}
                  </MenuItem>
                ))}
              </Select>
            </InputAdornment>
          }
        />
        {isFocusedOnPlaceSearch && (
          <PlacesSearchResultsPopover
            anchorEl={formControlEl}
            searchResults={
              items as Array<
                ExtractStrict<
                  (typeof items)[number],
                  { type: 'google_place_prediction' | 'place' }
                >
              >
            }
            handleClick={(e, place) => {
              handlePlaceClick(e, place)
              setIsFocusedOnPlaceSearch(false)
            }}
            isLoading={isFetchingPlacesResultsInPopover}
          />
        )}
      </FormControl>
    </ClickAwayListener>
  )
}

export default LeftPanelMultiSearchInput
