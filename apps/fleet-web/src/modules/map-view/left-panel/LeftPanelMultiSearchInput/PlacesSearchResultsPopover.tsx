import type React from 'react'
import {
  LinearProgress,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Popper,
} from '@karoo-ui/core'

import type { ResolvedPlaceOrGooglePredictionSearchItem } from '@fleet-web/modules/map-view/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const PlacesSearchResultsPopover = ({
  anchorEl,
  searchResults,
  handleClick,
  isLoading,
}: {
  anchorEl: HTMLDivElement | null
  searchResults: ReadonlyArray<ResolvedPlaceOrGooglePredictionSearchItem>
  handleClick: (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    place: ResolvedPlaceOrGooglePredictionSearchItem,
  ) => void
  isLoading: boolean
}) => (
  <Popper
    open
    anchorEl={anchorEl}
    placement="bottom-start"
    style={{ width: anchorEl ? anchorEl.clientWidth : 'auto', zIndex: 1300 }}
  >
    <Paper elevation={3}>
      <List>
        {isLoading && <LinearProgress />}
        {searchResults.length === 0 && (
          <ListItem>
            <ListItemText
              secondary={ctIntl.formatMessage({ id: 'No results found' })}
              secondaryTypographyProps={{ variant: 'caption' }}
            ></ListItemText>
          </ListItem>
        )}
        {searchResults.map((place) => (
          <ListItem
            disablePadding
            key={place.type === 'google_place_prediction' ? place.placeId : place.id}
            divider
          >
            <ListItemButton
              id={place.type === 'google_place_prediction' ? place.placeId : place.id}
              onClick={(event) => handleClick(event, place)}
            >
              <ListItemText
                primary={place.name}
                secondary={place.address}
                primaryTypographyProps={{ variant: 'subtitle2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Paper>
  </Popper>
)

export default PlacesSearchResultsPopover
