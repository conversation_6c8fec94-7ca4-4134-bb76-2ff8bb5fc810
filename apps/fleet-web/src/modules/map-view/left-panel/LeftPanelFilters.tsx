import {
  Autocomplete,
  Box,
  ClickAwayListener,
  FormControl,
  FormControlLabel,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Select,
  Stack,
  styled,
  TextField,
  type SelectChangeEvent,
} from '@karoo-ui/core'
import AccessTimeFilled from '@mui/icons-material/AccessTimeFilled'
import ChangeCircleIcon from '@mui/icons-material/ChangeCircle'
import CheckCircle from '@mui/icons-material/CheckCircle'
import CircleIcon from '@mui/icons-material/Circle'
import HandymanIcon from '@mui/icons-material/Handyman'
import LocationOffIcon from '@mui/icons-material/LocationOff'
import PersonIcon from '@mui/icons-material/Person'
import PersonOffIcon from '@mui/icons-material/PersonOff'
import RemoveCircle from '@mui/icons-material/RemoveCircle'
import WifiOffIcon from '@mui/icons-material/WifiOff'
import { useDispatch } from 'react-redux'
import { match } from 'ts-pattern'

import { colorHash, colorsName, type colors } from '@fleet-web/api/colors'
import SettingsPanel from '@fleet-web/components/_map/_panels/Settings'
import {
  getCarpoolVehiclesFilters,
  getDriversFilters,
  getFilters,
} from '@fleet-web/duxs/map'
import type {
  CarpoolFiltersIconName,
  MapCarpoolVehicleFilterName,
  MapDriverFilter,
  MapType,
  MapVehicleFilterName,
} from '@fleet-web/duxs/map-types'
import {
  getCarpoolSetting,
  savePreferences,
  type UserPreferences,
} from '@fleet-web/duxs/user'
import {
  filterCountValue,
  GrayInfo,
  ResetButton,
} from '@fleet-web/modules/map-view/shared/utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { toMutable } from '@fleet-web/util-functions/functional-utils'
import { Obj_mapValuesToArray } from '@fleet-web/util-functions/performance-critical-utils'

import {
  onLeftPanelCarpoolVehicleFilterChange,
  onLeftPanelDriverFilterChange,
  onLeftPanelResetAllVehicleFiltersButtonClick,
  onLeftPanelResetCarpoolVehicleFilter,
  onLeftPanelResetDriverFilterChange,
  onLeftPanelResetVehicleFilterChange,
  onLeftPanelVehicleFilterChange,
} from '../actions'
import { useFilters } from './FiltersProvider'
import {
  AccordionBlock,
  CheckBoxSection,
  SectionCheckbox,
  SectionFormControlLabel,
  SectionTitle,
  VehicleSectionSubHeaderContainer,
  VehicleSectionSubHeaderTitle,
} from './shared/utils'
import { MapSettingSections } from './types'

const mapSettingSectionsInOrder = [
  MapSettingSections.VEHICLES,
  MapSettingSections.GEOFENCES,
  MapSettingSections.POIS,
  MapSettingSections.VEHICLE_GROUPS,
]

const defaultSortOptions = [
  {
    name: 'Alphabetical',
    value: 'alphabetical',
  },
  {
    name: 'Status',
    value: 'status',
  },
] as const

type Props = {
  preferences: UserPreferences
  selectedSortMethod: 'alphabetical' | 'status'
  type: '' | MapType
  onFilterPanelClickAway: () => void
}

type SortBySelectOptionValue = 'alphabetical' | 'status'

const LeftPanelFilters = ({
  preferences,
  selectedSortMethod,
  type,
  onFilterPanelClickAway,
}: Props) => {
  const dispatch = useDispatch()

  const getSortOptions = () => defaultSortOptions

  const {
    selectedStatusCount,
    selectedDriverFiltersCount,
    selectedCarpoolFiltersCount,
    geofenceColorsCount,
    poiColorsCount,
    vehicleIconColorsCount,
  } = useFilters()

  const handleSortChange = (e: SelectChangeEvent<string>) => {
    dispatch(
      savePreferences('mapViewSortMethods', {
        ...preferences.mapViewSortMethods,
        [type]: e.target.value as SortBySelectOptionValue,
      }),
    )
  }

  const handleResetAllFilters = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: 'Reset All Vehicle Filters',
    })

    dispatch(onLeftPanelResetAllVehicleFiltersButtonClick())
  }

  return (
    <ClickAwayListener onClickAway={onFilterPanelClickAway}>
      <SettingsPanel
        sx={{
          position: 'absolute',
          left: variables.redesignMapLeftPanelWidth,
          overflow: 'auto',
          marginLeft: 0,
          zIndex: 2,
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
        }}
      >
        <Box
          p={2}
          borderBottom="1px solid rgba(0, 0, 0, 0.12)"
        >
          <SectionTitle pb={2}>{ctIntl.formatMessage({ id: 'Sort By' })}</SectionTitle>
          <FormControl
            sx={{ width: '100%' }}
            size="small"
          >
            <Select
              labelId="sort-by-label"
              id="sort-by"
              value={selectedSortMethod satisfies SortBySelectOptionValue}
              onChange={handleSortChange}
              input={<OutlinedInput />}
              MenuProps={{ disablePortal: true }}
            >
              {getSortOptions().map((option) => (
                <MenuItem
                  key={option.value}
                  value={option.value satisfies SortBySelectOptionValue}
                >
                  {ctIntl.formatMessage({ id: option.name })}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <Stack
          px={2}
          pt={2}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <SectionTitle>
            {ctIntl.formatMessage({ id: 'Filters' })}
            <GrayInfo>
              {filterCountValue(
                selectedStatusCount +
                  selectedDriverFiltersCount +
                  selectedCarpoolFiltersCount +
                  geofenceColorsCount +
                  poiColorsCount +
                  vehicleIconColorsCount,
              )}
            </GrayInfo>
          </SectionTitle>
          <ResetButton onClick={handleResetAllFilters}>
            {ctIntl.formatMessage({ id: 'Reset All' })}
          </ResetButton>
        </Stack>
        {mapSettingSectionsInOrder.map((section) =>
          match(section)
            .with(MapSettingSections.VEHICLES, () => (
              <VehicleSection
                key={section}
                preferences={preferences}
                selectedOptionsCount={{
                  selectedStatusCount,
                  selectedDriverFiltersCount,
                  selectedCarpoolFiltersCount,
                }}
              />
            ))
            .with(MapSettingSections.GEOFENCES, () => (
              <GeofenceSection
                key={section}
                preferences={preferences}
              />
            ))
            .with(MapSettingSections.POIS, () => (
              <POIsSection
                key={section}
                preferences={preferences}
              />
            ))
            .otherwise(() => null),
        )}
      </SettingsPanel>
    </ClickAwayListener>
  )
}

const renderColorsPicker = (
  type: string,
  values: ReadonlyArray<string>,
  handleChange: (event: Array<string>) => void,
) => (
  <Autocomplete
    multiple
    id={`${type}-color-label`}
    size="small"
    options={colorsName}
    value={toMutable(values)}
    disableClearable
    disableCloseOnSelect
    onChange={(_, options) => handleChange(options)}
    renderInput={(params) => (
      <TextField
        {...params}
        label={ctIntl.formatMessage({ id: 'Colors' })}
      />
    )}
    renderOption={(props, option, { selected }) => (
      <li {...props}>
        <ColorSection>
          <FormControlLabel
            control={<SectionCheckbox checked={selected} />}
            label={ctIntl.formatMessage({ id: option })}
            onClick={(e) => e.preventDefault()}
          />
          <ColorIcon iconColor={colorHash[option as keyof typeof colorHash]} />
        </ColorSection>
      </li>
    )}
  />
)

const VehicleSection = ({
  preferences: { vehicleIconColors = [] },
  selectedOptionsCount: {
    selectedStatusCount,
    selectedCarpoolFiltersCount,
    selectedDriverFiltersCount,
  },
}: {
  preferences: UserPreferences
  selectedOptionsCount: {
    selectedStatusCount: number
    selectedCarpoolFiltersCount: number
    selectedDriverFiltersCount: number
  }
}) => {
  const dispatch = useDispatch()
  const currentVehicleFilters = useTypedSelector(getFilters)
  const currentDriverFilters = useTypedSelector(getDriversFilters)
  const carpoolCurrentVehicleFilters = useTypedSelector(getCarpoolVehiclesFilters)
  const userHasCarpool = useTypedSelector(getCarpoolSetting)

  const getCarpoolFilterIcon = (key: CarpoolFiltersIconName) =>
    match(key)
      .with('carpool_free', () => (
        <RemoveCircle sx={{ fontSize: 20, color: '#9E9E9E' }} />
      ))
      .with('carpool_active', () => (
        <ChangeCircleIcon sx={{ fontSize: 20, color: '#00ACC1' }} />
      ))
      .with('carpool_issued', () => (
        <CheckCircle sx={{ fontSize: 20, color: '#43A047' }} />
      ))
      .with('carpool_requested', () => (
        <AccessTimeFilled sx={{ fontSize: 20, color: '#FF6D00' }} />
      ))
      .otherwise(() => null)

  const handleVehicleFilterChange = (
    filterChecked: boolean,
    filterName: MapVehicleFilterName,
  ) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Turn ${filterChecked ? 'on' : 'off'} Filter By "${filterName}"`,
    })

    dispatch(
      onLeftPanelVehicleFilterChange({
        filterChecked,
        filterName: filterName,
      }),
    )
  }

  const handleDriverFilterChange = (filterName: MapDriverFilter) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Filter By "${filterName}"`,
    })

    dispatch(onLeftPanelDriverFilterChange({ filterName }))
  }

  const handleResetVehicleFilter = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset Vehicle Filter By "Status"`,
    })

    dispatch(onLeftPanelResetVehicleFilterChange())
  }

  const handleResetDriverFilter = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset Vehicle Filter By "Driver"`,
    })

    dispatch(onLeftPanelResetDriverFilterChange())
  }

  const handleCarpoolVehicleFilterChange = (
    filterChecked: boolean,
    filterName: MapCarpoolVehicleFilterName,
  ) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Turn ${filterChecked ? 'on' : 'off'} Carpool Filter By "${filterName}"`,
    })

    dispatch(
      onLeftPanelCarpoolVehicleFilterChange({
        filterChecked,
        filterName: filterName,
      }),
    )
  }

  const handleResetCarpoolVehicleFilter = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset Carpool Filter By "Availability"`,
    })

    dispatch(onLeftPanelResetCarpoolVehicleFilter())
  }

  const handleVehicleIconColorsChange = (options: Array<string>) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Change "Vehicle Icon Colors"`,
    })
    dispatch(savePreferences('vehicleIconColors', options))
  }

  const handleResetVehicleIconColors = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset "Vehicle Icon Colors"`,
    })
    dispatch(savePreferences('vehicleIconColors', []))
  }

  return (
    <AccordionBlock header="Vehicles">
      <Stack gap={2}>
        <Box>
          <VehicleSectionSubHeaderContainer>
            <VehicleSectionSubHeaderTitle>
              {ctIntl.formatMessage({ id: 'Status' })}
              {filterCountValue(selectedStatusCount)}
            </VehicleSectionSubHeaderTitle>
            <ResetButton onClick={handleResetVehicleFilter}>
              {ctIntl.formatMessage({ id: 'Reset' })}
            </ResetButton>
          </VehicleSectionSubHeaderContainer>
          {match(currentVehicleFilters)
            .with({ type: 'vehicles' }, ({ value: { filters, filterOptions } }) =>
              Obj_mapValuesToArray(filterOptions, (filter, name) => {
                if (filter === 'not_used') {
                  return null
                }

                if (['driver', 'noDriver'].includes(name)) {
                  return null
                }

                return (
                  <CheckBoxSection key={name}>
                    <SectionFormControlLabel
                      control={
                        <SectionCheckbox checked={filters[name] !== undefined} />
                      }
                      label={ctIntl.formatMessage({ id: filter.label })}
                      onChange={(_, checked) =>
                        handleVehicleFilterChange(checked, name)
                      }
                      name={name}
                    />
                    {match(filter.label)
                      .with('Driving', 'Stationary', () => (
                        <CircleIcon sx={{ fontSize: 20, color: '#43A047' }} />
                      ))
                      .with('Idling', () => (
                        <CircleIcon sx={{ fontSize: 20, color: '#FBC02D' }} />
                      ))
                      .with('Moving - Ignition Off', () => (
                        <CircleIcon sx={{ fontSize: 20, color: '#607D8B' }} />
                      ))
                      .with('Ignition Off', () => (
                        <CircleIcon sx={{ fontSize: 20, color: '#9E9E9E' }} />
                      ))
                      .with('No Signal', () => (
                        <WifiOffIcon
                          sx={{ fontSize: 20, color: 'rgba(0, 0, 0, 0.56)' }}
                        />
                      ))
                      .with('No GPS', () => (
                        <LocationOffIcon
                          sx={{ fontSize: 20, color: 'rgba(0, 0, 0, 0.56)' }}
                        />
                      ))
                      .with('Under Maintenance', () => (
                        <HandymanIcon sx={{ fontSize: 20, color: '#E41515' }} />
                      ))
                      .otherwise(() => (
                        <CircleIcon sx={{ fontSize: 20 }} />
                      ))}
                  </CheckBoxSection>
                )
              }),
            )
            .exhaustive()}
        </Box>
        {userHasCarpool && (
          <Box>
            <VehicleSectionSubHeaderContainer>
              <VehicleSectionSubHeaderTitle>
                {ctIntl.formatMessage({ id: 'Availability' })}
                {filterCountValue(selectedCarpoolFiltersCount)}
              </VehicleSectionSubHeaderTitle>
              <ResetButton onClick={handleResetCarpoolVehicleFilter}>
                {ctIntl.formatMessage({ id: 'Reset' })}
              </ResetButton>
            </VehicleSectionSubHeaderContainer>
            {match(carpoolCurrentVehicleFilters)
              .with({ type: 'carpool' }, ({ value: { filters, filterOptions } }) =>
                Obj_mapValuesToArray(filterOptions, (filter, name) => (
                  <CheckBoxSection key={name}>
                    <SectionFormControlLabel
                      control={
                        <SectionCheckbox checked={filters[name] !== undefined} />
                      }
                      label={ctIntl.formatMessage({ id: filter.label })}
                      onChange={(_, checked) =>
                        handleCarpoolVehicleFilterChange(checked, name)
                      }
                      name={name}
                    />
                    {getCarpoolFilterIcon(filter.icon)}
                  </CheckBoxSection>
                )),
              )
              .otherwise(() => null)}
          </Box>
        )}

        <Box>
          <VehicleSectionSubHeaderContainer>
            <VehicleSectionSubHeaderTitle>
              {ctIntl.formatMessage({ id: 'Colour' })}
              {filterCountValue(vehicleIconColors.length)}
            </VehicleSectionSubHeaderTitle>
            <ResetButton onClick={handleResetVehicleIconColors}>
              {ctIntl.formatMessage({ id: 'Reset' })}
            </ResetButton>
          </VehicleSectionSubHeaderContainer>
          {renderColorsPicker(
            'Vehicle Icon',
            vehicleIconColors,
            handleVehicleIconColorsChange,
          )}
        </Box>

        <Box>
          <VehicleSectionSubHeaderContainer>
            <VehicleSectionSubHeaderTitle>
              {ctIntl.formatMessage({ id: 'Driver' })}
              {filterCountValue(selectedDriverFiltersCount)}
            </VehicleSectionSubHeaderTitle>
            <ResetButton onClick={handleResetDriverFilter}>
              {ctIntl.formatMessage({ id: 'Reset' })}
            </ResetButton>
          </VehicleSectionSubHeaderContainer>
          <RadioGroup
            value={Object.keys(currentDriverFilters.filters)[0] ?? ''}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              handleDriverFilterChange(event.target.value as MapDriverFilter)
            }}
          >
            {Obj_mapValuesToArray(
              currentDriverFilters.filterOptions,
              (filter, name) => (
                <CheckBoxSection key={name}>
                  <SectionFormControlLabel
                    control={
                      <Radio
                        sx={{
                          paddingTop: 0.5,
                          paddingBottom: 0.5,
                          paddingLeft: 1.5,
                          paddingRight: 1,
                        }}
                        size="small"
                      />
                    }
                    value={name}
                    label={ctIntl.formatMessage({ id: filter.label })}
                  />
                  {match(filter.label)
                    .with('Driver Assigned', () => (
                      <PersonIcon
                        sx={({ palette }) => ({
                          fontSize: 20,
                          color: palette.action.active,
                        })}
                      />
                    ))
                    .with('No Driver Assigned', () => (
                      <PersonOffIcon
                        sx={({ palette }) => ({
                          fontSize: 20,
                          color: palette.action.active,
                        })}
                      />
                    ))
                    .otherwise(() => null)}
                </CheckBoxSection>
              ),
            )}
          </RadioGroup>
        </Box>
      </Stack>
    </AccordionBlock>
  )
}

const GeofenceSection = ({
  preferences: { geofenceColors = [] },
}: {
  preferences: UserPreferences
}) => {
  const dispatch = useDispatch()

  const handleGeofenceColorsChange = (options: Array<string>) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Change "Geofence Colors"`,
    })
    dispatch(savePreferences('geofenceColors', options))
  }

  const handleResetGeofenceColors = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset "Geofence Colors"`,
    })
    dispatch(savePreferences('geofenceColors', []))
  }
  return (
    <AccordionBlock header="Geofences">
      <Stack spacing={2}>
        <Box>
          <VehicleSectionSubHeaderContainer>
            <VehicleSectionSubHeaderTitle>
              {ctIntl.formatMessage({ id: 'Colour' })}
              {filterCountValue(geofenceColors.length)}
            </VehicleSectionSubHeaderTitle>
            <ResetButton onClick={handleResetGeofenceColors}>
              {ctIntl.formatMessage({ id: 'Reset' })}
            </ResetButton>
          </VehicleSectionSubHeaderContainer>
          {renderColorsPicker('Geofences', geofenceColors, handleGeofenceColorsChange)}
        </Box>
      </Stack>
    </AccordionBlock>
  )
}

const POIsSection = ({
  preferences: { poiColors = [] },
}: {
  preferences: UserPreferences
}) => {
  const dispatch = useDispatch()

  const handlePOIColorsChange = (options: Array<string>) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Change "POI Colors"`,
    })
    dispatch(savePreferences('poiColors', options))
  }

  const handleResetPOIColors = () => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Reset "POI Colors"`,
    })
    dispatch(savePreferences('poiColors', []))
  }

  return (
    <AccordionBlock header="POIs">
      <Stack gap={2}>
        <Box>
          <VehicleSectionSubHeaderContainer>
            <VehicleSectionSubHeaderTitle>
              {ctIntl.formatMessage({ id: 'Colour' })}
              {filterCountValue(poiColors.length)}
            </VehicleSectionSubHeaderTitle>
            <ResetButton onClick={handleResetPOIColors}>
              {ctIntl.formatMessage({ id: 'Reset' })}
            </ResetButton>
          </VehicleSectionSubHeaderContainer>

          {renderColorsPicker('POIs', poiColors, handlePOIColorsChange)}
        </Box>
      </Stack>
    </AccordionBlock>
  )
}

export default LeftPanelFilters

const ColorSection = styled(Box)({
  width: '100%',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
})

const ColorIcon = styled(CircleIcon, {
  shouldForwardProp: (prop) => prop !== 'iconColor',
})<{ iconColor: (typeof colors)[number]['hex'] }>(({ iconColor }) => ({
  color: `${iconColor}80`,
  fontSize: '20px',
}))
