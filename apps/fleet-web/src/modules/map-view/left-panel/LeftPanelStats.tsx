import { useMemo } from 'react'
import { Box, styled, Tooltip, Typography } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { getMapVehicles } from '@fleet-web/duxs/map'
import type { MapType } from '@fleet-web/duxs/map-types'
import { getDaysUntilLostVisibilitySetting } from '@fleet-web/duxs/user'
import {
  getCarpool,
  getComputedPrivacyHideLocationsFromDay,
  getShowCarpoolStats,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { getVehicles } from '@fleet-web/duxs/vehicles'
import { BookingStatusMapping } from '@fleet-web/modules/carpool/utils/constants'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import SingleInfo from '@fleet-web/util-components/singleInfo'

type Props = {
  mapType: MapType | ''
  itemCount: string | number
  numOfItemsInLine?: number
}

const LeftPanelStats = ({ mapType, itemCount, numOfItemsInLine = 4 }: Props) => {
  const daysUntilLostVisibility = useTypedSelector(getDaysUntilLostVisibilitySetting)
  const mapVehicles = useTypedSelector(getMapVehicles)
  const allVehicles = useTypedSelector(getVehicles)
  const privacyHideLocationsFromDay = useTypedSelector(
    getComputedPrivacyHideLocationsFromDay,
  )
  const showCarpoolStats = useTypedSelector(getShowCarpoolStats)
  const hasCarpool = useTypedSelector(getCarpool)

  const itemsMetaData = useMemo((): {
    visibleCount: number
    labelTranslationIds: { visible: string; notVisible: string }
  } => {
    const defaultLabelTranslationIds = {
      visible: ctIntl.formatMessage(
        { id: 'map.stats.vehiclesVisible.tooltip.v2' },
        { values: { count: daysUntilLostVisibility } },
      ),
      notVisible: ctIntl.formatMessage(
        { id: 'map.stats.vehiclesLostVisibility.tooltip.v2' },
        { values: { count: daysUntilLostVisibility } },
      ),
    }

    if (privacyHideLocationsFromDay < 0) {
      return {
        visibleCount: allVehicles.filter((item) => {
          if ('wasActiveInLastXDays' in item) {
            return item.wasActiveInLastXDays
          }
          return true
        }).length,
        labelTranslationIds: {
          visible: ctIntl.formatMessage(
            { id: 'map.stats.vehiclesVisible.tooltip.lastCommunicated.v2' },
            { values: { count: daysUntilLostVisibility } },
          ),
          notVisible: ctIntl.formatMessage(
            { id: 'map.stats.vehiclesLostVisibility.tooltip.lastCommunicated.v2' },
            { values: { count: daysUntilLostVisibility } },
          ),
        },
      }
    }

    return {
      visibleCount: mapVehicles.length,
      labelTranslationIds: defaultLabelTranslationIds,
    }
  }, [
    daysUntilLostVisibility,
    privacyHideLocationsFromDay,
    mapVehicles.length,
    allVehicles,
  ])

  const {
    visibleCount: activeItemCount,
    labelTranslationIds: visibleVehiclesTooltipLabels,
  } = itemsMetaData

  const carpoolInfo = useMemo(() => {
    if (!hasCarpool) {
      return undefined
    }
    const stats = {
      maintenance: 0,
      issued: 0,
      notAssigned: 0,
    }

    for (const v of mapVehicles) {
      if (v.statusClassName === 'maintenance') {
        stats.maintenance++
      }
      if (
        BookingStatusMapping.free.some((s) => v.carpoolStatus && s === v.carpoolStatus)
      ) {
        stats.notAssigned++
      }
      if (
        BookingStatusMapping.issued.some(
          (s) => v.carpoolStatus && s === v.carpoolStatus,
        )
      ) {
        stats.issued++
      }
    }

    return stats
  }, [hasCarpool, mapVehicles])

  const buildStatsGenericComponent = (
    data: Array<{
      key: string
      value: string | number
      tooltipLabel?: string
    }>,
  ) =>
    data.map((statsItem) => (
      <Tooltip
        arrow
        key={statsItem.key}
        title={statsItem.tooltipLabel ?? undefined}
      >
        <SingleInfo>
          <StatsValue>{statsItem.value}</StatsValue>
          <StatsDescription>
            {ctIntl.formatMessage(
              {
                id: statsItem.key,
                defaultMessage: statsItem.key,
              },
              { values: { count: statsItem.value } },
            )}
          </StatsDescription>
        </SingleInfo>
      </Tooltip>
    ))

  const renderStats = () => {
    const inVehicleContext = mapType === 'fleet' || mapType === 'vehicles'

    const statsLabel = getStatsLabelFromMapType(mapType)
    const genericStatsData = [
      {
        key: statsLabel,
        value: itemCount,
        tooltipLabel: inVehicleContext
          ? ctIntl.formatMessage({ id: 'map.leftPanel.stats.vehicles.tooltip' })
          : undefined,
      },
    ]

    if (mapType !== 'fleet') {
      return buildStatsGenericComponent(genericStatsData)
    }

    const basicStats: Array<{
      key: string
      value: string | number
      tooltipLabel?: string
    }> = [
      ...genericStatsData,
      {
        key: 'Visible',
        value: activeItemCount,
        tooltipLabel: inVehicleContext
          ? visibleVehiclesTooltipLabels?.visible
          : undefined,
      },
      {
        key: 'Lost Visibility',
        value: Number.parseInt(itemCount.toString(), 10) - activeItemCount,
        tooltipLabel: inVehicleContext
          ? visibleVehiclesTooltipLabels?.notVisible
          : undefined,
      },
    ]

    if (carpoolInfo) {
      // CarPool specific info
      const carpoolStats = [
        {
          // TODO: Maintenance status will be part of FLEET core
          // Move to 'basicStats' once that change is done
          key: 'In Maintenance',
          value: carpoolInfo?.maintenance || 0,
        },
      ]

      if (showCarpoolStats) {
        carpoolStats.push(
          {
            key: 'Issued',
            value: carpoolInfo?.issued || 0,
          },
          {
            key: 'Not Assigned',
            value: carpoolInfo?.notAssigned || 0,
          },
        )
      }

      basicStats.push(...carpoolStats)
    }

    return buildStatsGenericComponent(basicStats)
  }

  return <StatsWrap numOfItemsInLine={numOfItemsInLine}>{renderStats()}</StatsWrap>
}

const getStatsLabelFromMapType = (mapType: Props['mapType']) =>
  match(mapType)
    .with('fleet', 'vehicles', () => 'map.leftPanel.stats.vehicles')
    .with('fullscreen', () => '')
    .with('svr-units', () => 'map.leftPanel.stats.svrUnits')
    .with('mini-tracker', () => 'map.leftPanel.stats.miniTracker')
    .with('active-rf-id', () => 'map.leftPanel.stats.activeRfId')
    .with('point-units', 'point units', () => 'map.leftPanel.stats.pointUnits')
    .with('asset-trackers', 'asset trackers', () => 'map.leftPanel.stats.assetTrackers')
    .with('devices', () => 'map.leftPanel.stats.devices')
    .with('location-only', () => 'map.leftPanel.stats.location-only')
    .otherwise(() => '')

const StatsWrap = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'numOfItemsInLine',
})<{ numOfItemsInLine: number }>(({ theme, numOfItemsInLine }) =>
  theme.unstable_sx({
    display: 'grid',
    gridTemplateColumns: `repeat(${numOfItemsInLine}, minmax(0, auto))`,
  }),
)

const Results = styled(Typography)({
  fontSize: '12px',
})

const StatsValue = styled(Results)({
  fontWeight: 500,
})

const StatsDescription = styled(Typography)({
  fontSize: '12px',
  color: 'rgba(0, 0, 0, 0.6)',
  whiteSpace: 'nowrap',
  textAlign: 'center',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  width: '100%',
})

export default LeftPanelStats
