import { Fragment } from 'react'
import {
  Box,
  ClickAwayListener,
  Radio,
  RadioGroup,
  Stack,
  Switch,
} from '@karoo-ui/core'
import { useDispatch } from 'react-redux'
import { match } from 'ts-pattern'

import SettingsPanel from '@fleet-web/components/_map/_panels/Settings'
import type { MapType } from '@fleet-web/duxs/map-types'
import {
  savePreferences,
  type LeftPanelVehicleSubTitles,
  type MapVehicleLabels,
  type UserPreferences,
} from '@fleet-web/duxs/user'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { formatSimpleMessage } from '@fleet-web/util-functions/intl-utils'

import {
  AccordionBlock,
  CheckBoxSection,
  SectionCheckbox,
  SectionFormControlLabel,
  SectionTitle,
  VehicleSectionSubHeaderTitle,
} from './shared/utils'
import { MapSettingSections } from './types'

const mapSettingSectionsInOrder = [
  MapSettingSections.VEHICLES,
  MapSettingSections.GEOFENCES,
  MapSettingSections.POIS,
  MapSettingSections.VEHICLE_GROUPS,
]

const vehicleLabelOptions = [
  {
    label: 'Vehicle Name',
    value: 'name',
  },
  {
    label: 'Registration',
    value: 'registration',
  },
  {
    label: 'Description',
    value: 'description',
  },
  {
    label: 'Description 2',
    value: 'description1',
  },
  {
    label: 'Description 3',
    value: 'description2',
  },
  {
    label: 'Odometer',
    value: 'odometer',
  },
  {
    label: 'Driver',
    value: 'driver',
  },
  {
    label: 'Clock (Raw)',
    value: 'unitRawClock',
  },
  {
    label: 'Location',
    value: 'location',
  },
] as const satisfies ReadonlyArray<{
  label: string
  value: keyof UserPreferences['mapVehicleLabels']
}>

const radioOptions = [
  {
    label: 'Vehicle Name',
    value: 'name',
  },
  {
    label: 'Registration',
    value: 'registration',
  },
  {
    label: 'Description',
    value: 'description',
  },
  {
    label: 'Description 2',
    value: 'description1',
  },
  {
    label: 'Description 3',
    value: 'description2',
  },
] as const satisfies ReadonlyArray<{
  label: string
  value: UserPreferences['vehicleDisplayName']
}>

const labelOptionals = [
  { label: 'Score', value: 'ratingLabel' },
  { label: 'Driver Name', value: 'driverLabel' },
  { label: 'Vehicle Group', value: 'vehicleGroup' },
] as const satisfies ReadonlyArray<{
  label: string
  value: keyof UserPreferences['leftPanelVehicleSubTitles']
}>

type Props = {
  preferences: UserPreferences
  selectedSortMethod: 'alphabetical' | 'status'
  type: '' | MapType
  onSettingsPanelClickAway: () => void
}

const LeftPanelSettings = ({ preferences, type, onSettingsPanelClickAway }: Props) => (
  <ClickAwayListener onClickAway={onSettingsPanelClickAway}>
    <SettingsPanel
      sx={{
        position: 'absolute',
        left: variables.redesignMapLeftPanelWidth,
        overflow: 'auto',
        marginLeft: 0,
        zIndex: 2,
        boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
      }}
    >
      <Box
        px={2}
        pt={2}
      >
        <SectionTitle>
          {ctIntl.formatMessage({ id: 'Vehicle display options' })}
        </SectionTitle>
      </Box>
      {mapSettingSectionsInOrder.map((section) => (
        <Fragment key={section}>
          {match(section)
            .with(MapSettingSections.VEHICLES, () => (
              <VehicleSection preferences={preferences} />
            ))
            .with(MapSettingSections.VEHICLE_GROUPS, () => (
              <VehicleGroupsSection
                type={type}
                preferences={preferences}
              />
            ))
            .otherwise(() => null)}
        </Fragment>
      ))}
    </SettingsPanel>
  </ClickAwayListener>
)

const VehicleSection = ({
  preferences: { vehicleDisplayName, leftPanelVehicleSubTitles, mapVehicleLabels },
}: {
  preferences: UserPreferences
}) => {
  const dispatch = useDispatch()

  const handleRadioChange = (value: (typeof radioOptions)[number]['value']) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Filter "Vehicle Display Name"`,
    })

    dispatch(savePreferences('vehicleDisplayName', value))
  }

  const handleLeftPanelVehicleSubTitlesChange = (value: LeftPanelVehicleSubTitles) => {
    const vehicleSubTitleSelectedCount = Object.values(value).filter(Boolean).length
    if (vehicleSubTitleSelectedCount <= 2) {
      GA4.event({
        category: 'Map - Left Panel Settings',
        action: `Change "Vehicle Sub title"`,
      })
      dispatch(savePreferences('leftPanelVehicleSubTitles', value))
    }
  }

  const handleMapVehicleLabelsChange = (value: MapVehicleLabels) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Change "Vehicle Labels"`,
    })
    dispatch(savePreferences('mapVehicleLabels', value))
  }

  return (
    <AccordionBlock header="Vehicles">
      <Stack gap={2}>
        <Box>
          <VehicleSectionSubHeaderTitle>
            {ctIntl.formatMessage({ id: 'Vehicle Display Name' })}
          </VehicleSectionSubHeaderTitle>
          <RadioGroup
            name="vehicleDisplayName"
            value={vehicleDisplayName}
            onChange={(_, value) =>
              handleRadioChange(value as (typeof radioOptions)[number]['value'])
            }
          >
            {radioOptions.map((option) => (
              <SectionFormControlLabel
                key={option.value}
                value={option.value}
                control={
                  <Radio
                    size="small"
                    sx={{
                      paddingTop: 0.7,
                      paddingBottom: 0.7,
                      paddingLeft: 1.5,
                      paddingRight: 1,
                    }}
                  />
                }
                label={ctIntl.formatMessage({ id: option.label })}
              />
            ))}
          </RadioGroup>
        </Box>
        <Box>
          <VehicleSectionSubHeaderTitle>
            {ctIntl.formatMessage({ id: 'map.setting.vehicleSubtitle' })}
          </VehicleSectionSubHeaderTitle>
          {labelOptionals.map((option) => (
            <CheckBoxSection key={option.value}>
              <SectionFormControlLabel
                disabled={
                  Object.values(leftPanelVehicleSubTitles).filter(Boolean).length > 1 &&
                  !leftPanelVehicleSubTitles[option.value]
                }
                control={
                  <SectionCheckbox checked={leftPanelVehicleSubTitles[option.value]} />
                }
                label={ctIntl.formatMessage({ id: option.label })}
                onChange={(_, checked) =>
                  handleLeftPanelVehicleSubTitlesChange({
                    ...leftPanelVehicleSubTitles,
                    [option.value]: checked,
                  })
                }
                name={option.value}
              />
            </CheckBoxSection>
          ))}
        </Box>
        <Box>
          <VehicleSectionSubHeaderTitle>
            {ctIntl.formatMessage({ id: 'map.setting.vehicleMapLabels' })}
          </VehicleSectionSubHeaderTitle>
          {vehicleLabelOptions.map((option) => (
            <CheckBoxSection key={option.value}>
              <SectionFormControlLabel
                control={<SectionCheckbox checked={mapVehicleLabels[option.value]} />}
                label={ctIntl.formatMessage({ id: option.label })}
                onChange={(_, checked) =>
                  handleMapVehicleLabelsChange({
                    ...mapVehicleLabels,
                    [option.value]: checked,
                  })
                }
                name={option.value}
              />
            </CheckBoxSection>
          ))}
        </Box>
      </Stack>
    </AccordionBlock>
  )
}

const VehicleGroupsSection = ({
  type,
  preferences: { mapViewGroups, useVehicleIconColor },
}: {
  type: '' | MapType
  preferences: UserPreferences
}) => {
  const dispatch = useDispatch()

  const handleToggleGroups = (value: boolean) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Turn ${value ? 'on' : 'off'} "Enable Groups"`,
    })

    dispatch(
      savePreferences('mapViewGroups', {
        ...mapViewGroups,
        [type]: value,
      }),
    )
  }

  const handleToggleVehicleIconColor = (value: boolean) => {
    GA4.event({
      category: 'Map - Left Panel Settings',
      action: `Turn ${value ? 'on' : 'off'} "Use Vehicle Icon Colour"`,
    })

    dispatch(savePreferences('useVehicleIconColor', value))
  }

  return (
    <AccordionBlock header="Vehicle Groups">
      <SectionFormControlLabel
        control={
          <Switch
            size="small"
            name="enableGroups"
            onChange={(_, checked) => handleToggleGroups(checked)}
            checked={mapViewGroups[type]}
          />
        }
        label={ctIntl.formatMessage({ id: 'Enable Groups' })}
      />
      <SectionFormControlLabel
        control={
          <Switch
            size="small"
            name="useVehicleIconColor"
            onChange={(_, checked) => handleToggleVehicleIconColor(checked)}
            checked={useVehicleIconColor}
          />
        }
        label={ctIntl.formatMessage({
          id: formatSimpleMessage('Use Vehicle Icon Color'),
        })}
      />
    </AccordionBlock>
  )
}

export default LeftPanelSettings
