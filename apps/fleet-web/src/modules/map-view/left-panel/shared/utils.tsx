import type { ReactNode } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Checkbox,
  FormControlLabel,
  ListItemButton,
  OverflowTypography,
  Stack,
  styled,
  Tooltip,
  Typography,
  type TypographyProps,
} from '@karoo-ui/core'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export const CellListEntry = styled(ListItemButton)(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    flexDirection: 'row',
    cursor: 'pointer',
    height: '76px',
    alignItems: 'center',
    gap: 2,
    py: 1,
    px: 2,
    '&:not(last-child)': {
      borderBottom: '1px solid #eee',
    },
  }),
)

export const CellListEntryTooltipMenu = ({
  title,
  children,
}: {
  title: React.ReactNode
  children: React.ReactElement
}) => (
  <Tooltip
    placement="right"
    disableFocusListener
    slotProps={{
      tooltip: {
        sx: {
          marginLeft: '-15px !important',
          marginTop: '80px !important',
          bgcolor: 'white',
          boxShadow: '0px 3px 14px 2px #0000001F',
          color: 'text.primary',
          padding: 0,
        },
      },
    }}
    PopperProps={{ sx: { zIndex: 1200 } }}
    title={title}
  >
    {children}
  </Tooltip>
)

export const CellIcon = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'bgColor',
})<{ bgColor: string }>(({ bgColor }) => ({
  width: '36px',
  minWidth: '36px',
  height: '36px',
  minHeight: '36px',
  background: bgColor,
  borderRadius: '50%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
}))

export const CellWrap = styled(Stack)({
  flexShrink: 1,
  flexGrow: 1,
  minWidth: 0,
})

export const CellTitle = (props: TypographyProps) => (
  <OverflowTypography
    typographyProps={{ variant: 'body2' }}
    tooltipProps={{ arrow: true }}
  >
    {props.children}
  </OverflowTypography>
)

export const CellInfo = (props: TypographyProps) => (
  <OverflowTypography
    typographyProps={{ variant: 'caption' }}
    tooltipProps={{ arrow: true }}
  >
    {props.children}
  </OverflowTypography>
)

export const SectionTitle = (props: TypographyProps) => (
  <OverflowTypography
    typographyProps={{ variant: 'subtitle2' }}
    tooltipProps={{ arrow: true }}
  >
    {props.children}
  </OverflowTypography>
)

export const CheckBoxSection = styled(Box)({
  alignItems: 'center',
  display: 'flex',
  justifyContent: 'space-between',
})

export const VehicleSectionSubHeaderTitle = styled(Typography)(({ theme }) =>
  theme.unstable_sx({
    color: theme.palette.secondary.light,
    marginBottom: 1,
  }),
)

export const VehicleSectionSubHeaderContainer = styled(Stack)({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
})

export const SectionFormControlLabel = styled(FormControlLabel)(({ theme }) =>
  theme.unstable_sx({
    '.MuiSwitch-root': {
      margin: 0.5,
    },
  }),
)

export const SectionCheckbox = styled(Checkbox)(({ theme }) =>
  theme.unstable_sx({
    paddingTop: 0.5,
    paddingBottom: 0.5,
    paddingLeft: 1.5,
    paddingRight: 1,
  }),
)

SectionCheckbox.defaultProps = {
  size: 'small',
}

export const AccordionBlock = ({
  header,
  children,
}: {
  header: string
  children: ReactNode
}) => (
  <Accordion
    defaultExpanded
    disableGutters
    sx={{
      borderRadius: 0,
      boxShadow: 'none',
      borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
    }}
  >
    <AccordionSummary
      sx={{
        minHeight: 'unset',
      }}
      expandIcon={<ExpandMoreIcon />}
    >
      <SectionTitle>{ctIntl.formatMessage({ id: header })}</SectionTitle>
    </AccordionSummary>

    <AccordionDetails
      sx={{
        paddingLeft: 2,
        paddingRight: 2,
        paddingBottom: 2,
        paddingTop: 0,
      }}
    >
      {children}
    </AccordionDetails>
  </Accordion>
)
