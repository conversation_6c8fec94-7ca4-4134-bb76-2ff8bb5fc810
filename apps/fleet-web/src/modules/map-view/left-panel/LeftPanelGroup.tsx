import { useMemo } from 'react'
import { countBy } from 'lodash'
import { Stack, styled, Typography } from '@karoo-ui/core'
import FolderOpenOutlinedIcon from '@mui/icons-material/FolderOpenOutlined'

import type { VehicleGroupId } from '@fleet-web/api/types'
import type { ReduxVehicles } from '@fleet-web/duxs/vehicles'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useMapViewSearchContext } from '../MapViewSearchProvider'
import { CellIcon, CellInfo, CellListEntry, CellTitle, CellWrap } from './shared/utils'

type Props = {
  type: string
  items: ReduxVehicles
  onGroupClick: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
  id: VehicleGroupId
  name: string
}

const countGroupItems = (items: ReduxVehicles) =>
  countBy(items, (i) => {
    if (
      i.statusClassName.includes('no-signal') ||
      i.statusClassName === 'sleeper-berth' ||
      i.statusClassName === 'ignition-off'
    ) {
      return 'off'
    }

    return i.statusClassName
  })

const Group = ({ type, name, items, onGroupClick, id }: Props) => {
  const { highlightText } = useMapViewSearchContext()
  const groupItemsCounts = useMemo(() => countGroupItems(items), [items])
  return (
    <CellListEntry
      id={id}
      onClick={onGroupClick}
    >
      <CellIcon bgColor="#eee">
        <FolderOpenOutlinedIcon sx={{ fontSize: '20px' }} />
      </CellIcon>
      <CellWrap>
        <CellTitle>{highlightText(name)}</CellTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
        >
          <CellInfo>
            {items.length}{' '}
            {ctIntl.formatMessage(
              { id: 'map.leftPanel.stats.vehicles' },
              { values: { count: items.length } },
            )}
          </CellInfo>
          <Stack
            direction="row"
            gap={1}
          >
            <CountCircle colorPal="green">{groupItemsCounts.driving || 0}</CountCircle>
            {type === 'vehicles' && (
              <CountCircle colorPal="green">{groupItemsCounts.idling || 0}</CountCircle>
            )}
            {type === 'drivers' && (
              <CountCircle colorPal="green">
                {groupItemsCounts['on-duty'] || 0}
              </CountCircle>
            )}
            {type === 'drivers' && (
              <CountCircle colorPal="green">
                {groupItemsCounts.driving || 0}
              </CountCircle>
            )}
            <CountCircle colorPal="grey">{groupItemsCounts.off || 0}</CountCircle>
          </Stack>
        </Stack>
      </CellWrap>
    </CellListEntry>
  )
}

export default Group

const CountCircle = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'colorPal',
})<{ colorPal: 'green' | 'grey' }>(({ colorPal }) => ({
  padding: '2px 8px',
  borderRadius: '100px',
  background: colorPal === 'green' ? '#4CAF50' : 'rgba(0, 0, 0, 0.08)',
  color: colorPal === 'green' ? '#fff' : 'rgba(0, 0, 0, 0.87)',
  fontSize: '12px',
  letterSpacing: '0.4px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}))
