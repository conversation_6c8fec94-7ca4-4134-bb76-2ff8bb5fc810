import type * as React from 'react'
import { forwardRef } from 'react'
import { Box } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import type { DriverId, VehicleId } from '@fleet-web/api/types'
import type { UserPreferences } from '@fleet-web/duxs/user'
import type { FixMeAny } from '@fleet-web/types'
import type { ExtractStrict } from '@fleet-web/types/utils'

import Vehicle from '../FleetMapView/LeftPanel/Vehicle'
import type { LeftPanelOpenSidePanel } from '../map-view'
import type { ResolvedSearchItems } from '../types'
import Geofence from './Geofence'
import Landmark from './Landmark'
import Group from './LeftPanelGroup'
import Place from './Place'
import type { VehicleContextMenuPositionState } from './types'

export type ListItemProps = {
  data: ResolvedSearchItems[number]
  leftPanelOpenSidePanel: LeftPanelOpenSidePanel
  events: {
    onFocusGroup: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
    onPlaceClick: (
      event: React.MouseEvent<HTMLDivElement, MouseEvent>,
      place: ExtractStrict<ResolvedSearchItems[number], { type: 'place' }>,
    ) => void
    onSearchItemMouseEnter: (...args: Array<FixMeAny>) => void
    onSearchItemMouseLeave: (...args: Array<FixMeAny>) => void
    onVehicleClick: (event: { vehicleId: VehicleId }) => void
    onDriverScoreClick: (driverId: DriverId) => void
    onVehicleContextMenu: (
      event: React.MouseEvent<HTMLDivElement>,
      { vehicleId }: { vehicleId: string },
    ) => void
    onVehicleContextMenuClose: ({ vehicleId }: { vehicleId: string }) => void
    onVehicleContextMenuClickOutside: ({ vehicleId }: { vehicleId: string }) => void
    getDriverScoreColor: (score: number) => {
      textColor: string
      backgroundColor: string
    }
  }
  vehicleContextMenuPosition: VehicleContextMenuPositionState
  parentProps: {
    driverScoreColorArr: [string, string, string]
    focusedItem: FixMeAny
    preferences: UserPreferences
    driverScores: {
      data: Record<string, number> | undefined
      isLoading: boolean
    } | null
  }
  style: React.CSSProperties
}

const ListItem = forwardRef<HTMLDivElement, ListItemProps>(function ListItem(
  {
    data,
    style,
    leftPanelOpenSidePanel,
    vehicleContextMenuPosition,
    events: {
      onFocusGroup,
      onPlaceClick,
      onSearchItemMouseEnter,
      onSearchItemMouseLeave,
      onVehicleClick,
      onVehicleContextMenu,
      onVehicleContextMenuClose,
      onVehicleContextMenuClickOutside,
      onDriverScoreClick,
      getDriverScoreColor,
    },
    parentProps: { focusedItem, preferences, driverScoreColorArr, driverScores },
  },
  ref,
) {
  return (
    <Box
      ref={ref}
      key={'id' in data ? data.id : undefined}
      style={style}
    >
      {match(data)
        .with({ type: 'group' }, (matchedData) => (
          <Group
            type={matchedData.itemType}
            items={matchedData.items}
            onGroupClick={onFocusGroup}
            id={matchedData.id}
            name={matchedData.name}
          />
        ))
        .with({ type: 'place' }, (matchedData) => (
          <Place
            id={matchedData.id}
            onClick={(e) => onPlaceClick(e, matchedData)}
            address={matchedData.address ?? ''}
          />
        ))
        .with({ type: 'landmark' }, (matchedData) => (
          <Landmark
            item={matchedData}
            onMouseEnter={onSearchItemMouseEnter}
            onMouseLeave={onSearchItemMouseLeave}
          />
        ))
        .with({ type: 'geofence' }, (matchedData) => (
          <Geofence
            item={matchedData}
            onMouseEnter={onSearchItemMouseEnter}
            onMouseLeave={onSearchItemMouseLeave}
          />
        ))
        .otherwise((matchedData) => {
          const vehicle = matchedData as ExtractStrict<
            ResolvedSearchItems[number],
            { id: VehicleId }
          >
          return (
            <Vehicle
              isFocused={(focusedItem && focusedItem.id === vehicle.id) || false}
              vehicle={vehicle}
              onVehicleClick={onVehicleClick}
              onDriverScoreClick={onDriverScoreClick}
              leftPanelOpenSidePanel={leftPanelOpenSidePanel}
              viewPreferences={preferences}
              handleContextMenu={onVehicleContextMenu}
              vehicleContextMenuPosition={vehicleContextMenuPosition}
              handleContextMenuClose={onVehicleContextMenuClose}
              handleContextMenuClickOutside={onVehicleContextMenuClickOutside}
              getDriverScoreColor={getDriverScoreColor}
              driverScoreColorArr={driverScoreColorArr}
              driverScores={driverScores?.data}
              isLoadingDriverScores={driverScores?.isLoading ?? false}
            />
          )
        })}
    </Box>
  )
})

export default ListItem
