import { Badge, Box, Button, IconButton, Tooltip } from '@karoo-ui/core'
import CompareIcon from '@mui/icons-material/Compare'
import FilterListIcon from '@mui/icons-material/FilterList'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { LeftPanelOpenSidePanel } from '../map-view'
import { useFilters } from './FiltersProvider'

type Props = {
  enableCompare?: boolean
  onCompareClick: () => void
  onSettingsIconClick: () => void
  onFiltersIconClick: () => void
  showCompareButton?: boolean
  leftPanelOpenSidePanel: LeftPanelOpenSidePanel
}

const LeftPanelSettingsButtons = ({
  enableCompare = true,
  onCompareClick,
  onSettingsIconClick,
  onFiltersIconClick,
  showCompareButton = true,
  leftPanelOpenSidePanel,
}: Props) => {
  const {
    selectedStatusCount,
    selectedDriverFiltersCount,
    selectedCarpoolFiltersCount,
    geofenceColorsCount,
    poiColorsCount,
    vehicleIconColorsCount,
  } = useFilters()

  const hasFilterApplied =
    selectedStatusCount > 0 ||
    selectedDriverFiltersCount > 0 ||
    selectedCarpoolFiltersCount > 0 ||
    geofenceColorsCount > 0 ||
    poiColorsCount > 0 ||
    vehicleIconColorsCount > 0

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gridColumn: 2,
        px: 2,
        pt: 1,
      }}
    >
      {showCompareButton && (
        <Box sx={{ gridColumn: 1 }}>
          <Button
            onClick={onCompareClick}
            color="secondary"
            disabled={!enableCompare}
            startIcon={<CompareIcon fontSize="small" />}
          >
            {ctIntl.formatMessage({
              id: 'Compare',
            })}
          </Button>
        </Box>
      )}
      <Box sx={{ textAlign: 'right', gridColumn: 2 }}>
        <Tooltip
          title={ctIntl.formatMessage({
            id: 'Filters',
          })}
          placement="bottom"
        >
          <IconButton
            size="small"
            sx={(theme) => ({
              bgcolor:
                leftPanelOpenSidePanel === 'filters'
                  ? theme.palette.action.focus
                  : 'inherit',
            })}
            onClick={onFiltersIconClick}
          >
            <Badge
              color="primary"
              variant="dot"
              invisible={!hasFilterApplied}
            >
              <FilterListIcon fontSize="small" />
            </Badge>
          </IconButton>
        </Tooltip>
        <Tooltip
          title={ctIntl.formatMessage({
            id: 'Settings',
          })}
          placement="bottom"
        >
          <IconButton
            size="small"
            sx={(theme) => ({
              ml: 0.5,
              bgcolor:
                leftPanelOpenSidePanel === 'settings'
                  ? theme.palette.action.focus
                  : 'inherit',
            })}
            onClick={onSettingsIconClick}
          >
            <SettingsOutlinedIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  )
}

export default LeftPanelSettingsButtons
