import { createContext, useContext, useMemo } from 'react'

import {
  getCarpoolVehiclesFilters,
  getDriversFilters,
  getFilters,
} from '@fleet-web/duxs/map'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import { useTypedSelector } from '@fleet-web/redux-hooks'

type FiltersContextType = {
  selectedStatusCount: number
  selectedDriverFiltersCount: number
  selectedCarpoolFiltersCount: number
  geofenceColorsCount: number
  poiColorsCount: number
  vehicleIconColorsCount: number
}

const FiltersContext = createContext<FiltersContextType>({
  selectedCarpoolFiltersCount: 0,
  selectedDriverFiltersCount: 0,
  selectedStatusCount: 0,
  geofenceColorsCount: 0,
  poiColorsCount: 0,
  vehicleIconColorsCount: 0,
})

const countFilterEntries = ({
  filters,
  excludes,
}: {
  filters: Record<string, any>
  excludes: Array<string>
}): number => {
  const entries = Object.entries(filters)
  const filteredEntries = entries.filter(([key]) => !excludes.includes(key))

  return filteredEntries.length
}

export const FiltersProvider = ({ children }: { children: React.ReactNode }) => {
  const currentVehicleFilters = useTypedSelector(getFilters)
  const currentDriverFilters = useTypedSelector(getDriversFilters)
  const carpoolCurrentVehicleFilters = useTypedSelector(getCarpoolVehiclesFilters)
  const { geofenceColors, poiColors, vehicleIconColors } =
    useTypedSelector(getPreferences)

  const selectedStatusCount = useMemo(
    () =>
      countFilterEntries({
        filters: currentVehicleFilters.value.filters,
        excludes: ['driver', 'noDriver'],
      }),
    [currentVehicleFilters.value.filters],
  )

  const selectedDriverFiltersCount = useMemo(
    () =>
      countFilterEntries({
        filters: currentDriverFilters.filters,
        excludes: [],
      }),
    [currentDriverFilters.filters],
  )

  const selectedCarpoolFiltersCount = useMemo(
    () =>
      countFilterEntries({
        filters: carpoolCurrentVehicleFilters.value.filters,
        excludes: [],
      }),
    [carpoolCurrentVehicleFilters.value.filters],
  )

  const mergedCounts = useMemo(
    () => ({
      selectedStatusCount,
      selectedDriverFiltersCount,
      selectedCarpoolFiltersCount,
      poiColorsCount: poiColors.length,
      geofenceColorsCount: geofenceColors.length,
      vehicleIconColorsCount: vehicleIconColors.length,
    }),
    [
      geofenceColors.length,
      poiColors.length,
      selectedCarpoolFiltersCount,
      selectedDriverFiltersCount,
      selectedStatusCount,
      vehicleIconColors.length,
    ],
  )

  return (
    <FiltersContext.Provider value={mergedCounts}>{children}</FiltersContext.Provider>
  )
}

export const useFilters = (): FiltersContextType => {
  const context = useContext(FiltersContext)
  if (!context) {
    throw new Error('useFilters must be used within a FiltersProvider')
  }
  return context
}
