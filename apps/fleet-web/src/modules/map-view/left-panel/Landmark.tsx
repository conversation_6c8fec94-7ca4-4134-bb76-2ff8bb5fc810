import { MenuItem, MenuList } from '@karoo-ui/core'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router'

import { getFocusedLandmarkId } from '@fleet-web/duxs/map'
import { UserFormattedPositionAddress } from '@fleet-web/modules/components/connected/UserFormattedPositionAddress'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { ExtractStrict } from '@fleet-web/types/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { clickedLeftPanelLandmarkViewOnMap } from '../actions'
import { getPOIDetailsModalMainPath } from '../FleetMapView/POI/utils'
import { useMapViewSearchContext } from '../MapViewSearchProvider'
import type { ResolvedSearchItems } from '../types'
import {
  CellIcon,
  CellInfo,
  CellListEntry,
  CellListEntryTooltipMenu,
  CellTitle,
  CellWrap,
} from './shared/utils'

type Props = {
  item: ExtractStrict<ResolvedSearchItems[number], { type: 'landmark' }>
  onMouseEnter: ({ id, type }: { id: string; type: string }) => void
  onMouseLeave: () => void
}

function Landmark({
  item: { id, color, name, address, description, type, lat, lng },
  onMouseEnter,
  onMouseLeave,
}: Props) {
  const { location, push } = useHistory()
  const { highlightText } = useMapViewSearchContext()
  const dispatch = useDispatch()
  const focusedLandmarkId = useTypedSelector(getFocusedLandmarkId)

  return (
    <CellListEntryTooltipMenu
      title={
        <MenuList>
          <MenuItem
            onClick={() => {
              dispatch(
                clickedLeftPanelLandmarkViewOnMap({ landmark: { lat, lng, id } }),
              )
            }}
          >
            {ctIntl.formatMessage({ id: 'map.poi.viewOnMap' })}
          </MenuItem>
          <MenuItem
            onClick={() => {
              push(getPOIDetailsModalMainPath(location, { poi: 'edit', id }))
              dispatch(
                clickedLeftPanelLandmarkViewOnMap({ landmark: { lat, lng, id } }),
              )
            }}
          >
            {ctIntl.formatMessage({ id: 'map.poi.viewDetails' })}
          </MenuItem>
        </MenuList>
      }
    >
      <CellListEntry
        onMouseEnter={() => onMouseEnter({ id, type })}
        onMouseLeave={onMouseLeave}
        selected={id === focusedLandmarkId}
        onClick={() => {
          dispatch(clickedLeftPanelLandmarkViewOnMap({ landmark: { lat, lng, id } }))
        }}
      >
        <CellIcon bgColor={color}>
          <LocationOnIcon sx={{ color: '#fff', fontSize: '20px' }} />
        </CellIcon>
        <CellWrap>
          <CellTitle>{highlightText(name)}</CellTitle>
          <CellInfo>
            <UserFormattedPositionAddress
              address={address}
              gpsFixType={null}
            />
          </CellInfo>
          {description && <CellInfo>{highlightText(description)}</CellInfo>}
        </CellWrap>
      </CellListEntry>
    </CellListEntryTooltipMenu>
  )
}

export default Landmark
