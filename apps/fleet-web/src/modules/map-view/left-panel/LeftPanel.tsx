import type React from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { compact, isEmpty, noop } from 'lodash'
import {
  Box,
  CircularProgress,
  IconButton,
  Stack,
  styled,
  Typography,
} from '@karoo-ui/core'
import ClearIcon from '@mui/icons-material/Clear'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import FileCopyOutlinedIcon from '@mui/icons-material/FileCopyOutlined'
import { useDispatch } from 'react-redux'
import {
  AutoSizer,
  CellMeasurer,
  CellMeasurerCache,
  List,
  type ListRowProps,
} from 'react-virtualized'

import type { VehicleId } from '@fleet-web/api/types'
import LeftPanelContainer from '@fleet-web/components/_map/_panels/Left'
import LeftPanelBody from '@fleet-web/components/_map/_panels/Left/Body'
import LeftPanelHeader from '@fleet-web/components/_map/_panels/Left/Header'
import {
  closeDetailsPanel,
  focusGroup,
  getFocusedGroup,
  getTypeSortMethod,
} from '@fleet-web/duxs/map'
import type { MapType } from '@fleet-web/duxs/map-types'
import {
  getClustersEnabled,
  getScorecardsSetting,
  type UserPreferences,
} from '@fleet-web/duxs/user'
import {
  getMapCompareVehicles,
  getPreferences,
  type UserAvailableMapApiProvider,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { getVehiclesHaveBeenLoadedAtLeastOnce } from '@fleet-web/duxs/vehicles'
import useDriverScore from '@fleet-web/hooks/useDriverScore'
import {
  clickedClearPlaceSearchButton,
  clickedLeftPanelCompareTripsButton,
  onLeftPanelSearchBoxClear,
} from '@fleet-web/modules/map-view/actions'
import LeftPanelMultiSearchInput from '@fleet-web/modules/map-view/left-panel/LeftPanelMultiSearchInput'
import {
  useDefaultDriverScoresQuery,
  type FetchDefaultDriverScoresData,
} from '@fleet-web/modules/scorecards/DriverScores/api/queries'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { FocusedVehicle } from '../FleetMapView/DetailsPanel/ui-types'
import RePlaces from '../FleetMapView/LeftPanel/Places'
import type { LeftPanelOpenSidePanel } from '../map-view'
import type { MapFleetContextValue } from '../MapFleetProvider'
import { useMapViewSearchContext } from '../MapViewSearchProvider'
import { ListContainer, NoResults, SearchMsg } from '../shared/utils'
import type {
  MapPlaceResult,
  ResolvedPlaceOrGooglePredictionSearchItem,
  ResolvedSearchItems,
} from '../types'
import { FiltersProvider } from './FiltersProvider'
import LeftPanelFilters from './LeftPanelFilters'
import {
  LeftPanelListContextProvider,
  useLeftPanelListContext,
} from './LeftPanelListContext'
import LeftPanelSettings from './LeftPanelSettings'
import LeftPanelSettingsButtons from './LeftPanelSettingsButtons'
import LeftPanelStats from './LeftPanelStats'
import ListItem, { type ListItemProps } from './ListItem'
import type { VehicleContextMenuPositionState } from './types'

type Props = {
  mapType: MapType | ''
  searchInput: string
  visibleLeftPanel: 'places' | 'default' | 'nearbyVehicles'
  isSearching: boolean
  places: Array<MapPlaceResult>
  onSettingsPanelClickAway: () => void
  onSettingsIconClick: () => void
  onFiltersIconClick: () => void
  onFilterPanelClickAway: () => void
  leftPanelOpenSidePanel: LeftPanelOpenSidePanel
  focusedItem: FocusedVehicle | null
  nonCompactedItems: ResolvedSearchItems
  changeSearchType: (value: MapFleetContextValue['searchType']) => void
  searchType: MapFleetContextValue['searchType']
  onSearchInputChange: (event: { target: { value: string } }) => void
  onVehicleClick: (args: { vehicleId: VehicleId }) => void
  mapTypeId: google.maps.MapTypeId
  mapApiProvider: UserAvailableMapApiProvider
  onSearchInputPlaceClick: (
    _event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    placeMeta: ResolvedPlaceOrGooglePredictionSearchItem,
  ) => void
  onLeftPanelPlaceItemClick: ListItemProps['events']['onPlaceClick']
  isFetchingGooglePlaceDetails: boolean
  isFetchingPlacesResultsInPopover: boolean
}

const options = [
  {
    name: 'Fleet Search',
    value: 'fleetSearch',
    renderValue: 'Fleet',
  },
  {
    name: 'Places Search', // equivalent to Google Search
    value: 'placeSearch',
    renderValue: 'Places',
  },
  {
    name: 'Coordinate Search',
    value: 'coordinateSearch',
    renderValue: 'Coordinate',
  },
] satisfies Array<{
  name: string
  value: MapFleetContextValue['searchType']
  renderValue: string
}>

const LeftPanel = ({
  mapType,
  places,
  searchType,
  isSearching,
  searchInput,
  focusedItem,
  onVehicleClick,
  visibleLeftPanel,
  changeSearchType,
  nonCompactedItems,
  onSearchInputChange,
  onSettingsIconClick,
  onFiltersIconClick,
  leftPanelOpenSidePanel,
  onSettingsPanelClickAway,
  onFilterPanelClickAway,
  mapTypeId,
  mapApiProvider,
  onLeftPanelPlaceItemClick,
  onSearchInputPlaceClick,
  isFetchingGooglePlaceDetails,
  isFetchingPlacesResultsInPopover,
}: Props) => {
  const dispatch = useDispatch()
  const { updateSearchTerm } = useMapViewSearchContext()
  const preferences = useTypedSelector(getPreferences)
  const focusedGroup = useTypedSelector(getFocusedGroup)
  const selectedSortMethod = useTypedSelector(getTypeSortMethod)
  const haveVehiclesLoaded = useTypedSelector(getVehiclesHaveBeenLoadedAtLeastOnce)
  const isSearchedPlaceSelected = visibleLeftPanel === 'places'
  const mapCompareVehicles = useTypedSelector(getMapCompareVehicles)
  const isPlaceSearch = Boolean(searchType === 'placeSearch')
  const isCoordinateSearch = Boolean(searchType === 'coordinateSearch')
  const scorecardsEnabled = useTypedSelector(getScorecardsSetting)

  // Fetch driver scores for all drivers - only when scorecards are enabled
  const driverScoresQuery = useDefaultDriverScoresQuery({
    enabled: scorecardsEnabled,
  })

  const noResultMsg = () => {
    if (isPlaceSearch && searchInput === '')
      return (
        <ListContainer>
          <FileCopyOutlinedIcon sx={{ fontSize: '35px', color: '#9E9E9E' }} />
          <SearchMsg>{ctIntl.formatMessage({ id: 'Type to search' })}</SearchMsg>
        </ListContainer>
      )

    if (isCoordinateSearch && searchInput === '')
      return (
        <ListContainer>
          <CoordMessage>
            {ctIntl.formatMessage(
              { id: 'map.coordinateSearch.instruction' },
              {
                values: {
                  ddExample: '1.317448, 103.871963',
                  dmsExample: `1°19'02.8", 103°52'19.1`,
                },
              },
            )}
          </CoordMessage>
          <FileCopyOutlinedIcon sx={{ fontSize: '35px', color: '#9E9E9E' }} />
          <SearchMsg>{ctIntl.formatMessage({ id: 'Type to search' })}</SearchMsg>
        </ListContainer>
      )

    return <NoResults />
  }

  const handleFocusGroup = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    dispatch(focusGroup(event.currentTarget.id || null))
  }

  const closeGroup = () => {
    dispatch(focusGroup(null))
  }

  const items = useMemo(
    () => compact(nonCompactedItems as Array<FixMeAny>) as typeof nonCompactedItems,
    [nonCompactedItems],
  )

  const handleVehicleClick = (args: { vehicleId: VehicleId }) => {
    onVehicleClick(args)
  }

  const normalizeItems = useCallback(() => {
    const normalizedItems = items

    const tabsToNormalize = [
      'fleet',
      'svr-units',
      'mini-tracker',
      'active-rf-id',
      'asset-trackers',
      'asset trackers',
    ] as const

    if (tabsToNormalize.includes(mapType as FixMeAny)) {
      return items.filter(
        (o) =>
          (o as FixMeAny).registration ||
          ['geofence', 'landmark'].includes((o as FixMeAny).type),
      )
    }

    return normalizedItems
  }, [items, mapType])

  const itemsCount = useMemo((): number => {
    if (isPlaceSearch) {
      return places.length
    }
    const normalizedItems = normalizeItems()

    return normalizedItems.length
  }, [isPlaceSearch, normalizeItems, places.length])

  const renderGroupHeader = () => (
    <Stack
      direction="row"
      justifyContent="space-between"
      gap={2}
      alignItems="center"
    >
      <Typography variant="subtitle2">{focusedGroup?.name}</Typography>
      <IconButton onClick={closeGroup}>
        <CloseOutlinedIcon sx={{ fontSize: '20px' }} />
      </IconButton>
    </Stack>
  )

  const onSearchInputTextChange = (e: { target: { value: string } }) => {
    onSearchInputChange(e)
    updateSearchTerm(e.target.value)
    if (!e.target.value) {
      dispatch(onLeftPanelSearchBoxClear())
    }
  }

  const clearInputWithList = () => {
    const isPlaceSearch = searchType === 'placeSearch'
    const isCoordinateSearch = searchType === 'coordinateSearch'

    if (isPlaceSearch || isCoordinateSearch) {
      dispatch(clickedClearPlaceSearchButton())
    }

    onSearchInputTextChange({ target: { value: '' } })
  }

  const renderVirtualizedList = (nonCompactedItems: Props['nonCompactedItems']) => (
    <VirtualizedItemsList
      nonCompactedItems={nonCompactedItems}
      preferences={preferences}
      focusedItem={focusedItem}
      handleFocusGroup={handleFocusGroup}
      handlePlaceClick={onLeftPanelPlaceItemClick}
      handleVehicleClick={handleVehicleClick}
      leftPanelOpenSidePanel={leftPanelOpenSidePanel}
      driverScores={
        scorecardsEnabled
          ? {
              data: driverScoresQuery.data ?? undefined,
              isLoading: driverScoresQuery.isLoading,
            }
          : null
      }
    />
  )

  const isLoading = isSearching || !haveVehiclesLoaded

  const renderNoResults =
    (!isCoordinateSearch && !isLoading && items.length === 0) ||
    (isCoordinateSearch &&
      (items.length === 0 || (items.length > 0 && !(items[0] as FixMeAny).address)))

  const areItemsFiltered = !isEmpty(searchInput) || isPlaceSearch

  return (
    <FiltersProvider>
      <>
        <LeftPanelContainer sx={{ width: variables.redesignMapLeftPanelWidth }}>
          <LeftPanelHeader>
            {focusedGroup ? (
              renderGroupHeader()
            ) : (
              <LeftPanelMultiSearchInput
                endAdornmentBeforeSelect={(() => {
                  if (isFetchingGooglePlaceDetails) {
                    return <CircularProgress size={20} />
                  }

                  if (searchInput !== '') {
                    return (
                      <IconButton onClick={() => clearInputWithList()}>
                        <ClearIcon sx={{ fontSize: '20px' }} />
                      </IconButton>
                    )
                  }

                  return null
                })()}
                searchTextValue={searchInput}
                searchTypeValue={searchType}
                options={options}
                items={items}
                isFetchingPlacesResultsInPopover={isFetchingPlacesResultsInPopover}
                handlePlaceClick={onSearchInputPlaceClick}
                onTextChange={onSearchInputTextChange}
                onTypeChange={(type) => {
                  changeSearchType(type)
                  clearInputWithList()
                }}
                onFocus={() => {
                  if (focusedItem) {
                    dispatch(closeDetailsPanel())
                  }
                }}
              />
            )}
            {!isCoordinateSearch &&
              (areItemsFiltered ? (
                <Box>
                  <Typography sx={{ fontSize: '12px' }}>
                    {itemsCount}{' '}
                    {ctIntl.formatMessage(
                      {
                        id: 'map.leftPanel.stats.results',
                      },
                      { values: { count: itemsCount } },
                    )}
                  </Typography>
                </Box>
              ) : (
                <LeftPanelStats
                  itemCount={itemsCount}
                  mapType={mapType}
                />
              ))}
          </LeftPanelHeader>
          {!isCoordinateSearch && !isPlaceSearch && (
            <LeftPanelSettingsButtons
              enableCompare={mapCompareVehicles}
              showCompareButton={mapType !== 'svr-units'}
              onCompareClick={() => dispatch(clickedLeftPanelCompareTripsButton())}
              onSettingsIconClick={onSettingsIconClick}
              onFiltersIconClick={onFiltersIconClick}
              leftPanelOpenSidePanel={leftPanelOpenSidePanel}
            />
          )}
          <LeftPanelListContextProvider>
            <LeftPanelBody sx={{ overflow: 'hidden' }}>
              {isSearchedPlaceSelected && (
                <RePlaces
                  mapApiProvider={mapApiProvider}
                  mapTypeId={mapTypeId}
                />
              )}
              {!isSearchedPlaceSelected && !isPlaceSearch && (
                <>
                  {isLoading && (
                    <Box
                      sx={{
                        display: 'flex',
                        p: 2,
                        gap: 2,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <CircularProgress />
                    </Box>
                  )}
                  {renderNoResults ? noResultMsg() : renderVirtualizedList(items)}
                </>
              )}
            </LeftPanelBody>
          </LeftPanelListContextProvider>
        </LeftPanelContainer>
        {!focusedItem && leftPanelOpenSidePanel === 'settings' && (
          <LeftPanelSettings
            key={mapType}
            type={mapType}
            selectedSortMethod={selectedSortMethod}
            preferences={preferences}
            onSettingsPanelClickAway={onSettingsPanelClickAway}
          />
        )}
        {!focusedItem && leftPanelOpenSidePanel === 'filters' && (
          <LeftPanelFilters
            key={mapType}
            type={mapType}
            selectedSortMethod={selectedSortMethod}
            preferences={preferences}
            onFilterPanelClickAway={onFilterPanelClickAway}
          />
        )}
      </>
    </FiltersProvider>
  )
}

export default LeftPanel

function VirtualizedItemsList({
  nonCompactedItems,
  leftPanelOpenSidePanel,
  focusedItem,
  handleVehicleClick,
  handleFocusGroup,
  handlePlaceClick,
  preferences,
  driverScores,
}: {
  nonCompactedItems: Props['nonCompactedItems']
  preferences: UserPreferences
  leftPanelOpenSidePanel: Props['leftPanelOpenSidePanel']
  focusedItem: Props['focusedItem']
  handleVehicleClick: (args: { vehicleId: VehicleId }) => void
  handleFocusGroup: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
  handlePlaceClick: ListItemProps['events']['onPlaceClick']
  driverScores: {
    data: FetchDefaultDriverScoresData | undefined
    isLoading: boolean
  } | null
}) {
  const { setScrollInfo } = useLeftPanelListContext()
  const clustersEnabled = useTypedSelector(getClustersEnabled)

  const [vehicleContextMenuPosition, setVehicleContextMenuPosition] =
    useState<VehicleContextMenuPositionState>(null)

  const rootRef = useRef<HTMLDivElement>(null)

  const { onDriverScoreClick, driverScoreColorArr, getDriverScoreColor } =
    useDriverScore()

  const cache = useMemo(
    () =>
      new CellMeasurerCache({
        fixedWidth: true,
        defaultHeight: 76,
      }),
    [],
  )

  useEffect(() => {
    if (rootRef.current) {
      const listEl = rootRef.current.querySelector<HTMLDivElement>(
        '#mapLeftPanelVehicleList',
      )
      if (listEl) {
        const isListScrollable = listEl.scrollHeight > listEl.clientHeight
        const scrollbarWidth = listEl.offsetWidth - listEl.clientWidth

        setScrollInfo({
          scrollable: isListScrollable,
          scrollbarWidth,
        })
      }
    }
  }, [nonCompactedItems.length, setScrollInfo])

  const rowRenderer = useCallback(
    ({ index, key, style, parent }: ListRowProps) => {
      const data = nonCompactedItems[index]

      const onVehicleContextMenuClickOutside = ({
        vehicleId,
      }: {
        vehicleId: string
      }) => {
        if (vehicleId === vehicleContextMenuPosition?.vehicleId) {
          setVehicleContextMenuPosition(null)
        }
      }

      const onVehicleContextMenu = (
        event: React.MouseEvent<HTMLDivElement>,
        { vehicleId }: { vehicleId: string },
      ) => {
        if (!clustersEnabled) {
          return
        }

        event.preventDefault()
        setVehicleContextMenuPosition({
          vehicleId,
          pos: {
            mouseX: event.clientX + 2,
            mouseY: event.clientY - 6,
          },
        })
      }

      const onVehicleContextMenuClose = (_: { vehicleId: string }) => {
        setVehicleContextMenuPosition(null)
      }

      return (
        <CellMeasurer
          key={key}
          cache={cache}
          parent={parent}
          columnIndex={0}
          rowIndex={index}
        >
          {({ registerChild }) => (
            <ListItem
              ref={(el) => registerChild && el && registerChild(el as Element)}
              key={(data as FixMeAny).id}
              data={data}
              style={style}
              leftPanelOpenSidePanel={leftPanelOpenSidePanel}
              parentProps={{
                focusedItem,
                preferences,
                driverScoreColorArr,
                driverScores,
              }}
              vehicleContextMenuPosition={vehicleContextMenuPosition}
              events={{
                onVehicleContextMenu,
                onVehicleContextMenuClose,
                onVehicleContextMenuClickOutside,
                onVehicleClick: handleVehicleClick,
                onDriverScoreClick,
                onFocusGroup: handleFocusGroup,
                onPlaceClick: handlePlaceClick,
                onSearchItemMouseEnter: noop,
                onSearchItemMouseLeave: noop,
                getDriverScoreColor,
              }}
            />
          )}
        </CellMeasurer>
      )
    },
    [
      nonCompactedItems,
      cache,
      vehicleContextMenuPosition,
      clustersEnabled,
      leftPanelOpenSidePanel,
      focusedItem,
      preferences,
      driverScoreColorArr,
      driverScores,
      handleVehicleClick,
      onDriverScoreClick,
      handleFocusGroup,
      handlePlaceClick,
      getDriverScoreColor,
    ],
  )

  return (
    <div
      ref={rootRef}
      className="MapView-leftPanel-listTransitionContainer"
    >
      <AutoSizer>
        {() => (
          <List
            width={Number.parseInt(variables.redesignMapLeftPanelWidth)}
            height={rootRef.current?.clientHeight ?? window.innerHeight - 204}
            rowCount={nonCompactedItems.length}
            rowHeight={cache.rowHeight}
            rowRenderer={rowRenderer}
            deferredMeasurementCache={cache}
            style={{ outline: 'none' }}
            id="mapLeftPanelVehicleList"
          />
        )}
      </AutoSizer>
    </div>
  )
}

const CoordMessage = styled(Typography)({
  color: 'rgba(0, 0, 0, 0.6)',
  fontSize: '12px',
})
