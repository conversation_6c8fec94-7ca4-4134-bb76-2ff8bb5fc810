import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { useQueryErrorHandlerWithSnackbar } from '@fleet-web/api/helpers'
import type { VehicleId } from '@fleet-web/api/types'

export declare namespace Ct_fleet_list_tags {
  type ApiOutput = {
    vehicle_id: VehicleId
    registration: string
    terminal_id: string
    terminal_serial: string
    longitude: number
    latitude: number
    event_ts: string
  }
}

export const useFetchTagsListQuery = () =>
  useQuery({
    queryKey: ['useFetchTagsListQuery'],
    queryFn: () => fetchTagsList(),
    ...useQueryErrorHandlerWithSnackbar(),
  })

const fetchTagsList = () =>
  apiCallerNoX('ct_fleet_list_tags').then((res) =>
    parseTagsList(res as Array<Ct_fleet_list_tags.ApiOutput>),
  )

const parseTagsList = (response: Array<Ct_fleet_list_tags.ApiOutput>) =>
  response.map((res) => ({
    ...res,
    latitude: Number(res.latitude),
    longitude: Number(res.longitude),
  }))
