import { PureComponent, type ComponentProps, type ComponentType } from 'react'
import { isNil, noop } from 'lodash'
import { DateTime } from 'luxon'
import { connect, useDispatch } from 'react-redux'
import type { RouteComponentProps } from 'react-router-dom'
import screenfull from 'screenfull'
import type { Except } from 'type-fest'

import type { VehicleId } from '@fleet-web/api/types'
import { MapApiProvider } from '@fleet-web/api/user/types'
import { MapThemeContextProvider } from '@fleet-web/components/context/MapThemeContext'
import {
  changeViewMode,
  getFocusedItem,
  getItems,
  getMapTypeId,
  getViewMode,
  selectTripSummary,
  setLayerVisibility,
} from '@fleet-web/duxs/map'
import { getTimelineEventsUIDateRange } from '@fleet-web/duxs/timeline'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import {
  clickedVehicleMarker,
  onGoogleMapTypeId<PERSON>hange,
  setNonGoogleMapTypeId,
} from '@fleet-web/modules/map-view/actions'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import { MapAreaThemeContextProvider } from '@fleet-web/util-components/map/shared/MapAreaThemeContext'
import {
  mapViewContainer,
  type MapViewContainerInjectedProps,
} from '@fleet-web/util-components/map/shared/mapViewContainer'
import type { MapProviderMetaData } from '@fleet-web/util-components/map/shared/types'
import { VIEW_MODE } from '@fleet-web/util-functions/constants'

import { useLocalMapApiProviderWithUserRestrictedSetter } from '../../components/connected/useLocalMapApiProviderWithUserRestrictedSetter'
import Map from '../map/map'
import MapOpenLayers from '../map/map-open-layers'
import {
  getFullscreenMapState,
  getPreviousPathname,
  unmountedFullscreenMap,
  type FullscreenMapNavigationState,
} from './slice'

type Props = RouteComponentProps & ReduxProps & MapViewContainerInjectedProps

function FullscreenMap({ mapApiProviderId: TO_IGNORE, mapTypeId, ...rest }: Props) {
  const dispatch = useDispatch()
  const fullscreenMapState = useTypedSelector(getFullscreenMapState)
  const previousPathname = useTypedSelector(getPreviousPathname)
  const {
    currentMapProvider,
    setCurrentMapProvider,
    hasCurrentMapProviderChangedAfterInitialization,
  } = useLocalMapApiProviderWithUserRestrictedSetter({
    currentMapTypeId: mapTypeId,
    setMapTypeId: (selectedMapTypeId) =>
      dispatch(
        currentMapProvider === MapApiProvider.GOOGLE
          ? onGoogleMapTypeIdChange({ selectedMapTypeId })
          : setNonGoogleMapTypeId(selectedMapTypeId),
      ),
  })

  return (
    <Content
      mapTypeId={mapTypeId}
      fullscreenMapState={fullscreenMapState}
      previousPathname={previousPathname}
      onChangeMapTypeId={(selectedMapTypeId) =>
        dispatch(
          currentMapProvider === MapApiProvider.GOOGLE
            ? onGoogleMapTypeIdChange({
                selectedMapTypeId,
              })
            : setNonGoogleMapTypeId(selectedMapTypeId),
        )
      }
      mapProviderMetaData={{
        currentMapProvider,
        selectionUI: {
          onClick: setCurrentMapProvider,
        },
      }}
      hasCurrentMapProviderChangedAfterInitialization={
        hasCurrentMapProviderChangedAfterInitialization
      }
      {...rest}
    />
  )
}

type ContentProps = Except<Props, 'mapApiProviderId'> & {
  fullscreenMapState: FullscreenMapNavigationState
  previousPathname: string
  mapProviderMetaData: MapProviderMetaData
  hasCurrentMapProviderChangedAfterInitialization: boolean
  onChangeMapTypeId: (type: google.maps.MapTypeId) => void
}

type State = {
  mapObject: MapsExtended.MapObject | undefined
}

class Content extends PureComponent<ContentProps, State> {
  state: State = {
    mapObject: undefined,
  }

  componentDidMount = () => {
    const { focusItem, selectTripSummary, changeViewMode, fullscreenMapState } =
      this.props

    if (fullscreenMapState.focusedItem) {
      if (fullscreenMapState.focusedItemStartDate) {
        selectTripSummary(
          fullscreenMapState.selectedType,
          fullscreenMapState.focusedItem,
          fullscreenMapState.focusedItemStartDate,
        )
      } else {
        focusItem(
          fullscreenMapState.selectedType,
          fullscreenMapState.focusedItem,
          'default',
        )
      }
    }

    changeViewMode(VIEW_MODE.fullscreen)
  }

  componentDidUpdate(prevProps: ContentProps) {
    const { viewMode } = this.props
    if (prevProps.viewMode !== viewMode && viewMode !== VIEW_MODE.fullscreen) {
      this.props.history.push({
        pathname: this.props.previousPathname,
        search: this.props.location.search,
      })
    }
  }

  componentWillUnmount() {
    this.props.unmountedFullscreenMap()
    if (screenfull) {
      screenfull.exit()
      return
    }
  }

  handleFullscreenEscape = () => {
    const { focusedItem, focusedItemStartDate, selectedType } =
      this.props.fullscreenMapState

    if (!isNil(focusedItem)) {
      this.props.selectTripSummary(
        selectedType,
        focusedItem,
        focusedItemStartDate ?? DateTime.local().toJSDate(),
      )
    }
  }

  handleVehicleMarkerClick = (vehicleId: VehicleId) =>
    this.props.clickedVehicleMarker({ vehicleId })

  handleMapsApiLoaded: MapsExtended.OnGoogleApiLoaded = (mapObject) => {
    this.setState({ mapObject })
  }

  render() {
    const {
      changeViewMode,
      history,
      location,
      mapHeight,
      mapProviderMetaData,
      useVehicleIconColor,
      vehicleDisplayName,
      focusedItem,
      timelineStartTime,
      hasCurrentMapProviderChangedAfterInitialization,
      mapTypeId,
      onChangeMapTypeId,
      viewMode,
      fullscreenMapState,
    } = this.props
    const { mapObject } = this.state

    return (
      <MapThemeContextProvider mapTypeId={mapTypeId}>
        <MapAreaThemeContextProvider fullscreen={viewMode === VIEW_MODE.fullscreen}>
          {mapProviderMetaData.currentMapProvider === MapApiProvider.GOOGLE ? (
            <Map
              mapTypeId={mapTypeId}
              onChangeMapTypeId={onChangeMapTypeId}
              changeViewMode={changeViewMode}
              isFullscreen
              history={history as FixMeAny}
              location={location as FixMeAny}
              mapProviderMetaData={{
                currentMapProvider: mapProviderMetaData.currentMapProvider,
                selectionUI: mapProviderMetaData.selectionUI,
              }}
              mapHeight={mapHeight}
              onFullscreenEscape={this.handleFullscreenEscape}
              onVehicleMarkerClick={this.handleVehicleMarkerClick}
              viewMode={VIEW_MODE.fullscreen}
              mapObject={mapObject}
              onMapsApiLoaded={this.handleMapsApiLoaded}
              disableZoomOnMount
              places={[]}
              focusedItem={focusedItem}
              selectedType={fullscreenMapState.selectedType}
              type={fullscreenMapState.type}
              vehicleDisplayName={vehicleDisplayName}
              useVehicleIconColor={useVehicleIconColor}
              timelineStartTime={timelineStartTime}
            />
          ) : (
            <MapOpenLayers
              focusVehiclesOnMapMountAndVehiclesSizeChange={
                !hasCurrentMapProviderChangedAfterInitialization
              }
              mapProviderMetaData={{
                currentMapProvider: mapProviderMetaData.currentMapProvider,
                selectionUI: mapProviderMetaData.selectionUI,
              }}
              mapTypeId={mapTypeId}
              changeViewMode={changeViewMode}
              isFullscreen
              history={history as FixMeAny}
              location={location as FixMeAny}
              mapHeight={mapHeight}
              onFullscreenEscape={this.handleFullscreenEscape}
              onVehicleMarkerClick={this.handleVehicleMarkerClick}
              viewMode={VIEW_MODE.fullscreen}
              preferences={this.props.preferences}
              focusedItem={focusedItem}
              timelineStartTime={timelineStartTime}
              mapType=""
              onEventMarkerClick={noop}
            />
          )}
        </MapAreaThemeContextProvider>
      </MapThemeContextProvider>
    )
  }
}

function mapStateToProps(state: AppState) {
  const preferences = getPreferences(state)
  const { useVehicleIconColor, vehicleDisplayName } = preferences
  const { timelineStartTime } = getTimelineEventsUIDateRange(state)

  return {
    preferences,
    useVehicleIconColor,
    viewMode: getViewMode(state),
    vehicleDisplayName,
    timelineStartTime,
    mapTypeId: getMapTypeId(state),
  }
}

const mapDispatchToProps = {
  changeViewMode,
  setLayerVisibility,
  selectTripSummary,
  clickedVehicleMarker,
  setMapTypeId: setNonGoogleMapTypeId,
  unmountedFullscreenMap,
}

type ReduxProps = ReturnType<typeof mapStateToProps> & typeof mapDispatchToProps

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(
  mapViewContainer(FullscreenMap as FixMeAny, {
    getItems,
    getFocusedItem,
  }) as FixMeAny,
) as ComponentType<
  Except<
    ComponentProps<typeof FullscreenMap>,
    keyof MapViewContainerInjectedProps | keyof ReduxProps
  >
>
