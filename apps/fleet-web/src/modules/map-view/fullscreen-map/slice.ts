import { createSlice } from '@reduxjs/toolkit'

import type { MapType } from '@fleet-web/duxs/map-types'
import type { AppState } from '@fleet-web/root-reducer'
import { batchReduxStateUpdatesFromSaga } from '@fleet-web/sagas/actions'
import type { FixMeAny } from '@fleet-web/types'

type State = {
  fullscreenMapState: {
    type: MapType | ''
    selectedType: 'vehicle'
    focusedItemStartDate?: Date | null
    focusedItem: Record<string, FixMeAny> | null | undefined
  }
  prePathname: string
}

export type FullscreenMapNavigationState = State['fullscreenMapState']

export const fullscreenMapInitialState: State = {
  fullscreenMapState: {
    type: '',
    selectedType: 'vehicle',
    focusedItemStartDate: null,
    focusedItem: null,
  },
  prePathname: '',
}

export type FullscreenMapState = State

const slice = createSlice({
  name: 'fullscreen-map',
  initialState: fullscreenMapInitialState,
  reducers: {
    unmountedFullscreenMap(draft) {
      draft.fullscreenMapState = fullscreenMapInitialState.fullscreenMapState
      draft.prePathname = fullscreenMapInitialState.prePathname
    },
  },
  extraReducers: (builder) => {
    builder.addCase(batchReduxStateUpdatesFromSaga, (draft, { payload }) =>
      payload.mapViewFullscreenMapState
        ? { ...draft, ...payload.mapViewFullscreenMapState }
        : draft,
    )
  },
})

export const { unmountedFullscreenMap } = slice.actions

export default slice.reducer

const getState = (state: AppState) => state.fullscreenMap

export const getFullscreenMapState = (state: AppState) =>
  getState(state).fullscreenMapState

export const getPreviousPathname = (state: AppState) => getState(state).prePathname
