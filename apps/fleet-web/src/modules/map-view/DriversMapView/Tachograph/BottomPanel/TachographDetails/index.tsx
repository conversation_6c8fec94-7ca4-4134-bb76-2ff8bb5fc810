import { first, last } from 'lodash'
import { Box } from '@karoo-ui/core'

import ReActivityTimesData from '@fleet-web/components/_data/ReActivityTimes'
import DetailedTimelineActivity from '@fleet-web/components/_timeline/Activity/Detailed'
import { Marker } from '@fleet-web/components/_timeline/Activity/Marker'
import ServiceEntryMarkerWithToolTip from '@fleet-web/components/_timeline/Activity/Marker/Service/Entry/WithTooltip'
import ServiceExitMarkerWithToolTip from '@fleet-web/components/_timeline/Activity/Marker/Service/Exit/WithTooltip'
import {
  getTimePeriodPct,
  getTimePeriodSeconds,
} from '@fleet-web/modules/map-view/DriversMapView/Tachograph/utils'
import { getFragmentTooltipMessage } from '@fleet-web/modules/tachograph/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { FetchDriverDayActivity } from '../../api/useDriverDayActivityQuery'

type Props = {
  driverDayActivityData: FetchDriverDayActivity.Return
}

const TachographDetails = ({ driverDayActivityData }: Props) => {
  const firstActivity = first(driverDayActivityData.activity.timeline)
  const lastActivity = last(driverDayActivityData.activity.timeline)

  return (
    <>
      <ReActivityTimesData driverDayActivityData={driverDayActivityData} />
      <Box pb={4}>
        <DetailedTimelineActivity>
          {firstActivity !== undefined && lastActivity !== undefined && (
            <>
              <ServiceEntryMarkerWithToolTip pctLeft={firstActivity.startPct} />
              <Marker pctLeft={firstActivity.startPct} />
              {driverDayActivityData.activity.timeline.map((period) => (
                <DetailedTimelineActivity.FragmentWithTooltip
                  key={`${period.startPct}_${period.endPct}_${period.status}`}
                  status={period.status}
                  pctLeft={period.startPct}
                  pctWidth={getTimePeriodPct(period)}
                  tooltipProps={{
                    title: getFragmentTooltipMessage(period),
                    placement: 'top',
                  }}
                >
                  {ctIntl.formatDurationWithHourMinute(getTimePeriodSeconds(period))}
                </DetailedTimelineActivity.FragmentWithTooltip>
              ))}
              <Marker pctLeft={lastActivity.endPct} />
              <ServiceExitMarkerWithToolTip pctLeft={lastActivity.endPct} />
            </>
          )}
        </DetailedTimelineActivity>
      </Box>
    </>
  )
}

export default TachographDetails
