import { useCallback, useMemo } from 'react'
import { isEmpty } from 'lodash'
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query'
import { useDispatch } from 'react-redux'
import type { Writable } from 'type-fest'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import {
  makeMutationErrorHandlerWithToast,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import type { GPSFixType, VehicleId, VehicleType } from '@fleet-web/api/types'
import { normalizeGpsFixType, parseApiOutputVehicleId } from '@fleet-web/api/utils'
import { getCurrentVehicleLivePositionStrategy } from '@fleet-web/duxs/user-sensitive-selectors'
import { getVehicles } from '@fleet-web/duxs/vehicles'
import { useDeepCompareMemo } from '@fleet-web/hooks'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { PromiseResolvedType } from '@fleet-web/types'
import type { ExcludeStrict } from '@fleet-web/types/utils'
import { ctToast } from '@fleet-web/util-components/ctToast'
import {
  minutesToMs,
  removeFirstWhere,
  secondsToMs,
  toImmutable,
  updateFirstWhere,
} from '@fleet-web/util-functions/functional-utils'
import { isValidGPSData } from '@fleet-web/util-functions/map-utils'
import { Array_sort } from '@fleet-web/util-functions/performance-critical-utils'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import { onVehicleClusterDeleteSuccess } from '../VehicleClusterPanel/slice'
import type {
  AddVehicleCluster,
  AddVehiclesToCluster,
  Ct_fleet_get_clusters,
  Ct_fleet_get_vehicle_positions,
  DeleteCluster,
  FetchClusterOnboarding,
  FetchVehicleClusterDetails,
  RawCluster,
  RemoveVehiclesFromCluster,
  UpdateCluster,
  UpdateClusterOnboarding,
  VehicleCluster,
  VehicleClusterId,
} from './types'

const clusterKeys = {
  all: () => ['vehicleCluster'] as const,
}

export const parseVehicleCluster = (cluster: RawCluster): VehicleCluster => ({
  id: cluster.cluster_id,
  name: cluster.cluster_name,
  vehicles: cluster.vehicles.map(({ registration, vehicle_id }) => ({
    id: parseApiOutputVehicleId(vehicle_id),
    registration,
  })),
})

async function fetchVehicleClusters() {
  const response = await apiCallerNoX<Ct_fleet_get_clusters.ApiOutput>(
    'ct_fleet_get_clusters',
    undefined,
  )

  return response.map((c) => parseVehicleCluster(c))
}

export type FetchVehicleClustersResolved = PromiseResolvedType<
  typeof fetchVehicleClusters
>

async function fetchVehicleClusterDetails(
  params: FetchVehicleClusterDetails.ApiParams,
) {
  const response = await apiCallerNoX<FetchVehicleClusterDetails.ApiOutput>(
    'ct_fleet_get_cluster_vehicles',
    params,
  )

  return { vehicleIds: response }
}

async function addVehicleCluster(params: AddVehicleCluster.ApiParams) {
  const response = await apiCallerNoX<AddVehicleCluster.ApiOutput>(
    'ct_fleet_add_cluster',
    params,
  )

  return { id: response.cluster_id }
}

async function addVehiclesToCluster(params: AddVehiclesToCluster.ApiParams) {
  return await apiCallerNoX('ct_fleet_add_vehicles_to_cluster', params)
}

async function removeVehiclesFromCluster(params: RemoveVehiclesFromCluster.ApiParams) {
  return await apiCallerNoX('ct_fleet_remove_vehicles_from_cluster', params)
}

async function updateCluster(params: UpdateCluster.ApiParams) {
  const response = await apiCallerNoX<UpdateCluster.ApiOutput>(
    'ct_fleet_update_cluster',
    {
      ...params,
      /* don't do anything to vehicles */
      vehicleIds: null,
    },
  )

  return parseVehicleCluster(response)
}

async function deleteCluster(params: DeleteCluster.ApiParams) {
  return await apiCallerNoX('ct_fleet_remove_cluster', params)
}

async function fetchClusterOnboarding() {
  const response = await apiCallerNoX<FetchClusterOnboarding.ApiOutput>(
    'ct_fleet_get_cluster_onboarding',
    undefined,
  )

  return {
    userHasNeverSkippedOrClosedYet:
      isEmpty(response) /* if first time showing there will be no record */,
  }
}

async function updateClusterOnboarding(params: UpdateClusterOnboarding.Params) {
  return await apiCallerNoX('ct_fleet_update_cluster_onboarding', {
    onboarding_start_time: params.startTime,
    onboarding_end_time: params.endTime,
    onboarding_skipped_step: params.skippedStep,
    onboarding_skipped: params.skipped,
  })
}

export const vehicleClustersQuery = () =>
  createQuery({
    queryKey: [...clusterKeys.all(), 'fetchVehicleClusters'] as const,
    queryFn: fetchVehicleClusters,
    staleTime: minutesToMs(3),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useVehicleClustersQuery(enabled?: boolean) {
  return useQuery({
    ...vehicleClustersQuery(),
    select: useCallback((clusters: FetchVehicleClustersResolved) => {
      const clustersById = new Map<VehicleClusterId, VehicleCluster>()

      for (const cluster of clusters) {
        clustersById.set(cluster.id, cluster)
      }

      return { clustersArray: clusters, clustersById }
    }, []),
    enabled: enabled ?? true,
  })
}

export type ClustersDetailsQueryResolved = PromiseResolvedType<
  typeof fetchVehicleClusterDetails
>

const vehicleClusterDetailQuery = ({
  params,
}: {
  params: FetchVehicleClusterDetails.ApiParams
}) =>
  createQuery({
    queryKey: [...clusterKeys.all(), 'vehicleClusterDetail', params] as const,
    queryFn: () => fetchVehicleClusterDetails(params),
    staleTime: 20_000,
    ...makeQueryErrorHandlerWithToast(),
  })

export function useVehicleClusterDetailsQuery(
  params: FetchVehicleClusterDetails.ApiParams,
) {
  return useQuery(vehicleClusterDetailQuery({ params }))
}

export function useVehicleClusterDetailsQueryWithRefreshInterval(
  params: FetchVehicleClusterDetails.ApiParams,
) {
  return useQuery({
    ...vehicleClusterDetailQuery({ params }),
    refetchInterval: secondsToMs(25),
  })
}

export const useAddVehicleClusterMutation = ({
  onSuccess,
}: {
  onSuccess: (
    data: { id: VehicleClusterId },
    variables: AddVehicleCluster.ApiParams,
    context: unknown,
  ) => void | Promise<unknown>
}) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: addVehicleCluster,
    onSuccess(...args) {
      queryClient.invalidateQueries(vehicleClustersQuery())
      onSuccess(...args)
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

export const useAddVehiclesToClusterMutation = ({
  onSuccess,
}: {
  onSuccess: (
    data: PromiseResolvedType<typeof addVehiclesToCluster>,
    variables: AddVehiclesToCluster.ApiParams,
    context: unknown,
  ) => void | Promise<unknown>
}) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: addVehiclesToCluster,
    onSuccess(...args) {
      ctToast.fire('success', 'Update vehicle cluster successfully.')
      onSuccess(...args)
      return queryClient.invalidateQueries(vehicleClustersQuery())
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

export const useRemoveVehiclesFromClusterMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: removeVehiclesFromCluster,
    onMutate: async ({ clusterId, vehicleIds }) => {
      await queryClient.cancelQueries(vehicleClustersQuery())

      const previousData = vehicleClustersQuery().getData(queryClient)

      if (previousData) {
        vehicleClustersQuery().setData(queryClient, {
          updater: updateFirstWhere(
            previousData,
            (c) => c.id === clusterId,
            (c) => ({
              id: c.id,
              name: c.name,
              vehicles: c.vehicles.filter(({ id }) => !vehicleIds.includes(id)),
            }),
          ),
        })
      }

      return { previousData }
    },
    onError: (err, _variables, context) => {
      if (context?.previousData) {
        vehicleClustersQuery().setData(queryClient, { updater: context.previousData })
      }
      makeMutationErrorHandlerWithToast().onError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries(vehicleClustersQuery())
    },
  })
}

export const useUpdateClusterMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateCluster,
    async onMutate({ clusterId, name: newName }) {
      await queryClient.cancelQueries(vehicleClustersQuery())

      const previousData = vehicleClustersQuery().getData(queryClient)

      if (previousData) {
        vehicleClustersQuery().setData(queryClient, {
          updater: updateFirstWhere(
            previousData,
            (c) => c.id === clusterId,
            (c) => ({
              id: c.id,
              name: newName,
              vehicles: c.vehicles,
            }),
          ),
        })
      }

      return { previousData }
    },
    onError(err, _variables, context) {
      if (context?.previousData) {
        vehicleClustersQuery().setData(queryClient, { updater: context.previousData })
      }
      makeMutationErrorHandlerWithToast().onError(err)
    },
    onSuccess(updatedCluster) {
      vehicleClustersQuery().setData(queryClient, {
        updater: (data) =>
          updateFirstWhere(
            data ?? [],
            (c) => c.id === updatedCluster.id,
            () => updatedCluster,
          ),
      })
    },
  })
}

export const useDeleteClusterMutation = () => {
  const queryClient = useQueryClient()
  const dispatch = useDispatch()

  return useMutation({
    mutationFn: deleteCluster,
    async onMutate({ clusterId }) {
      await queryClient.cancelQueries(vehicleClustersQuery())

      const previousData = vehicleClustersQuery().getData(queryClient)

      if (previousData) {
        vehicleClustersQuery().setData(queryClient, {
          updater: removeFirstWhere(previousData, (c) => c.id === clusterId),
        })
      }

      return { previousData }
    },
    onSuccess() {
      const clustersAfterDeletion = vehicleClustersQuery().getData(queryClient)
      dispatch(onVehicleClusterDeleteSuccess({ clustersAfterDeletion }))
    },
    onError(err, _variables, context) {
      if (context?.previousData) {
        vehicleClustersQuery().setData(queryClient, { updater: context.previousData })
      }
      makeMutationErrorHandlerWithToast().onError(err)
    },
    onSettled() {
      queryClient.invalidateQueries(vehicleClustersQuery())
    },
  })
}

export const clusterOnboardingQuery = () =>
  createQuery({
    queryKey: [...clusterKeys.all(), 'clusterOnboarding'] as const,
    queryFn: fetchClusterOnboarding,
    staleTime: minutesToMs(5),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useClusterOnboardingQuery() {
  return useQuery({
    ...clusterOnboardingQuery(),
  })
}

export const useClusterOnboardingMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateClusterOnboarding,
    onSuccess() {
      queryClient.invalidateQueries(clusterOnboardingQuery())
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

async function fetchVehiclePositions({
  vehicleIds,
}: {
  vehicleIds: ReadonlyArray<VehicleId>
}) {
  const { ct_fleet_get_vehicle_positions } = (await apiCallerNoX(
    'ct_fleet_get_vehicle_positions',
    {
      vehicleIds,
    },
  )) as Ct_fleet_get_vehicle_positions.ApiOutput

  const vehiclePositionsByVehicleId = new Map<
    VehicleId,
    {
      bearing: number
      coords: google.maps.LatLngLiteral | null
      vehicleId: VehicleId
      statusClassName: Ct_fleet_get_vehicle_positions.ApiOutputVehiclePosition['statusClassName']
      gpsFixType: GPSFixType
      vehicleType: VehicleType
      registration: string
    }
  >()

  for (const rawVehicle of Object.values(ct_fleet_get_vehicle_positions)) {
    const vehicleId = parseApiOutputVehicleId(rawVehicle.vehicle_id)

    vehiclePositionsByVehicleId.set(vehicleId, {
      vehicleId,
      bearing: Number(rawVehicle.bearing),
      coords:
        rawVehicle.latitude == null || rawVehicle.longitude == null
          ? null
          : { lat: Number(rawVehicle.latitude), lng: Number(rawVehicle.longitude) },
      statusClassName: rawVehicle.statusClassName,
      gpsFixType: normalizeGpsFixType(rawVehicle.gps_fix_type),
      vehicleType: rawVehicle.vehicle_type,
      registration: rawVehicle.registration,
    })
  }

  return toImmutable({ vehiclePositionsByVehicleId })
}

const vehiclePositionsQuery = ({
  deterministicVehicleIds,
}: {
  deterministicVehicleIds: ReadonlyArray<VehicleId>
}) =>
  createQuery({
    queryKey: ['ct_fleet_get_vehicle_positions', { deterministicVehicleIds }] as const,
    queryFn: () => fetchVehiclePositions({ vehicleIds: deterministicVehicleIds }),
    staleTime: secondsToMs(10),
    ...makeMutationErrorHandlerWithToast(),
  })

export type UseRealTimeVehiclePositionsQuerySuccessReturn = {
  status: 'success'
  data: FetchVehiclePositionsResolved
}

export type UseRealTimeVehiclePositionsQueryWithDataReturn =
  | UseRealTimeVehiclePositionsQuerySuccessReturn
  | {
      status: 'error'
      error: unknown
      data: FetchVehiclePositionsResolved
    }

type UseRealTimeVehiclePositionsQueryReturn =
  | UseRealTimeVehiclePositionsQueryWithDataReturn
  | {
      status: 'error'
      error: unknown
      data: undefined
    }
  | {
      status: 'pending'
      data: undefined
    }

export function useRealTimeVehiclePositionsQuery({
  vehicleIds,
}: {
  vehicleIds: Array<VehicleId>
}): UseRealTimeVehiclePositionsQueryReturn {
  // Using deep compare memo to make sure deterministicVehicleIds __actually__ changes by deep comparison.
  const deterministicVehicleIds = useDeepCompareMemo(
    () => Array_sort(vehicleIds, (a, b) => a.localeCompare(b)),
    [vehicleIds],
  )

  const currentVehicleLivePositionStrategy = useTypedSelector(
    getCurrentVehicleLivePositionStrategy,
  )
  const reduxVehicles = useTypedSelector(getVehicles)

  const vehicleIdsSet = useMemo(
    () => new Set(deterministicVehicleIds),
    [deterministicVehicleIds],
  )

  const query = useQuery({
    ...vehiclePositionsQuery({ deterministicVehicleIds }),
    refetchInterval: secondsToMs(20),
    enabled:
      currentVehicleLivePositionStrategy.type === 'polling_or_idle' &&
      vehicleIds.length > 0,
    placeholderData: keepPreviousData,
  })

  // Compat layer while we are moving to web sockets. Keep the hook return type unchanged, from outside to avoid breaking changes.
  const socketQuery = useMemo(():
    | UseRealTimeVehiclePositionsQueryReturn
    | 'NOT_ENABLED' => {
    if (currentVehicleLivePositionStrategy.type === 'polling_or_idle') {
      return 'NOT_ENABLED'
    }

    const vehiclePositionsByVehicleId: Writable<
      NonNullable<
        UseRealTimeVehiclePositionsQueryReturn['data']
      >['vehiclePositionsByVehicleId']
    > = new Map()

    // When using web sockets, the vehicles in redux are updated with latest positions.
    for (const vehicle of reduxVehicles) {
      if (vehicleIdsSet.has(vehicle.id)) {
        vehiclePositionsByVehicleId.set(vehicle.id, {
          vehicleId: vehicle.id,
          bearing: vehicle.bearing,
          coords: isValidGPSData(vehicle.latitude, vehicle.longitude)
            ? { lat: vehicle.latitude, lng: vehicle.longitude }
            : null,
          statusClassName: vehicle.statusClassName,
          gpsFixType: vehicle.gpsFixType,
          registration: vehicle.registration,
          vehicleType: vehicle.type,
        })
      }
    }

    return {
      status: 'success',
      data: { vehiclePositionsByVehicleId },
    }
  }, [currentVehicleLivePositionStrategy, reduxVehicles, vehicleIdsSet])

  return socketQuery === 'NOT_ENABLED' ? query : socketQuery
}

export type UseRealTimeVehiclePositionsQueryData = ExcludeStrict<
  ReturnType<typeof useRealTimeVehiclePositionsQuery>['data'],
  undefined
>

export type UseRealTimeVehiclePositionsQueryDataPosition = ExcludeStrict<
  ReturnType<
    ExcludeStrict<
      ReturnType<typeof useRealTimeVehiclePositionsQuery>['data'],
      undefined
    >['vehiclePositionsByVehicleId']['get']
  >,
  undefined
>

export type FetchVehiclePositionsResolved = PromiseResolvedType<
  typeof fetchVehiclePositions
>

export type UseVehicleClustersQueryData = ExcludeStrict<
  ReturnType<typeof useVehicleClustersQuery>['data'],
  undefined
>
