import { z } from 'zod'

import type { ApiOutputVehicleId, VehicleId, VehicleType } from '@fleet-web/api/types'
import type { FetchVehicles } from '@fleet-web/api/vehicles/types'

export const vehicleClusterIdSchema = z.string().min(1).brand<'VehicleClusterId'>()

export type VehicleClusterId = z.infer<typeof vehicleClusterIdSchema>

export type RawCluster = {
  cluster_id: VehicleClusterId
  cluster_name: string
  vehicles: Array<{ vehicle_id: ApiOutputVehicleId; registration: string }>
}

export type VehicleCluster = {
  id: RawCluster['cluster_id']
  name: RawCluster['cluster_name']
  vehicles: Array<{ id: VehicleId; registration: string }>
}

export declare namespace Ct_fleet_get_clusters {
  type ApiOutput = ReadonlyArray<RawCluster>
  type Return = Array<VehicleCluster>
}

export declare namespace FetchVehicleClusterDetails {
  type ApiParams = {
    clusterId: VehicleClusterId
  }
  type ApiOutput = Array<VehicleId>
}

export declare namespace AddVehicleCluster {
  type ApiParams = {
    name: string
    vehicleIds: Array<VehicleId>
  }
  type ApiOutput = Pick<RawCluster, 'cluster_id' | 'cluster_name'>
  type Return = VehicleCluster
}

export declare namespace AddVehiclesToCluster {
  type ApiParams = {
    clusterId: VehicleClusterId
    vehicleIds: Array<VehicleId>
  }
  type ApiOutput = void
}

export declare namespace RemoveVehiclesFromCluster {
  type ApiParams = {
    clusterId: VehicleClusterId
    vehicleIds: Array<VehicleId>
  }
  type ApiOutput = void
}

export declare namespace UpdateCluster {
  type ApiParams = {
    clusterId: VehicleClusterId
    name: string
  }
  type ApiOutput = RawCluster
}

export declare namespace DeleteCluster {
  type ApiParams = {
    clusterId: VehicleClusterId
  }
  type ApiOutput = unknown
}

export declare namespace FetchClusterOnboarding {
  type ApiOutput = any
}

export declare namespace UpdateClusterOnboarding {
  type Params = {
    startTime: Date
    endTime: Date
    skippedStep: string
    skipped: boolean
  }

  type ApiOutput = unknown
}

export declare namespace Ct_fleet_get_vehicle_positions {
  type ApiOutputVehiclePosition = {
    vehicle_id: ApiOutputVehicleId
    status_updated: string | null
    longitude: number | `${number}` | null
    latitude: number | `${number}` | null
    speed: number
    gps_fix_type: number | `${number}` | null
    last_ignition: string | null
    event_ts: string | null
    event_description: string | null
    rpm: number | `${number}` | null
    vehicle_type: VehicleType
    colour_code: string
    registration: string
    vehicle_name: string
    bearing: number | `${number}` | null
    last_valid_gps_ts: string | null
    roadSpeed: number
    is_idle: boolean
    statusClassName: FetchVehicles.ApiOutput['ct_fleet_get_vehiclelist'][number]['statusClassName']
  }

  type ApiOutput = {
    ct_fleet_get_vehicle_positions: {
      [vehicleIdAsNumber: ApiOutputVehicleId]: ApiOutputVehiclePosition
    }
  }
}
