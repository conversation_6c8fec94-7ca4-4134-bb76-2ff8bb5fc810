import type { <PERSON><PERSON><PERSON>, Size } from 'google-map-react'
import type { Points } from 'points-cluster'

import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { getBoundsFrom } from '@fleet-web/modules/shared/map-utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { MapTypeId, type MapsExtended } from '@fleet-web/types/extended/google-maps'
import {
  MAP_MAX_ZOOM,
  MAP_MIN_ZOOM,
  MAP_ZOOM_CALCULATION_VALUE,
} from '@fleet-web/util-functions/constants'

export const clusterMapAreaUtils = {
  clickedZoomInButton: (currentZoom: number) =>
    currentZoom + MAP_ZOOM_CALCULATION_VALUE <= MAP_MAX_ZOOM
      ? currentZoom + MAP_ZOOM_CALCULATION_VALUE
      : currentZoom,
  clickedZoomOutButton: (currentZoom: number) =>
    currentZoom - MAP_ZOOM_CALCULATION_VALUE >= MAP_MIN_ZOOM
      ? currentZoom - MAP_ZOOM_CALCULATION_VALUE
      : currentZoom,
  getBoundsFromWithExtraPadding: ({
    pathItems,
    mapSize,
    mapObject,
    addedPadding = 0.00036,
  }: {
    pathItems: Points
    mapSize: Size
    mapObject: MapsExtended.MapObject
    addedPadding?: number
  }) =>
    getBoundsFrom({
      pathItems,
      mapSize,
      mapObject,
      addedPadding,
    }),
}

export const useVehicleClusterMapAreaInitialMapOptions = (): {
  center: Coords
  zoom: number
  mapTypeId: google.maps.MapTypeId
} => {
  const { defaultMapLat, defaultMapLon, defaultMapZoom } =
    useTypedSelector(getSettings_UNSAFE)

  return {
    center: {
      lat: defaultMapLat || 0,
      lng: defaultMapLon || 0,
    },
    zoom: defaultMapZoom || 1,
    mapTypeId: MapTypeId.ROADMAP,
  }
}
