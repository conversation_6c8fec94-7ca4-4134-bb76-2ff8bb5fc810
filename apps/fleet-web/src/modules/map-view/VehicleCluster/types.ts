import type { GPSFixType, VehicleId, VehicleType } from '@fleet-web/api/types'

import type { UseRealTimeVehiclePositionsQueryDataPosition } from './api/queries'

export type NormalizedVehiclePoint = {
  id: VehicleId
  lat: number
  lng: number
  statusClassName: UseRealTimeVehiclePositionsQueryDataPosition['statusClassName']
  bearing: number
  type: VehicleType
  gpsFixType: GPSFixType
  registration: string
}
