import type { Coords } from 'google-map-react'
import type { Points } from 'points-cluster'

import type { VehicleId } from '@fleet-web/api/types'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import { Array_filterMap } from '@fleet-web/util-functions/performance-critical-utils'

import type { FetchVehiclePositionsResolved } from '../api/queries'
import { clusterMapAreaUtils } from '../utils'

export const generateClusterMapFocusStateFrom = ({
  vehiclePositionsByVehicleId,
  clusterVehicleIds,
  mapObject,
  currentFocusState,
  currentIsFollowingVehicles,
}: {
  vehiclePositionsByVehicleId: FetchVehiclePositionsResolved['vehiclePositionsByVehicleId']
  clusterVehicleIds: Array<VehicleId>
  mapObject: MapsExtended.MapObject
  currentFocusState: { center: Coords; zoom: number }
  currentIsFollowingVehicles: boolean
}): { center: Coords; zoom: number } => {
  if (!currentIsFollowingVehicles || !mapObject.ref) {
    // No need to focus map on vehicles when now following them
    return currentFocusState
  }

  const vehiclePoints: Points = Array_filterMap(
    clusterVehicleIds,
    (vehicleId, { RemoveSymbol }) => {
      const vehiclePosition = vehiclePositionsByVehicleId.get(vehicleId)
      if (!vehiclePosition || !vehiclePosition.coords) {
        return RemoveSymbol
      }

      return vehiclePosition.coords
    },
  )

  if (vehiclePoints.length === 0) {
    return currentFocusState
  }

  const mapSize = mapObject.ref.getBoundingClientRect()

  return clusterMapAreaUtils.getBoundsFromWithExtraPadding({
    mapObject,
    mapSize,
    pathItems: vehiclePoints,
  })
}
