import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { isEmpty, throttle } from 'lodash'
import {
  Box,
  CircularProgressDelayedAbsolute,
  Slider,
  SliderThumb,
  Stack,
  styled,
} from '@karoo-ui/core'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import PauseCircleFilledIcon from '@mui/icons-material/PauseCircleFilled'
import PlayCircleFilledIcon from '@mui/icons-material/PlayCircleFilled'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'

import type { BasicEvent, BasicEventUI, EventUI } from '@fleet-web/api/timeline/types'
import { getTimelineProgress, updateTimelineSliderPlaying } from '@fleet-web/duxs/map'
import { getIsTimelineBarUILoading } from '@fleet-web/duxs/timeline'
import { savePreferences } from '@fleet-web/duxs/user'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import useEffectExceptOnMount from '@fleet-web/hooks/useEffectExceptOnMount'
import { useEffectEvent } from '@fleet-web/hooks/useEventHandler'
import useInterval from '@fleet-web/hooks/useInterval'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import { TooltipAsChild } from '@fleet-web/shared/karoo-ui/TooltipAsChild'
import type { DateTimeRange, FixMeAny } from '@fleet-web/types'
import ActionIconButton from '@fleet-web/util-components/actionIconButton'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { getDateDiffByDays } from '@fleet-web/util-functions/moment-helper-storeless'

import { jumpToTime } from '../actions'
import {
  getActivityActiveTab,
  getActivityDateRange,
  type TabsType,
} from '../FleetMapView/DetailsPanel/slice'
import ReSpeedControlBox from './SpeedControlBox'
import { findNearestEventAtTime } from './utils/find-event-at-time'

const SLIDER_INTERVAL_MS = 1000
const DEFAULT_TIMELINE_PCT_SPEED = 0.005

const getTimelineSpeedBasedOnNumberOfDays = (
  timelineSpeed = DEFAULT_TIMELINE_PCT_SPEED,
  dateRange: DateTimeRange,
  activityActiveTab: TabsType,
) => {
  const numberOfSelectedDays =
    activityActiveTab === 'daily'
      ? 1
      : getDateDiffByDays(dateRange.from, dateRange.to) + 1

  return timelineSpeed / numberOfSelectedDays
}

type Props = {
  events: Array<{ coords: BasicEvent['coords']; time: number }>
  timelineBarUI: ReadonlyArray<EventUI | BasicEventUI>
  timeRange: {
    startDateTime: DateTime
    endDateTime: DateTime
  }
}

const TimelineSlider = ({ events, timeRange, timelineBarUI }: Props) => {
  const dispatch = useDispatch()

  const isLoadingUI = useTypedSelector(getIsTimelineBarUILoading)
  const activityActiveTab = useTypedSelector(getActivityActiveTab)
  const activityDateRange = useTypedSelector(getActivityDateRange)
  const progress = useTypedSelector(getTimelineProgress)
  const { timelineSpeed: timelineSpeedFromPreferences } =
    useTypedSelector(getPreferences)

  const timelineSpeedBasedOnNumberOfDays = getTimelineSpeedBasedOnNumberOfDays(
    timelineSpeedFromPreferences,
    activityDateRange,
    activityActiveTab,
  )

  const [isTimelineSliderPlaying, setIsTimelineSliderPlaying] = useState(false)

  // Save in global state whenever isTimelineSliderPlaying changes
  useEffectExceptOnMount(() => {
    dispatch(updateTimelineSliderPlaying(isTimelineSliderPlaying))
  }, [dispatch, isTimelineSliderPlaying])

  const onProgressChange = useEffectEvent(() => {
    if (isTimelineSliderPlaying && progress >= 1) {
      // Handle timeline slider pausing when reaching the end
      setIsTimelineSliderPlaying(false)
    }
  })
  useEffect(() => {
    onProgressChange()
  }, [progress])

  useEffect(
    () => () => {
      setIsTimelineSliderPlaying(false)
    },
    [activityActiveTab],
  )

  useEffect(
    () => () => {
      setIsTimelineSliderPlaying(false)
    },
    [],
  )

  // Run progressThroughTime whenever timeline is playing
  useInterval(
    () => {
      progressThroughTime()
    },
    isTimelineSliderPlaying ? SLIDER_INTERVAL_MS : null,
  )

  const handlePlayClick = () => {
    if (!isEmpty(events)) {
      if (progress >= 1) {
        const nextActiveEventIndex = 0
        const nextActiveEvent = events[nextActiveEventIndex]

        dispatch(
          jumpToTime({
            nextProgress: 0,
            nextActiveEventIndex,
            nextCoords: nextActiveEvent.coords,
          }),
        )
      }

      setIsTimelineSliderPlaying(
        (prevIsTimelineSliderPlaying) => !prevIsTimelineSliderPlaying,
      )
    }
  }

  const handleOnSliderChange = useMemo(
    () =>
      throttle((sliderPct: number) => {
        if (!isEmpty(events)) {
          setIsTimelineSliderPlaying(false)

          const pct = sliderPct / 100
          const timeSpan =
            timeRange.endDateTime.toMillis() - timeRange.startDateTime.toMillis()
          const timeOfSlider = timeRange.startDateTime.toMillis() + pct * timeSpan

          const nextActiveEventIndex = findNearestEventAtTime(timeOfSlider, events)

          if (nextActiveEventIndex !== undefined) {
            const nextActiveEvent = events[nextActiveEventIndex]

            dispatch(
              jumpToTime({
                nextProgress: pct,
                nextActiveEventIndex,
                nextCoords: nextActiveEvent.coords,
              }),
            )
          }
        }
      }, 15),
    [dispatch, events, timeRange.endDateTime, timeRange.startDateTime],
  )

  /*
   * Increments time forward one step. Used with setInterval to "play" through the timeline.
   */
  const progressThroughTime = () => {
    if (!isEmpty(events)) {
      const timeSpan =
        timeRange.endDateTime.toMillis() - timeRange.startDateTime.toMillis()
      const nextProgress = progress + timelineSpeedBasedOnNumberOfDays
      const nextTime = timeRange.startDateTime.toMillis() + nextProgress * timeSpan

      const nextActiveEventIndex = findNearestEventAtTime(nextTime, events)

      if (nextActiveEventIndex !== undefined) {
        const nextActiveEvent = events[nextActiveEventIndex]

        dispatch(
          jumpToTime({
            nextProgress,
            nextActiveEventIndex,
            nextCoords: nextActiveEvent.coords,
          }),
        )
      }
    }
  }

  const handleSpeedSliderChange = (value: number) => {
    dispatch(savePreferences('timelineSpeed', value / 1000))
  }

  const renderTimelineMarkers = useCallback(() => {
    if (activityActiveTab === 'multipleDays') {
      const dateDiffInDays = Math.floor(
        timeRange.endDateTime.diff(timeRange.startDateTime, 'days').as('days'),
      )

      const dateStrings: Array<string> = []

      // n weeks = n step
      const step = Math.max(Math.ceil(dateDiffInDays / 7), 1)
      const todayDateTime = DateTime.local()
      const yesterdayDateTime = todayDateTime.minus({ days: 1 })

      for (let i = 0; i <= dateDiffInDays; i++) {
        if (i % step !== 0) {
          continue
        }

        const dateTime = timeRange.startDateTime.plus({ days: i })

        if (dateTime.hasSame(todayDateTime, 'day')) {
          dateStrings.push(ctIntl.formatMessage({ id: 'Today' }))
        } else if (dateTime.hasSame(yesterdayDateTime, 'day')) {
          dateStrings.push(ctIntl.formatMessage({ id: 'Yesterday' }))
        } else {
          dateStrings.push(dateTime.toFormat('D'))
        }
      }

      return (
        <DayMarkersContainer>
          {dateStrings.map((dString, index) => {
            const dayMarkerEnd =
              Math.min(index * step + step, dateDiffInDays + 1) / (dateDiffInDays + 1)
            const dayMarkerStart = (index * step) / (dateDiffInDays + 1)

            return (
              <DayMarker
                key={dString}
                flexWidth={dayMarkerEnd - dayMarkerStart}
              >
                {dString}
              </DayMarker>
            )
          })}
        </DayMarkersContainer>
      )
    } else {
      const timeSpan =
        timeRange.endDateTime.toMillis() - timeRange.startDateTime.toMillis()
      const increment = timeSpan / 4

      return (
        <TimeMarkersContainer>
          {[0, 1, 2, 3, 4].map((i) => {
            const formattedTime = timeRange.startDateTime
              .plus({ milliseconds: increment * i })
              .toFormat('t')

            return <span key={formattedTime}>{formattedTime}</span>
          })}
        </TimeMarkersContainer>
      )
    }
  }, [activityActiveTab, timeRange.endDateTime, timeRange.startDateTime])

  const getScrubberCurrentTime = useCallback(
    (progressValue: number) => {
      const timeSpan =
        timeRange.endDateTime.toMillis() - timeRange.startDateTime.toMillis()
      return timeRange.startDateTime
        .plus({ milliseconds: progressValue * timeSpan })
        .toFormat('t')
    },
    [timeRange.endDateTime, timeRange.startDateTime],
  )

  const renderTimelineBarUI = useCallback(
    () =>
      timelineBarUI.map((evt) => (
        <span
          key={evt.uniqueReactKey}
          className={`TimelineSlider-timelineEvent ${evt.statusClass}`}
          style={{
            left: `${Math.max(evt.pctLeft, 0)}%`,
            width: `${evt.pctWidth}%`,
          }}
        />
      )),
    [timelineBarUI],
  )

  if (isLoadingUI) {
    return <CircularProgressDelayedAbsolute />
  }

  return (
    <Stack
      direction="row"
      gap={2}
      paddingX={2}
      paddingY={4}
    >
      <ActionIconButton
        variant="outlined"
        color="secondary"
        onClick={() => {
          GA4.event({
            category: 'Timeline',
            action: '"Play" Button Click',
          })

          handlePlayClick()
        }}
      >
        {isTimelineSliderPlaying ? <PauseCircleFilledIcon /> : <PlayCircleFilledIcon />}
      </ActionIconButton>
      <Stack sx={{ width: '100%', justifyContent: 'center', position: 'relative' }}>
        <Box
          sx={{
            borderRadius: '30px',
            height: '10px',
            padding: 0,
            width: '100%',
            backgroundColor: '#ddd',
            position: 'relative',
          }}
        >
          {renderTimelineBarUI()}
          <Slider
            step={0.1}
            value={progress * 100}
            onChange={(_, value) => handleOnSliderChange(value)}
            min={0}
            max={100}
            track={false}
            sx={{
              zIndex: 101,
            }}
            // Do not display their value label since we have our own tooltip
            valueLabelDisplay="off"
            slots={{
              thumb: SliderThumbComponent,
            }}
            color="secondary"
            slotProps={{
              rail: { style: { display: 'none' } },
              thumb: {
                tooltipContent: getScrubberCurrentTime(progress),
                tooltipPlacement:
                  activityActiveTab === 'multipleDays' ? 'bottom' : 'top',
              } satisfies SliderThumbComponentExtraProps as any,
            }}
          />
        </Box>
        {timelineBarUI.length > 0 && renderTimelineMarkers()}
      </Stack>
      <ReSpeedControlBox
        speed={(timelineSpeedFromPreferences as FixMeAny) * 1000}
        onChange={handleSpeedSliderChange}
      />
    </Stack>
  )
}

export default memo(TimelineSlider)

const Container = styled(Box)({
  color: '#666',
  display: 'flex',
  justifyContent: 'space-between',
  fontSize: '12px',
})

const DayMarkersContainer = styled(Container)({
  position: 'absolute',
  width: '100%',
})

const TimeMarkersContainer = styled(Container)({
  height: '14px',
  marginTop: '6px',
})

const DayMarker = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'flexWidth',
})<{ flexWidth: number }>(({ flexWidth }) => ({
  position: 'relative',
  borderLeft: '2px solid #cbcbcb',
  height: '35px',
  paddingLeft: '1px',
  flex: flexWidth,
}))

type SliderThumbComponentExtraProps = {
  tooltipContent: React.ReactNode
  tooltipPlacement: 'top' | 'bottom'
}

function SliderThumbComponent({
  children,
  tooltipContent,
  tooltipPlacement,
  ...other
}: React.HTMLAttributes<unknown> & SliderThumbComponentExtraProps) {
  return (
    // This slider thumb has a position: absolute/relative, that's why we can use TooltipAsChild
    <StyledSliderThumb {...other}>
      {children}
      <DragIndicatorIcon sx={{ fontSize: '16px', fill: 'white' }} />

      <TooltipAsChild
        placement={tooltipPlacement}
        offsetFromElement="10px"
      >
        {tooltipContent}
      </TooltipAsChild>
    </StyledSliderThumb>
  )
}

const StyledSliderThumb = styled(SliderThumb)(({ theme }) =>
  theme.unstable_sx({
    top: '15%',
  }),
)
