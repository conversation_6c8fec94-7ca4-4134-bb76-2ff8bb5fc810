import { Box, CircularProgressDelayedAbsolute, Dialog } from '@karoo-ui/core'

import { getIsFetchingStatistics } from '@fleet-web/duxs/timeline'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import TimelineTableTripGraph from './TimelineTableTripGraph'

type Props = {
  closeModal: () => void
  tripId: string | null
}

const TimelineTableTripModal = ({ closeModal, tripId }: Props) => {
  const isLoading = useTypedSelector(getIsFetchingStatistics)
  return (
    <Dialog
      open
      onClose={closeModal}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: 'none',
        },
      }}
    >
      <Box sx={{ width: '800px', height: '450px' }}>
        {isLoading ? (
          <CircularProgressDelayedAbsolute />
        ) : (
          <TimelineTableTripGraph tripId={tripId ?? ''} />
        )}
      </Box>
    </Dialog>
  )
}

export default TimelineTableTripModal
