import { useRef, useState } from 'react'
import { Box, Stack, Typography } from '@karoo-ui/core'
import {
  CartesianGrid,
  Label,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

import { mainStyleSettings } from '@fleet-web/duxs/client-settings-helper'
import { parsedTripsDataSelector, TRIP_STATISTICS } from '@fleet-web/duxs/timeline'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import ReTabGroup from '@fleet-web/util-components/ReTabGroup'

type TripStatistics = keyof typeof TRIP_STATISTICS

type Props = {
  tripId: string
}

const TimelineTableTripGraph = ({ tripId }: Props) => {
  const lineRef = useRef<LineChart>(null)

  const tripStatisticsHeaderTabs = [
    {
      name: ctIntl.formatMessage({ id: TRIP_STATISTICS.Speed.buttonName }),
      value: TRIP_STATISTICS.Speed.buttonName,
    },
    {
      name: ctIntl.formatMessage({
        id: TRIP_STATISTICS['Throttle Position'].buttonName,
      }),
      value: TRIP_STATISTICS['Throttle Position'].buttonName,
    },
    {
      name: ctIntl.formatMessage({ id: TRIP_STATISTICS.RPM.buttonName }),
      value: TRIP_STATISTICS.RPM.buttonName,
    },
  ] satisfies Array<{ name: string; value: TripStatistics }>

  const [activeButton, setActiveButton] = useState<TripStatistics>(
    TRIP_STATISTICS.Speed.buttonName,
  )
  const parsedTripsData = useTypedSelector((state) =>
    parsedTripsDataSelector(state, tripId),
  )

  const changeActiveButton = (newActiveButton: TripStatistics) => {
    setActiveButton(newActiveButton)
  }

  const tickFormatter = (YData: FixMeAny) => `${YData}%`

  const renderHeaderButtons = () => (
    <Stack>
      <ReTabGroup
        value={activeButton}
        options={tripStatisticsHeaderTabs}
        handleClick={changeActiveButton}
      />
    </Stack>
  )

  const renderLabel = () => (
    <svg width="310">
      <text
        x="50%"
        y={-10}
        className="TimelineTripGraph-chart-name-container-Y"
        transform="rotate(90 0 0)"
      >
        {ctIntl.formatMessage({
          id: TRIP_STATISTICS[activeButton].YAxisName,
        })}
      </text>
    </svg>
  )

  const currentChart = TRIP_STATISTICS[activeButton].chartName
  const activeColor = mainStyleSettings().styleSubmenuActiveColour

  return (
    <Stack sx={{ paddingY: 2, alignItems: 'center', gap: 2 }}>
      {renderHeaderButtons()}
      {parsedTripsData[currentChart] && parsedTripsData[currentChart].length > 0 ? (
        <Stack sx={{ width: '100%', paddingX: 1 }}>
          <ResponsiveContainer
            width="100%"
            height={310}
          >
            <LineChart
              ref={lineRef}
              data={parsedTripsData[currentChart]}
            >
              <CartesianGrid vertical={false} />
              <XAxis dataKey={currentChart} />
              <YAxis
                axisLine={false}
                type="number"
                domain={[0, 'dataMax']}
                tickFormatter={tickFormatter}
              >
                <Label content={renderLabel()} />
              </YAxis>
              <Tooltip content={CustomTooltip} />
              <Line
                type="monotone"
                dataKey="percentage"
                stroke="#000000"
                dot={{
                  stroke: activeColor,
                  strokeWidth: 2,
                  r: 3,
                  fill: activeColor,
                }}
                activeDot={false}
                isAnimationActive={false}
              />
            </LineChart>
          </ResponsiveContainer>
          <Box
            sx={{
              alignSelf: 'flex-end',
              color: '#666',
              textAlign: 'right',
            }}
          >
            {ctIntl.formatMessage({
              id: TRIP_STATISTICS[activeButton].XAxisName,
            })}
          </Box>
        </Stack>
      ) : (
        <Box>
          <Typography variant="body2">
            {ctIntl.formatMessage({ id: 'No data available' })}
          </Typography>
        </Box>
      )}
    </Stack>
  )
}

export default TimelineTableTripGraph

// Sub component
const CustomTooltip = ({
  payload,
  active,
}: {
  payload: Array<{ value: number }>
  active: boolean
}) => {
  if (!active) return null

  const value = payload[0] ? `${payload[0].value.toFixed(1)}%` : '%'
  return (
    <Box sx={{ background: 'rgba(0,0,0,0.7)', borderRadius: '3px', p: 0.5 }}>
      <Typography
        sx={{ color: '#fff' }}
        variant="subtitle2"
      >
        {value}
      </Typography>
    </Box>
  )
}
