import { useState } from 'react'
import { isNil, map, startCase } from 'lodash'
import {
  <PERSON>ge,
  Button,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import AccessTimeFilledIcon from '@mui/icons-material/AccessTimeFilled'
import CachedIcon from '@mui/icons-material/Cached'
import CalendarViewMonthIcon from '@mui/icons-material/CalendarViewMonth'
import DirectionsOutlinedIcon from '@mui/icons-material/DirectionsOutlined'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import GppGoodOutlinedIcon from '@mui/icons-material/GppGoodOutlined'
import LocalCarWashIcon from '@mui/icons-material/LocalCarWash'
import OfflineBoltOutlinedIcon from '@mui/icons-material/OfflineBoltOutlined'
import ShowChartIcon from '@mui/icons-material/ShowChart'
import SpeedIcon from '@mui/icons-material/Speed'
import WifiTetheringOutlinedIcon from '@mui/icons-material/WifiTetheringOutlined'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'

import { changeTimelineTablesActiveTab } from '@fleet-web/duxs/map'
import { getMapType, getTimelineTablesActiveTab } from '@fleet-web/duxs/map-timeline'
import type { MapActiveView, MapType } from '@fleet-web/duxs/map-types'
import {
  fetchApiTimelineData,
  getTimelineEventsUI,
  getTimelineTotals,
  getTimelineTripsSensors,
  requestSVRPoll,
} from '@fleet-web/duxs/timeline'
import {
  getSettings_UNSAFE,
  getShowAverageFuelConsumption,
  getShowTripsFuelConsumptionMetaDataSetting,
} from '@fleet-web/duxs/user'
import {
  getPreferences,
  getSensorPermission,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { downloadVehicleTripSpecial } from '@fleet-web/duxs/vehicles'
import DisclaimerModal from '@fleet-web/modules/app/components/disclaimer-modal'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { FixMeAny, TripsDownloadFileExtension } from '@fleet-web/types'
import { IconButton } from '@fleet-web/util-components'
import ActionIconButton from '@fleet-web/util-components/actionIconButton'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import SingleInfo from '@fleet-web/util-components/singleInfo'
import TooltipWrap from '@fleet-web/util-components/tooltipWrap'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import {
  getActivityActiveTab,
  getActivityDateRange,
} from '../FleetMapView/DetailsPanel/slice'
import type { FocusedVehicle } from '../FleetMapView/DetailsPanel/ui-types'
import { useMapFleetContext, type MapFleetContextValue } from '../MapFleetProvider'
import MapBottomPanelTableGridToolbar from './MapBottomPanelTable/MapBottomPanelTableGridToolbar'
import TimelineTableViewOrder from './TimelineTableViewOrder'
import TripDownloadModal from './TripDownloadModal'

const isVehicleType = (mapType: MapType | '') => ['vehicles', 'fleet'].includes(mapType)

type TimelineTableHarshEventName =
  | 'Speeding'
  | 'Road Speed'
  | 'Harsh Accel'
  | 'Harsh Braking'
  | 'Harsh Turning'
  | 'Excessive RPM'

type Props = {
  showRawData: boolean
  focusedItem: FocusedVehicle
  onTripViewClick: (view: MapActiveView) => void
  bottomPanelState: NonNullable<
    NonNullable<MapFleetContextValue['selectedVehicleMeta']>['bottomPanel']
  >
  selectedTripStartDate: Date
}

type TooltipBlockProps = {
  tooltipMessage: string
  value: string | number | undefined
  label: string
}

const TooltipBlock = ({ tooltipMessage, value, label }: TooltipBlockProps) => (
  <Tooltip
    arrow
    title={ctIntl.formatMessage({ id: tooltipMessage })}
    placement="top"
  >
    <SingleInfo>
      <Typography>{value ?? '--'}</Typography>
      <Typography variant="caption">{ctIntl.formatMessage({ id: label })}</Typography>
    </SingleInfo>
  </Tooltip>
)

const TimelineHeader = ({
  bottomPanelState,
  focusedItem,
  onTripViewClick,
  showRawData,
  selectedTripStartDate,
}: Props) => {
  const dispatch = useDispatch()
  const timelineTripsSensors = useTypedSelector(getTimelineTripsSensors)
  const timelineTablesActiveTab = useTypedSelector(getTimelineTablesActiveTab)
  const activityActiveTab = useTypedSelector(getActivityActiveTab)
  const showTripsFuelConsumptionMetaDataSetting = useTypedSelector(
    getShowTripsFuelConsumptionMetaDataSetting,
  )
  const showAverageFuelConsumption = useTypedSelector(getShowAverageFuelConsumption)
  const totals = useTypedSelector(getTimelineTotals)
  const sensorPermission = useTypedSelector(getSensorPermission)
  const {
    mapEnableTripDownload,
    timelineHasEconomyDisplay: showEconomy,
    timelineEconomyKmPerLiter: isKmPerLiter,
    downloadGoogleTrip,
    roadSpeed: allowRoadSpeed,
    distanceInMiles,
  } = useTypedSelector(getSettings_UNSAFE)
  const { dispatch: contextDispatch } = useMapFleetContext()
  const activityDateRange = useTypedSelector(getActivityDateRange)
  const {
    alerts: {
      speeding,
      roadSpeeding,
      harshAcceleration,
      harshBraking,
      harshCornering,
      overRev,
      totalEventsCount,
    } = {
      speeding: 0,
      roadSpeeding: 0,
      harshAcceleration: 0,
      harshBraking: 0,
      harshCornering: 0,
      overRev: 0,
      totalEventsCount: 0,
    },
  } = useTypedSelector(getTimelineEventsUI)
  const {
    tripsTablesPreferences: { visibility: tripsVisibility },
  } = useTypedSelector(getPreferences)
  const mapType = useTypedSelector(getMapType)

  const [pollTimer, setPollTimer] = useState(0)
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const timelineTablesIconMap: Record<
    TimelineTableHarshEventName,
    { icon: React.ReactNode; flag?: boolean }
  > = {
    Speeding: {
      icon: <SpeedIcon />,
    },
    'Road Speed': {
      icon: <OfflineBoltOutlinedIcon />,
      flag: allowRoadSpeed,
    },
    'Harsh Accel': {
      icon: <LocalCarWashIcon />,
    },
    'Harsh Braking': {
      icon: <WifiTetheringOutlinedIcon />,
    },
    'Harsh Turning': {
      icon: <DirectionsOutlinedIcon />,
    },
    'Excessive RPM': {
      icon: (
        <svg
          {...makeSanitizedInnerHtmlProp({
            dirtyHtml: require('../../../../assets/icons/excessive-rpm.svg'),
          })}
          width={24}
          height={24}
        />
      ),
    },
  } as const

  const handlePollOnClick = () => {
    dispatch(requestSVRPoll(focusedItem.id, selectedTripStartDate))

    const countDown = (timer: number) => {
      if (timer > 0) {
        window.setTimeout(() => countDown(timer - 1), 1000)
      }

      setPollTimer(timer)
    }

    countDown(12)
  }

  const isTodayDateSelected = () => {
    const todayDateTime = DateTime.local()
    const selectedTimelineDateTime = DateTime.fromJSDate(selectedTripStartDate)

    return todayDateTime.hasSame(selectedTimelineDateTime, 'day')
  }

  const handleRefreshClick = () => {
    const { id, isSingleTrip } = focusedItem

    const selectedTripStartDateTime = DateTime.fromJSDate(selectedTripStartDate)
    const startDate = selectedTripStartDateTime.startOf('day').toJSDate()
    const endDate = selectedTripStartDateTime.endOf('day').toJSDate()
    dispatch(
      fetchApiTimelineData({
        vehicleId: id,
        startTime: startDate,
        endTime: endDate,
        isSingleTrip,
      }),
    )
  }

  const handleOpenCloseDownloadModal = () => setIsDownloadModalOpen((prev) => !prev)

  const handleDownloadTripSpecial = (activeFileTab: TripsDownloadFileExtension) => {
    const type = activeFileTab
    const { id } = focusedItem

    GA4.event({
      category: 'Timeline',
      action: `Download Day Trip - ${type}`,
    })

    const dateRangeFromDate =
      activityActiveTab === 'multipleDays'
        ? activityDateRange.from
        : selectedTripStartDate
    const dateRangeToDate =
      activityActiveTab === 'multipleDays'
        ? activityDateRange.to
        : selectedTripStartDate

    dispatch(
      downloadVehicleTripSpecial({
        type,
        id,
        startTime: DateTime.fromJSDate(dateRangeFromDate).startOf('day').toJSDate(),
        endTime: DateTime.fromJSDate(dateRangeToDate).endOf('day').toJSDate(),
        zeroKmTrips: tripsVisibility.zeroKmTrips,
      }),
    )
    handleOpenCloseDownloadModal()
  }

  const handleTimelineTablesTabClick = (clickedTab: TimelineTableHarshEventName) => {
    dispatch(changeTimelineTablesActiveTab(clickedTab))
  }

  const displayDownload = isVehicleType(mapType) && downloadGoogleTrip

  const tableViewIcons = map(timelineTablesIconMap, (iconType, unTypedTab) => {
    const tab = unTypedTab as keyof typeof timelineTablesIconMap

    if ('flag' in iconType && !iconType.flag) return null

    // Get Total Badge Count
    let countType = 0
    if (tab === 'Speeding') {
      countType = speeding
    } else if (tab === 'Road Speed') {
      countType = roadSpeeding
    } else if (tab === 'Harsh Accel') {
      countType = harshAcceleration
    } else if (tab === 'Harsh Braking') {
      countType = harshBraking
    } else if (tab === 'Harsh Turning') {
      countType = harshCornering
    } else if (tab === 'Excessive RPM') {
      countType = overRev
    }
    return (
      <TooltipWrap
        key={tab}
        tooltipMessage={tab}
      >
        <ActionIconButton
          isActive={tab === timelineTablesActiveTab}
          variant="outlined"
          counter={countType}
          color="secondary"
          onClick={() => {
            handleTimelineTablesTabClick(tab)
            GA4.event({
              category: 'Timeline Table',
              action: `Harsh Event "${tab}" Button Click`,
            })
          }}
        >
          {timelineTablesIconMap[tab].icon}
        </ActionIconButton>
      </TooltipWrap>
    )
  })

  const statBlocks = isVehicleType(mapType)
    ? [
        <TooltipBlock
          key="STOP"
          tooltipMessage="Total Stop Time"
          value={totals?.stopTime}
          label="Stop"
        />,
        <TooltipBlock
          key="DISTANCE"
          tooltipMessage="Total Distance"
          value={totals?.totalDistance}
          label={distanceInMiles ? 'Miles' : 'Kilometers'}
        />,
        <TooltipBlock
          key="DRIVING"
          tooltipMessage="Hours Driving"
          value={totals?.drivingTime}
          label="Driving"
        />,
        <TooltipBlock
          key="IDLING"
          tooltipMessage="Hours Idling"
          value={totals?.idlingTime}
          label="Idling"
        />,
        <TooltipBlock
          key="IGNITION"
          tooltipMessage="Ignition Time"
          value={totals?.ignitionTime}
          label="Ignition"
        />,
        /* TEMP - Commented due to incorrect values coming from API
        <TooltipBlock
          key="ENGINE"
          tooltipMessage="Hours Engine Running"
          value={totalEngine}
          label='Engine'
        />
        */
      ]
    : null

  if (
    showTripsFuelConsumptionMetaDataSetting &&
    totals &&
    !isNil(totals.totalFuelUsed) &&
    statBlocks &&
    timelineTripsSensors.hasFuelSensors &&
    sensorPermission.fuel
  ) {
    const fuelEconomyVal = (
      Math.round(10000 * Number(totals.totalFuelEconomy)) / 10000
    ).toFixed(2)

    statBlocks.unshift(
      <TooltipBlock
        key="FUEL"
        tooltipMessage="Total Fuel Used"
        value={Math.round((totals.totalFuelUsed + Number.EPSILON) * 100) / 100}
        label="Fuel Used"
      />,
    )

    if (!isNaN(fuelEconomyVal as FixMeAny) && showEconomy) {
      const label = `Economy (${isKmPerLiter ? 'KM/l' : 'Lts/100 Kms'})`
      statBlocks.unshift(
        <TooltipBlock
          key="FUEL ECONOMY"
          tooltipMessage="Fuel Economy"
          value={fuelEconomyVal}
          label={label}
        />,
      )
    }

    if (!isNil(totals.totalDistance) && showAverageFuelConsumption) {
      statBlocks.unshift(
        <TooltipBlock
          key="AVERAGE FUEL CONSUMPTION"
          tooltipMessage="Average Fuel Consumption"
          value={
            totals.totalDistance > 0
              ? (
                  Math.round(
                    ((totals.totalFuelUsed + Number.EPSILON) * 10_000) /
                      totals.totalDistance,
                  ) / 100
                ).toFixed(2)
              : 0
          }
          label={distanceInMiles ? 'L/100Miles' : 'L/100km'}
        />,
      )
    }
  }

  const sectionType = bottomPanelState.sectionType

  return (
    <>
      <Stack
        sx={{
          paddingX: 2,
          paddingY: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: 'row',
          gap: 1,
        }}
      >
        <Stack>
          {isVehicleType(mapType) && (
            <ToggleButtonGroup
              value={sectionType}
              size="small"
              exclusive
              onChange={(_, newActiveView) => {
                if (newActiveView !== null) {
                  GA4.event({
                    category: 'Timeline',
                    action: `"${startCase(newActiveView)}" Button Click`,
                  })
                  onTripViewClick(newActiveView)
                  contextDispatch({
                    type: 'timelineBottomPanel_sectionTypeChange',
                    newSection: newActiveView,
                  })
                }
              }}
            >
              <ToggleButton value="timeline">
                <Tooltip title={ctIntl.formatMessage({ id: 'Timeline' })}>
                  <AccessTimeFilledIcon />
                </Tooltip>
              </ToggleButton>

              <ToggleButton value="graph">
                <Tooltip title={ctIntl.formatMessage({ id: 'Graph' })}>
                  <ShowChartIcon />
                </Tooltip>
              </ToggleButton>
              {activityActiveTab === 'daily' && (
                <ToggleButton value="table">
                  <Badge
                    badgeContent={sectionType !== 'table' ? totalEventsCount : 0}
                    color="primary"
                  >
                    <Tooltip title={ctIntl.formatMessage({ id: 'Table' })}>
                      <CalendarViewMonthIcon />
                    </Tooltip>
                  </Badge>
                </ToggleButton>
              )}
            </ToggleButtonGroup>
          )}
        </Stack>

        <Stack
          direction="row"
          gap={2}
        >
          <Stack direction="row">{statBlocks}</Stack>
          <Stack
            direction="row"
            gap={1}
          >
            {mapType === 'svr-units' &&
              focusedItem.can_poll_svr &&
              isTodayDateSelected() && (
                <>
                  <Button
                    variant="outlined"
                    color="secondary"
                    size="small"
                    onClick={handlePollOnClick}
                    disabled={pollTimer !== 0}
                  >
                    {pollTimer === 0 ? 'Poll' : `Polling ${pollTimer}s`}
                  </Button>
                  <TooltipWrap tooltipMessage="Refresh">
                    <ActionIconButton
                      onClick={handleRefreshClick}
                      isHalfPadding
                      variant="outlined"
                      color="secondary"
                    >
                      <CachedIcon />
                    </ActionIconButton>
                  </TooltipWrap>
                </>
              )}
            {showRawData && (
              <Link
                to={`/raw/vehicle/${focusedItem.id}`}
                target="_blank"
              >
                <IconButton
                  tooltipMessage="View Raw Data"
                  icon="database"
                />
              </Link>
            )}
            {displayDownload && (
              <TooltipWrap tooltipMessage="timelineHeader.downloadAllButton.tooltipMessage">
                <ActionIconButton
                  onClick={handleOpenCloseDownloadModal}
                  disabled={!mapEnableTripDownload}
                  isHalfPadding
                  variant="outlined"
                  color="secondary"
                >
                  <DownloadOutlinedIcon />
                </ActionIconButton>
              </TooltipWrap>
            )}
            {mapType !== 'asset trackers' && mapType !== 'asset-trackers' && (
              <TooltipWrap tooltipMessage="Disclaimer">
                <ActionIconButton
                  onClick={() => setIsModalOpen(true)}
                  isHalfPadding
                  variant="outlined"
                  color="secondary"
                >
                  <GppGoodOutlinedIcon />
                </ActionIconButton>
              </TooltipWrap>
            )}
            {isDownloadModalOpen && (
              <TripDownloadModal
                fileExtensionSelectorOnly
                onClose={handleOpenCloseDownloadModal}
                handleDownloadRequest={handleDownloadTripSpecial}
              />
            )}
            {isModalOpen && <DisclaimerModal onClose={() => setIsModalOpen(false)} />}
          </Stack>
        </Stack>
      </Stack>
      {sectionType === 'table' && (
        <Stack
          sx={{
            p: 2,
            flexDirection: 'row',
            justifyContent:
              mapType !== 'svr-units' &&
              mapType !== 'asset trackers' &&
              mapType !== 'asset-trackers'
                ? 'space-between'
                : 'flex-end',
            alignItems: 'center',
          }}
        >
          {mapType !== 'svr-units' &&
            mapType !== 'asset trackers' &&
            mapType !== 'asset-trackers' && (
              <Stack
                direction="row"
                gap={2}
              >
                {tableViewIcons}
              </Stack>
            )}
          <Stack direction="row">
            <TimelineTableViewOrder />
            <MapBottomPanelTableGridToolbar />
          </Stack>
        </Stack>
      )}
    </>
  )
}

export default TimelineHeader
