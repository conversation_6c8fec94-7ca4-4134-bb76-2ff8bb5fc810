import type { ScaleConfig } from '@visx/scale'
import { match, P } from 'ts-pattern'

import type { DigitalTimelineSensor } from '@fleet-web/duxs/timeline'

import type { ChartDatum } from './ui-types'

export const generateCategoryAxisData = <Category extends string>(
  categories: Array<Category>,
) => {
  const { categoryMap, tickValues } = categories.reduce(
    (acc, category, i) => {
      acc.categoryMap[category] = i
      acc.tickValues.push(i)
      return acc
    },
    {
      categoryMap: {} as Record<Category, number>,
      tickValues: [] as Array<number>,
    },
  )
  const yMax = tickValues[tickValues.length - 1]
  return {
    scale: { type: 'linear', domain: [0, yMax] } satisfies ScaleConfig,
    categoryMap,
    tickFormatter: (value: number) => categories[value],
    categories,
  }
}

export const createVehicleEventsChartYDigitalAccessorGetter =
  ({ categoryMap }: { categoryMap: Record<string, number> }) =>
  (sensor: DigitalTimelineSensor) =>
  (d: ChartDatum) => {
    // NOTE: the type of sensorValue can be string or number
    const sensorValue = d.sensors[sensor.sensorNumber]

    //ignore the “-” values
    if (sensorValue === '-') {
      return null
    }

    return match(sensorValue)
      .with(P.string, P.number, (value) => categoryMap[value.toString()])
      .otherwise(() => null)
  }
