import { memo, useEffect, useMemo } from 'react'
import { isNil } from 'lodash'
import { Stack, styled as styledMui } from '@karoo-ui/core'
import { ParentSize } from '@visx/responsive'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'
import styled from 'styled-components'
import { z } from 'zod'

import {
  getActivityActiveTabTimelineChartData,
  getIsTimelineChartDataLoadingState,
  getTimelineSensorWithUniqueColorData,
  getTimelineSortedSensorList,
  getTimelineXAxisChartType,
  onChartXVariantTabGroupClick,
  onTimelineChartsPointerUp,
  type AnalogTimelineSensor,
  type DigitalTimelineSensor,
} from '@fleet-web/duxs/timeline'
import { useUserIdbState, type IdbStateSetter } from '@fleet-web/hooks/useUserIdbState'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { DropdownOption } from '@fleet-web/types'
import { Spinner } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import ReTabGroup from '@fleet-web/util-components/ReTabGroup'

import ChartCheckboxLabel from './ChartCheckboxLabel'
import VehicleEventsCharts, {
  brushContainerHeightInPx,
  chartsAccessibilityLabel,
} from './VehicleEventsCharts'

const sensorLegends: Array<
  DropdownOption<ReturnType<typeof getTimelineXAxisChartType>>
> = [
  { value: 'time', name: 'Time' },
  { value: 'distance', name: 'Distance' },
]

const chartSelectedSensorsSchema = z.record(z.string(), z.boolean())

type ChartSelectedSensors = z.infer<typeof chartSelectedSensorsSchema>

const TimelineChart = () => {
  const chartSelectedSensorsState = useUserIdbState({
    stateKey: 'mapBottomPanelChartSelectedSensors',
    getZodSchema: () => chartSelectedSensorsSchema,
    defaultValue: {},
    keepStateOnIdbAfterLogout: true,
  })

  if (chartSelectedSensorsState === 'loading') {
    return null
  }

  return (
    <Content
      chartSelectedSensors={chartSelectedSensorsState[0]}
      setChartSelectedSensors={chartSelectedSensorsState[1]}
    />
  )
}

const Content = ({
  chartSelectedSensors,
  setChartSelectedSensors,
}: {
  setChartSelectedSensors: IdbStateSetter<ChartSelectedSensors>
  chartSelectedSensors: ChartSelectedSensors
}) => {
  const dispatch = useDispatch()

  const sensorsList = useTypedSelector(getTimelineSensorWithUniqueColorData)
  const chartType = useTypedSelector(getTimelineXAxisChartType) ?? ''
  const activityTabChartData = useTypedSelector(getActivityActiveTabTimelineChartData)
  const isLoadingData = useTypedSelector(getIsTimelineChartDataLoadingState)
  const timelineSortedSensorList = useTypedSelector(getTimelineSortedSensorList)

  // Select the first available sensor by default
  useEffect(() => {
    if (
      Object.keys(chartSelectedSensors).length === 0 &&
      timelineSortedSensorList.length > 0
    ) {
      setChartSelectedSensors((prev) => ({
        ...prev,
        [timelineSortedSensorList[0].sensorNumber]: true,
      }))
    }
  }, [timelineSortedSensorList, setChartSelectedSensors, chartSelectedSensors])

  const { analogYAxisChartSelectedSensors, digitalYAxisChartSelectedSensors } =
    useMemo(() => {
      const analogYAxisChartSelectedSensors: Array<AnalogTimelineSensor> = []
      const digitalYAxisChartSelectedSensors: Array<DigitalTimelineSensor> = []

      for (const sensor of sensorsList) {
        if (chartSelectedSensors[sensor.sensorNumber]) {
          if (sensor.signalType === 'DIGITAL') {
            digitalYAxisChartSelectedSensors.push(sensor)
          } else {
            analogYAxisChartSelectedSensors.push(sensor)
          }
        }
      }

      return {
        analogYAxisChartSelectedSensors,
        digitalYAxisChartSelectedSensors,
      }
    }, [sensorsList, chartSelectedSensors])

  const timeTickFormat = (() => {
    switch (activityTabChartData.type) {
      case 'daily': {
        return 't'
      }
      case 'multipleDays':
      case 'booking': {
        return 'f'
      }
    }
  })()

  const distanceTickFormatter = (distance: number) => `${Math.round(Number(distance))}`

  const timeTickFormatter = (date: Date) =>
    DateTime.fromJSDate(date).toFormat(timeTickFormat)

  const sensorBoundariesValues = useMemo(() => {
    const boundaries = {} as Record<
      string,
      { firstInstance: string | number | null; lastInstance: string | number | null }
    >
    const sensorsArray = sensorsList.map((sensor) => sensor.sensorNumber)
    let sensorsToFind = new Set(sensorsArray)

    // Initialize boundaries for all sensors
    for (const sensor of sensorsArray) {
      boundaries[sensor] = { firstInstance: null, lastInstance: null }
    }

    if (
      activityTabChartData.type !== 'booking' &&
      activityTabChartData.data.length > 0
    ) {
      // Forward pass to find the first instance
      for (const reading of activityTabChartData.data) {
        for (const sensor of sensorsToFind) {
          if (!isNil(reading.sensors[sensor])) {
            boundaries[sensor].firstInstance = reading.sensors[sensor] as
              | string
              | number

            sensorsToFind.delete(sensor)
          }
        }

        if (sensorsToFind.size === 0) break
      }

      sensorsToFind = new Set(sensorsArray)

      // Backward pass to find the last instance
      for (let i = activityTabChartData.data.length - 1; i >= 0; i--) {
        const reading = activityTabChartData.data[i]
        for (const sensor of sensorsToFind) {
          if (!isNil(reading.sensors[sensor])) {
            boundaries[sensor].lastInstance = reading.sensors[sensor] as string | number

            sensorsToFind.delete(sensor)
          }
        }

        if (sensorsToFind.size === 0) break // Stop if all last instances found
      }
    }

    return boundaries
  }, [activityTabChartData.data, activityTabChartData.type, sensorsList])

  if (isLoadingData) {
    return <Spinner absolute />
  }

  return (
    <Stack
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
        px: 2,
        pt: 2,
        overflow: 'auto',
      }}
    >
      <ControlPanel>
        <ReTabGroup
          value={chartType}
          options={sensorLegends}
          handleClick={(value) => {
            GA4.event({
              category: 'Timeline Chart',
              action: `Select ${value} Chart Type`,
            })

            dispatch(
              onChartXVariantTabGroupClick(
                value as (typeof sensorLegends)[number]['value'],
              ),
            )
          }}
        />
        <SensorLabels>
          {sensorsList.map((sensor) => (
            <ChartCheckboxLabel
              key={sensor.sensorNumber}
              lineColor={sensor.color}
              isActive={chartSelectedSensors[sensor.sensorNumber]}
              text={sensor.name}
              onClick={() => {
                setChartSelectedSensors((prev) => ({
                  ...prev,
                  [sensor.sensorNumber]: !prev[sensor.sensorNumber],
                }))
              }}
            />
          ))}
        </SensorLabels>
      </ControlPanel>
      <Body>
        {activityTabChartData.type !== 'booking' &&
        activityTabChartData.data.length > 0 ? (
          <StyledParentSize>
            {({ height, width }) => (
              <VehicleEventsCharts
                width={width}
                height={height}
                sensorBoundariesValues={sensorBoundariesValues}
                analogYAxisChartSelectedSensors={analogYAxisChartSelectedSensors}
                digitalYAxisChartSelectedSensors={digitalYAxisChartSelectedSensors}
                data={activityTabChartData.data}
                distanceTickFormatter={distanceTickFormatter}
                timeTickFormatter={timeTickFormatter}
                xType={chartType}
                onChartsPointerUp={({ datum }) =>
                  dispatch(
                    onTimelineChartsPointerUp({
                      nearestEventId: datum.eventId,
                    }),
                  )
                }
              />
            )}
          </StyledParentSize>
        ) : (
          <EmptyDataMessage>
            {ctIntl.formatMessage({ id: 'No Events for Current Day' })}
          </EmptyDataMessage>
        )}
      </Body>
    </Stack>
  )
}

export default memo(TimelineChart)

const ControlPanel = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    justifyContent: 'space-between',
    flexDirection: 'row',
    gap: 1,
  }),
)

const Body = styledMui('div')({
  display: 'flex',
  flexGrow: 1,
  flexDirection: 'column',
  height: '100%',
})

const StyledParentSize = styled(ParentSize)`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - ${brushContainerHeightInPx - 8}px) !important;

  > svg[aria-label='${chartsAccessibilityLabel}'] {
    cursor: pointer;

    text {
      font-size: 8px;
      font-weight: 400;
    }
  }
`

const SensorLabels = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    flexDirection: 'row',
    gap: 0.5,
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
  }),
)

const EmptyDataMessage = styledMui('div')`
  display: flex;
  justify-content: center;
`
