import {
  useEffect,
  useMemo,
  useState,
  type ComponentPropsWithoutRef,
  type ReactNode,
} from 'react'
import { flatMap, isNil, uniq } from 'lodash'
import type { AxisScale } from '@visx/axis'
import { Brush } from '@visx/brush'
import type { Bounds as BrushBounds } from '@visx/brush/lib/types'
import { curveStepAfter } from '@visx/curve'
import { scaleLinear, scaleTime, type ScaleConfig } from '@visx/scale'
import { extent } from '@visx/vendor/d3-array'
import {
  Axis,
  EventEmitterProvider,
  Grid,
  LineSeries,
  Tooltip,
  XYChart,
  type EventHandlerParams,
} from '@visx/xychart'
import type { AxisProps } from '@visx/xychart/lib/components/axis/Axis'
import styled from 'styled-components'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import type {
  AnalogTimelineSensor,
  DigitalTimelineSensor,
} from '@fleet-web/duxs/timeline'
import { variables } from '@fleet-web/shared/components/styled/global-styles'

import type { ChartDatum } from './ui-types'
import {
  createVehicleEventsChartYDigitalAccessorGetter,
  generateCategoryAxisData,
} from './utils'

export const chartsAccessibilityLabel = 'XYChart'

const chartMargin = { top: 25, right: 25, bottom: 25, left: 50 }
export const brushContainerHeightInPx = 32
const brushBaseColor = 'steelblue'
const sensorValuePlaceholder = '--'

/** Will only format with two decimal places if original number has 2 or more decimal places. Otherwise, it will be displayed as is.
 *  It will NOT add unnecessary leading zeros.
 */
const roundNumberUpTo2DecimalPlaces = (num: number) =>
  Math.round((num + Number.EPSILON) * 100) / 100

const xDistanceAccessor = (d: ChartDatum) => d?.distance ?? 0

const xTimeAccessor = (d: ChartDatum) => d?.date ?? 0

const getYAnalogAccessor = (sensor: AnalogTimelineSensor) => (d: ChartDatum) =>
  isNil(d.sensors[sensor.sensorNumber]) ? null : Number(d.sensors[sensor.sensorNumber])

type Props = {
  width: number
  height: number
  sensorBoundariesValues: Record<
    string,
    { firstInstance: string | number | null; lastInstance: string | number | null }
  >
  data: Array<ChartDatum>
  xType: 'distance' | 'time'
  analogYAxisChartSelectedSensors: Array<AnalogTimelineSensor>
  digitalYAxisChartSelectedSensors: Array<DigitalTimelineSensor>
  timeTickFormatter: (date: Date) => string
  distanceTickFormatter: (distance: number) => string
  onChartsPointerUp: (params: EventHandlerParams<ChartDatum>) => void
}

export default function VehicleEventsCharts({
  width,
  height,
  sensorBoundariesValues,
  data,
  xType,
  digitalYAxisChartSelectedSensors,
  analogYAxisChartSelectedSensors,
  timeTickFormatter,
  distanceTickFormatter,
  onChartsPointerUp,
}: Props) {
  const [filteredChartData, setFilteredChartData] = useState(data)

  const chartsToDisplay =
    analogYAxisChartSelectedSensors.length > 0 &&
    digitalYAxisChartSelectedSensors.length > 0
      ? 'two'
      : 'one' // It is assumed that when this components renders, we have data to at least show __one__ chart

  /* On initialization, "width" prop will be __zero__ so the first calculation returns a negative number. To account for this, we set a min brushMaxWidth of __zero__ */
  const brushMaxWidth = Math.max(width - chartMargin.right - chartMargin.left, 0)

  /** Forces brush to reset when on window resize. This prevents brush from being out of sync with chart data on window resize */
  const brushKey = brushMaxWidth

  useEffect(() => {
    setFilteredChartData(data)
  }, [data, brushKey])

  const xScale = (
    xType === 'distance'
      ? {
          type: 'linear',
          zero: false,
        }
      : { type: 'time' }
  ) satisfies ScaleConfig

  const chartHeight = (() => {
    const availableHeight = height - brushContainerHeightInPx
    const computedHeight =
      chartsToDisplay === 'two' ? availableHeight / 2 : availableHeight

    return Math.max(computedHeight, 80 /* min height */)
  })()

  const tooltipCommonProps = {
    snapTooltipToDatumX: true,
    snapTooltipToDatumY: true,
    showVerticalCrosshair: true,
  }

  const commonXYChartProps = {
    width,
    height: chartHeight,
    margin: chartMargin,
    xScale,
    onPointerUp: onChartsPointerUp,
    accessibilityLabel: chartsAccessibilityLabel,
  } as const

  const xAccessor = xType === 'distance' ? xDistanceAccessor : xTimeAccessor

  const categoryAxisData = useMemo(() => {
    const categories = uniq(
      flatMap(digitalYAxisChartSelectedSensors, (sensor) => sensor.categories),
    )

    return generateCategoryAxisData(categories)
  }, [digitalYAxisChartSelectedSensors])

  const getYDigitalAccessor = useMemo(
    () =>
      createVehicleEventsChartYDigitalAccessorGetter({
        categoryMap: categoryAxisData.categoryMap,
      }),
    [categoryAxisData.categoryMap],
  )

  const validChartDataBySensorNumber = useMemo(() => {
    const alreadyRenderedSensors = {} as Record<string, boolean>

    const validChartDataBySensorNumber: {
      [sensorNumber: string]: typeof filteredChartData | undefined
    } = {}

    const addChartDatumToDictionary = (
      sensor: AnalogTimelineSensor | DigitalTimelineSensor,
      chartDatum: ChartDatum,
    ) => {
      if (validChartDataBySensorNumber[sensor.sensorNumber] === undefined) {
        validChartDataBySensorNumber[sensor.sensorNumber] = [chartDatum]
      } else {
        validChartDataBySensorNumber[sensor.sensorNumber]?.push(chartDatum)
      }
    }

    const editedValueWithBoundary = (
      sensor: AnalogTimelineSensor | DigitalTimelineSensor,
      chartDatum: ChartDatum,
    ) =>
      match(sensor.sensorTypeId)
        .with(
          '6',
          '9', // fuel level sensors
          () =>
            ({
              ...chartDatum,
              sensors: {
                ...chartDatum.sensors,
                [sensor.sensorNumber]: alreadyRenderedSensors[sensor.sensorNumber]
                  ? sensorBoundariesValues[sensor.sensorNumber].lastInstance
                  : sensorBoundariesValues[sensor.sensorNumber].firstInstance,
              },
            }) satisfies ChartDatum,
        )
        .otherwise(() => chartDatum)

    const writeFallbackBoundary = (
      sensor:
        | (typeof digitalYAxisChartSelectedSensors)[number]
        | (typeof analogYAxisChartSelectedSensors)[number],
      chartDatum: (typeof filteredChartData)[number],
      idx: number,
    ) => {
      // BE trolled us by not giving us the juice, magic must be done
      // write in the last array entry
      if (
        alreadyRenderedSensors[sensor.sensorNumber] &&
        filteredChartData.length - 1 === idx
      ) {
        addChartDatumToDictionary(sensor, editedValueWithBoundary(sensor, chartDatum))
        // write at the beginning
      } else if (!alreadyRenderedSensors[sensor.sensorNumber]) {
        addChartDatumToDictionary(sensor, editedValueWithBoundary(sensor, chartDatum))
        alreadyRenderedSensors[sensor.sensorNumber] = true
      }
    }

    // eslint-disable-next-line
    // eslint-disable-next-line unicorn/no-array-for-each
    filteredChartData.forEach((chartDatum, idx) => {
      for (const sensor of digitalYAxisChartSelectedSensors) {
        if (isNil(getYDigitalAccessor(sensor)(chartDatum))) {
          writeFallbackBoundary(sensor, chartDatum, idx)
        } else {
          alreadyRenderedSensors[sensor.sensorNumber] = true
          addChartDatumToDictionary(sensor, chartDatum)
        }
      }

      for (const sensor of analogYAxisChartSelectedSensors) {
        if (isNil(getYAnalogAccessor(sensor)(chartDatum))) {
          writeFallbackBoundary(sensor, chartDatum, idx)
        } else {
          alreadyRenderedSensors[sensor.sensorNumber] = true
          addChartDatumToDictionary(sensor, chartDatum)
        }
      }
    })

    return validChartDataBySensorNumber
  }, [
    analogYAxisChartSelectedSensors,
    digitalYAxisChartSelectedSensors,
    filteredChartData,
    getYDigitalAccessor,
    sensorBoundariesValues,
  ])

  const xAxisTickFormatter = (value: ReturnType<typeof xAccessor>) => {
    if (typeof value === 'number') {
      return distanceTickFormatter(value)
    }
    return timeTickFormatter(value)
  }

  const brushXConfig = useMemo(() => {
    const range = [0, brushMaxWidth]

    if (xType === 'time') {
      return {
        accessor: xTimeAccessor,
        scale: scaleTime({
          range,
          domain: extent(data, xTimeAccessor) as [Date, Date],
        }),
      }
    }

    return {
      accessor: xDistanceAccessor,
      scale: scaleLinear({
        range,
        domain: extent(data, xDistanceAccessor) as [number, number],
      }),
    }
  }, [brushMaxWidth, data, xType])

  const brushYScale = useMemo(() => scaleLinear<number>(), [])

  const initialBrushPosition = useMemo(
    () => ({
      start: {
        x: brushXConfig.scale(brushXConfig.accessor(data[0])),
      },
      end: {
        x: brushXConfig.scale(brushXConfig.accessor(data[data.length - 1])),
      },
    }),
    [brushXConfig, data],
  )

  const onBrushChange = (domain: BrushBounds | null) => {
    if (domain === null) return

    const { x0, x1 } = domain
    const filteredData = data.filter((d) => {
      const xNumOrDate = brushXConfig.accessor(d)
      const x = typeof xNumOrDate === 'number' ? xNumOrDate : xNumOrDate.getTime()

      return x > x0 && x < x1
    })

    setFilteredChartData(filteredData)
  }

  /** Workaround to prevent overlap between tooltips of the two graphs. Please, REMOVE this if visx current version supports this behavior out of the box */
  const getAnalogYAxisChartTooltipOffsetTop = () => {
    if (chartsToDisplay === 'one') {
      return undefined
    }
    const tooltipRowApproxHeight = 29
    const heightCompensation = height * 0.46
    const topChartTooltipRowsCount = analogYAxisChartSelectedSensors.length + 1
    const bottomChartTooltipRowsCount = digitalYAxisChartSelectedSensors.length + 1

    const offset =
      topChartTooltipRowsCount * tooltipRowApproxHeight +
      bottomChartTooltipRowsCount * tooltipRowApproxHeight +
      tooltipRowApproxHeight -
      heightCompensation /** The more height we have available for two graphs, the less offset we need */

    return offset > 0 ? -offset : -tooltipRowApproxHeight // default
  }

  const renderAnalogYAxisChart = () => (
    <XYChart
      {...commonXYChartProps}
      yScale={{ type: 'linear' }}
    >
      <ChartAxis
        orientation="bottom"
        tickFormat={xAxisTickFormatter}
        numTicks={4}
      />
      <ChartAxis orientation="left" />
      <Grid
        columns={false}
        numTicks={4}
      />
      {analogYAxisChartSelectedSensors.map((sensor) => (
        <LineSeries
          key={sensor.sensorNumber}
          stroke={sensor.color}
          dataKey={sensor.sensorNumber}
          data={validChartDataBySensorNumber[sensor.sensorNumber] ?? []}
          xAccessor={xAccessor}
          yAccessor={getYAnalogAccessor(sensor)}
        />
      ))}
      <Tooltip<ChartDatum>
        {...tooltipCommonProps}
        offsetTop={getAnalogYAxisChartTooltipOffsetTop()}
        renderTooltip={({ tooltipData }) => {
          if (tooltipData?.nearestDatum === undefined) {
            return null
          }
          const nearestDatum = tooltipData.nearestDatum

          return (
            <ChartTooltipWithXAxisValueContainer
              xAxisValue={xAxisTickFormatter(xAccessor(nearestDatum.datum))}
            >
              {analogYAxisChartSelectedSensors.map((sensor) => {
                const sensorValue = getYAnalogAccessor(sensor)(nearestDatum.datum)
                return (
                  <ChartTooltipSensorInfo
                    key={sensor.sensorNumber}
                    sensorColor={sensor.color}
                    sensorName={sensor.name}
                    sensorValue={
                      isNil(sensorValue)
                        ? sensorValuePlaceholder
                        : `${roundNumberUpTo2DecimalPlaces(sensorValue)} ${sensor.unit}`
                    }
                  />
                )
              })}
            </ChartTooltipWithXAxisValueContainer>
          )
        }}
      />
    </XYChart>
  )

  const renderDigitalYAxisChart = () => {
    type YAccessorReturnType = ReturnType<ReturnType<typeof getYDigitalAccessor>>
    return (
      <XYChart
        {...commonXYChartProps}
        yScale={categoryAxisData.scale}
      >
        <ChartAxis
          orientation="bottom"
          tickFormat={xAxisTickFormatter}
        />
        <DigitalSensorYAxis
          axisProps={{
            // Anchor text to start so that labels are __always__ visible. Previously they were aligned to the end and the beginning of the word was cut off sometimes.
            // This way, it always show the entire word. Downside is that it may overflow into the chart area.
            tickLabelProps: () => ({ textAnchor: 'start', x: -45 }),
            tickFormat: (value: YAccessorReturnType) =>
              isNil(value) ? undefined : categoryAxisData.tickFormatter(value),
          }}
        />
        <Grid
          columns={false}
          numTicks={2}
        />
        {digitalYAxisChartSelectedSensors.map((sensor) => (
          <LineSeries
            key={sensor.sensorNumber}
            dataKey={sensor.sensorNumber}
            stroke={sensor.color}
            curve={curveStepAfter}
            data={validChartDataBySensorNumber[sensor.sensorNumber] ?? []}
            xAccessor={xAccessor}
            yAccessor={getYDigitalAccessor(sensor)}
          />
        ))}
        <Tooltip<ChartDatum>
          {...tooltipCommonProps}
          renderTooltip={({ tooltipData }) => {
            if (tooltipData?.nearestDatum === undefined) {
              return null
            }
            const nearestDatum = tooltipData.nearestDatum

            return (
              <ChartTooltipWithXAxisValueContainer
                xAxisValue={xAxisTickFormatter(xAccessor(nearestDatum.datum))}
              >
                {digitalYAxisChartSelectedSensors.map((sensor) => {
                  const sensorValue = getYDigitalAccessor(sensor)(nearestDatum.datum)

                  return (
                    <ChartTooltipSensorInfo
                      key={sensor.sensorNumber}
                      sensorColor={sensor.color}
                      sensorName={sensor.name}
                      sensorValue={
                        isNil(sensorValue)
                          ? sensorValuePlaceholder
                          : categoryAxisData.categories[sensorValue]
                      }
                    />
                  )
                })}
              </ChartTooltipWithXAxisValueContainer>
            )
          }}
        />
      </XYChart>
    )
  }

  return (
    /** Allows tooltips to be synced */
    <EventEmitterProvider>
      {analogYAxisChartSelectedSensors.length > 0
        ? renderAnalogYAxisChart()
        : renderDigitalYAxisChart()}
      {brushMaxWidth > 0 ? (
        <BrushGroup width={brushMaxWidth}>
          <rect
            width={brushMaxWidth}
            height={brushContainerHeightInPx}
            stroke={brushBaseColor}
            fill="white"
            strokeWidth="1"
          />
          <Brush
            key={brushKey}
            xScale={brushXConfig.scale}
            yScale={brushYScale}
            width={brushMaxWidth}
            height={brushContainerHeightInPx}
            onChange={onBrushChange}
            handleSize={4}
            selectedBoxStyle={{
              fill: brushBaseColor,
              fillOpacity: 0.2,
              stroke: brushBaseColor,
              strokeWidth: 2,
              strokeOpacity: 0.8,
            }}
            initialBrushPosition={initialBrushPosition}
            useWindowMoveEvents
          />
        </BrushGroup>
      ) : null}
      {chartsToDisplay === 'two' ? renderDigitalYAxisChart() : null}
    </EventEmitterProvider>
  )
}

const ChartTooltipSensorValue = styled.span`
  color: ${variables.gray80};
`

const BrushGroup = styled.svg`
  height: ${brushContainerHeightInPx}px;
  margin-left: ${chartMargin.left}px;
  margin-right: ${chartMargin.right}px;
`

type ChartTooltipSensorInfoProps = {
  sensorColor: string
  sensorName: string
  sensorValue: ReactNode
}

function ChartTooltipSensorInfo({
  sensorColor,
  sensorName,
  sensorValue,
}: ChartTooltipSensorInfoProps) {
  return (
    <p>
      <span style={{ color: sensorColor }}>{sensorName} </span>
      <ChartTooltipSensorValue>{sensorValue}</ChartTooltipSensorValue>
    </p>
  )
}

type ChartTooltipWithXAxisValueContainerProps = {
  children: ReactNode
  xAxisValue: ReactNode
}

function ChartTooltipWithXAxisValueContainer({
  children,
  xAxisValue,
}: ChartTooltipWithXAxisValueContainerProps) {
  return (
    <div style={{ color: variables.gray60 }}>
      <p>{xAxisValue}</p>
      {children}
    </div>
  )
}

type DigitalSensorYAxisProps = {
  axisProps: Except<AxisProps<AxisScale>, 'orientation'>
}

function DigitalSensorYAxis({ axisProps }: DigitalSensorYAxisProps) {
  return (
    <ChartAxis
      orientation="left"
      {...axisProps}
    />
  )
}

const ChartAxis = styled(
  ({
    className,
    ...props
  }: ComponentPropsWithoutRef<typeof Axis> & { className?: string }) => (
    <Axis
      axisClassName={className}
      tickClassName="styled-tick"
      {...props}
    />
  ),
)`
  & .styled-tick {
    /* Prevents unintentional ticks selection while dragging/moving the brush */
    user-select: none;
  }
`
