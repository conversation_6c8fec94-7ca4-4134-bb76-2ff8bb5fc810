import { isNil } from 'lodash'
import {
  DataGrid,
  useCallbackBranded,
  type GridColumnVisibilityModel,
} from '@karoo-ui/core'
import { useDispatch } from 'react-redux'

import type {
  TripIdOrStopTripId,
  TripsWithOrWithoutStops,
} from '@fleet-web/api/timeline/types'
import type { VehicleTripId } from '@fleet-web/api/types'
import type {
  reGetTimelineTableDataFilteredByCategory,
  TimelineEventWithRoadSpeed,
} from '@fleet-web/duxs/timeline'
import { getMapZoomOptions } from '@fleet-web/duxs/user'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTimelineTableQVCNColumns } from '@fleet-web/modules/tables/re-timeline-table-qvcn'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import { zoomToEvents } from '../actions'
import { useMapBottomPanelTripTablesContext } from './MapBottomPanelTable/MapBottomPanelTripTablesContext'
import type { TimelineTableSingleTripToSelect } from './types'

type Props = {
  tripsUI: TripsWithOrWithoutStops
  distanceInMiles: boolean
  selectedTripId: VehicleTripId | Array<VehicleTripId> | null
  timelineTrips: Array<Array<TimelineEventWithRoadSpeed>>
  dayTripEvents: ReturnType<typeof reGetTimelineTableDataFilteredByCategory>
  displayDownload: boolean
  onTripSelect: (trip: TimelineTableSingleTripToSelect | null) => void
  onDataExportRequest: (tripId: TripIdOrStopTripId) => void
  onTripStatsShow: (tripId: TripIdOrStopTripId) => void
}

const TimelineTableQVCN = ({
  tripsUI,
  distanceInMiles,
  selectedTripId,
  timelineTrips,
  dayTripEvents,
  displayDownload,
  onTripSelect,
  onDataExportRequest,
  onTripStatsShow,
}: Props) => {
  const dispatch = useDispatch()
  const { maxZoom } = useTypedSelector(getMapZoomOptions)
  const { gridApiRef } = useMapBottomPanelTripTablesContext()
  const { getTimelineTableCompactColumns } = useTimelineTableQVCNColumns()

  const handleTripSelect = (tripId: TripIdOrStopTripId) => {
    const isSelected =
      !isNil(tripId) &&
      !!selectedTripId &&
      selectedTripId.includes(tripId as VehicleTripId)
    const tripIndex = tripsUI.findIndex(
      (trip) => 'tripId' in trip && trip.tripId !== undefined && trip.tripId === tripId,
    )
    const tripData = timelineTrips[tripIndex]

    onTripSelect(
      isSelected
        ? null
        : { selectionType: 'single', id: tripId as VehicleTripId, events: tripData },
    )
    dispatch(zoomToEvents({ events: isSelected ? dayTripEvents : tripData, maxZoom }))
  }

  const timelineTableCompactColumns = getTimelineTableCompactColumns({
    distanceInMiles,
    onTripStatsShow,
    onTripSelect: handleTripSelect,
    onDataExportRequest,
    displayDownload,
  })

  const columnVisibilityModel: GridColumnVisibilityModel = Object.fromEntries(
    timelineTableCompactColumns.map((c) => [c.field, true]),
  )

  return (
    <UserDataGridWithSavedSettingsOnIDB
      apiRef={gridApiRef}
      disableRowSelectionOnClick
      pagination
      Component={DataGrid}
      dataGridId={'timelineTableQVCN'}
      columns={timelineTableCompactColumns}
      rows={tripsUI}
      getRowId={useCallbackBranded((row: (typeof tripsUI)[number]) => row.tripId, [])}
      columnVisibilityModel={columnVisibilityModel}
    />
  )
}

export default TimelineTableQVCN
