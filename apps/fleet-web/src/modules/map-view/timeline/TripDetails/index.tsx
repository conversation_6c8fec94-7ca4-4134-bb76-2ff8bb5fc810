import { Stack, Typography } from '@karoo-ui/core'
import FileCopyOutlinedIcon from '@mui/icons-material/FileCopyOutlined'
import StickyNote2OutlinedIcon from '@mui/icons-material/StickyNote2Outlined'
import { useDispatch } from 'react-redux'
import { z } from 'zod'

import type { FetchTimelineEventsUI } from '@fleet-web/api/timeline/types'
import { updateTripDetails } from '@fleet-web/duxs/timeline'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import TripPopover from './tripPopover'
import TripTypeDropdown from './tripTypeDropdown'

type TripUI = FetchTimelineEventsUI.ParsedApiTrip

type TripDetailButtonsProps = {
  trip: TripUI
  info: { vehicleId: string }
  vehiclesViewStatus: boolean
  isTripOngoing: boolean
}

const TripDetails = ({ trip, info, isTripOngoing }: TripDetailButtonsProps) => {
  const dispatch = useDispatch()

  const { notes, extraNotes, noteTypeId } = trip

  const updateDetail = ({ field, value }: { field: string; value: string | null }) => {
    dispatch(
      updateTripDetails({
        vehicleId: info.vehicleId,
        trip: {
          ...trip,
          [field]: value,
        },
      }),
    )
  }

  return (
    <Stack
      direction="row"
      gap={2}
      alignItems="center"
    >
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
      >
        <FileCopyOutlinedIcon sx={{ fontSize: '16px', color: 'rgba(0, 0, 0, 0.56)' }} />
        <Typography
          variant="caption"
          color="textSecondary"
          noWrap
        >
          {ctIntl.formatMessage({ id: 'Trip details' })}
        </Typography>
      </Stack>
      <Stack
        direction="row"
        gap={2}
        alignItems="center"
      >
        <TripPopover
          props={{
            detail: notes,
            fieldName: 'notes',
            fieldLabel: 'Add title',
            defaultValues: { notes: notes ?? '' },
            schema: z.object({
              notes: z.string().max(50).min(1),
            }),
            onDelete: (fieldName) => {
              updateDetail({ field: fieldName, value: null })
            },
            onSave: (values, field) => {
              updateDetail({ field, value: values.notes })
            },
            isTripOngoing,
          }}
        />
        <TripPopover
          props={{
            detail: extraNotes,
            fieldName: 'extraNotes',
            fieldLabel: 'Add note',
            defaultValues: { extraNotes: extraNotes ?? '' },
            schema: z.object({
              extraNotes: z.string().max(200).min(1),
            }),
            onDelete: (field) => {
              updateDetail({ field, value: null })
            },
            onSave: (values, field) => {
              updateDetail({ field, value: values.extraNotes })
            },
            chipProps: {
              avatar: <StickyNote2OutlinedIcon sx={{ fontSize: '20px' }} />,
            },
            textFieldProps: { multiline: true },
            isTripOngoing,
          }}
        />

        <TripTypeDropdown
          props={{
            selectedValue: noteTypeId,
            onSelectOption: (option) => {
              updateDetail({ field: 'noteTypeId', value: option })
            },
            isTripOngoing,
          }}
        />
      </Stack>
    </Stack>
  )
}

export default TripDetails
