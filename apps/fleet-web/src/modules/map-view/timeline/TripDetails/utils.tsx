import type { ReactElement } from 'react'
import { Button, styled, Tooltip } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export const TripOptionButton = styled(Button)({
  color: 'rgba(0, 0, 0, 0.6)',
})
TripOptionButton.defaultProps = {
  size: 'small',
}

export const TooltipedTripOption = ({
  children,
  disabled,
  message,
}: {
  children: ReactElement
  disabled?: boolean
  message: string
}) => {
  if (disabled) {
    return (
      <Tooltip title={message}>
        <span>{children}</span>
      </Tooltip>
    )
  }
  return children
}

export const TripOption = ({
  fieldLabel,
  disabled,
  onClick,
}: {
  fieldLabel: string
  disabled?: boolean
  onClick: (event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => void
}) => (
  <TripOptionButton
    onClick={onClick}
    disabled={disabled}
  >
    {ctIntl.formatMessage({ id: fieldLabel })}
  </TripOptionButton>
)
