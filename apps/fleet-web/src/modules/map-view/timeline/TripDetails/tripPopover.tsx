import { useState } from 'react'
import {
  Box,
  Button,
  Chip,
  Divider,
  Paper,
  Popover,
  Stack,
  type ChipProps,
} from '@karoo-ui/core'
import { TextFieldControlled, type TextFieldControlledProps } from '@karoo-ui/core-rhf'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import { Controller, useForm, type FieldPath } from 'react-hook-form'
import type { Except } from 'type-fest'
import type { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { TooltipedTripOption, TripOption } from './utils'

function TripPopover<Schema extends z.ZodObject>({
  props: {
    detail,
    fieldName,
    fieldLabel,
    defaultValues,
    schema,
    onDelete,
    onSave,
    chipProps,
    textFieldProps,
    isTripOngoing,
  },
}: {
  props: {
    detail: string | null
    fieldName: FieldPath<z.infer<Schema>>
    fieldLabel: string
    schema: Schema
    defaultValues: z.infer<Schema>
    onDelete: (fieldName: FieldPath<z.infer<Schema>>) => void
    onSave: (value: z.infer<Schema>, fieldName: FieldPath<z.infer<Schema>>) => void
    chipProps?: Except<ChipProps, 'label' | 'size' | 'onChange' | 'sx'>
    textFieldProps?: Except<
      TextFieldControlledProps<z.infer<Schema>>,
      'fullWidth' | 'label' | 'ControllerProps'
    >
    isTripOngoing?: boolean
  }
}) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | HTMLDivElement | null>(
    null,
  )

  const {
    handleSubmit,
    control,
    formState: { isValid },
    reset,
  } = useForm<z.infer<typeof schema>>({
    resolver: zodResolverV4(schema),
    values: defaultValues,
    mode: 'onChange',
  })

  const openPopover = (event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const closePopover = (
    event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>,
  ) => {
    event.stopPropagation()
    reset()
    setAnchorEl(null)
  }

  const handleSave =
    (fieldName: FieldPath<z.infer<Schema>>) => (data: z.infer<Schema>) => {
      onSave(data, fieldName)
      setAnchorEl(null)
    }

  const handleDelete = (fieldName: FieldPath<z.infer<Schema>>) => {
    onDelete(fieldName)
    setAnchorEl(null)
  }

  return (
    <>
      {detail ? (
        <Chip
          size="small"
          onClick={openPopover}
          label={ctIntl.formatMessage({ id: detail })}
          sx={{ maxWidth: 200 }}
          {...chipProps}
        />
      ) : (
        <TooltipedTripOption
          disabled={isTripOngoing}
          message={ctIntl.formatMessage({
            id: ctIntl.formatMessage({
              id:
                fieldName === 'notes'
                  ? 'fleet.timeline.tripDetails.title.disabled'
                  : 'fleet.timeline.tripDetails.note.disabled',
            }),
          })}
        >
          <TripOption
            onClick={openPopover}
            disabled={isTripOngoing} // Ongoing trips cannot be annotated
            fieldLabel={fieldLabel}
          />
        </TooltipedTripOption>
      )}
      <Controller
        control={control}
        name={fieldName}
        render={({ field }) => (
          <Popover
            open={Boolean(anchorEl)}
            anchorEl={anchorEl}
            onClose={closePopover}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Paper
              onClick={(e) => e.stopPropagation()}
              sx={{ width: '400px' }}
            >
              <form onSubmit={handleSubmit(handleSave(field.name))}>
                <Box sx={{ paddingX: 3, paddingY: 2 }}>
                  <TextFieldControlled
                    ControllerProps={{
                      name: field.name,
                      control,
                    }}
                    fullWidth
                    label={ctIntl.formatMessage({ id: fieldLabel })}
                    maxRows={4}
                    autoFocus
                    {...textFieldProps}
                  />
                </Box>
                <Divider />
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  p={1}
                >
                  <Stack
                    direction="row"
                    gap={1}
                  >
                    <Button
                      size="small"
                      variant="outlined"
                      color="secondary"
                      onClick={closePopover}
                    >
                      {ctIntl.formatMessage({ id: 'Cancel' })}
                    </Button>
                    {detail && (
                      <Button
                        size="small"
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteOutlineOutlinedIcon />}
                        onClick={() => handleDelete(field.name)}
                      >
                        {ctIntl.formatMessage({ id: 'Delete' })}
                      </Button>
                    )}
                  </Stack>
                  <Button
                    size="small"
                    disabled={!isValid}
                    variant="contained"
                    type="submit"
                  >
                    {ctIntl.formatMessage({ id: 'Save' })}
                  </Button>
                </Stack>
              </form>
            </Paper>
          </Popover>
        )}
      />
    </>
  )
}

export default TripPopover
