import { useState } from 'react'
import { Chip, Menu, MenuItem } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { ClientTripTypeId } from '@fleet-web/api/vehicles/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { TooltipedTripOption, TripOption } from './utils'

const options = [
  { label: 'None', value: ClientTripTypeId.None },
  {
    label: 'global.clientTripTypeId.business',
    value: ClientTripTypeId.Business,
  },
  { label: 'global.clientTripTypeId.private', value: ClientTripTypeId.Private },
] as const

const TripTypeDropdown = ({
  props: { selectedValue, onSelectOption, isTripOngoing },
}: {
  props: {
    selectedValue: ClientTripTypeId | null
    onSelectOption: (option: ClientTripTypeId) => void
    isTripOngoing?: boolean
  }
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | HTMLDivElement | null>(
    null,
  )

  const openPopover = (event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const closePopover = (
    event: React.MouseEvent<HTMLLIElement | HTMLButtonElement | HTMLDivElement>,
  ) => {
    event.stopPropagation()
    setAnchorEl(null)
  }

  const handleOption = (
    event: React.MouseEvent<HTMLLIElement>,
    option: ClientTripTypeId,
  ) => {
    event.stopPropagation()
    setAnchorEl(null)
    onSelectOption(option)
  }

  return (
    <>
      {selectedValue && selectedValue !== ClientTripTypeId.None ? (
        <Chip
          size="small"
          onClick={openPopover}
          label={match(selectedValue)
            .with(ClientTripTypeId.Business, () =>
              ctIntl.formatMessage({
                id: 'global.clientTripTypeId.business',
              }),
            )
            .with(ClientTripTypeId.Charging, () =>
              ctIntl.formatMessage({
                id: 'global.clientTripTypeId.charging',
              }),
            )
            .with(ClientTripTypeId.Private, () =>
              ctIntl.formatMessage({
                id: 'global.clientTripTypeId.private',
              }),
            )
            .exhaustive()}
        />
      ) : (
        <TooltipedTripOption
          disabled={isTripOngoing}
          message={ctIntl.formatMessage({
            id: ctIntl.formatMessage({
              id: 'fleet.timeline.tripDetails.type.disabled',
            }),
          })}
        >
          <TripOption
            onClick={openPopover}
            disabled={isTripOngoing} // Ongoing trips cannot be annotated
            fieldLabel={ctIntl.formatMessage({ id: 'Add type' })}
          />
        </TooltipedTripOption>
        // <TripOption
        //   onClick={openPopover}
        //   disabled={disabled}
        // >
        //   {ctIntl.formatMessage({ id: 'Add type' })}
        // </TripOption>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={closePopover}
      >
        {options.map(({ label, value }) => (
          <MenuItem
            selected={value === selectedValue}
            onClick={(e) => handleOption(e, value)}
            key={value}
          >
            {ctIntl.formatMessage({ id: label })}
          </MenuItem>
        ))}
      </Menu>
    </>
  )
}

export default TripTypeDropdown
