import { useState } from 'react'
import {
  <PERSON>,
  Button,
  CircularProgress,
  Stack,
  styled,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import VideocamOffIcon from '@mui/icons-material/VideocamOff'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { VehicleId } from '@fleet-web/api/types'
import type { FootageBlock } from '@fleet-web/modules/vision/CameraFootageModal/api/useVisionFootageBlocksQuery'
import { getCameraFootageModalMainPath } from '@fleet-web/modules/vision/CameraFootageModal/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import useTripFootage from './useTripFootage'

type TripInfo = {
  startTime: DateTime
  endTime: DateTime
  vehicleId: VehicleId
}

type Props = {
  tripInfo: TripInfo
}

const VIDEO_BOX_DIMENSIONS = {
  width: 143,
  height: 80,
}

const CameraFootageContainer = ({ tripInfo }: Props) => {
  const { footage, status } = useTripFootage(tripInfo)

  return match(status)
    .with('pending', () => (
      <LoadingContainer>
        <CircularProgress size={24} />
      </LoadingContainer>
    ))
    .with('error', () => null)
    .otherwise(() => (
      <CameraFootage
        tripInfo={tripInfo}
        footage={footage}
      />
    ))
}

type CameraFootageProps = {
  tripInfo: TripInfo
  footage: FootageBlock | null
}

const CameraFootage = ({ tripInfo, footage }: CameraFootageProps) => {
  const history = useHistory()
  const [isHover, setIsHover] = useState(false)

  const takenAt = footage?.start
    ? DateTime.fromJSDate(footage.start).toLocaleString(DateTime.TIME_WITH_SECONDS)
    : null

  const snapshotUrl = footage?.image_url ?? null
  const hasVideo = !!footage

  const handleCameraFootageBoxClick = () => {
    if (hasVideo) {
      history.push(
        getCameraFootageModalMainPath(history.location, {
          selectedVehicleId: tripInfo.vehicleId,
          selectedDate: tripInfo.startTime.toISO(),
          initialSelectedFootage: {
            startTime: DateTime.fromJSDate(footage.start).toISO(),
            camera: footage.camera as `${number}`,
          },
        }),
      )
    }
  }

  return (
    <Container
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      isHover={isHover}
      hasVideo={hasVideo}
      onClick={(e) => {
        e.stopPropagation()
        handleCameraFootageBoxClick()
      }}
    >
      {hasVideo ? (
        <>
          {snapshotUrl ? (
            <SnapshotBackground style={{ backgroundImage: `url(${snapshotUrl})` }} />
          ) : (
            <NoSnapshotContainer>
              <Typography variant="caption">
                {ctIntl.formatMessage({ id: 'Video has no preview' })}
              </Typography>
            </NoSnapshotContainer>
          )}
          {takenAt && (
            <BottomOverlay>
              <Typography
                variant="caption"
                sx={(theme) => ({
                  color: theme.palette.grey[400],
                })}
              >
                {`${ctIntl.formatMessage({ id: 'Taken at' })} `}
                <TimeLabel>{takenAt}</TimeLabel>
              </Typography>
            </BottomOverlay>
          )}
          {isHover && hasVideo && (
            <>
              <HoverOverlay />
              <CenterOverlay>
                <Tooltip
                  title={ctIntl.formatMessage({ id: 'View footage' })}
                  arrow
                >
                  <StyledButton variant="outlined">
                    <>
                      <VisibilityOutlinedIcon fontSize="small" />
                      <Typography
                        variant="caption"
                        sx={{ fontWeight: 500, marginLeft: '4px' }}
                      >
                        {ctIntl.formatMessage({ id: 'View' })}
                      </Typography>
                    </>
                  </StyledButton>
                </Tooltip>
              </CenterOverlay>
            </>
          )}
        </>
      ) : (
        <NoCameraContainer>
          <VideocamOffIcon sx={{ fontSize: 24, color: 'action.active' }} />
          <Typography variant="caption">
            {ctIntl.formatMessage({ id: 'Unavailable' })}
          </Typography>
        </NoCameraContainer>
      )}
    </Container>
  )
}

export default CameraFootageContainer

const Container = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isHover' && prop !== 'hasVideo',
})<{ isHover: boolean; hasVideo: boolean }>(({ theme, isHover, hasVideo }) =>
  theme.unstable_sx({
    position: 'relative',
    minWidth: `${VIDEO_BOX_DIMENSIONS.width}px`,
    width: `${VIDEO_BOX_DIMENSIONS.width}px`,
    height: `${VIDEO_BOX_DIMENSIONS.height}px`,
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: 0.5,
    overflow: 'hidden',
    boxShadow:
      isHover && hasVideo
        ? `0px 0px 6px 1px ${rgba(theme.palette.primary.main, 0.3)}`
        : 'none',
    transition: 'box-shadow 0.3s ease',
    backgroundColor: hasVideo ? theme.palette.grey[900] : theme.palette.grey[200],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: hasVideo ? 'pointer' : 'default',
  }),
)

const SnapshotBackground = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
})

const BottomOverlay = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    position: 'absolute',
    padding: '2px 4px',
    backgroundColor: rgba(theme.palette.common.black, 0.6),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    right: '2px',
    bottom: '2px',
    borderRadius: 1,
  }),
)

const CenterOverlay = styled(Box)(() => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 2,
}))

const HoverOverlay = styled(Box)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: rgba(0, 0, 0, 0.4),
  zIndex: 1,
}))

const StyledButton = styled(Button)(({ theme }) =>
  theme.unstable_sx({
    backgroundColor: 'transparent',
    color: '#fff',
    borderColor: '#fff',
    borderRadius: '4px',
    display: 'flex',
    py: 0.25,
    px: 0.5,
  }),
)

const NoCameraContainer = styled(Stack)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
  textAlign: 'center',
  gap: '2px',
}))

const NoSnapshotContainer = styled(Stack)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: 'center',
  alignItems: 'center',
  textAlign: 'center',
  gap: '2px',
  backgroundColor: '#E0E0E0',
}))

const TimeLabel = styled('span')({
  color: '#fff',
})

const LoadingContainer = styled(Box)({
  width: `${VIDEO_BOX_DIMENSIONS.width}px`,
  height: `${VIDEO_BOX_DIMENSIONS.height}px`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
})
