import { useMemo } from 'react'
import type { DateTime } from 'luxon'

import type { VehicleId } from '@fleet-web/api/types'
import useVisionFootageBlocksQuery from '@fleet-web/modules/vision/CameraFootageModal/api/useVisionFootageBlocksQuery'

type Params = {
  vehicleId: VehicleId
  startTime: DateTime
  endTime: DateTime
}

// TODO: actually we can move this logic to an upper level and map it with trip Id,
// so we can run one loop for all trips instead of calling this for each trip.
// But considering there aren’t many trips in a single day, and we have all footage for that date,
// it shouldn’t be a problem for now.
export default function useTripFootage({ vehicleId, startTime, endTime }: Params) {
  // Here actually we can use the trip's start time and end time, but we use the start date and end date
  // to prevent calling the API too much, since other trips and events will need to fetch footage as well
  const selectedDateTsRange = useMemo(() => {
    const dateTime = startTime
    return {
      startTs: dateTime.startOf('day').toJSDate(),
      endTs: dateTime.endOf('day').toJSDate(),
    }
  }, [startTime])

  const query = useVisionFootageBlocksQuery({
    vehicleId,
    startTs: selectedDateTsRange.startTs,
    endTs: selectedDateTsRange.endTs,
  })

  const { data, status } = query

  const footage = useMemo(() => {
    if (data?.footage?.array) {
      // The footage array is sorted by start time so can use find to
      // retrieves the first footage block
      return data.footage.array.find(
        (f) =>
          startTime.toJSDate() <= f.start && endTime.toJSDate() >= f.end && !!f.url,
      )
    }
    return undefined
  }, [data, startTime, endTime])

  return {
    footage: footage ?? null,
    status,
  }
}
