import { useEffect, useState } from 'react'
import { startCase } from 'lodash'
import type { History } from 'history'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'

import { exportData } from '@fleet-web/duxs/export'
import { getSelectedTimeline } from '@fleet-web/duxs/timeline'
import {
  fetchVehicleRawData,
  getActiveVehiclesWithDrivers,
  getVehicleRawData,
  isFetchingRawData,
} from '@fleet-web/duxs/vehicles'
import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'
import { Button, DatePicker, InputDropdown, Table } from '@fleet-web/util-components'
import { formatDate } from '@fleet-web/util-functions/moment-helper'
import { camelToSnake } from '@fleet-web/util-functions/string-utils'

type Props = {
  history: History
  vehicles: Array<Record<string, FixMeAny>> | FixMeAny
} & typeof actionCreators &
  ReturnType<typeof mapStateToProps>

const VehicleRawData = ({
  exportData,
  fetchVehicleRawData,
  history,
  isLoading,
  timelineDate,
  vehicleId,
  vehicleRawData,
  vehicles,
}: Props) => {
  const [selectedDate, setSelectedDate] = useState<FixMeAny>(timelineDate || new Date())
  const columns = vehicleRawData.length > 0 ? Object.keys(vehicleRawData[0]) : []

  useEffect(() => {
    fetchVehicleRawData(vehicleId, selectedDate)
  }, [fetchVehicleRawData, vehicleId, selectedDate])

  const handleDateChange = (selectedDate: FixMeAny) => {
    setSelectedDate(selectedDate)
    fetchVehicleRawData(vehicleId, selectedDate)
  }

  const handleVehicleChange = (vehicleId: string) => {
    history.push(`/raw/vehicle/${vehicleId}`)
  }

  const handleExportData = () => {
    const selectedVehicle = vehicles.find(
      ({ id }: { id: FixMeAny }) => id === vehicleId,
    )
    const formattedDate = formatDate(selectedDate, 'YYYY-MM-DD')
    const fileName = `${selectedVehicle.registration}-${formattedDate}`
    exportData('vehicle-raw-data', vehicleRawData, columns, fileName)
  }
  return (
    <div className="VehicleRawData-container">
      <div className="VehicleRawData-filter">
        <DatePicker
          placeholder="Date"
          value={selectedDate}
          onChange={handleDateChange}
          disabledDays="future"
          extraClassNames={{
            containerClassNames: 'VehicleRawData-filter-field',
          }}
        />
        <InputDropdown
          activeOption={vehicleId}
          options={vehicles.map(
            ({ id, registration }: { id: string; registration: FixMeAny }) => ({
              key: id,
              value: id,
              name: registration,
            }),
          )}
          placeholder="Vehicle"
          extraClassNames={{
            containerClassNames: 'VehicleRawData-filter-field',
          }}
          onChange={handleVehicleChange}
        />
        <Button
          label="Download"
          icon="download"
          className="VehicleRawData-filter-field-right"
          onClick={handleExportData}
          disabled={isLoading}
        />
      </div>
      <Table
        className="VehicleRawData-table"
        loading={isLoading}
        columns={columns.map((c) => ({
          Header: startCase(camelToSnake(c).replace('_', ' ')),
          accessor: c,
          className: 'table-cell',
        }))}
        data={vehicleRawData}
      />
    </div>
  )
}

const mapStateToProps = (state: AppState, ownProps: FixMeAny) => {
  const { startTime: timelineDate, vehicleId: timelineVehicleId } =
    getSelectedTimeline()

  let vehicleId = null
  if (ownProps.match.params.vehicleId) {
    // Use vehicleId params if supplied
    vehicleId = ownProps.match.params.vehicleId
  } else if (timelineVehicleId) {
    // Use selected vehicleId from timeline if a vehicle is focused
    vehicleId = timelineVehicleId
  }

  return {
    isLoading: isFetchingRawData(state),
    timelineDate: timelineDate || new Date(), // Use selected timeline date if available, else use current date
    vehicleId: vehicleId,
    vehicleRawData: getVehicleRawData(state),
    vehicles: getActiveVehiclesWithDrivers(state),
  }
}

const actionCreators = {
  exportData,
  fetchVehicleRawData,
}

export default withRouter(connect(mapStateToProps, actionCreators)(VehicleRawData))
