import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { difference, isEmpty, isNil, isNumber } from 'lodash'
import { Box, CircularProgress, Stack, styled, Typography } from '@karoo-ui/core'
import StopCircleOutlinedIcon from '@mui/icons-material/StopCircleOutlined'
import { DateTime } from 'luxon'
import moment from 'moment-timezone'
import { useDispatch } from 'react-redux'
import { match } from 'ts-pattern'

import useEventsGeofencesQuery from '@fleet-web/api/hooks/useEventsGeofencesQuery'
import type {
  FetchTimelineEventsUI,
  TripsWithOrWithoutStops,
} from '@fleet-web/api/timeline/types'
import type {
  DriverName,
  PositionDescription,
  VehicleTripId,
} from '@fleet-web/api/types'
import { getMapType, getTimelineTablesActiveTab } from '@fleet-web/duxs/map-timeline'
import {
  getIsTimelineTableLoading,
  getSelectedTripId,
  getTimelineEventsUI,
  getTimelineTableOrderMode,
  getTimelineTrips,
  reGetTimelineTableDataFilteredByCategory,
  selectTimelineTrip,
  setSelectedCompactTrip,
  showTripStatistics,
  type TimelineEventWithRoadSpeed,
} from '@fleet-web/duxs/timeline'
import { getSettings_UNSAFE, type ViewMode } from '@fleet-web/duxs/user'
import {
  getAllowRoadSpeed,
  getPreferences,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { downloadVehicleTripSpecial, setTripFlag } from '@fleet-web/duxs/vehicles'
import { useEffectEvent } from '@fleet-web/hooks/useEventHandler'
import { UserFormattedPositionAddress } from '@fleet-web/modules/components/connected/UserFormattedPositionAddress'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { FixMeAny, TripsDownloadFileExtension } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { isValidGPSData } from '@fleet-web/util-functions/map-utils'

import type { FocusedVehicle } from '../FleetMapView/DetailsPanel/ui-types'
import type { MapFleetBottomPanelState } from '../MapFleetProvider'
import FilteredEventsTable from './MapBottomPanelTable/FilteredEventsTable'
import TimelineTableCompact from './TimelineTableCompact'
import TimelineTableQVCN from './TimelineTableQVCN'
import TimelineTablesTrip from './TimelineTablesTrip'
import TimelineTableTripModal from './TimelineTableTripModal'
import TripDownloadModal from './TripDownloadModal'
import type { TimelineTableTripToSelect } from './types'

export type TripEventWithExtraFields = TimelineEventWithRoadSpeed & {
  geofence: { status: 'idle' | 'pending' | 'success' | 'error'; name: string }
  driverName: DriverName
  lastKnownCoords?: { lat: number; lng: number } | null
}

type StopTimeProps = {
  stopTime: string
  stopLocation: PositionDescription
  stopGpsFixType: number | null
}

const StopTime = ({ stopTime, stopLocation, stopGpsFixType }: StopTimeProps) => (
  <>
    <StyledStopTime>
      <StopCircleOutlinedIcon sx={{ color: 'error.main', fontSize: '20px' }} />
      <Typography variant="caption">
        {ctIntl.formatMessage({ id: 'Stopped for' })} {stopTime}{' '}
        <UserFormattedPositionAddress
          address={stopLocation}
          gpsFixType={stopGpsFixType}
          statesRenderer={{
            privateAddress: () =>
              `${ctIntl.formatMessage({ id: 'at' })} ${ctIntl.formatMessage({
                id: 'timelineTables.withStopTimeFlag.undisclosedLocation',
              })}`,
            publicAddress: ({ isAddressShown, processedJSX }) => {
              if (isAddressShown) {
                return (
                  <>
                    {ctIntl.formatMessage({ id: 'at' })} {processedJSX}
                  </>
                )
              }
              return processedJSX
            },
          }}
        />
      </Typography>
    </StyledStopTime>
  </>
)

type Props = {
  focusedItem: FocusedVehicle
  onEventClick: (id: string) => void
  initialSelectedTripId: MapFleetBottomPanelState['initialSelectedTripId']
  selectedTripStartDate: Date
}

const TimelineTables = ({
  focusedItem,
  onEventClick,
  initialSelectedTripId,
  selectedTripStartDate,
}: Props) => {
  const dispatch = useDispatch()

  const {
    mapEnableTripDownload,
    downloadGoogleTrip: displayDownload,
    distanceInMiles,
  } = useTypedSelector(getSettings_UNSAFE)
  const { trips: tripsSummaryUI } = useTypedSelector(getTimelineEventsUI)
  const timelineTrips = useTypedSelector(getTimelineTrips)
  const tableOrderMode = useTypedSelector(getTimelineTableOrderMode)
  const mapType = useTypedSelector(getMapType)
  const timelineTablesActiveTab = useTypedSelector(getTimelineTablesActiveTab)
  const allowRoadSpeed = useTypedSelector(getAllowRoadSpeed)
  const filteredTimeLineTableEvents = useTypedSelector(
    reGetTimelineTableDataFilteredByCategory,
  )
  const {
    tripsTablesPreferences: { selectedViewMode, visibility: tripsVisibility },
  } = useTypedSelector(getPreferences)
  const loading = useTypedSelector(getIsTimelineTableLoading)
  const selectedTripId = useTypedSelector(getSelectedTripId)

  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'trip_stats'
        data: { tripId: string }
      }
    | {
        type: 'download_trip'
        data: { tripId: string }
      }
    | null
  >(null)

  const [selectedCompactTripIndex, setSelectedCompactTripIndex] = useState<
    Array<number>
  >([])

  const allEventsGeometries = useMemo(() => {
    const geometriesSet = new Set<string>()
    for (const tripData of timelineTrips) {
      for (const event of tripData) {
        if (event.event_geom && typeof event.event_geom === 'string') {
          geometriesSet.add(event.event_geom)
        }
      }
    }

    // We sort it and return it as an array without duplicates and sorted
    // so that query is not invalidated more often than needed
    return Array.from(geometriesSet).sort()
  }, [timelineTrips])

  const eventsGeofencesQuery = useEventsGeofencesQuery(allEventsGeometries, {
    enabled: allEventsGeometries.length > 0,
  })

  const onTripsVisibilityFlaggedChange = useEffectEvent(() => {
    if (tripsVisibility.flagged) {
      dispatch(
        setSelectedCompactTrip({
          id: tripsSummaryUI.map((t) => t.tripId),
          events: timelineTrips,
        }),
      )
    } else {
      dispatch(setSelectedCompactTrip(null))
    }
  })
  useEffect(() => {
    onTripsVisibilityFlaggedChange()
    return () => {
      dispatch(setSelectedCompactTrip(null))
    }
  }, [dispatch, tripsVisibility.flagged])

  const handleDownloadTripSpecial = ({
    activeFileTab,
    tripId,
  }: {
    activeFileTab: TripsDownloadFileExtension
    tripId: string
  }) => {
    const type = activeFileTab
    const trip = tripsSummaryUI.find((a) => a.tripId === tripId)
    if (trip === undefined) {
      return
    }

    GA4.event({
      category: 'Timeline Table',
      action: `Download Single Trip - ${type}`,
    })

    dispatch(
      downloadVehicleTripSpecial({
        type,
        id: focusedItem.id,
        startTime: trip.startTime,
        endTime: trip.endTime,
        zeroKmTrips: tripsVisibility.zeroKmTrips,
      }),
    )

    handleCloseDownloadModal()
  }

  const tripsUIMaybeWithStops = useMemo(() => {
    const baseTrips = timelineTrips.map((tripData, index) => {
      if (!isEmpty(tripData)) {
        for (const draft of tripData) {
          draft.allowRoadSpeed = allowRoadSpeed
        }
      }
      return tripsSummaryUI[index]
    })

    if (tripsVisibility.stopTime) {
      return getTripsWithStops(
        tableOrderMode.current,
        baseTrips,
        selectedTripStartDate,
        'detailed',
      )
    }
    return baseTrips
  }, [
    timelineTrips,
    tripsVisibility.stopTime,
    tripsSummaryUI,
    allowRoadSpeed,
    tableOrderMode,
    selectedTripStartDate,
  ])

  const addExtraFieldsToTrip = useCallback(
    (
      tripData: Array<TimelineEventWithRoadSpeed>,
      tripIndex: number,
    ): Array<TripEventWithExtraFields> => {
      let lastKnownCoords: { lat: number; lng: number } | null = null

      return tripData.map((event) => {
        const geofence = {
          status: eventsGeofencesQuery.status,
          name: '',
        }

        if (eventsGeofencesQuery.status === 'success') {
          const names = event.event_geom
            ? eventsGeofencesQuery.data.get(event.event_geom)
            : undefined
          if (names) {
            geofence.name = names.join(', ')
          } else {
            geofence.name = '--'
          }
        }

        if (isValidGPSData(event.lat, event.lng)) {
          lastKnownCoords = { lat: event.lat, lng: event.lng }
        }

        return {
          ...event,
          geofence,
          driverName: tripsUIMaybeWithStops[tripIndex]?.driverName ?? '',
          lastKnownCoords,
        }
      })
    },
    [eventsGeofencesQuery.data, eventsGeofencesQuery.status, tripsUIMaybeWithStops],
  )

  const timelineTripsWithExtraFields = useMemo(
    () => timelineTrips.map((trip, tripIndex) => addExtraFieldsToTrip(trip, tripIndex)),
    [addExtraFieldsToTrip, timelineTrips],
  )

  const filteredTimeLineTableEventsWithExtraFields = useMemo(
    () =>
      filteredTimeLineTableEvents.map((event) => {
        const geofence = {
          status: eventsGeofencesQuery.status,
          name: '',
        }

        if (eventsGeofencesQuery.status === 'success') {
          const names = event.event_geom
            ? eventsGeofencesQuery.data.get(event.event_geom)
            : undefined
          if (names) {
            geofence.name = names.join(', ')
          } else {
            geofence.name = '--'
          }
        }

        return {
          ...event,
          geofence,
        }
      }),
    [
      eventsGeofencesQuery.data,
      eventsGeofencesQuery.status,
      filteredTimeLineTableEvents,
    ],
  )

  const handleCloseDownloadModal = () => {
    setCurrentModal(null)
  }

  const handleOpenDownloadModal = (tripId: string) => {
    setCurrentModal({
      type: 'download_trip',
      data: { tripId },
    })
  }

  const handleOpenTripStatsModal = (tripId: string) => {
    dispatch(showTripStatistics(tripId))
    setCurrentModal({
      type: 'trip_stats',
      data: { tripId },
    })
  }

  const handleTripFlagClick = (trip: FetchTimelineEventsUI.ParsedApiTrip) => {
    const { id, isSingleTrip } = focusedItem

    const selectedTripStartDateTime = DateTime.fromJSDate(selectedTripStartDate)
    const startTime = selectedTripStartDateTime.startOf('day').toJSDate()
    const endTime = selectedTripStartDateTime.endOf('day').toJSDate()

    dispatch(
      setTripFlag(id, trip.startTime, trip.flagged, {
        isSingleTrip,
        startTime,
        endTime,
      }),
    )
  }

  const handleSelectTrip = (trip: TimelineTableTripToSelect, index?: number) => {
    if (trip !== null && trip.selectionType === 'single') {
      const { id: lastTripEventId } = trip.events[trip.events.length - 1]
      onEventClick(lastTripEventId)
    }

    dispatch(selectTimelineTrip({ trip }))

    if (isEmpty(selectedCompactTripIndex) && isNumber(index)) {
      setSelectedCompactTripIndex([index])
    } else if (trip?.selectionType === 'multiple' && isNumber(index)) {
      setSelectedCompactTripIndex((prev) => {
        if (prev.length === 1 && prev.includes(index)) {
          return prev
        } else {
          return prev.includes(index) ? difference(prev, [index]) : [...prev, index]
        }
      })
    } else {
      setSelectedCompactTripIndex([])
    }
  }

  const renderTables = () => {
    const commonProps = {
      distanceInMiles,
      displayDownload,
      disableDownload: !mapEnableTripDownload,
      dayTripEvents: filteredTimeLineTableEvents,
    }

    if (
      (mapType === 'vehicles' || mapType === 'fleet') &&
      timelineTablesActiveTab === 'all-trips'
    ) {
      if (selectedViewMode === 'compact' || selectedViewMode === 'qvcn') {
        let compactTripsUI = timelineTrips.map<TripsWithOrWithoutStops[number]>(
          (_data, index) => tripsSummaryUI[index],
        )

        if (tripsVisibility.stopTime) {
          compactTripsUI = getTripsWithStops(
            tableOrderMode.current,
            compactTripsUI,
            selectedTripStartDate,
            selectedViewMode,
          )
        }

        return match(selectedViewMode)
          .with('compact', () => (
            <TimelineTableCompact
              tripsUI={compactTripsUI}
              onTripSelect={handleSelectTrip}
              selectedTripId={selectedTripId}
              timelineTrips={timelineTrips}
              onTripStatsShow={handleOpenTripStatsModal}
              onDataExportRequest={handleOpenDownloadModal}
              {...commonProps}
            />
          ))
          .with('qvcn', () => (
            <TimelineTableQVCN
              tripsUI={compactTripsUI}
              onTripSelect={handleSelectTrip}
              selectedTripId={selectedTripId}
              timelineTrips={timelineTrips}
              onTripStatsShow={handleOpenTripStatsModal}
              onDataExportRequest={handleOpenDownloadModal}
              {...commonProps}
            />
          ))
          .exhaustive()
      }

      if (selectedViewMode === 'detailed') {
        return (
          <DetailedTimelineTable
            timelineTripsWithExtraFields={timelineTripsWithExtraFields}
            tripsUIMaybeWithStops={tripsUIMaybeWithStops}
            tripsVisibility={tripsVisibility}
            onEventClick={onEventClick}
            handleOpenDownloadModal={handleOpenDownloadModal}
            initialSelectedTripId={initialSelectedTripId}
            handleTripFlagClick={handleTripFlagClick}
            handleSelectTrip={handleSelectTrip}
            selectedCompactTripIndex={selectedCompactTripIndex}
            selectedTripId={selectedTripId}
            focusedItem={focusedItem}
            handleOpenTripStatsModal={handleOpenTripStatsModal}
            commonProps={commonProps}
          />
        )
      }
    }
    return (
      <FilteredEventsTable
        rows={filteredTimeLineTableEventsWithExtraFields}
        isSVRUnit={mapType === 'svr-units'}
        isAssetTracker={mapType === 'asset trackers' || mapType === 'asset-trackers'}
        onTableEventRowClick={onEventClick}
      />
    )
  }

  return loading ? (
    <Box sx={{ textAlign: 'center' }}>
      <CircularProgress />
    </Box>
  ) : (
    <Box
      paddingX={2}
      height="100%"
    >
      {renderTables()}
      {match(currentModal)
        .with(null, () => null)
        .with({ type: 'download_trip' }, ({ data }) => (
          <TripDownloadModal
            fileExtensionSelectorOnly
            onClose={() => setCurrentModal(null)}
            handleDownloadRequest={(activeFileTab) =>
              handleDownloadTripSpecial({
                activeFileTab,
                tripId: data.tripId,
              })
            }
          />
        ))
        .with({ type: 'trip_stats' }, ({ data }) => (
          <TimelineTableTripModal
            closeModal={() => setCurrentModal(null)}
            tripId={data.tripId}
          />
        ))
        .exhaustive()}
    </Box>
  )
}

export default memo(TimelineTables)

function getTripsWithStops(
  orderMode: 'recent' | 'older',
  trips: TripsWithOrWithoutStops,
  selectedTripStartDate: Date,
  viewMode: ViewMode,
) {
  const today = DateTime.local()

  const startDate = DateTime.fromJSDate(selectedTripStartDate).startOf('day').toJSDate()
  const endDateTime = DateTime.fromJSDate(selectedTripStartDate).endOf('day')
  const currentDay =
    today.diff(endDateTime, 'day').as('days') > 0
      ? endDateTime.toJSDate()
      : today.toJSDate()

  return trips.reduce<TripsWithOrWithoutStops>((acc, currentTrip, index) => {
    const nextTrip: TripsWithOrWithoutStops[number] | undefined = trips[index + 1]

    if (orderMode === 'recent') {
      // Add first stop
      if (index === 0) {
        acc.push({
          tripId: `stop-${currentDay}`,
          startTime: currentTrip.endTime,
          endTime: currentDay,
          stopTime: getTimeDiffByTimestamp(currentTrip.endTime, currentDay, viewMode),
          startLocation: currentTrip.endLocation,
          endLocation: currentTrip.endLocation,
          startGeofence: currentTrip.startGeofence,
          endGeofence: currentTrip.endGeofence,
          startGpsFixType: currentTrip.endGpsFixType,
          endGpsFixType: currentTrip.endGpsFixType,
          isStopTime: true,
          maxSpeed: 0,
          totalDistance: 0,
          driverName: currentTrip.driverName,
          startCoordinates: currentTrip.startCoordinates,
          endCoordinates: currentTrip.endCoordinates,
        })
      }

      acc.push(currentTrip)

      acc.push({
        tripId: `stop-${currentTrip.startTime}`,
        startTime: nextTrip?.endTime ?? startDate,
        endTime: currentTrip.startTime,
        stopTime: getTimeDiffByTimestamp(
          nextTrip?.endTime ?? startDate,
          currentTrip.startTime,
          viewMode,
        ),
        startLocation: currentTrip.startLocation,
        endLocation: currentTrip.startLocation,
        startGeofence: currentTrip.startGeofence,
        endGeofence: currentTrip.endGeofence,
        startGpsFixType: currentTrip.startGpsFixType,
        endGpsFixType: currentTrip.startGpsFixType,
        isStopTime: true,
        maxSpeed: 0,
        totalDistance: 0,
        driverName: currentTrip.driverName,
        startCoordinates: currentTrip.startCoordinates,
        endCoordinates: currentTrip.endCoordinates,
      })
    } else if (orderMode === 'older') {
      // Add first stop
      if (index === 0) {
        acc.push({
          tripId: `stop-${currentTrip.startTime}`,
          startTime: startDate,
          endTime: currentTrip.startTime,
          stopTime: getTimeDiffByTimestamp(startDate, currentTrip.startTime, viewMode),
          startLocation: currentTrip.startLocation,
          endLocation: currentTrip.startLocation,
          startGeofence: currentTrip.startGeofence,
          endGeofence: currentTrip.endGeofence,
          startGpsFixType: currentTrip.startGpsFixType,
          endGpsFixType: currentTrip.startGpsFixType,
          isStopTime: true,
          maxSpeed: 0,
          totalDistance: 0,
          driverName: currentTrip.driverName,
          startCoordinates: currentTrip.startCoordinates,
          endCoordinates: currentTrip.endCoordinates,
        })
      }

      acc.push(currentTrip)

      acc.push({
        tripId: `stop-${nextTrip?.startTime ?? currentDay}`,
        startTime: currentTrip.endTime,
        endTime: nextTrip?.startTime ?? currentDay,
        stopTime: getTimeDiffByTimestamp(
          currentTrip.endTime,
          nextTrip?.startTime ?? currentDay,
          viewMode,
        ),
        startLocation: currentTrip.endLocation,
        endLocation: currentTrip.endLocation,
        startGeofence: currentTrip.startGeofence,
        endGeofence: currentTrip.endGeofence,
        startGpsFixType: currentTrip.endGpsFixType,
        endGpsFixType: currentTrip.endGpsFixType,
        isStopTime: true,
        maxSpeed: 0,
        totalDistance: 0,
        driverName: currentTrip.driverName,
        startCoordinates: currentTrip.startCoordinates,
        endCoordinates: currentTrip.endCoordinates,
      })
    }

    return acc
  }, [])
}

const getTimeDiffByTimestamp = (
  endTime: moment.MomentInput,
  nextStartTime: moment.MomentInput,
  viewMode: ViewMode,
) => {
  let stopTime = ''
  const { hours, minutes, seconds } = (
    moment.duration(moment(nextStartTime).diff(moment(endTime))) as FixMeAny
  )._data

  if (viewMode === 'detailed') {
    if (hours > 0) {
      stopTime += `${hours} ${ctIntl.formatMessage({
        id: hours > 1 ? 'hours' : 'hour',
      })} `
    }

    if (minutes > 0) {
      stopTime += `${minutes} ${ctIntl.formatMessage({
        id: minutes > 1 ? 'minutes' : 'minute',
      })} `
    }

    if (seconds > 0) {
      stopTime += `${seconds} ${ctIntl.formatMessage({
        id: seconds > 1 ? 'seconds' : 'second',
      })}`
    }
  } else {
    stopTime += `${hours < 10 ? '0' + hours : hours}:`
    stopTime += `${minutes < 10 ? '0' + minutes : minutes}:`
    stopTime += `${seconds < 10 ? '0' + seconds : seconds}`
  }

  return stopTime
}

const StyledStopTime = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    paddingX: 2,
    paddingY: 1,
    background: '#f5f5f5',
    border: '1px solid #eee',
    borderRadius: '4px',
    gap: 1,
    flexDirection: 'row',
  }),
)

function DetailedTimelineTable({
  timelineTripsWithExtraFields,
  tripsUIMaybeWithStops,
  tripsVisibility,
  onEventClick,
  handleOpenDownloadModal,
  initialSelectedTripId,
  handleTripFlagClick,
  handleSelectTrip,
  selectedCompactTripIndex,
  selectedTripId,
  focusedItem,
  commonProps,
  handleOpenTripStatsModal,
}: {
  timelineTripsWithExtraFields: Array<Array<TripEventWithExtraFields>>
  tripsUIMaybeWithStops: TripsWithOrWithoutStops
  tripsVisibility: {
    zeroKmTrips: boolean
    stopTime: boolean
    flagged: boolean
  }
  onEventClick: (id: string) => void
  handleOpenDownloadModal: (tripId: string) => void
  initialSelectedTripId: MapFleetBottomPanelState['initialSelectedTripId']
  handleTripFlagClick: (trip: FetchTimelineEventsUI.ParsedApiTrip) => void
  handleSelectTrip: (trip: TimelineTableTripToSelect, index?: number) => void
  selectedCompactTripIndex: Array<number>
  selectedTripId: VehicleTripId | Array<VehicleTripId> | null
  focusedItem: FocusedVehicle
  handleOpenTripStatsModal: (tripId: VehicleTripId) => void
  commonProps: {
    distanceInMiles: boolean | undefined
    displayDownload: boolean | undefined
    disableDownload: boolean
    dayTripEvents: ReturnType<typeof reGetTimelineTableDataFilteredByCategory>
  }
}) {
  const [expandedTrips, setExpandedTrips] = useState<ReadonlySet<VehicleTripId>>(
    new Set(),
  )

  const toggleTripVisibility = (
    e: React.MouseEvent<HTMLElement | HTMLDivElement | SVGElement, MouseEvent>,
    tripId: VehicleTripId,
  ) => {
    e.stopPropagation()

    GA4.event({
      category: 'Timeline Table',
      action: `Timeline Trip Details Click`,
    })

    const hasTrip = expandedTrips.has(tripId)
    const newExpandedTrips = new Set(expandedTrips)
    if (hasTrip) {
      newExpandedTrips.delete(tripId)
    } else {
      newExpandedTrips.add(tripId)
    }
    setExpandedTrips(newExpandedTrips)
  }

  let stopsNumber = 0
  return (
    <Stack gap={1}>
      {!isEmpty(tripsUIMaybeWithStops) &&
        tripsUIMaybeWithStops.map((trip, index) => {
          if (isNil(trip)) {
            return null
          }

          if (trip.isStopTime) {
            if (
              trip.startLocation !== undefined &&
              trip.startGpsFixType !== undefined
            ) {
              stopsNumber++
              return (
                <StopTime
                  key={trip.tripId}
                  stopTime={trip.stopTime}
                  stopLocation={trip.startLocation}
                  stopGpsFixType={trip.startGpsFixType}
                />
              )
            }
            return null
          }

          const tripData = timelineTripsWithExtraFields[index - stopsNumber]
          return (
            <TimelineTablesTrip
              key={trip.tripId}
              onTableEventRowClick={onEventClick}
              onDataExportRequest={handleOpenDownloadModal}
              initialSelectedTripId={initialSelectedTripId}
              onTripFlagClick={handleTripFlagClick}
              onTripSelect={handleSelectTrip}
              isSelected={
                selectedCompactTripIndex.includes(index) ||
                (!isNil(trip.tripId) && selectedTripId === trip.tripId)
              }
              tripData={tripData} // Events data grouped into trip
              timelineTripsWithExtraFields={timelineTripsWithExtraFields}
              selectedCompactTripIndex={selectedCompactTripIndex}
              tripUI={trip}
              vehicleId={focusedItem.id}
              toggleTripVisibility={toggleTripVisibility}
              isExpanded={expandedTrips.has(trip.tripId)}
              onTripStatsShow={handleOpenTripStatsModal}
              index={index}
              {...commonProps}
            />
          )
        })}
      {timelineTripsWithExtraFields.length === 0 && (
        <em className="util-textLight">
          {tripsVisibility.flagged
            ? ctIntl.formatMessage({ id: 'No Flagged Trips' })
            : ctIntl.formatMessage({ id: 'No Trips' })}
        </em>
      )}
    </Stack>
  )
}
