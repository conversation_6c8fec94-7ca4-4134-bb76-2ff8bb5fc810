import { DataGrid, type GridRowParams } from '@karoo-ui/core'

import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import type { FixMeAny } from '@fleet-web/types'

import {
  MAP_BOTTOM_PANEL_TABLES_GRID_ID,
  useMapBottomPanelTripTablesContext,
  type MapBottomPanelTripTableRowData,
} from '../MapBottomPanelTripTablesContext'

type RowData = MapBottomPanelTripTableRowData & {
  geofence: { status: 'idle' | 'pending' | 'success' | 'error'; name: string }
}

type Props = {
  rows: Array<RowData>
  isSVRUnit: boolean
  isAssetTracker: boolean
  onTableEventRowClick: (eventId: string) => void
}

const FilteredEventsTable = ({
  rows,
  isSVRUnit,
  isAssetTracker,
  onTableEventRowClick,
}: Props) => {
  const { gridApiRef, getColumns, getSVRUnitsColumns, getAssetTrackerColumns } =
    useMapBottomPanelTripTablesContext()
  const columns = getColumns()
  const assetTrackerColumns = getAssetTrackerColumns()

  const conditionalTableColumns = () => {
    if (isSVRUnit) {
      return getSVRUnitsColumns
    } else if (isAssetTracker) {
      return assetTrackerColumns
    }

    return columns
  }

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      apiRef={gridApiRef}
      dataGridId={MAP_BOTTOM_PANEL_TABLES_GRID_ID}
      rows={rows}
      onRowClick={({ row }: GridRowParams<RowData>) => {
        onTableEventRowClick(row.id)
      }}
      columns={conditionalTableColumns() as FixMeAny} // to be fixed later, this might require a bigger refactor
      slotProps={{
        basePagination: { material: { showFirstButton: true, showLastButton: true } },
      }}
      pagination
      pageSizeOptions={[25, 50, 100]}
      initialState={{
        pagination: {
          paginationModel: { pageSize: 25, page: 0 },
        },
      }}
    />
  )
}

export default FilteredEventsTable
