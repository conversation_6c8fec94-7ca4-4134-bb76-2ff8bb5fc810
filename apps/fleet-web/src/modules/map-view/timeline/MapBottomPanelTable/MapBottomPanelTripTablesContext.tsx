import { createContext, useContext, useMemo, useState } from 'react'
import {
  useGridApiRef,
  type GridApi,
  type GridColumnVisibilityModel,
} from '@karoo-ui/core'

import type { TimelineEventWithRoadSpeed } from '@fleet-web/duxs/timeline'
import {
  useAssetTrackerEventColumns,
  useSVRUnitsEventColumns,
  useVehicleEventColumns,
  type VehicleEventColumn,
} from '@fleet-web/modules/tables/vehicle-events'

export const MAP_BOTTOM_PANEL_TABLES_GRID_ID = 'MapBottomPanelTripTables'

export type MapBottomPanelTripTableRowData = VehicleEventColumn &
  TimelineEventWithRoadSpeed

export type MapBottomPanelTripTablesContextType = {
  gridApiRef: React.MutableRefObject<GridApi | null>
  /**
   * Use this variable when you need to access the gridApi but the gridApiRef is not set yet in the context
   */
  gridApi: GridApi | null
  /**
   * Use this if you need to force set the gridApi while the gridApiRef is not set yet in the context
   */
  setGridApi: React.Dispatch<React.SetStateAction<GridApi | null>>
  getColumns: ReturnType<typeof useVehicleEventColumns>['getVehicleEventColumns']
  getAssetTrackerColumns: ReturnType<
    typeof useAssetTrackerEventColumns
  >['getAssetTrackerEventColumns']
  getSVRUnitsColumns: ReturnType<typeof useSVRUnitsEventColumns>
  columnVisibilityModel: GridColumnVisibilityModel
  setColumnVisibilityModel: React.Dispatch<
    React.SetStateAction<GridColumnVisibilityModel>
  >
}

const MapBottomPanelTripTablesContext = createContext<
  MapBottomPanelTripTablesContextType | undefined
>(undefined)

const useMapBottomPanelTripTablesContext = () => {
  const context = useContext(MapBottomPanelTripTablesContext)

  if (!context) {
    throw new Error(
      `useMapBottomPanelTripTablesContext must be used within a MapBottomPanelTripTablesProvider`,
    )
  }

  return context
}

type Props = {
  children: React.ReactNode
}

const MapBottomPanelTripTablesProvider = ({ children }: Props) => {
  const gridApiRef = useGridApiRef()
  const { getVehicleEventColumns } = useVehicleEventColumns()
  const svrUnitsColumns = useSVRUnitsEventColumns()
  const { getAssetTrackerEventColumns } = useAssetTrackerEventColumns()

  const [columnVisibilityModel, setColumnVisibilityModel] = useState<
    MapBottomPanelTripTablesContextType['columnVisibilityModel']
  >(() => {
    const newModel: GridColumnVisibilityModel = {}

    getVehicleEventColumns().map((cell) => {
      newModel[cell.field as keyof typeof newModel] = true
    })

    return newModel
  })

  const [gridApi, setGridApi] = useState<GridApi | null>(null)

  const contextValue = useMemo(
    () => ({
      gridApiRef,
      gridApi: gridApiRef.current ?? gridApi,
      setGridApi,
      getColumns: getVehicleEventColumns,
      getSVRUnitsColumns: svrUnitsColumns,
      getAssetTrackerColumns: getAssetTrackerEventColumns,
      columnVisibilityModel,
      setColumnVisibilityModel,
    }),
    [
      gridApiRef,
      gridApi,
      getVehicleEventColumns,
      getAssetTrackerEventColumns,
      svrUnitsColumns,
      columnVisibilityModel,
    ],
  )

  return (
    <MapBottomPanelTripTablesContext.Provider value={contextValue}>
      {children}
    </MapBottomPanelTripTablesContext.Provider>
  )
}

export { MapBottomPanelTripTablesProvider, useMapBottomPanelTripTablesContext }
