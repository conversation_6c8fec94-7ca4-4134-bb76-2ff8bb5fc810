import { useEffect, useMemo, useState } from 'react'
import { concat, includes, isEmpty, without } from 'lodash'
import {
  FormControlLabel,
  FormGroup,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  Switch,
  type GridApi,
  type GridColumnVisibilityModel,
  type GridDensity,
  type GridStateColDef,
} from '@karoo-ui/core'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import { useDispatch } from 'react-redux'

import {
  setTimelineTripsTablesViewModePreference,
  setTimelineTripsTablesVisibilityPreferences,
} from '@fleet-web/duxs/timeline'
import {
  getIsQvcnDefaultOption,
  getIsUserDistanceInMiles,
  type ViewMode,
} from '@fleet-web/duxs/user'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import { useEffectEvent } from '@fleet-web/hooks/useEventHandler'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import Karoo<PERSON>oolbarPopper from '@fleet-web/shared/data-grid/KarooToolbar/Popper'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import IntlTypography from '@fleet-web/util-components/IntlTypography'

import { useMapBottomPanelTripTablesContext } from './MapBottomPanelTripTablesContext'

const isGridApiSet = (
  gridApi: GridApi | null | Record<string, never>,
): gridApi is GridApi => gridApi !== null && !isEmpty(gridApi)

const MapBottomPanelTableGridToolbar = () => {
  const dispatch = useDispatch()
  const isMiles = useTypedSelector(getIsUserDistanceInMiles)
  const {
    tripsTablesPreferences: { viewModeOptions, selectedViewMode, visibility },
  } = useTypedSelector(getPreferences)
  const isQvcnDefaultOption = useTypedSelector(getIsQvcnDefaultOption)

  const [popperOpen, setPopperOpen] = useState(false)
  const [popperAnchorEl, setPopperAnchorEl] = useState<null | HTMLElement>(null)
  const canPopperBeOpen = popperOpen && Boolean(popperAnchorEl)
  const popperId = canPopperBeOpen ? 'MapBottomPanelTableGridToolbar-popper' : undefined

  const {
    gridApiRef,
    gridApi: gridApiFromContext,
    columnVisibilityModel,
    setColumnVisibilityModel,
  } = useMapBottomPanelTripTablesContext()

  /**
   * Fallbacks directly to the gridApi from context if the gridApiRef is not set yet
   */
  const gridApi = useMemo(() => {
    if (gridApiRef.current !== null && !isEmpty(gridApiRef.current)) {
      return gridApiRef.current
    }

    return gridApiFromContext
  }, [gridApiFromContext, gridApiRef])

  // We add columnVisibilityModel as a dependency to force the useMemo to recompute the columns when the columnVisibilityModel changes
  const [allColumns, visibleColumnsFields] = useMemo((): [
    Array<GridStateColDef>,
    Array<string>,
  ] => {
    if (!isGridApiSet(gridApi)) {
      return [[], []]
    }

    const allColumns = gridApi.getAllColumns()
    const visibleColumnsFields = gridApi.getVisibleColumns().map((col) => col.field)

    return [allColumns, visibleColumnsFields]
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gridApi, columnVisibilityModel])

  const handleColumnsVisibilityChange = (field: string, state: boolean) => {
    setColumnVisibilityModel((prev) => {
      const newModel = {
        ...prev,
        [field]: state,
      }

      if (isGridApiSet(gridApi)) {
        gridApi.setColumnVisibility(field, state)
      }

      return newModel
    })
  }

  const handleAllColumnsVisibilityChange = (state: boolean) => {
    const newModel: GridColumnVisibilityModel = {}

    allColumns.map((cell) => {
      newModel[cell.field as keyof typeof newModel] = state
    })

    setColumnVisibilityModel(newModel)

    if (isGridApiSet(gridApi)) {
      gridApi.setColumnVisibilityModel(newModel)
    }
  }

  const handleGridDensityChange = (density: GridDensity) => {
    if (isGridApiSet(gridApi)) {
      gridApi.setDensity(density)
    }
  }

  const zeroKmTripsLabel = isMiles ? '0 mile trips' : '0 km trips'

  const onMount = useEffectEvent(() => {
    if (isQvcnDefaultOption) {
      dispatch(setTimelineTripsTablesViewModePreference('qvcn'))
    }
  })

  useEffect(() => {
    onMount()
  }, [])

  const viewModeOptionsBasedOnCountry = useMemo(() => {
    if (isQvcnDefaultOption) {
      return includes(viewModeOptions, 'qvcn')
        ? concat('qvcn', without(viewModeOptions, 'qvcn'))
        : viewModeOptions
    }
    return viewModeOptions
  }, [isQvcnDefaultOption, viewModeOptions])

  return (
    <>
      <IconButton
        aria-describedby={popperId}
        color="default"
        onClick={(e) => {
          setPopperAnchorEl(e.currentTarget)
          setPopperOpen((prev) => !prev)
        }}
      >
        <SettingsOutlinedIcon />
      </IconButton>

      <KarooToolbarPopper
        isOpen={canPopperBeOpen}
        anchorEl={popperAnchorEl as HTMLElement}
        popperId={popperId}
        allColumns={allColumns}
        visibleColumnsFields={visibleColumnsFields}
        onGridDensityChange={handleGridDensityChange}
        onColumnsVisibilityChange={handleColumnsVisibilityChange}
        onAllColumnsVisibilityChange={handleAllColumnsVisibilityChange}
        forceKarooToolbarPopperClose={() => setPopperOpen(false)}
        slots={{
          renderSettingsMenu: ({ standardContent }) => (
            <>
              <Stack
                sx={{
                  ml: 2,
                  mb: 1,
                }}
                gap={1}
              >
                <Stack>
                  <IntlTypography
                    variant="body2"
                    sx={{ fontWeight: 'bold' }}
                    msgProps={{
                      id: 'Trip View Mode',
                    }}
                  />
                  <RadioGroup
                    defaultValue="detailed"
                    sx={{ ml: 1 }}
                    value={selectedViewMode}
                    onChange={(event) =>
                      dispatch(
                        setTimelineTripsTablesViewModePreference(
                          event.target.value as ViewMode,
                        ),
                      )
                    }
                  >
                    {viewModeOptionsBasedOnCountry.map((viewMode) => (
                      <FormControlLabel
                        key={viewMode}
                        value={viewMode}
                        control={<Radio size="small" />}
                        label={ctIntl.formatMessage({ id: viewMode })}
                      />
                    ))}
                  </RadioGroup>
                </Stack>

                {(selectedViewMode === 'detailed' || selectedViewMode === 'qvcn') && (
                  <Stack>
                    <IntlTypography
                      variant="body2"
                      sx={{ fontWeight: 'bold' }}
                      msgProps={{
                        id: 'Trips to Show',
                      }}
                    />
                    <FormGroup sx={{ ml: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={visibility.zeroKmTrips}
                            onChange={() =>
                              dispatch(
                                setTimelineTripsTablesVisibilityPreferences(
                                  'zeroKmTrips',
                                  !visibility.zeroKmTrips,
                                ),
                              )
                            }
                          />
                        }
                        label={ctIntl.formatMessage({ id: zeroKmTripsLabel })}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={visibility.stopTime}
                            onChange={() =>
                              dispatch(
                                setTimelineTripsTablesVisibilityPreferences(
                                  'stopTime',
                                  !visibility.stopTime,
                                ),
                              )
                            }
                          />
                        }
                        label={ctIntl.formatMessage({ id: 'Stop Time' })}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={visibility.flagged}
                            onChange={() =>
                              dispatch(
                                setTimelineTripsTablesVisibilityPreferences(
                                  'flagged',
                                  !visibility.flagged,
                                ),
                              )
                            }
                          />
                        }
                        label={ctIntl.formatMessage({ id: 'Flagged only' })}
                      />
                    </FormGroup>
                  </Stack>
                )}
              </Stack>
              {selectedViewMode === 'detailed' && standardContent()}
              {selectedViewMode === 'qvcn' &&
                standardContent({ disabled: { columns: true, density: false } })}
            </>
          ),
        }}
      />
    </>
  )
}

export default MapBottomPanelTableGridToolbar
