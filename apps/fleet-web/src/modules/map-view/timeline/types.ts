import type { VehicleTripId } from '@fleet-web/api/types'
import type { TimelineEventWithRoadSpeed } from '@fleet-web/duxs/timeline'

export type TimelineTableSingleTripToSelect = {
  id: VehicleTripId
  selectionType: 'single'
  events: Array<TimelineEventWithRoadSpeed>
}

export type TimelineTableMultipleTripsToSelect = {
  id: VehicleTripId
  isTripAlreadySelected: boolean
  selectionType: 'multiple'
  events: Array<Array<TimelineEventWithRoadSpeed>>
}

export type TimelineTableTripToSelect =
  | TimelineTableMultipleTripsToSelect
  | TimelineTableSingleTripToSelect
  | null
