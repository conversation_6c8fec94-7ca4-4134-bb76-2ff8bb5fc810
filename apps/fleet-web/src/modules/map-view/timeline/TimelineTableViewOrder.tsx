import { MenuItem, Select, Stack, Typography } from '@karoo-ui/core'
import { useDispatch } from 'react-redux'

import { getMapType, getTimelineTablesActiveTab } from '@fleet-web/duxs/map-timeline'
import {
  getTimelineTableOrderMode,
  setTimelineTripsTableOrderMode,
} from '@fleet-web/duxs/timeline'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const TimelineTableViewOrder = () => {
  const dispatch = useDispatch()
  const mapType = useTypedSelector(getMapType)
  const tableOrderMode = useTypedSelector(getTimelineTableOrderMode)
  const timelineTablesActiveTab = useTypedSelector(getTimelineTablesActiveTab)

  if (
    (mapType === 'vehicles' || mapType === 'fleet') &&
    timelineTablesActiveTab === 'all-trips'
  ) {
    const { current, modes } = tableOrderMode
    return (
      <Stack
        direction="row"
        gap={0.5}
        alignItems="center"
      >
        <Typography variant="caption">
          {ctIntl.formatMessage({ id: 'Sort By' })}
        </Typography>
        <Select
          labelId="order-by-select"
          id="order-by"
          size="small"
          sx={(theme) => ({
            '& .MuiOutlinedInput-notchedOutline': { border: 'none !important' },
            '& .MuiSelect-select': { color: theme.palette.primary.main },
          })}
          value={current}
          label={ctIntl.formatMessage({ id: 'Sort By' })}
          onChange={(e) =>
            dispatch(setTimelineTripsTableOrderMode(e.target.value as typeof current))
          }
        >
          {modes.map((option) => (
            <MenuItem
              key={option}
              value={option}
            >
              {ctIntl.formatMessage({ id: option })}
            </MenuItem>
          ))}
        </Select>
      </Stack>
    )
  }
  return null
}

export default TimelineTableViewOrder
