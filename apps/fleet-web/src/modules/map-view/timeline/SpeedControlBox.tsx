import type React from 'react'
import { useState } from 'react'
import {
  Box,
  ClickAwayListener,
  Fade,
  Paper,
  Popper,
  Slider,
  Stack,
  Typography,
} from '@karoo-ui/core'
import FastForwardIcon from '@mui/icons-material/FastForward'

import { GA4 } from '@fleet-web/shared/google-analytics4'
import ActionIconButton from '@fleet-web/util-components/actionIconButton'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type Props = {
  speed: number
  onChange: (value: number) => void
}
const ReSpeedControlBox = ({ speed, onChange }: Props) => {
  const [isSpeedOpen, setIsSpeedOpen] = useState<null | HTMLElement>(null)

  const onClose = () => {
    GA4.event({
      category: 'Timeline',
      action: '"Timeline Speed" Close Button Click',
    })

    setIsSpeedOpen(null)
  }

  const speedClick = (e: React.MouseEvent<HTMLElement>) => {
    GA4.event({
      category: 'Timeline',
      action: '"Timeline Speed" Button Click',
    })

    setIsSpeedOpen(e.currentTarget)
  }

  return (
    <ClickAwayListener onClickAway={onClose}>
      <Box>
        <ActionIconButton
          onClick={speedClick}
          variant="outlined"
          color="secondary"
        >
          <FastForwardIcon />
        </ActionIconButton>
        <Popper
          open={Boolean(isSpeedOpen)}
          anchorEl={isSpeedOpen}
          transition
          placement="bottom-end"
        >
          {({ TransitionProps }) => (
            <Fade
              {...TransitionProps}
              timeout={350}
            >
              <Paper sx={{ width: '160px', px: 2, py: 1 }}>
                <Typography variant="caption">
                  {ctIntl.formatMessage({ id: 'Timeline Speed' })}
                </Typography>
                <Slider
                  min={1}
                  max={10}
                  aria-label="speed"
                  value={speed}
                  onChange={(_, value) => onChange(value as number)}
                />
                <Stack
                  direction="row"
                  justifyContent="space-between"
                >
                  <Typography variant="caption">
                    {ctIntl.formatMessage({ id: 'Slow' })}
                  </Typography>
                  <Typography variant="caption">
                    {ctIntl.formatMessage({ id: 'Fast' })}
                  </Typography>
                </Stack>
              </Paper>
            </Fade>
          )}
        </Popper>
      </Box>
    </ClickAwayListener>
  )
}

export default ReSpeedControlBox
