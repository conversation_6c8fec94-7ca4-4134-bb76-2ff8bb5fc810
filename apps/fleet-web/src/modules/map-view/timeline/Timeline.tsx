import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { isEmpty, isEqual, isNil } from 'lodash'
import { Box, Stack, styled } from '@karoo-ui/core'
import { useDispatch } from 'react-redux'

import useTimelineHardwareTypeQuery from '@fleet-web/api/timeline/useTimelineHardwareTypeQuery'
import {
  changeTimelineTablesActiveTab,
  getSelectedTripStartDate,
} from '@fleet-web/duxs/map'
import { getMapType } from '@fleet-web/duxs/map-timeline'
import type { MapActiveView, MapType } from '@fleet-web/duxs/map-types'
import {
  getTimelineEventsRaw,
  getTimelineSliderDataFilteredByActivity,
  getTimelineTripsSensors,
  tableColumnsVisibility,
} from '@fleet-web/duxs/timeline'
import {
  getAuthenticatedUser,
  getHardwareTypeList,
  savePreferences,
} from '@fleet-web/duxs/user'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import { usePrevious } from '@fleet-web/hooks'
import { useEffectEvent, useEventHandler } from '@fleet-web/hooks/useEventHandler'
import { pointToPointTableId } from '@fleet-web/modules/tables/point-to-point-events'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { FixMeAny } from '@fleet-web/types'
import type { TypedColumns } from '@fleet-web/types/extended/react-table'

import { vehicleEventsTablesIds as vehicleTableId } from '../../tables/vehicle-events-tables'
import { jumpToTime } from '../actions'
import type { FocusedVehicle } from '../FleetMapView/DetailsPanel/ui-types'
import type { MapFleetBottomPanelState } from '../MapFleetProvider'
import { MapBottomPanelTripTablesProvider } from './MapBottomPanelTable/MapBottomPanelTripTablesContext'
import TimelineChart from './TimelineChart'
import TimelineHeader from './TimelineHeader'
import TimelineSlider from './TimelineSlider'
import TimelineTables from './TimelineTables'

const TIMELINE_MIN_HEIGHT_MAP = {
  vehicles: {
    timeline: 195,
    graph: 380,
    table: 380,
  },
} as const

const TABLE_TIMELINE_HEADER_HEIGHT = 154
const MARGIN_BOTTOM = 16
const TABLE_ASSET_TRACKER_HEIGHT = 110

function resolveHeightPercentage(newHeight: number) {
  const pct = Math.min(Math.round((newHeight / window.innerHeight) * 100), 100)
  return {
    map: `${100 - pct}%`,
    timeline: `${pct}%`,
  }
}

function getIsTimelineTripsView(
  mapType: MapType | '',
): mapType is 'vehicles' | 'fleet' {
  return mapType === 'vehicles' || mapType === 'fleet'
}

type ResizableTimelineWrapperProps = {
  onChangeMapHeight: (height: string) => void
  focusedItem: FocusedVehicle
  bottomPanelState: MapFleetBottomPanelState
}

const ResizableTimelineWrapper = ({
  onChangeMapHeight: onChangeMapHeightProp,
  focusedItem,
  bottomPanelState,
}: ResizableTimelineWrapperProps) => {
  const rafIdRef = useRef<number | null>(null)
  const timelineRef = useRef<HTMLDivElement>(null)
  // Doing a "lazy" ref
  const [mapEl_] = useState(() => document.querySelector('.Map') as HTMLElement | null)
  const mapElRef = useRef<HTMLElement | null>(mapEl_)

  const sectionType = bottomPanelState.sectionType

  const initialHeightRef = useRef<number | null>(null)

  // Stable reference
  const onChangeMapHeight = useEventHandler(onChangeMapHeightProp)

  const onSectionTypeChange = useEffectEvent(() => {
    // Initialize the panel height
    const availableHeight = window.innerHeight
    const timelineHeight = TIMELINE_MIN_HEIGHT_MAP['vehicles'][sectionType]

    if (timelineRef.current) {
      timelineRef.current.style.height = `${timelineHeight}px`
      timelineRef.current.style.maxHeight = `${availableHeight}px`
    }

    const { map } = resolveHeightPercentage(timelineHeight)
    onChangeMapHeight(map)
  })

  useEffect(() => {
    onSectionTypeChange()
  }, [sectionType])

  const onUnmount = useEffectEvent(() => {
    onChangeMapHeight('100%')
    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current)
    }
  })

  useEffect(
    () => () => {
      onUnmount()
    },
    [],
  )

  const handleMouseMove = useEventHandler((e: MouseEvent) => {
    if (rafIdRef.current !== null) {
      cancelAnimationFrame(rafIdRef.current)
    }

    rafIdRef.current = requestAnimationFrame(() => {
      const minTimelineHeight = TIMELINE_MIN_HEIGHT_MAP['vehicles'][sectionType]
      const height = Math.max(window.innerHeight - e.pageY, minTimelineHeight)
      const { timeline, map } = resolveHeightPercentage(height)

      if (timelineRef.current) {
        const isIncreasing =
          initialHeightRef.current !== null && height > initialHeightRef.current

        timelineRef.current.style.height = timeline

        // If increasing height, we can update the map height when finish resizing
        if (!isIncreasing) {
          // If decreasing height, we update the map height immediately
          onChangeMapHeight(map)
        }
      }
    })
  })

  const onDocumentMouseUp = useEventHandler(() => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', onDocumentMouseUp)

    // Re-enable text selection
    document.body.style.userSelect = ''
    document.body.style.cursor = ''

    if (mapElRef.current) {
      mapElRef.current.style.pointerEvents = 'auto'
    }

    if (timelineRef.current && initialHeightRef.current !== null) {
      const finalHeight = timelineRef.current.getBoundingClientRect().height

      // if increase the height of timeline, we update the map height as we haven't udpate it in mouse move
      if (finalHeight > initialHeightRef.current) {
        const { map } = resolveHeightPercentage(finalHeight)
        onChangeMapHeight(map)
      }
    }

    initialHeightRef.current = null // Reset after finishing
  })

  const handleResizeMouseDown = useCallback(() => {
    if (timelineRef.current) {
      const currentTimelineRect = timelineRef.current.getBoundingClientRect()
      initialHeightRef.current = currentTimelineRect.height
    }

    // Disable text selection
    document.body.style.userSelect = 'none'
    document.body.style.cursor = 'row-resize'

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', onDocumentMouseUp)

    if (mapElRef.current) {
      mapElRef.current.style.pointerEvents = 'none'
    }
  }, [handleMouseMove, onDocumentMouseUp])

  const handleResizeTimelineHeight = useCallback(
    (selectedView: MapActiveView) => {
      const newHeight = TIMELINE_MIN_HEIGHT_MAP['vehicles'][selectedView]
      const { timeline, map } = resolveHeightPercentage(newHeight)

      onChangeMapHeight(map)
      if (timelineRef.current) {
        timelineRef.current.style.minHeight = `${newHeight}px`
        if (selectedView === 'timeline') {
          timelineRef.current.style.height = timeline
        }
      }
    },
    [onChangeMapHeight],
  )

  return (
    <Timeline ref={timelineRef}>
      <div
        className="Resizer"
        onMouseDown={handleResizeMouseDown}
        style={{
          cursor: 'row-resize',
        }}
      />
      <ReTimeline
        focusedItem={focusedItem}
        bottomPanelState={bottomPanelState}
        handleResizeTimelineHeight={handleResizeTimelineHeight}
      />
    </Timeline>
  )
}

type Props = {
  focusedItem: FocusedVehicle
  bottomPanelState: MapFleetBottomPanelState
  handleResizeTimelineHeight: (selectedView: MapActiveView) => void
}

const ReTimeline = ({
  focusedItem,
  bottomPanelState,
  handleResizeTimelineHeight,
}: Props) => {
  const dispatch = useDispatch()
  const user = useTypedSelector(getAuthenticatedUser)
  const username = user.username
  const mapType = useTypedSelector(getMapType)
  const {
    events: timelineSliderEvents,
    timeRange: timelineTimeRange,
    timelineBarUI,
  } = useTypedSelector(getTimelineSliderDataFilteredByActivity)
  const eventsRaw = useTypedSelector(getTimelineEventsRaw)
  const timelineTripsSensors = useTypedSelector(getTimelineTripsSensors)
  const preferences = useTypedSelector(getPreferences)
  const hardwareTypeList = useTypedSelector(getHardwareTypeList)
  const focusedVehicleId = focusedItem.id
  const selectedTripStartDate = useTypedSelector(getSelectedTripStartDate)

  const sectionType = bottomPanelState.sectionType

  const hardwareType = useTimelineHardwareTypeQuery({
    vehicleId: focusedVehicleId,
  })

  // get the table type based on the hardware type and list
  const tableType = useMemo(() => {
    if (hardwareType.status === 'success') {
      const { hardwareTypeListId, hardwareTypeId } = hardwareType.data

      if (
        isNil(hardwareTypeList) ||
        isNil(hardwareTypeListId) ||
        isNil(hardwareTypeId)
      ) {
        return 'default'
      }

      const hardwareTypeData = hardwareTypeList[hardwareTypeListId].hardwareTypeData

      let isBasic = true

      if (hardwareTypeData[hardwareTypeId]) {
        isBasic = hardwareTypeData[hardwareTypeId].isBasic ?? true
      }

      return isBasic ? 'basic' : 'default'
    }

    return 'default'
  }, [hardwareType, hardwareTypeList])

  const tableId = vehicleTableId[tableType]

  const onMount = useEffectEvent(() => {
    // Initialize the  preferences on mount
    const parsedTableColumnsVisibility = preferences[tableId]?.columnVisibility

    const hasNewTableKey = Object.keys(tableColumnsVisibility).some(
      (key) => !parsedTableColumnsVisibility?.[key],
    )
    if (hasNewTableKey) {
      dispatch(
        savePreferences(tableId, {
          columnVisibility: {
            ...tableColumnsVisibility,
            ...parsedTableColumnsVisibility,
          },
        }),
      )
    }
  })
  useEffect(() => {
    onMount()
  }, [])

  const prevSensorFields = usePrevious(timelineTripsSensors.defaultSensorFields)
  const prevPreferences = usePrevious(preferences)

  useEffect(() => {
    const { defaultSensorFields } = timelineTripsSensors
    if (getIsTimelineTripsView(mapType)) {
      if (isNil(preferences[tableId])) {
        // save the columns visibility in specified field to avoid pollute others
        dispatch(
          savePreferences(tableId, {
            columnVisibility: {
              ...tableColumnsVisibility,
              ...defaultSensorFields,
            },
          }),
        )
        return
      }

      const parsedTableColumnsVisibility = preferences[tableId]?.columnVisibility

      // Set Column Visibility Preferences after Sensor Fields loads
      if (
        !isEqual(prevSensorFields, defaultSensorFields) &&
        !isEmpty(defaultSensorFields)
      ) {
        dispatch(
          savePreferences(tableId, {
            columnVisibility: {
              ...parsedTableColumnsVisibility,
              ...defaultSensorFields, // use default to override the vehicle specified sensors
            },
          }),
        )
      }
    } else if (prevPreferences) {
      const prevTableColumnsVisibility = prevPreferences[pointToPointTableId]

      if (isNil(prevTableColumnsVisibility)) {
        dispatch(
          savePreferences(pointToPointTableId, {
            columnVisibility: {
              ...tableColumnsVisibility,
            },
          }),
        )
        return
      }
    }
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timelineTripsSensors.defaultSensorFields, tableId, preferences, mapType])

  const handleEventClick = (clickedEventId: string) => {
    // When we enabled the eslint rule below, there was not enough confidence to use trip equals here
    // eslint-disable-next-line eqeqeq
    const nextIndex = eventsRaw.findIndex((event) => event.id == clickedEventId)
    const nextEvent = eventsRaw[nextIndex]
    const timelineTimeSpan =
      timelineTimeRange.endDateTime.toMillis() -
      timelineTimeRange.startDateTime.toMillis()
    if (nextEvent) {
      const nextProgress =
        (nextEvent.time - timelineTimeRange.startDateTime.toMillis()) / timelineTimeSpan

      if (
        nextEvent.position == null ||
        (nextEvent.position.visibility === 'PUBLIC' &&
          nextEvent.position.principal.description !== 'No GPS')
      ) {
        dispatch(
          jumpToTime({
            nextProgress,
            nextActiveEventIndex: nextIndex,
            nextCoords: {
              lat: nextEvent.lat,
              lng: nextEvent.lng,
            },
          }),
        )
      }
    }
  }

  const handleTripViewClick = useCallback(
    (newView: MapActiveView) => {
      if (newView !== sectionType) {
        dispatch(changeTimelineTablesActiveTab('all-trips'))
        handleResizeTimelineHeight(newView)
      }
    },
    [dispatch, sectionType, handleResizeTimelineHeight],
  )

  if (!focusedItem || !selectedTripStartDate) {
    return null
  }

  return (
    <MapBottomPanelTripTablesProvider>
      <TimelineHeader
        bottomPanelState={bottomPanelState}
        onTripViewClick={handleTripViewClick}
        focusedItem={focusedItem}
        selectedTripStartDate={selectedTripStartDate}
        showRawData={/^grab/gi.test(username)} // RMM: Temporary handling of when to show raw data page
      />
      {(() => {
        switch (sectionType) {
          case 'timeline': {
            return (
              <TimelineSlider
                events={timelineSliderEvents}
                timeRange={timelineTimeRange}
                timelineBarUI={timelineBarUI}
              />
            )
          }
          case 'graph': {
            return <TimelineChart />
          }
          case 'table': {
            return (
              <TimelineContainer
                tableHeightOffset={
                  mapType === 'asset trackers' || mapType === 'asset-trackers'
                    ? TABLE_ASSET_TRACKER_HEIGHT
                    : TABLE_TIMELINE_HEADER_HEIGHT + MARGIN_BOTTOM
                }
              >
                <TimelineTables
                  selectedTripStartDate={selectedTripStartDate}
                  onEventClick={handleEventClick}
                  focusedItem={focusedItem}
                  initialSelectedTripId={bottomPanelState.initialSelectedTripId}
                />
              </TimelineContainer>
            )
          }
          default: {
            return null
          }
        }
      })()}
    </MapBottomPanelTripTablesProvider>
  )
}

export default ResizableTimelineWrapper

/**
 * Remove sensor options if sensor does not exist in vehicle
 */
export function filterTableColumnOptions<T extends Record<string, FixMeAny>>(
  tableVisibility: T,
  tableColumns: TypedColumns<FixMeAny>,
) {
  return Object.keys(tableVisibility).reduce<T>((acc, unTypedKey) => {
    const key = unTypedKey as keyof T
    const isValidHeader = tableColumns.some((column) => isEqual(column.Header, key))

    if (isValidHeader) {
      acc[key] = tableVisibility[key]
    }

    return acc
  }, {} as T)
}

const TimelineContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== 'tableHeightOffset',
})<{ tableHeightOffset: number }>(({ tableHeightOffset }) => ({
  height: `calc(100% - ${tableHeightOffset}px)`,
  justifyContent: 'space-between',
  minHeight: '130px',
  overflow: 'auto',
  position: 'relative',
  paddingBottom: '1px', // To show the border of the timeline table
  paddingTop: '1px',
}))

const Timeline = styled(Box)({
  position: 'absolute',
  width: '100%',
  zIndex: '100',
  boxShadow: '0 1px 0 0 #eee',
  bottom: 0,
  background: '#fff',
  display: 'flex',
  flexDirection: 'column',
  transition: 'height 0.1s ease-out',
  willChange: 'height',
})
