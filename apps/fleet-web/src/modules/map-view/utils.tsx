import {
  Chip,
  Stack,
  styled as styledMui,
  Typography,
  type TypographyProps,
} from '@karoo-ui/core'
import VideoCameraFrontOutlinedIcon from '@mui/icons-material/VideoCameraFrontOutlined'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import { DateTime } from 'luxon'
import moment from 'moment'
import { rgba } from 'polished'

import type { FetchVehicleSummaryTripsUI } from '@fleet-web/api/vehicles/types'
import { OldTimelineBar } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { formatDistance } from '@fleet-web/util-components/formatted-distance'
import IntlTypography from '@fleet-web/util-components/IntlTypography'
import SingleInfo from '@fleet-web/util-components/singleInfo'
import { toMutable } from '@fleet-web/util-functions/functional-utils'

const getTotalTimeText = (duration: moment.Duration) => {
  const hours = Math.floor(duration.asHours())
  const minutes = duration.minutes()
  const seconds = duration.seconds()

  return `${hours < 10 ? `0${hours}` : hours}:${
    minutes < 10 ? `0${minutes}` : minutes
  }:${seconds < 10 ? `0${seconds}` : seconds}`
}

export const generateTimelines = ({
  tripsArray,
  selectedTripStartDate,
  onTripSummaryClick,
  showTimelineBar,
  isVehicleInRecovery,
  onVisionEventsChipClick,
  isVisionEnabled,
}: {
  tripsArray: FetchVehicleSummaryTripsUI.Return
  selectedTripStartDate: Date | null
  onTripSummaryClick: (trip: FetchVehicleSummaryTripsUI.Return[number]) => void
  showTimelineBar: boolean
  isVehicleInRecovery: boolean
  onVisionEventsChipClick: (selectedDate: DateTime) => void
  isVisionEnabled: boolean
}) => {
  const timelines = []
  const today = new Date()

  for (const [i, element] of tripsArray.entries()) {
    let dayLabel
    const { date } = element
    const tripDateTime = DateTime.fromJSDate(date)
    const isSelected = selectedTripStartDate
      ? DateTime.fromJSDate(selectedTripStartDate).hasSame(tripDateTime, 'day')
      : false

    const todayDate = DateTime.local()
    const yesterdayDate = todayDate.minus({ day: 1 })
    if (tripDateTime.hasSame(todayDate, 'day')) {
      dayLabel = ctIntl.formatMessage({ id: `Today` })
    } else if (tripDateTime.hasSame(yesterdayDate, 'day')) {
      dayLabel = ctIntl.formatMessage({ id: `Yesterday` })
    } else {
      dayLabel = `${tripDateTime.toFormat('cccc')} (${tripDateTime.toFormat('D')})`
    }

    const { trips, eventsTotal: eventCount, eventsTotalVision } = element

    timelines.push(
      <TimelineItem
        key={i}
        isSelected={isSelected}
        onClick={() => onTripSummaryClick(element)}
      >
        {isVehicleInRecovery && tripDateTime.hasSame(todayDate, 'day') ? (
          <Stack>
            <Typography variant="subtitle2">{dayLabel}</Typography>
            <Stack
              direction="row"
              gap={1}
              mt={1}
            >
              <WarningAmberOutlinedIcon />
              <IntlTypography
                sx={{
                  fontWeight: '500',
                }}
                color="error"
                msgProps={{
                  id: 'map.timeline.vehicleInRecovery',
                }}
              />
            </Stack>
          </Stack>
        ) : (
          <>
            <Stack
              direction="row"
              justifyContent="space-between"
            >
              <Typography variant="subtitle2">{dayLabel}</Typography>
              <Stack
                spacing={1}
                direction="row"
              >
                {isVisionEnabled && eventsTotalVision > 0 && (
                  <Chip
                    sx={{ height: '24px' }}
                    label={
                      <Stack
                        direction="row"
                        gap={0.5}
                      >
                        <Typography>{eventsTotalVision}</Typography>
                        <VideoCameraFrontOutlinedIcon
                          fontSize="small"
                          sx={{ mr: 0.5 }}
                        />
                      </Stack>
                    }
                    onClick={() => onVisionEventsChipClick(tripDateTime)}
                  />
                )}
                {showTimelineBar && eventCount > 0 && (
                  <Chip
                    sx={{ height: '24px' }}
                    deleteIcon={<WarningAmberOutlinedIcon fontSize="small" />}
                    onDelete={() => {}} // Must be defined to show delete icon
                    label={eventCount}
                  />
                )}
              </Stack>
            </Stack>
            {showTimelineBar && (
              <OldTimelineBar
                className="DetailsPanel-bar"
                events={toMutable(trips) ?? []}
              />
            )}
          </>
        )}
      </TimelineItem>,
    )
    today.setDate(today.getDate() - 1)
  }

  return timelines
}

export const generateMultipleDayActivity = (
  data: FetchVehicleSummaryTripsUI.Return,
  distanceInMiles: boolean,
) => {
  const todayDate = DateTime.local()
  const yesterdayDate = todayDate.minus({ day: 1 })

  return data.map((day) => {
    const dayDate = DateTime.fromJSDate(day.date)

    const formattedDistance = formatDistance(day.drivingDistance ?? 0, {
      round: true,
      isMiles: distanceInMiles,
    })

    const dayLabel = (
      <DateInfo>
        {/* eslint-disable-next-line no-nested-ternary */}
        {dayDate.hasSame(todayDate, 'day')
          ? ctIntl.formatMessage({ id: `Today` })
          : dayDate.hasSame(yesterdayDate, 'day')
            ? ctIntl.formatMessage({ id: `Yesterday` })
            : dayDate.toFormat('EEEE')}
        <span style={{ fontWeight: 'normal' }}>{` (${dayDate.toFormat('D')})`}</span>
      </DateInfo>
    )

    return (
      <SingleActivity key={day.date.getTime()}>
        {dayLabel}
        <TripInfo>
          {`${day.trips.length} ${ctIntl.formatMessage(
            { id: 'map.detailsPanel.overall.trips' },
            { values: { count: day.trips.length } },
          )} / `}
          {`${formattedDistance} ${ctIntl.formatMessage({
            id: distanceInMiles ? 'Miles' : 'kms',
          })} / `}
          {`${day.drivingTime} ${ctIntl.formatMessage({
            id: 'map.detailsPanel.overall.drivingTime',
          })}`}
        </TripInfo>
      </SingleActivity>
    )
  })
}

export const generateOverallDetails = (data: FetchVehicleSummaryTripsUI.Return) => {
  let tripsCount = 0
  let totalDistance = 0
  const totalTime = moment.duration('00:00:00')

  for (const day of data) {
    tripsCount += day.trips?.length ?? 0
    totalDistance += day.drivingDistance
    totalTime.add(moment.duration(day.drivingTime))
  }

  const formattedDistance =
    Math.round((Number(totalDistance) + Number.EPSILON) * 100) / 100

  return (
    <Stack direction="row">
      <SingleInfo>
        <StatsNumber>{tripsCount}</StatsNumber>
        <StatsInfo>
          {ctIntl.formatMessage({
            id: 'map.detailsPanel.totals.trips',
          })}
        </StatsInfo>
      </SingleInfo>
      <SingleInfo>
        <StatsNumber>{formattedDistance}</StatsNumber>
        <StatsInfo>
          {ctIntl.formatMessage({
            id: 'map.detailsPanel.totals.distance',
          })}
        </StatsInfo>
      </SingleInfo>
      <SingleInfo>
        <StatsNumber>{getTotalTimeText(totalTime)}</StatsNumber>
        <StatsInfo>
          {ctIntl.formatMessage({
            id: 'map.detailsPanel.totals.time',
          })}
        </StatsInfo>
      </SingleInfo>
    </Stack>
  )
}

export const TimelineItem = styledMui(Stack, {
  shouldForwardProp: (prop) => prop !== 'isSelected',
})<{ isSelected: boolean }>(({ isSelected, theme }) =>
  theme.unstable_sx({
    padding: 2,
    cursor: 'pointer',
    backgroundColor: isSelected
      ? rgba(theme.palette.primary.main, theme.palette.action.selectedOpacity)
      : '#ffffff',
    '&:hover': {
      backgroundColor: rgba(
        theme.palette.primary.main,
        theme.palette.action.selectedOpacity,
      ),
    },
  }),
)

const SingleActivity = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    paddingX: 2,
    paddingY: 1,
  }),
)

const StatsNumber = (props: TypographyProps) => (
  <Typography
    sx={{ color: 'rgba(0, 0, 0, 0.87)' }}
    {...props}
    variant="h6"
  />
)

const DateInfo = (props: TypographyProps) => (
  <Typography
    sx={{ color: 'rgba(0, 0, 0, 0.87)' }}
    {...props}
    variant="subtitle2"
  />
)

const TripInfo = (props: TypographyProps) => (
  <Typography
    color="secondary.light"
    {...props}
    variant="body2"
  />
)

const StatsInfo = (props: TypographyProps) => (
  <Typography
    textAlign="center"
    color="secondary.light"
    {...props}
    variant="caption"
  />
)
