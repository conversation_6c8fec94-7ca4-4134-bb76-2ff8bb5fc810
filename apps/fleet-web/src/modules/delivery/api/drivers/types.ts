//types
import type { DriverId } from '@fleet-web/api/types'
import type { LookupOptionItems } from '@fleet-web/modules/delivery/api/jobs/types'
import type {
  DRIVER_STATUS_SORTING_ID,
  DRIVER_STATUS_TO_ID,
} from '@fleet-web/modules/delivery/utils/constants'
import type { FetchDeliveryCustomers } from '@fleet-web/modules/deliveryRevamp/api/customers/useDeliveryCustomers'
import type { ValueOf } from '@fleet-web/types'

export type DriverDetails = {
  driver_status_id: ValueOf<typeof DRIVER_STATUS_TO_ID>
  client_driver_id: null | string
  contact_number: null | string
  delivery_driver_id: string
  fleetDriverId: DriverId | null
  driver_status: {
    description: string
    driver_status_id: ValueOf<typeof DRIVER_STATUS_TO_ID>
    color: string
    sort?: ValueOf<typeof DRIVER_STATUS_SORTING_ID>
  }
  email: string
  first_name: string
  is_active: boolean
  is_online: boolean
  stops_completed: number
  stops_total: number
  last_name: string
  mobile_device_id: number | null
  device_name: string
  phone_code: null | string
  phone_number: string
  registration: string
  released_total: number
  start_location_customer_id: string
  startLocationCustomer: FetchDeliveryCustomers.ApiOutput['customers'][number] | null
  end_location_customer_id: string
  endLocationCustomer: FetchDeliveryCustomers.ApiOutput['customers'][number] | null
  shift_time_start: string
  shift_time_end: string
  tracking_number: Array<string>
  item_container?: {
    max_weight: string
    max_volume: string
    deliveryCapabilities: Array<LookupOptionItems>
    driver_id: string
    container_id: string
  }
  update_ts: null | string
  login_username: string
  lastOnlineTs: string | null
  offlineModeSinceTs: string | null
  onBreakSinceTs: string | null
  isLoggedIn: boolean
  isPlanning?: boolean
  subuserId: string | null
  shift_work_days?: Array<number> | null
}
