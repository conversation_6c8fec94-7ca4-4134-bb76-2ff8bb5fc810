import NavList from '@fleet-web/modules/app/components/NavList'

import useCollapseSidebar from '../app/components/MainSideNavbar/useCollapseSidebar'
import { SETTINGS } from '../app/components/routes/settings'
import { SubMenusSwitch } from '../app/components/routes/SubMenusSwitch'

export default function Settings() {
  useCollapseSidebar()

  return (
    <NavList.PageWithNavListContainer>
      <NavList
        title={SETTINGS.tab.text}
        navItems={SETTINGS.tab.subMenus}
      />

      <SubMenusSwitch subMenus={SETTINGS.tab.subMenus} />
    </NavList.PageWithNavListContainer>
  )
}
