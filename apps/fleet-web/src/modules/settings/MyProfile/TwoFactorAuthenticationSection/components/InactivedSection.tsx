import { Box, Button, Stack } from '@karoo-ui/core'

import TwoFactorAuthenticationIllustration from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-illustration.svg'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

type Props = {
  isLoading: boolean
  onActivate: () => void
}

export default function InactivedSection({ isLoading, onActivate }: Props) {
  return (
    <Stack
      gap={2}
      data-testid="Profile2FAInactive"
    >
      <Box>
        <Button
          data-testid="Profile2FAInactiveButton"
          loading={isLoading}
          variant="contained"
          onClick={onActivate}
        >
          {ctIntl.formatMessage({ id: 'settings.profile.twoFactor.button' })}
        </Button>
      </Box>
      <div
        style={{
          width: '360px',
          marginLeft: '80px',
        }}
        {...makeSanitizedInnerHtmlProp({
          dirtyHtml: TwoFactorAuthenticationIllustration,
        })}
      />
    </Stack>
  )
}
