import TwoFactorAuthenticationPhoneGreen from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-phone-green.svg'
import TwoFactorAuthenticationPhoneFrame from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-phone-orange-frame.svg'
import TwoFactorAuthenticationPhoneOrange from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-phone-orange.svg'
import TwoFactorAuthenticationPhoneRed from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-phone-red.svg'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import AddContactModal from './AddContactProcessModal'
import CodeValidation from './AddContactProcessModal/CodeValidation'
import PhoneFillContact from './AddContactProcessModal/PhoneFillContact'
import ValidationError from './AddContactProcessModal/ValidationError'
import ValidationSuccess from './AddContactProcessModal/ValidationSuccess'

type Props = {
  onClose: () => void
}

export default function AddPhoneProcessSection({ onClose }: Props) {
  return (
    <AddContactModal
      type="phone"
      onClose={onClose}
      stepComponents={{
        fillContact: (props) => (
          <PhoneFillContact
            {...props}
            title={ctIntl.formatMessage({
              id: 'settings.profile.twoFactor.addPhone.title',
            })}
            image={TwoFactorAuthenticationPhoneOrange}
          />
        ),
        codeValidation: (props) => (
          <CodeValidation
            {...props}
            desp={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.desp',
              },
              {
                values: {
                  method: ctIntl.formatMessage({ id: 'Phone Number' }).toLowerCase(),
                },
              },
            )}
            image={TwoFactorAuthenticationPhoneFrame}
          />
        ),
        validationSuccess: ({ onClose, contact }) => (
          <ValidationSuccess
            onSuccessCloseButtonClick={() => onClose('clickedCancelButton')}
            mainText={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.success',
              },
              { values: { method: 'phone number', value: contact.value } },
            )}
            image={TwoFactorAuthenticationPhoneGreen}
          />
        ),
        validationError: (props) => (
          <ValidationError
            {...props}
            resendText={ctIntl.formatMessage({ id: 'Select another phone number' })}
            desp={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.error',
              },
              { values: { method: 'phone number', value: props.contact.value } },
            )}
            image={TwoFactorAuthenticationPhoneRed}
          />
        ),
      }}
    />
  )
}
