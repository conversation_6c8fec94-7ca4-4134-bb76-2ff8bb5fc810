import type {
  TwoFAContactProvisionParamType,
  TwoFADispatchType,
} from '@fleet-web/util-components/two-factor-authentication/twoFAContactValidationEventHandler'

export type TwoFactorCloseFuncType = (
  reason: 'backdropClick' | 'escapeKeyDown' | 'clickedCancelButton',
) => void

export type StepComponentProps = {
  dispatch: TwoFADispatchType
  onClose: TwoFactorCloseFuncType
  contact: TwoFAContactProvisionParamType
}
