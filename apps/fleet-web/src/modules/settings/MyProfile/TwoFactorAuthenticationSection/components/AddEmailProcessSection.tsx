import TwoFactorAuthenticationEmailGreen from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-email-green.svg'
import TwoFactorAuthenticationEmailFrame from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-email-orange-frame.svg'
import TwoFactorAuthenticationEmailOrange from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-email-orange.svg'
import TwoFactorAuthenticationEmailRed from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-email-red.svg'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import AddContactModal from './AddContactProcessModal'
import CodeValidation from './AddContactProcessModal/CodeValidation'
import EmailFillContact from './AddContactProcessModal/EmailFillContact'
import ValidationError from './AddContactProcessModal/ValidationError'
import ValidationSuccess from './AddContactProcessModal/ValidationSuccess'

type Props = {
  onClose: () => void
}

export default function AddEmailProcessSection({ onClose }: Props) {
  return (
    <AddContactModal
      type="email"
      onClose={onClose}
      stepComponents={{
        fillContact: (props) => (
          <EmailFillContact
            {...props}
            title={ctIntl.formatMessage({
              id: 'settings.profile.twoFactor.addEmail.title',
            })}
            image={TwoFactorAuthenticationEmailOrange}
          />
        ),
        codeValidation: (props) => (
          <CodeValidation
            {...props}
            desp={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.desp',
              },
              {
                values: {
                  method: ctIntl.formatMessage({ id: 'Email Address' }).toLowerCase(),
                },
              },
            )}
            image={TwoFactorAuthenticationEmailFrame}
          />
        ),
        validationSuccess: ({ onClose, contact }) => (
          <ValidationSuccess
            onSuccessCloseButtonClick={() => onClose('clickedCancelButton')}
            mainText={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.success',
              },
              { values: { method: 'email address', value: contact.value } },
            )}
            image={TwoFactorAuthenticationEmailGreen}
          />
        ),
        validationError: (props) => (
          <ValidationError
            {...props}
            resendText={ctIntl.formatMessage({ id: 'Select another email' })}
            desp={ctIntl.formatMessage(
              {
                id: 'settings.profile.twoFactor.verifyCode.error',
              },
              { values: { method: 'email address', value: props.contact.value } },
            )}
            image={TwoFactorAuthenticationEmailRed}
          />
        ),
      }}
    />
  )
}
