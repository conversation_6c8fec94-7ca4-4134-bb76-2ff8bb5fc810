import { <PERSON>, <PERSON><PERSON>, Card, Stack, styled, Typography } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CheckCircleSharpIcon from '@mui/icons-material/CheckCircleSharp'
import DeleteIcon from '@mui/icons-material/Delete'

import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import type { TwoFAContactType } from '../../types'

type Props = {
  titleMsgId: string
  despMsgOption: string
  buttonMsgId: string
  value: TwoFAContactType
  image: string
  onAdd: () => void
  onDelete: () => void
}

export default function AuthenticationMethodSection({
  titleMsgId,
  despMsgOption,
  buttonMsgId,
  value,
  image,
  onAdd,
  onDelete,
}: Props) {
  return (
    <Card
      variant="outlined"
      sx={{
        padding: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 3,
      }}
      data-testid={'Profile2FA' + titleMsgId}
    >
      <ImageContainer {...makeSanitizedInnerHtmlProp({ dirtyHtml: image })} />
      <Stack
        justifyContent="space-between"
        alignItems="flex-start"
      >
        <Typography variant="body2">
          {ctIntl.formatMessage({ id: titleMsgId })}
        </Typography>
        {
          // if email or phone is empty, show the add button other show the value and delete button
          value.value ? (
            <>
              <Box>
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center',
                  }}
                >
                  {value.value}
                  <CheckCircleSharpIcon color="success" />
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ marginBottom: 1 }}
                >{`${ctIntl.formatMessage({
                  id: 'Added on',
                })} ${value.addedTime?.toLocaleString()}`}</Typography>
              </Box>
              <Button
                size="small"
                data-testid="Profile2FA-Delete"
                variant="outlined"
                startIcon={<DeleteIcon />}
                onClick={onDelete}
              >
                {ctIntl.formatMessage({ id: 'Delete' })}
              </Button>
            </>
          ) : (
            <>
              <Typography
                variant="caption"
                sx={(theme) => ({
                  marginBottom: 1,
                  color: theme.palette.text.secondary,
                })}
              >
                {ctIntl.formatMessage({
                  id: despMsgOption,
                })}
              </Typography>
              <Button
                size="small"
                variant="contained"
                startIcon={<AddIcon />}
                onClick={onAdd}
              >
                {ctIntl.formatMessage({ id: buttonMsgId })}
              </Button>
            </>
          )
        }
      </Stack>
    </Card>
  )
}

const ImageContainer = styled('div')`
  svg {
    width: 80px;
  }
`
