import { useState, type ReactNode } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import { match } from 'ts-pattern'

import { useEventHandler } from '@fleet-web/hooks/useEventHandler'
import twoFAContactValidationEventHandler, {
  type TwoFAContactValidationStateType,
  type TwoFAContactValidationStatusEventType,
  type TwoFADispatchType,
} from '@fleet-web/util-components/two-factor-authentication/twoFAContactValidationEventHandler'

import { useAddTwoFAContactMutation, useVerifyCodeMutation } from '../../api/query'
import type { StepComponentProps, TwoFactorCloseFuncType } from './types'

type Props = {
  type: string
  stepComponents: {
    fillContact: (props: {
      isLoading: boolean
      dispatch: TwoFADispatchType
    }) => ReactNode
    codeValidation: (
      props: StepComponentProps & {
        isLoading: boolean
        expireMinute: number
      },
    ) => ReactNode
    validationError: (
      props: StepComponentProps & {
        isLoading: boolean
      },
    ) => ReactNode
    validationSuccess: (props: StepComponentProps) => ReactNode
  }
  onClose: TwoFactorCloseFuncType
}

// In this modal, it will have four steps switched by the status of the addContactStatus, and all the steps share the same modal.
// we use useEventHandler to generate the dispatch method and each step would dispatch the event with payload which would be handled
// by the eventHandler.
// 1. FillContact: the user will be asked to fill the contact field
// 2. CodeValidation: the user will be asked to enter the code sent to the contact
// 3. ValidationError: the page shown when validating fails or server error to allow user to resend code or enter new contact
// 4. ValidationSuccess: the page shown when validating succeeds
export default function AddContactModal({ onClose, type, stepComponents }: Props) {
  const [internalState, setInternalState] = useState<TwoFAContactValidationStateType>({
    status: 'ContactProvision',
    expireMinute: 5, // default 5
    contact: { type: 'email', value: '' },
  })

  const addTwoFAContactMutation = useAddTwoFAContactMutation()
  const verifyCodeMutation = useVerifyCodeMutation()

  const dispatch = useEventHandler((event: TwoFAContactValidationStatusEventType) => {
    twoFAContactValidationEventHandler({
      event,
      setState: setInternalState,
      state: internalState,
      handlerInput: {
        clickResendCodeLinkMutation: addTwoFAContactMutation,
        clickSendCodeButtonMutation: addTwoFAContactMutation,
        verifyCodeMutation,
      },
    })
  })

  return (
    <Dialog
      open
      onClose={onClose}
      maxWidth="md"
      data-testid={'Profile2FAAddContactModal' + type}
      PaperProps={{
        sx: {
          maxWidth: '500px',
          padding: '32px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          overflow: 'hidden',
        },
      }}
    >
      <Button
        data-testid={'Profile2FAAddContactModal' + type + 'CloseButton'}
        aria-label="close"
        onClick={() => onClose('clickedCancelButton')}
        color="secondary"
        variant="outlined"
        sx={{
          position: 'absolute',
          right: 10,
          top: 10,
          width: 36,
          height: 36,
          minWidth: 36,
          minHeight: 36,
        }}
      >
        <CloseIcon sx={{ fontSize: 24, width: 24, height: 24 }} />
      </Button>

      {match<TwoFAContactValidationStateType, ReactNode>(internalState)
        .with({ status: 'ContactProvision' }, () =>
          stepComponents.fillContact({
            dispatch,
            isLoading: addTwoFAContactMutation.isPending,
          }),
        )
        .with({ status: 'CodeValidation' }, (state) =>
          stepComponents.codeValidation({
            onClose,
            dispatch,
            expireMinute: state.expireMinute,
            isLoading:
              addTwoFAContactMutation.isPending || verifyCodeMutation.isPending,
            contact: internalState.contact,
          }),
        )
        .with({ status: 'ValidationSuccess' }, () =>
          stepComponents.validationSuccess({
            onClose,
            dispatch,
            contact: internalState.contact,
          }),
        )
        .with({ status: 'ValidationError' }, () =>
          stepComponents.validationError({
            onClose,
            dispatch,
            isLoading: addTwoFAContactMutation.isPending,
            contact: internalState.contact,
          }),
        )
        .exhaustive()}
    </Dialog>
  )
}
