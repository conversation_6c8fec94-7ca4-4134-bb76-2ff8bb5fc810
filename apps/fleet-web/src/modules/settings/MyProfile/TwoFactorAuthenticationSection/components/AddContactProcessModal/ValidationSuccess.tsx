import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import { ImageContainer } from '../../api/types'

type Props = {
  image: string
  mainText: string
  onSuccessCloseButtonClick: () => void
}

export default function ValidationSuccess({
  mainText,
  image,
  onSuccessCloseButtonClick,
}: Props) {
  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
        <ImageContainer {...makeSanitizedInnerHtmlProp({ dirtyHtml: image })} />
      </Box>
      <DialogTitle data-testid="Profile2FAAddContactModalSuccessTitle">
        {ctIntl.formatMessage({ id: 'Validation Successfully' })}
      </DialogTitle>
      <DialogContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '15px',
        }}
      >
        <DialogContentText sx={{ textAlign: 'center' }}>{mainText}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          data-testid="Profile2FAAddContactModalSuccessCloseButton"
          onClick={onSuccessCloseButtonClick}
          variant="contained"
        >
          {ctIntl.formatMessage({ id: 'Close' })}
        </Button>
      </DialogActions>
    </>
  )
}
