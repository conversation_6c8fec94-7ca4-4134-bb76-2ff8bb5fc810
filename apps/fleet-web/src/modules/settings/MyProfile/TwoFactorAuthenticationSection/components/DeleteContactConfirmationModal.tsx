import ConfirmationModal from '@fleet-web/components/_modals/Confirmation'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useDeleteTwoFAContactMutation } from '../api/query'
import type { DeleteTwoFAContactParamType, TwoFAContactTypeType } from '../api/types'

type Props = {
  onClose: () => void
} & DeleteTwoFAContactParamType

export default function DeleteContactConfirmationModal({
  type,
  value,
  onClose,
}: Props) {
  const delete2FAContactMutation = useDeleteTwoFAContactMutation()

  const handleConfirm = () =>
    delete2FAContactMutation.mutate(
      { type: type as TwoFAContactTypeType },
      { onSettled: () => onClose() },
    )

  return (
    <ConfirmationModal
      open
      data-testid="Profile2FADeleteModal"
      title="settings.profile.twoFactor.deleteHeader"
      onClose={onClose}
      onConfirm={handleConfirm}
      isLoading={delete2FAContactMutation.isPending}
    >
      {ctIntl.formatMessage(
        { id: 'settings.profile.twoFactor.deleteContact.confirmationText' },
        { values: { contact: value } },
      )}
    </ConfirmationModal>
  )
}
