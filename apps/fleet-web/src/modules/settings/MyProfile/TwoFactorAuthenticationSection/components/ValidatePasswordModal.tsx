import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import VisibilityIcon from '@mui/icons-material/Visibility'
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useValidatePasswordMutation } from '../api/query'

type Props = {
  username: string
  titleMsgId: string
  extraDespMsgId: string | undefined
  onSuccess: () => void
  onClose: () => void
}

const ValidatePasswordModal = ({
  username,
  titleMsgId,
  extraDespMsgId,
  onClose,
  onSuccess,
}: Props) => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const validatePasswordMutation = useValidatePasswordMutation()

  const validateFailure = validatePasswordMutation.data
    ? !validatePasswordMutation.data.isPasswordValid
    : false

  const handlePasswordChange = (event: React.ChangeEvent<HTMLInputElement>) =>
    setPassword(event.target.value)

  const handleProceed = () => {
    validatePasswordMutation.mutate(
      { password },
      {
        onSuccess: ({ isPasswordValid }) => {
          if (isPasswordValid) {
            handleClose()
            onSuccess()
          }
        },
      },
    )
  }

  const handleClose = () => {
    // reset the state
    setPassword('')
    setShowPassword(false)
    onClose()
  }

  const handleClickShowPassword = () => setShowPassword((prevValue) => !prevValue)

  return (
    <Dialog
      open
      data-testid="Profile2FAValidatePasswordModal"
    >
      <DialogTitle>
        {ctIntl.formatMessage({ id: titleMsgId })}
        <IconButton
          aria-label="close"
          onClick={handleClose}
          style={{ position: 'absolute', top: 8, right: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          gap: '20px',
          display: 'flex',
          flexDirection: 'column',
          minWidth: '500px',
        }}
      >
        {extraDespMsgId && (
          <DialogContentText>
            {ctIntl.formatMessage({ id: extraDespMsgId })}
          </DialogContentText>
        )}
        <DialogContentText>
          {ctIntl.formatMessage({ id: 'Please validate your password to proceed.' })}
        </DialogContentText>
        <TextField
          data-testid="Profile2FAValidatePasswordModalUsername"
          label={ctIntl.formatMessage({ id: 'Username' })}
          fullWidth
          variant="standard"
          value={username}
          disabled
        />
        <TextField
          data-testid="Profile2FAValidatePasswordModalPassword"
          fullWidth
          label={ctIntl.formatMessage({ id: 'Password' })}
          type={showPassword ? 'text' : 'password'}
          value={password}
          error={validatePasswordMutation.isError || validateFailure}
          onChange={handlePasswordChange}
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                  >
                    {showPassword ? <VisibilityIcon /> : <VisibilityOffIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            },
          }}
        />
        {(validatePasswordMutation.isError || validateFailure) && (
          <Typography
            variant="body2"
            color="error"
          >
            {ctIntl.formatMessage({
              id: 'The password does not match',
            })}
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={handleClose}
          color="primary"
        >
          {ctIntl.formatMessage({ id: 'Cancel' })}
        </Button>
        <Button
          data-testid="Profile2FAValidatePasswordModalProceedButton"
          onClick={handleProceed}
          color="primary"
          disabled={!password}
          loading={validatePasswordMutation.isPending}
        >
          {ctIntl.formatMessage({ id: 'Proceed' })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ValidatePasswordModal
