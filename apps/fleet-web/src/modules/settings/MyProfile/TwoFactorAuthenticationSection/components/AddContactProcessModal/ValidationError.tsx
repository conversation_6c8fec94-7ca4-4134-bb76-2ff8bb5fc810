import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  NativeLink,
  type NativeLinkProps,
} from '@karoo-ui/core'
import type { SetRequired } from 'type-fest'

import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import { ImageContainer } from '../../api/types'
import type { StepComponentProps } from './types'

type Props = {
  image: string
  desp: string
  resendText: string
  isLoading: boolean
} & StepComponentProps

export default function ValidationError({
  desp,
  image,
  isLoading,
  resendText,
  dispatch,
  onClose,
}: Props & StepComponentProps) {
  return (
    <ValidationErrorBase
      image={image}
      mainText={desp}
      isLoading={isLoading}
      onCloseButtonClick={() => onClose('clickedCancelButton')}
      renderRetryActionButtons={() => (
        <>
          <ValidationErrorModalRetryActionButton
            data-testid="Profile2FAAddContactModalErrorResend"
            onClick={() => dispatch({ type: 'clickResendCodeLink' })}
          >
            {ctIntl.formatMessage({ id: 'Resend Code' })}
          </ValidationErrorModalRetryActionButton>
          <ValidationErrorModalRetryActionButton
            data-testid="Profile2FAAddContactModalErrorReenter"
            onClick={() => dispatch({ type: 'clickReenterContactLink' })}
          >
            {resendText}
          </ValidationErrorModalRetryActionButton>
        </>
      )}
    />
  )
}

export type ValidationErrorBaseProps = {
  renderRetryActionButtons: () => React.ReactNode
  image: string
  mainText: string
  isLoading: boolean
  onCloseButtonClick: () => void
}
export function ValidationErrorBase({
  renderRetryActionButtons,
  mainText,
  image,
  isLoading,
  onCloseButtonClick,
}: ValidationErrorBaseProps) {
  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
        <ImageContainer {...makeSanitizedInnerHtmlProp({ dirtyHtml: image })} />
      </Box>
      <DialogTitle data-testid="Profile2FAAddContactModalErrorTitle">
        {ctIntl.formatMessage({ id: 'Validation Error' })}
      </DialogTitle>
      <DialogContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '15px',
        }}
      >
        <DialogContentText sx={{ textAlign: 'center' }}>{mainText}</DialogContentText>
        <Button
          data-testid="Profile2FAAddContactModalErrorCloseButton"
          loading={isLoading}
          onClick={onCloseButtonClick}
          variant="contained"
        >
          {ctIntl.formatMessage({ id: 'Close' })}
        </Button>
      </DialogContent>
      <DialogActions>
        <Box
          component="span"
          sx={{ mr: 0.5 }}
        >
          {ctIntl.formatMessage({ id: 'Want to try again?' })}
        </Box>
        {renderRetryActionButtons()}
      </DialogActions>
    </>
  )
}

type ValidationErrorModalRetryActionButtonProps = SetRequired<
  NativeLinkProps<'button'>,
  'onClick'
>

export function ValidationErrorModalRetryActionButton({
  children,
  ...props
}: ValidationErrorModalRetryActionButtonProps) {
  return (
    <NativeLink
      component="button"
      variant="body2"
      color="inherit"
      {...props}
    >
      <strong>{children}</strong>
    </NativeLink>
  )
}
