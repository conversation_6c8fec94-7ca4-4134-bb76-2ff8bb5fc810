import { useState } from 'react'
import { Field, type FieldProps } from 'formik'
import { Box, Button, CircularProgress } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import TwoFactorAuthenticationEmailOrange from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-email-orange.svg'
import TwoFactorAuthenticationPhoneOrange from '@fleet-web/../assets/svg/TwoFactorAuthentication/2fa-phone-orange.svg'
import { getAuthenticatedUser } from '@fleet-web/duxs/user'
import {
  getContactEmailEnabled,
  getContactSmsEnabled,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import SectionHeader from '@fleet-web/util-components/section-header'

import { sections } from '..'
import type { TwoFAContactType } from '../types'
import { useActivateTwoFAMutation } from './api/query'
import type { DeleteTwoFAContactParamType } from './api/types'
import AddEmailProcessSection from './components/AddEmailProcessSection'
import AddPhoneProcessSection from './components/AddPhoneProcessSection'
import AuthenticationMethodSection from './components/AuthenticationMethodSection'
import DeleteContactConfirmationModal from './components/DeleteContactConfirmationModal'
import InactivedSection from './components/InactivedSection'
import ValidatePasswordModal from './components/ValidatePasswordModal'

type Props = {
  isActivated: boolean
}

const TwoFactorAuthenticationSection = ({ isActivated }: Props) => {
  const user = useTypedSelector(getAuthenticatedUser)
  const contactEmailEnabled = useTypedSelector(getContactEmailEnabled)
  const contactSmsEnabled = useTypedSelector(getContactSmsEnabled)

  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'add_phone' | 'add_email'
      }
    | {
        type: 'delete_contact'
        context: DeleteTwoFAContactParamType
      }
    | {
        type: 'authentication'
        context: {
          titleMsgId: string
          extraDespMsgId: string | undefined
        }
      }
    | null
  >(null)

  const activateTwoFAMutation = useActivateTwoFAMutation()

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          margin: '50px 0 30px',
          borderBottom: '1px solid #999',
          paddingBottom: '10px',
          '& .SectionHeader': {
            margin: 0,
            borderBottom: 0,
            padding: 0,
          },
        }}
      >
        <SectionHeader {...sections.twoFactor} />
        {isActivated ? (
          <Button
            data-testid="Profile2FADeactivateButton"
            variant="outlined"
            onClick={() =>
              setCurrentModal({
                type: 'authentication',
                context: {
                  titleMsgId: 'Deactivate two factor authentication',
                  extraDespMsgId:
                    'settings.profile.twoFactor.validation.deactivate.desp',
                },
              })
            }
            startIcon={
              activateTwoFAMutation.isPending && (
                <CircularProgress
                  size={16}
                  color="inherit"
                />
              )
            }
          >
            {ctIntl.formatMessage({ id: 'Deactivate' })}
          </Button>
        ) : null}
      </Box>
      {isActivated ? (
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
          {contactEmailEnabled && (
            <Field name="twoFA.email">
              {({ field }: FieldProps<TwoFAContactType>) => (
                <AuthenticationMethodSection
                  {...field}
                  titleMsgId="Email Address"
                  despMsgOption="an email address"
                  buttonMsgId="Add Email"
                  image={TwoFactorAuthenticationEmailOrange}
                  onAdd={() => setCurrentModal({ type: 'add_email' })}
                  onDelete={() =>
                    field.value?.value &&
                    setCurrentModal({
                      type: 'delete_contact',
                      context: {
                        type: 'email',
                        value: field.value?.value,
                      },
                    })
                  }
                />
              )}
            </Field>
          )}
          {contactSmsEnabled && (
            <Field name="twoFA.phone">
              {({ field }: FieldProps<TwoFAContactType>) => (
                <AuthenticationMethodSection
                  {...field}
                  titleMsgId="Phone Number"
                  despMsgOption="a phone number"
                  buttonMsgId="Add Phone"
                  image={TwoFactorAuthenticationPhoneOrange}
                  onAdd={() => setCurrentModal({ type: 'add_phone' })}
                  onDelete={() =>
                    field.value?.value &&
                    setCurrentModal({
                      type: 'delete_contact',
                      context: {
                        type: 'phone',
                        value: field.value?.value,
                      },
                    })
                  }
                />
              )}
            </Field>
          )}
        </Box>
      ) : (
        <InactivedSection
          isLoading={activateTwoFAMutation.isPending}
          onActivate={() =>
            setCurrentModal({
              type: 'authentication',
              context: {
                titleMsgId: 'Login',
                extraDespMsgId: undefined,
              },
            })
          }
        />
      )}

      {match(currentModal)
        .with(null, () => null)
        .with({ type: 'authentication' }, ({ context }) => (
          <ValidatePasswordModal
            extraDespMsgId={context.extraDespMsgId}
            titleMsgId={context.titleMsgId}
            onClose={() => setCurrentModal(null)}
            onSuccess={() => activateTwoFAMutation.mutate(!isActivated)}
            username={user.username}
          />
        ))
        .with({ type: 'delete_contact' }, ({ context }) => (
          <DeleteContactConfirmationModal
            type={context.type}
            value={context.value}
            onClose={() => setCurrentModal(null)}
          />
        ))
        .with({ type: 'add_email' }, () => (
          <AddEmailProcessSection onClose={() => setCurrentModal(null)} />
        ))
        .with({ type: 'add_phone' }, () => (
          <AddPhoneProcessSection onClose={() => setCurrentModal(null)} />
        ))
        .exhaustive()}
    </>
  )
}

export default TwoFactorAuthenticationSection
