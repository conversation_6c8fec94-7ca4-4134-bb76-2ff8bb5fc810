/* eslint-disable no-param-reassign */
// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { match } from 'ts-pattern'

import type { MainUserId } from '@fleet-web/api/types'
import { duxsMocks } from '@fleet-web/cypress-ct/mocks/duxs'
import {
  TWOFA_DISABLED,
  TWOFA_ENABLED_WITH_EMAIL,
  TWOFA_ENABLED_WITHOUT_CONTACT,
  userProfileEndpointMocks,
} from '@fleet-web/cypress-ct/mocks/endpoints/userProfile'
import {
  exhaustiveEndpointCallCheck,
  mountWithProviders,
} from '@fleet-web/cypress-ct/utils'
import type { UserState } from '@fleet-web/duxs/user'

import UserSettings from '..'

const mockState = duxsMocks.user().mockState
const user = {
  username: 'testUser',
  id: '123' as MainUserId,
  cuid: null,
  companyName: 'testCompany',
  account: 'testAccount',
  primaryEmail: '',
  firstName: null,
  lastName: null,
  isTokenAccount: undefined,
  phoneNumber: null,
} satisfies UserState['user']

function mountTwoFA() {
  mountWithProviders(<UserSettings />, {
    reduxOptions: {
      preloadedState: {
        user: {
          ...mockState,
          user,
          settings: {
            ...mockState.settings,
            twoFactorAuthentication: true,
          },
        },
      },
    },
  })
}

function inactiveSectionCheck() {
  cy.get('#two-factor-authentication')
    .children()
    .first()
    .should('contain.text', 'Two Factor Authentication')
}

const activateAndPasswordValidationCheck = () => {
  // click the activate button
  cy.findByTestId('Profile2FAInactiveButton').click()

  // validation password modal with username
  cy.findByTestId('Profile2FAValidatePasswordModal').should('exist')
  cy.findByTestId('Profile2FAValidatePasswordModalUsername')
    .find('input')
    .should('contain.value', user.username)
    .should('be.disabled')

  // proceed button should be disabled if no password filled
  cy.get('.MuiDialogActions-root > button').last().should('be.disabled')
}

const deactivateAndPasswordValidationCheck = () => {
  // click the activate button
  cy.findByTestId('Profile2FADeactivateButton').click()

  // validation password modal with username
  cy.findByTestId('Profile2FAValidatePasswordModal').should('exist')
  cy.findByTestId('Profile2FAValidatePasswordModalUsername')
    .find('input')
    .should('contain.value', user.username)
    .should('be.disabled')

  // proceed button should be disabled if no password filled
  cy.get('.MuiDialogActions-root > button').last().should('be.disabled')
}

const onlyEmailContactExistCheck = () => {
  cy.get('#two-factor-authentication')
    .children()
    .first()
    .should('contain.text', 'Two Factor Authentication')

  // 2fa activated
  cy.findByTestId('Profile2FA-Delete').should('exist')
  cy.findByTestId('Profile2FAPhone Number').should('exist')

  cy.findByTestId('Profile2FAEmail Address')
    .find('button')
    .should('contain.text', 'Delete')
  cy.findByTestId('Profile2FAPhone Number')
    .find('button')
    .should('contain.text', 'Add Phone')

  cy.findByTestId('Profile2FAEmail Address')
    .get('p')
    .eq(1)
    .should('contain.text', 'joseph.feng@cartrack')
}

const typeCodeWithIndex = (index: number) => {
  cy.findByTestId('Profile2FAAddContactModalValidatingCodeContent')
    .find('input[type="text"]')
    .eq(index)
    .type('1', { force: true })
}

const selectPhoneInputCountry = () =>
  cy
    .findByTestId('PhoneNumberInput-country-code-selection')
    .find('input')
    .type('singapore{downarrow}{enter}', {
      force: true,
    })

const fillCode = () => {
  // fill the code
  typeCodeWithIndex(0)
  typeCodeWithIndex(1)
  typeCodeWithIndex(2)
  // the validation button should be disabled before filling all inputs
  cy.findByTestId('Profile2FAAddContactModalValidationCodeButton').should('be.disabled')
  typeCodeWithIndex(3)
  typeCodeWithIndex(4)
  typeCodeWithIndex(5)
}

const handleApiCalls = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_prelogin_data' }, () =>
      req.reply({ delay: 50, body: { id: 10, result: {} } }),
    )
    .with({ method: 'ct_fleet_two_factor_auth_validate_password' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_two_factor_auth_validate_password())
    })
    .with({ method: 'ct_fleet_activate_two_factor_auth' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_activate_two_factor_auth())
    })
    .with({ method: 'ct_fleet_update_two_factor_auth_type' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_update_two_factor_auth_type())
    })
    .with({ method: 'ct_fleet_two_factor_validate_otp' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_two_factor_validate_otp())
    })
    .with({ method: 'ct_fleet_delete_two_factor_auth_type' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_delete_two_factor_auth_type())
    })
    .with({ method: 'ct_fleet_get_start_prevent_settings_section' }, () => {
      req.reply(userProfileEndpointMocks.ct_fleet_get_start_prevent_settings_section())
    })
    .otherwise(exhaustiveEndpointCallCheck)

describe('Two Factor Authentication Profile Inactive', () => {
  it('show the two factor authentication with active button', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(TWOFA_DISABLED),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })
    mountTwoFA()

    inactiveSectionCheck()
  })
})

describe('Two Factor Authentication Profile validation and activation', () => {
  it('click on the active button and close the validation modal', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(TWOFA_DISABLED),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    inactiveSectionCheck()
    activateAndPasswordValidationCheck()

    // click cancel button
    cy.get('.MuiDialogActions-root > button').first().click()

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')
  })

  it('click on the active button and fill the password to activate the 2fa', () => {
    let firstTimeCallFetUserProfile = true
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          const data = firstTimeCallFetUserProfile
            ? TWOFA_DISABLED
            : TWOFA_ENABLED_WITHOUT_CONTACT
          firstTimeCallFetUserProfile = false
          req.reply(userProfileEndpointMocks.ct_fleet_get_user_profile_settings(data))
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    inactiveSectionCheck()
    activateAndPasswordValidationCheck()
    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')

    // the inactivate button should be loading sign
    cy.findByTestId('Profile2FAInactiveButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')
    cy.findByTestId('Profile2FADeactivateButton').should('exist')
  })

  it('click on the active button and failed to validate the filled password', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(TWOFA_DISABLED),
          )
        })
        .with({ method: 'ct_fleet_two_factor_auth_validate_password' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: { isPasswordValid: false },
              error: null,
            },
          })
        })

        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    inactiveSectionCheck()
    activateAndPasswordValidationCheck()
    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // the password is wrong
    cy.findByTestId('Profile2FAValidatePasswordModalPassword')
      .find('.Mui-error')
      .should('exist')
  })

  it('click on the active button and failed to activate the 2fa', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(TWOFA_DISABLED),
          )
        })
        .with({ method: 'ct_fleet_activate_two_factor_auth' }, () => {
          req.reply({ statusCode: 500, delay: 50 })
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    inactiveSectionCheck()
    activateAndPasswordValidationCheck()
    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')

    // the inactivate button should be loading sign
    cy.findByTestId('Profile2FAInactiveButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // not activated
    cy.findByTestId('Profile2FAInactive').should('exist')
  })
})

describe('Two Factor Authentication Profile deactivation', () => {
  it('click on the deactive button and close the validation modal', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    cy.findByTestId('Profile2FADeactivateButton').should('exist')
    deactivateAndPasswordValidationCheck()

    // click cancel button
    cy.get('.MuiDialogActions-root > button').first().click()

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')
  })

  it('click on the deactivate button and fill the password to deactivate the 2fa', () => {
    let firstTimeCallFetUserProfile = true
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          const data = firstTimeCallFetUserProfile
            ? TWOFA_ENABLED_WITHOUT_CONTACT
            : TWOFA_DISABLED
          firstTimeCallFetUserProfile = false

          req.alias = 'apiRequestDeactivateSuccess'
          req.reply(userProfileEndpointMocks.ct_fleet_get_user_profile_settings(data))
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    cy.findByTestId('Profile2FADeactivateButton').should('exist')
    deactivateAndPasswordValidationCheck()
    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    cy.wait('@apiRequestDeactivateSuccess')

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')

    // the inactivate button should be loading sign
    cy.findByTestId('Profile2FADeactivateButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // 2fa deactivated
    inactiveSectionCheck()
  })

  it('click on the deactivate button and failed to validate the filled password', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.alias = 'apiRequestDeactivateValidateFail'
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .with({ method: 'ct_fleet_two_factor_auth_validate_password' }, () => {
          req.reply({
            delay: 50,
            body: { id: 10, result: { isPasswordValid: false }, error: null },
          })
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    cy.findByTestId('Profile2FADeactivateButton').should('exist')
    deactivateAndPasswordValidationCheck()

    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    cy.wait('@apiRequestDeactivateValidateFail')

    // the password is wrong
    cy.findByTestId('Profile2FAValidatePasswordModalPassword')
      .find('.Mui-error')
      .should('exist')
  })

  it('click on the deactivate button and failed to deactivate the 2fa', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.alias = 'apiRequestDeactivateFail'
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .with({ method: 'ct_fleet_activate_two_factor_auth' }, () => {
          req.reply({ statusCode: 500, delay: 50 })
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    cy.findByTestId('Profile2FADeactivateButton').should('exist')
    deactivateAndPasswordValidationCheck()
    // fill any password
    cy.findByTestId('Profile2FAValidatePasswordModalPassword').find('input').type('pwd')

    // click proceed button
    cy.get('.MuiDialogActions-root > button').last().click()

    // button loading sign
    cy.findByTestId('Profile2FAValidatePasswordModalProceedButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    cy.wait('@apiRequestDeactivateFail')

    // modal closed
    cy.findByTestId('Profile2FAValidatePasswordModal').should('not.exist')

    // the inactivate button should be loading sign
    cy.findByTestId('Profile2FADeactivateButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // not deactivated
    cy.findByTestId('Profile2FADeactivateButton').should('exist')
    // cy.findByTestId('Profile2FAInactive').should('exist')
  })
})

describe('Two Factor Authentication Profile display and delete contact', () => {
  it("show the 2fa config when it's enabled", () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITH_EMAIL,
            ),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    onlyEmailContactExistCheck()
  })

  it('delete the email contact', () => {
    let firstTimeCallFetchUserProfile = true
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          const data = firstTimeCallFetchUserProfile
            ? TWOFA_ENABLED_WITH_EMAIL
            : TWOFA_ENABLED_WITHOUT_CONTACT
          firstTimeCallFetchUserProfile = false
          req.reply(userProfileEndpointMocks.ct_fleet_get_user_profile_settings(data))
        })
        .with({ method: 'ct_fleet_delete_two_factor_auth_type' }, () => {
          req.reply(userProfileEndpointMocks.ct_fleet_delete_two_factor_auth_type())
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    onlyEmailContactExistCheck()

    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    cy.findByTestId('Profile2FADeleteModal').should('exist')

    // click the close button
    cy.findByTestId('Profile2FADeleteModal').find('button').first().click()
    cy.findByTestId('Profile2FADeleteModal').should('not.exist')

    // open the delete modal again
    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    // click the confirm button
    cy.findByTestId('Profile2FADeleteModal').find('button').last().click()

    // loading sign
    cy.findByTestId('Profile2FADeleteModal')
      .find('button')
      .last()
      .find('.MuiCircularProgress-root')
      .should('exist')

    // the email part should be empty
    cy.findByTestId('Profile2FAEmail Address')
      .find('button')
      .should('contain.text', 'Add Email')
  })

  it('fail to delete the email contact', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () =>
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITH_EMAIL,
            ),
          ),
        )
        .with({ method: 'ct_fleet_delete_two_factor_auth_type' }, () =>
          req.reply({ statusCode: 500, delay: 50 }),
        )
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    onlyEmailContactExistCheck()

    // cy.get('.EditButtons > button').click()
    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    cy.findByTestId('Profile2FADeleteModal').should('exist')

    // click the close button
    cy.findByTestId('Profile2FADeleteModal').find('button').first().click()
    cy.findByTestId('Profile2FADeleteModal').should('not.exist')

    // open the delete modal again
    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    // click the confirm button
    cy.findByTestId('Profile2FADeleteModal').find('button').last().click()

    // loading sign
    cy.findByTestId('Profile2FADeleteModal')
      .find('button')
      .last()
      .find('.MuiCircularProgress-root')
      .should('exist')

    // the email part should still exist
    onlyEmailContactExistCheck()
  })
})

describe('Two Factor Authentication Profile Add contact', () => {
  it('click the add email button to add a new email', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalemail').should('exist')

    // click the close button
    cy.findByTestId('Profile2FAAddContactModalemailCloseButton').click()
    cy.findByTestId('Profile2FAAddContactModalemail').should('not.exist')

    // open the add contact modal again
    cy.findByTestId('Profile2FAEmail Address').find('button').click()

    // send button disabled when no value
    cy.findByTestId('Profile2FAAddContactModalEmailSendCodeButton').should(
      'be.disabled',
    )

    // fill the email field
    cy.findByTestId('Profile2FAAddContactModalEmailField').type(
      '<EMAIL>',
    )

    // send the email value
    cy.findByTestId('Profile2FAAddContactModalEmailSendCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalEmailSendCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // fill the code
    fillCode()

    // send the code
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // successs
    cy.findByTestId('Profile2FAAddContactModalSuccessTitle').should('exist')

    // close the modal
    cy.findByTestId('Profile2FAAddContactModalSuccessCloseButton').click()

    cy.findByTestId('Profile2FAAddContactModalemail').should('not.exist')
  })

  it('click the add phone button to add a new phone', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAPhone Number').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalphone').should('exist')

    // send button disabled when no value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').should(
      'be.disabled',
    )

    // show the country list to select the country
    selectPhoneInputCountry()

    // fill the phone field
    cy.findByTestId('Profile2FAAddContactModalPhoneContent')
      .find('input[type="tel"]')
      .type('11222223', { force: true })

    // send the phone value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // fill the code
    fillCode()

    // send the code
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // successs
    cy.findByTestId('Profile2FAAddContactModalSuccessTitle').should('exist')

    // close the modal
    cy.findByTestId('Profile2FAAddContactModalSuccessCloseButton').click()

    cy.findByTestId('Profile2FAAddContactModalphone').should('not.exist')
  })

  it('click the add phone button and phone is invalid', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAPhone Number').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalphone').should('exist')

    // send button disabled when no value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').should(
      'be.disabled',
    )

    // show the country list to select the country
    selectPhoneInputCountry()

    // fill the phone field
    cy.findByTestId('Profile2FAAddContactModalPhoneContent')
      .find('input[type="tel"]')
      .type('11222', { force: true })

    // send button enabled when value but not submitted yet
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton')
      .should('be.enabled')
      .click()

    cy.findByTestId('Profile2FAAddContactModalPhoneContentPhoneNumberInput')
      .find('p')
      .should('contain.text', 'Please provide a valid phone number')
  })

  it('failed to send phone number to get validation code', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () => {
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          )
        })
        .with({ method: 'ct_fleet_update_two_factor_auth_type' }, () => {
          req.reply({ statusCode: 500, delay: 50 })
        })
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAPhone Number').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalphone').should('exist')

    // show the country list to select the country
    selectPhoneInputCountry()

    // fill the phone field
    cy.findByTestId('Profile2FAAddContactModalPhoneContent')
      .find('input[type="tel"]')
      .type('11222223', { force: true })

    // send the phone value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')
  })

  it('click resend code link in code validation, then to validation error and click the resend code link', () => {
    // let updateTwoFAContactApiTimes = 0
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () =>
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          ),
        )
        .with({ method: 'ct_fleet_update_two_factor_auth_type' }, () => {
          // updateTwoFAContactApiTimes++
          req.reply(userProfileEndpointMocks.ct_fleet_update_two_factor_auth_type())
        })
        .with({ method: 'ct_fleet_two_factor_validate_otp' }, () =>
          req.reply({ statusCode: 500, delay: 50 }),
        )
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAPhone Number').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalphone').should('exist')

    // show the country list to select the country
    selectPhoneInputCountry()

    // fill the phone field
    cy.findByTestId('Profile2FAAddContactModalPhoneContent')
      .find('input[type="tel"]')
      .type('11222223', { force: true })

    // send the phone value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // click the resend link
    cy.findByTestId('Profile2FAAddContactModalCodeValidationResend').click()

    // the verify code button should be loading sign
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // fill the code
    fillCode()

    // send the code
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton').click()

    // loading sign
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // validation error
    cy.findByTestId('Profile2FAAddContactModalErrorTitle').should('exist')

    // click the resend link
    cy.findByTestId('Profile2FAAddContactModalErrorResend').click()

    // error close button should be loading sign
    cy.findByTestId('Profile2FAAddContactModalErrorCloseButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // cy.wait('@apiRequestsResend').then(() => {
    //   cy.wrap(updateTwoFAContactApiTimes).should('eq', 3)
    // })

    // to the code validation
    cy.findByTestId('Profile2FAAddContactModalValidatingCodeContent').should('exist')
  })

  it('switch to code validation error page and click the reenter contact link', () => {
    let updateTwoFAContactApiTimes = 0
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_user_profile_settings' }, () =>
          req.reply(
            userProfileEndpointMocks.ct_fleet_get_user_profile_settings(
              TWOFA_ENABLED_WITHOUT_CONTACT,
            ),
          ),
        )
        .with({ method: 'ct_fleet_update_two_factor_auth_type' }, () => {
          updateTwoFAContactApiTimes++
          req.alias = 'updateRequests'
          req.reply(userProfileEndpointMocks.ct_fleet_update_two_factor_auth_type())
        })
        .with({ method: 'ct_fleet_two_factor_validate_otp' }, () =>
          req.reply({ statusCode: 500, delay: 50 }),
        )
        .otherwise(() => handleApiCalls(req))
    })

    mountTwoFA()

    // cy.get('.EditButtons > button').click()
    cy.get('#two-factor-authentication')
      .children()
      .first()
      .should('contain.text', 'Two Factor Authentication')

    // 2fa activated
    cy.findByTestId('Profile2FAEmail Address').should('exist')
    cy.findByTestId('Profile2FAPhone Number').should('exist')

    cy.findByTestId('Profile2FAPhone Number').find('button').click()

    // open the add contact modal
    cy.findByTestId('Profile2FAAddContactModalphone').should('exist')

    // show the country list to select the country
    selectPhoneInputCountry()

    // fill the phone field
    cy.findByTestId('Profile2FAAddContactModalPhoneContent')
      .find('input[type="tel"]')
      .type('11222223', { force: true })

    // send the phone value
    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton').click()

    cy.findByTestId('Profile2FAAddContactModalPhoneSendCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // fill the code
    fillCode()

    // send the code
    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton').click()

    cy.findByTestId('Profile2FAAddContactModalValidationCodeButton')
      .find('.MuiCircularProgress-root')
      .should('exist')

    // validation error
    cy.findByTestId('Profile2FAAddContactModalErrorTitle').should('exist')

    // click the reenter link
    cy.findByTestId('Profile2FAAddContactModalErrorReenter').click()

    cy.wait('@updateRequests').then(() => {
      cy.wrap(updateTwoFAContactApiTimes).should('eq', 1)
    })

    // to the code validation
    cy.findByTestId('Profile2FAAddContactModalPhoneContent').should('exist')
  })
})
