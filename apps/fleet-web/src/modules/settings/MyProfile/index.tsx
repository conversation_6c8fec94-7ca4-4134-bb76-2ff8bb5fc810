import { useMemo } from 'react'
import { CircularProgressDelayedAbsolute, Stack } from '@karoo-ui/core'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import { FormProvider, useForm } from 'react-hook-form'
import { parsePhoneNumber } from 'react-phone-number-input'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import PageHeader from '@fleet-web/components/_containers/PageHeader'
import PageWithMainTableContainer from '@fleet-web/components/_containers/PageWithMainTable'
import {
  getISO3166Alpha2CountryCode,
  getSettings_UNSAFE,
  getTwoFactorAuthenticationSetting,
} from '@fleet-web/duxs/user'
import {
  doesCurrentUserHaveAccessFromSetting,
  getCostsSetting,
  getIsSubUser,
  getUserProfileSettingsEditUser,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { AppState } from '@fleet-web/root-reducer'
import { messages } from '@fleet-web/shared/forms/messages'
import { getPhoneInfoSchema } from '@fleet-web/shared/react-hook-form/PhoneNumberInput'
import useCountryCodes from '@fleet-web/shared/react-hook-form/PhoneNumberInput/useCountryCodes'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import {
  generateEmailSchema,
  generateStringSchema,
} from '@fleet-web/util-functions/zod-utils'

import AccountFormSection from './AccountFormSection'
import { useUserProfileSettingsQuery } from './api'
import { ImmobiliseVehicleSection } from './ImmobiliseVehicleSection'
import { useStartPreventSettingsSection } from './ImmobiliseVehicleSection/api/queries'
import PasswordFormSection from './PasswordFormSection'
import SSOFormSection from './SSOFormSection'
import TwoFactorAuthenticationFormSection from './TwoFactorAuthenticationFormSection'
import type { GetUserProfileSettings } from './types'

export const sections = {
  personalInfo: {
    label: 'Personal Info',
    id: 'personal-info',
  },
  miFleetPersonalInfo: {
    label: 'MiFleet Personal Info',
    id: 'mifleet-personal-info',
    selector: (state: AppState) => doesCurrentUserHaveAccessFromSetting(state, 'costs'),
  },
  immobiliseVehicle: {
    label: 'Immobilise Vehicle',
    id: 'immobilise-vehicle',
  },
  twoFactor: {
    label: 'Two Factor Authentication',
    id: 'two-factor-authentication',
  },
  userAccount: {
    label: 'User Account',
    id: 'user-account',
  },
  settings: {
    label: 'Preferences',
    id: 'settings-settings',
  },
  advancedSettings: {
    self: { label: 'Advanced', id: 'settings-advanced' },
    sub: {
      sso: {
        label: 'SSO Token',
        id: 'sso-advanced',
      },
      clear: {
        label: 'Clear Fuel Validation Data',
        id: 'clear-advanced',
      },
    },
  },
} as const

const profileSchema = ({
  userHasMifleet,
  isSubUser,
}: {
  userHasMifleet: boolean
  isSubUser: boolean
}) =>
  z.object({
    email: isSubUser
      ? generateEmailSchema({ isRequired: true })
      : z.string().nullable(),
    cellphone: isSubUser
      ? getPhoneInfoSchema({ isRequired: false })
      : z.object({
          countryCode: z.string().nullable(),
          number: z.string().nullable(),
        }),
    languageId: generateStringSchema({ isRequired: true }),
    gpsFormat: z.literal('DMS').or(z.literal('D')),
    logo: z.string().nullable().optional(),
    username: z.string().trim().min(1).max(50),
    ssoKey: z.string().nullable(),
    timeZone: z.string().nullable(),
    useLocalTime: z.boolean().nullable(),
    roadDescriptionType: z.literal('principal').or(z.literal('alternative')),
    //mifleet
    name: userHasMifleet
      ? z
          .string()
          .trim()
          .min(
            1,
            ctIntl.formatMessage({
              id: messages.required,
            }),
          )
          .max(
            50,
            ctIntl.formatMessage(
              {
                id: messages.validStringMax,
              },
              {
                values: { number: 50 },
              },
            ),
          )
          .optional()
      : z.string().nullable(),
    //2fa
    twofaEnabled: z.boolean(),
    twofaPhone: z.string().nullable(),
    twofaPhoneAddedTime: z.date().nullable(),
    twofaEmail: z.string().nullable(),
    twofaEmailAddedTime: z.date().nullable(),
  })

export type ProfileSchema = z.infer<ReturnType<typeof profileSchema>>

function ProfileForm({
  userSettingsSuccessQuery,
}: {
  userSettingsSuccessQuery:
    | QueryObserverSuccessResult<GetUserProfileSettings.Return>
    | QueryObserverPlaceholderResult<GetUserProfileSettings.Return, Error>
}) {
  const { data } = userSettingsSuccessQuery
  const userHasMifleet = useTypedSelector(getCostsSetting)
  const defaultCountryCode = useTypedSelector(getISO3166Alpha2CountryCode)
  const userProfileSettingsEditUser = useTypedSelector(getUserProfileSettingsEditUser)
  const is2FAFeatureAvailable = useTypedSelector(getTwoFactorAuthenticationSetting)
  const startPreventSettingsSectionQuery = useStartPreventSettingsSection()
  const { byCountryCode } = useCountryCodes()
  const { userImage } = useTypedSelector(getSettings_UNSAFE) ?? null
  const countryCode = byCountryCode[defaultCountryCode]
  const isSubUser = useTypedSelector(getIsSubUser)

  const formValues = useMemo(() => {
    const parsedPhoneNumber = parsePhoneNumber(data.mobileNumber)

    return {
      cellphone: {
        number: parsedPhoneNumber?.nationalNumber ?? null,
        countryCode: parsedPhoneNumber?.country ?? countryCode.value ?? null,
      },
      email: data.email,
      name: data.mifleetName,
      timeZone: data.timeZone,
      useLocalTime: data.useLocalTime,
      roadDescriptionType: data.roadDescriptionType,
      username: data.username,
      languageId: data.languageId,
      gpsFormat: data.gpsFormat,
      // The correct logo is from appsettings. The logo's data might be the main user's logo even if the current user is a sub-user.
      logo: userImage ?? null,
      ssoKey: data.ssoKey,
      twofaEnabled: data.twofaEnabled ?? false,
      twofaPhone: data.twofaPhone,
      twofaPhoneAddedTime: data.twofaPhoneAddedTime,
      twofaEmail: data.twofaEmail,
      twofaEmailAddedTime: data.twofaEmailAddedTime,
    }
  }, [data, userImage, countryCode.value])

  const methods = useForm<ProfileSchema>({
    resolver: zodResolverV4(profileSchema({ userHasMifleet, isSubUser })),
    values: formValues,
    mode: 'all',
  })

  return (
    <FormProvider {...methods}>
      <PageWithMainTableContainer>
        <PageHeader>
          <PageHeader.Title>
            {ctIntl.formatMessage({ id: 'myProfile.tabName' })}
          </PageHeader.Title>
        </PageHeader>
        <Stack
          gap={2}
          pb={2}
        >
          <AccountFormSection
            control={methods.control}
            userHasMifleet={userHasMifleet}
            canEditForm={userProfileSettingsEditUser}
          />
          <PasswordFormSection />
          {is2FAFeatureAvailable && (
            <TwoFactorAuthenticationFormSection control={methods.control} />
          )}
          <ImmobiliseVehicleSection
            startPreventSettingsSectionQuery={startPreventSettingsSectionQuery}
          />
          <SSOFormSection control={methods.control} />
        </Stack>
      </PageWithMainTableContainer>
    </FormProvider>
  )
}

const UserSettings = () => {
  const userProfileSettingsQuery = useUserProfileSettingsQuery()

  return match(userProfileSettingsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, (successQuery) => (
      <ProfileForm userSettingsSuccessQuery={successQuery} />
    ))
    .exhaustive()
}

export default UserSettings
