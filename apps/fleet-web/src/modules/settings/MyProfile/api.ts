import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useDispatch } from 'react-redux'

import apiCaller, { apiCallerNoX } from '@fleet-web/api/api-caller'
import {
  makeMutationErrorHandlerWithToast,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { logout } from '@fleet-web/duxs/user'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import type { GetUserProfileSettings } from './types'

/************************************************ Fetch User Profile Settings *************************************************/

export const userSettingsApi = {
  fetchUserProfileSettings() {
    return apiCallerNoX<GetUserProfileSettings.ApiOutput>(
      'ct_fleet_get_user_profile_settings',
    ).then((res) => {
      const twoFA = res.two_factor_authentication
      const twoFAParts = twoFA
        ? {
            twofaEnabled:
              typeof twoFA.enabled === 'string'
                ? twoFA.enabled === 'true'
                : twoFA.enabled,
            twofaEmail: twoFA.email,
            twofaPhone: twoFA.phone,
            twofaEmailAddedTime: twoFA.email_date_added
              ? new Date(twoFA.email_date_added)
              : null,
            twofaPhoneAddedTime: twoFA.phone_date_added
              ? new Date(twoFA.phone_date_added)
              : null,
          }
        : {
            twofaEnabled: false,
            twofaEmail: null,
            twofaPhone: null,
            twofaEmailAddedTime: null,
            twofaPhoneAddedTime: null,
          }
      return {
        username: res.personal_settings.username,
        firstName: res.personal_settings.firstName,
        lastName: res.personal_settings.lastName,
        logo: res.personal_settings.logo,
        email: res.personal_settings.email,
        mobileNumber: res.personal_settings.mobile_number,
        gpsFormat: res.personal_settings.gps_format,
        datetimeFormat: res.personal_settings.date_time_format,
        timeZone: res.personal_settings.timezone_of_user,
        useLocalTime: res.personal_settings.timezone_from_pc,
        roadDescriptionType: res.personal_settings.road_description_type,
        languageId: res.personal_settings.language_id,
        mifleetName: res.mifleet_settings ? res.mifleet_settings.user_name : null,
        ssoKey: res.access_keys.sso.key,
        apiKey: res.access_keys.api.key,
        ...twoFAParts,
      }
    })
  },
}

const fetchUserProfileSettingsQueryKey = ['userProfileSettings'] as const

export const fetchUserProfileSettingsQuery = () =>
  createQuery({
    queryKey: fetchUserProfileSettingsQueryKey,
    queryFn: () => userSettingsApi.fetchUserProfileSettings(),
    staleTime: 20_000,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useUserProfileSettingsQuery = () =>
  useQuery(fetchUserProfileSettingsQuery())

export declare namespace ToggleSSOKey {
  type ApiInput = {
    status: boolean
  }

  type ApiOutput = {
    sso_fetch_api_token?: string
  }
}

export function useToggleSSOKeyMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (params: ToggleSSOKey.ApiInput) => toggleSSOKey(params),
    ...makeMutationErrorHandlerWithToast(),
    onSuccess(data) {
      queryClient.setQueryData<GetUserProfileSettings.Return>(
        fetchUserProfileSettingsQueryKey,
        (userProfileSettingsInCache) => {
          if (userProfileSettingsInCache !== undefined) {
            return {
              ...userProfileSettingsInCache,
              ssoKey: data.sso_fetch_api_token ?? null,
            }
          }

          return userProfileSettingsInCache
        },
      )
    },
  })
}

function toggleSSOKey(params: ToggleSSOKey.ApiInput) {
  return apiCaller('ct_fleet_enable_api_key', params).then(
    (res: ToggleSSOKey.ApiOutput) => res,
  )
}

export declare namespace UpdateUserProfileSettings {
  type ApiInput = {
    email: string
    mobile_number: string
    gps_format: 'DMS' | 'D'
    logo: string | undefined | null
    road_description_type: 'principal' | 'alternative'
    timezone_from_pc: boolean
    timezone_of_user: string | null
    language_id: string | null
    mifleet: {
      user_name: string | null
    } | null
  }

  type Response = {
    personal_settings: {
      username: string
      logo: string | null
      email: string
      mobile_number: string
      gps_format: 'DMS' | 'D'
      date_time_format: string
      timezone: string | null
      timezone_of_user: string | null
      timezone_from_pc: boolean
      road_description_type: 'principal' | 'alternative'
    }
    two_factor_authentication?: {
      enabled: boolean | 'true' | 'false' | null
      email: string | null
      phone: string | null
      email_date_added: string | null
      phone_date_added: string | null
    }
    mifleet_settings: {
      language_id: string
      user_name: string
    } | null

    access_keys: {
      sso: {
        key: null | string
      }
      api: {
        key: null | string
      }
    }
  }
}

export function useUpdateUserProfileSettingsMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: UpdateUserProfileSettings.ApiInput) =>
      updateUserProfileSettings(params),
    ...makeMutationErrorHandlerWithToast(),
    onSuccess: () => {
      queryClient.invalidateQueries(fetchUserProfileSettingsQuery())
    },
  })
}

function updateUserProfileSettings(params: UpdateUserProfileSettings.ApiInput) {
  return apiCaller(
    'ct_fleet_update_user_profile_settings',
    params,
  ) as Promise<UpdateUserProfileSettings.Response>
}

export declare namespace UpdateUserProfilePassword {
  type ApiInput = {
    currentPassword: string
    password: string
  }
}

export function useUpdateUserProfilePasswordMutation() {
  const dispatch = useDispatch()

  return useMutation({
    mutationFn: (params: UpdateUserProfilePassword.ApiInput) =>
      updateUserProfilePassword(params),
    ...makeMutationErrorHandlerWithToast(),
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'settings.profile.password.changed.loginAgain',
        }),
        {
          variant: 'error',
          onClose: () => {
            dispatch(logout({}))
          },
        },
      )
    },
  })
}

export function updateUserProfilePassword(params: UpdateUserProfilePassword.ApiInput) {
  // Order of params meter for now. That's why the object destructuring is needed.
  const { password, currentPassword } = params
  return apiCaller('ct_set_user_password', { password, currentPassword })
}
