import type { PromiseResolvedType } from '@fleet-web/types/global'

import type { userSettingsApi } from './api'

export declare namespace GetUserProfileSettings {
  type ApiOutput = {
    personal_settings: {
      username: string
      logo: null | string
      email: string
      mobile_number: string
      gps_format: any
      date_time_format: string
      timezone_of_user: string | null
      timezone_from_pc: boolean
      road_description_type: 'principal' | 'alternative'
      firstName: string | null
      lastName: string | null
      language_id: string
    }
    two_factor_authentication?: {
      enabled: boolean | 'true' | 'false' | null
      email: string | null
      phone: string | null
      email_date_added: string | null
      phone_date_added: string | null
    }
    mifleet_settings: {
      user_name: string
    } | null
    access_keys: {
      sso: {
        key: null | string
      }
      api: {
        key: null | string
      }
    }
  }

  type Return = PromiseResolvedType<typeof userSettingsApi.fetchUserProfileSettings>
}

export type TwoFAContactType = {
  value: string | null
  addedTime: Date | null
}
