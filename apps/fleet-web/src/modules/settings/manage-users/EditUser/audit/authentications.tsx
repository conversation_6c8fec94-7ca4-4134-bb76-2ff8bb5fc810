import { useEffect, useMemo } from 'react'
import {
  DataGrid,
  LinearProgress,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
} from '@karoo-ui/core'
import { connect, type DispatchProp } from 'react-redux'

import type { FetchAuthentications } from '@fleet-web/api/mifleet/audit/types'
import DataStatePlaceholder from '@fleet-web/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import type { AppState } from '@fleet-web/root-reducer'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { authenticationFilters } from '../../../../admin/shared/audit/authentications/utils'
import useFilteredAuditedData from '../../../../admin/shared/audit/useFilteredAuditedData'
import { useUserEditingContext } from '../UserEditingContext'
import { getAuthentications, getIsLoadingAuthentications, userAudit } from './slice'

const { fetchAuthentications } = userAudit.actions

type Authentication = FetchAuthentications.Authentication

type Props = ReturnType<typeof mapStateToProps> & DispatchProp

const Authentications = ({ authentications, dispatch, isLoading }: Props) => {
  const { userId } = useUserEditingContext()

  const { dateRange, filteredData } = useFilteredAuditedData({
    data: authentications,
    filters: authenticationFilters,
  })
  const columnHelper = useDataGridColumnHelper<Authentication>({ filterMode: 'client' })

  const columns = useMemo(
    (): Array<GridColDef<Authentication>> => [
      columnHelper.dateTime({
        field: 'eventTimeDate',
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        valueGetter: (_, row) =>
          row.eventTimeDate ? row.eventTimeDate.date : row.eventTimeDate,
        valueFormatter: (_, row) =>
          row.eventTimeDate
            ? row.eventTimeDate.formatted
            : ctIntl.formatMessage({ id: 'Invalid date' }),
        flex: 1,
      }),

      columnHelper.string((_, row) => row.sourceIpAddress, {
        field: 'sourceIpAddress',
        headerName: ctIntl.formatMessage({ id: 'Source IP' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.origin, {
        field: 'origin',
        headerName: ctIntl.formatMessage({ id: 'Origin' }),
        flex: 1,
      }),
      columnHelper.singleSelect((_, row) => row.success, {
        field: 'success',
        headerName: ctIntl.formatMessage({ id: 'Success' }),
        flex: 1,
        valueOptions: [
          { value: true, label: ctIntl.formatMessage({ id: 'Succeeded' }) },
          { value: false, label: ctIntl.formatMessage({ id: 'Failed' }) },
        ],
        renderCell: ({ row }) => (
          <Typography
            sx={(theme) => ({
              fontSize: 'inherit',
              color: row.success
                ? theme.palette.success.main
                : theme.palette.error.main,
            })}
          >
            {ctIntl.formatMessage({ id: row.success ? 'Succeeded' : 'Failed' })}
          </Typography>
        ),
      }),
    ],
    [columnHelper],
  )

  useEffect(() => {
    dispatch(fetchAuthentications({ userId, dateRange }))
  }, [dateRange, dispatch, userId])

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="UserSessions"
        data-testid="UserSessions"
        columns={columns}
        rows={filteredData}
        loading={isLoading}
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
              filterButton: { show: true },
            },
          }),
        }}
      />
    </>
  )
}

const mapStateToProps = (state: AppState) => ({
  isLoading: getIsLoadingAuthentications(state),
  authentications: getAuthentications(state),
})

export default connect(mapStateToProps)(Authentications)
