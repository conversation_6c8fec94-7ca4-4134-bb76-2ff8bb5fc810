import type { CreateOrUpdateSubUserHandledError } from '@fleet-web/api/admin/types'

export const getUpdateOrCreateSubUserProfileErrorMessage = (
  errorResponse: CreateOrUpdateSubUserHandledError,
) => {
  const { userWithPropertiesAlreadyExists } = errorResponse
  if (userWithPropertiesAlreadyExists && userWithPropertiesAlreadyExists.length > 0) {
    const props = new Set(userWithPropertiesAlreadyExists)

    if (props.has('username') && props.has('email')) {
      return 'updateOrCreateUser.error.usernameAndEmailAlreadyTaken'
    }
    if (props.has('username')) {
      return 'updateOrCreateUser.error.usernameAlreadyTaken'
    }
    if (props.has('email')) {
      return 'updateOrCreateUser.error.emailAlreadyTaken'
    }
  }

  return 'We are experiencing a technical error.'
}
