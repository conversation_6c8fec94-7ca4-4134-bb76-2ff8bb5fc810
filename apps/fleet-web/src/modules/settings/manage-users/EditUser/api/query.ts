import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import adminApi from '@fleet-web/api/admin'
import type { UserPermissionsDataType } from '@fleet-web/api/admin/types'
import { makeQueryErrorHandlerWithToast } from '@fleet-web/api/helpers'
import type { ClientUserId } from '@fleet-web/api/types'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { ctToast } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

/****************************************Fetch user's permissions******************************************/
async function fetchUserPermission(userId: ClientUserId) {
  if (!userId) {
    return Promise.reject(new Error('Empty user id!'))
  }
  const drivers = await adminApi.fetchClientPermissions(userId)
  return drivers
}

export const fetchUserPermissionQuery = (userId: ClientUserId) =>
  createQuery({
    queryKey: ['manageUsers/fetchUserPermission', userId] as const,
    queryFn: async () => fetchUserPermission(userId),
    gcTime: 0, // Since the changes in roles permissions affect the users (under that role) permissions, we don't want to use the cached data because it might be outdated
    ...makeQueryErrorHandlerWithToast(),
  })

export const useFetchUserPermission = (userId: ClientUserId) =>
  useQuery(fetchUserPermissionQuery(userId))

/****************************************Update client's data permissions******************************************/
export const useUpdateUserPermissionMutation = (
  userId: ClientUserId,
  onSuccess?: () => void,
) => {
  const queryClient = useQueryClient()
  const fetchUserPermissionQueryForUserId = fetchUserPermissionQuery(userId)

  return useMutation({
    onMutate: async (variables) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries(fetchUserPermissionQueryForUserId)

      const previousUserPermissions =
        fetchUserPermissionQueryForUserId.getData(queryClient)

      if (previousUserPermissions) {
        fetchUserPermissionQueryForUserId.setData(queryClient, {
          updater: {
            ...previousUserPermissions,
            ...variables.permissions,
          },
        })
      }

      return { previousUserPermissions, fetchUserPermissionQueryForUserId }
    },
    mutationFn: async ({
      permissions,
    }: {
      permissions: Partial<UserPermissionsDataType>
    }) => {
      await adminApi.updateUserClientPermissions(userId, permissions)
    },
    onSuccess: () => {
      if (onSuccess) onSuccess()

      queryClient.invalidateQueries(fetchUserPermissionQueryForUserId)
    },
    onError: (_, _vars, context) => {
      if (context?.previousUserPermissions) {
        context.fetchUserPermissionQueryForUserId.setData(queryClient, {
          updater: context.previousUserPermissions,
        })
      }
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Update Failed. Please try again' }),
        { variant: 'error' },
      )
    },
    onSettled: (_, _error, _vars, context) => {
      if (context) {
        queryClient.invalidateQueries(context.fetchUserPermissionQueryForUserId)
      }
    },
  })
}

/****************************************Update admin's data permissions******************************************/
export const useUpdateAdminPermissionMutation = (
  userId: ClientUserId,
  { onSuccess }: { onSuccess?: () => void } = {},
) => {
  const queryClient = useQueryClient()

  return useMutation({
    onMutate: async (variables) => {
      const fetchUserPermissionQueryForUserId = fetchUserPermissionQuery(userId)

      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries(fetchUserPermissionQueryForUserId)

      const previousUserPermissions =
        fetchUserPermissionQueryForUserId.getData(queryClient)

      if (previousUserPermissions) {
        fetchUserPermissionQueryForUserId.setData(queryClient, {
          updater: {
            ...previousUserPermissions,
            ...variables.permissions,
          },
        })
      }

      return { previousUserPermissions, fetchUserPermissionQueryForUserId }
    },
    mutationFn: async ({
      permissions,
    }: {
      permissions: Partial<UserPermissionsDataType>
    }) => {
      await adminApi.updateUserAdminPermissions(userId, permissions)
    },
    onSuccess: () => {
      if (onSuccess) onSuccess()

      ctToast.fire('success', `Update successful!`)
    },
    onError: (_, _vars, context) => {
      if (context?.previousUserPermissions) {
        context.fetchUserPermissionQueryForUserId.setData(queryClient, {
          updater: context.previousUserPermissions,
        })
      }
      ctToast.fire('error', `Update Failed. Please try again`)
    },
    onSettled: (_, _error, _vars, context) => {
      if (context) {
        queryClient.invalidateQueries(context.fetchUserPermissionQueryForUserId)
      }
    },
  })
}
