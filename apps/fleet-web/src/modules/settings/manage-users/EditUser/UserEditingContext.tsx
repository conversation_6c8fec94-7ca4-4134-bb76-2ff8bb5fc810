import { createContext, useContext, type ComponentProps } from 'react'

import type { ClientUserId } from '@fleet-web/api/types'

type UserContext = {
  userId: ClientUserId | null
  editType: 'user' | 'role'
}

const UserEditingContext = createContext<UserContext>({
  userId: null,
  editType: 'user',
})

export function UserEditingContextProvider(
  props: ComponentProps<typeof UserEditingContext.Provider>,
) {
  return <UserEditingContext.Provider {...props} />
}

export function useUserEditingContext() {
  const context = useContext(UserEditingContext)
  if (context === null || context.userId === null) {
    throw new Error(
      'useUserEditingContext must be used within a UserEditingContextProvider',
    )
  }
  return { userId: context.userId, editType: context.editType }
}
