import { useMemo } from 'react'

import type { VehicleId } from '@fleet-web/api/types'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { getActivatedVehicles } from '@fleet-web/duxs/vehicles'
import { useUpdateUserMutation } from '@fleet-web/modules/settings/manage-users/api/queries'
import { useUserEditingContext } from '@fleet-web/modules/settings/manage-users/EditUser/UserEditingContext'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DataAccess from '../../../admin/shared/data-access'
import { useFleetUserOrRoleVehicles } from '../../../admin/shared/data-access/vehicles/queries'

const DataAccessUser = () => {
  const { userId } = useUserEditingContext()
  const availableActiveVehicles = useTypedSelector(getActivatedVehicles)

  const fleetUserOrRoleVehicles = useFleetUserOrRoleVehicles({ data: userId })

  const updateUserMutation = useUpdateUserMutation()

  const visibleActiveVehicles = useMemo(() => {
    if (fleetUserOrRoleVehicles.data && availableActiveVehicles) {
      return {
        vehicles: availableActiveVehicles.filter((item) => item.subUserVisibility),
        selectedVehicles: fleetUserOrRoleVehicles.data,
      }
    }
    return { vehicles: [], selectedVehicles: [] }
  }, [availableActiveVehicles, fleetUserOrRoleVehicles.data])

  const handleVehicleAccessChange = (newRowSelectionModel: ReadonlySet<VehicleId>) => {
    updateUserMutation.mutate(
      {
        client_user_id: userId,
        general: null,
        profile: null,
        base_features: null,
        vehicles: {
          all: newRowSelectionModel.size === visibleActiveVehicles.vehicles.length,
          group: Array.from(newRowSelectionModel),
        },
      },
      {
        onSuccess: () => {
          enqueueSnackbarWithCloseAction(
            ctIntl.formatMessage({
              id: 'settings.manageUsers.editUser.vehicleAccess.success',
            }),
            { variant: 'success' },
          )
        },
      },
    )
  }

  return (
    <DataAccess
      isVehicleAccessMutationPending={updateUserMutation.status === 'pending'}
      isVehicleAccessMutationSuccess={updateUserMutation.isSuccess}
      onVehicleAccessChange={handleVehicleAccessChange}
    />
  )
}

export default DataAccessUser
