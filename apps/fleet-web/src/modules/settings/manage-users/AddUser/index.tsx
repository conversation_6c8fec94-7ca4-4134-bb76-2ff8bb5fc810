import { useMemo, useState } from 'react'
import { Box, Button, Dialog, IconButton, Stack, Typography } from '@karoo-ui/core'
import { useControlledForm } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import { getCountryCallingCode, type Country } from 'react-phone-number-input/input'
import { useDispatch } from 'react-redux'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { actions } from '@fleet-web/duxs/admin'
import { getISO3166Alpha2CountryCode } from '@fleet-web/duxs/user'
import { doesCurrentUserHaveAccessFromSetting } from '@fleet-web/duxs/user-sensitive-selectors'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useCreateUserMutation, useFetchUserInfoQuery } from '../api/queries'
import UserForm from '../shared/UserForm'
import {
  getFleetUserSchema,
  getFleetUserWithCostsSchema,
  type UserProfileSchema,
} from '../shared/utils'

type Props = {
  navigateToDetailsPage: (id: string) => void
}

const { fetchUsers } = actions

const AddUser = ({ navigateToDetailsPage }: Props) => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  const closeModal = () => {
    setIsModalVisible(false)
  }

  return (
    <>
      <Button
        variant="contained"
        className="ManageUsers-addUser"
        onClick={() => setIsModalVisible(true)}
        startIcon={<AddIcon />}
      >
        {ctIntl.formatMessage({ id: 'Add User' })}
      </Button>
      {isModalVisible && (
        <AddUserDialog
          closeModal={closeModal}
          navigateToDetailsPage={navigateToDetailsPage}
        />
      )}
    </>
  )
}

const AddUserDialog = ({
  closeModal,
  navigateToDetailsPage,
}: { closeModal: () => void } & Props) => {
  const dispatch = useDispatch()
  const ISO3166Alpha2CountryCode = useTypedSelector(getISO3166Alpha2CountryCode)

  const hasCosts = useTypedSelector((state) =>
    doesCurrentUserHaveAccessFromSetting(state, 'costs'),
  )

  const fetchUserInfoQuery = useFetchUserInfoQuery({ client_user_id: null })

  const createUserMutation = useCreateUserMutation()

  const formValues = useMemo(() => {
    const initialValues = {
      username: '',
      email: '',
      cellPhone: {
        countryCode: ISO3166Alpha2CountryCode || null,
        number: null,
      },
      departmentId: null,
      clientUserRoleId: '',
    }
    if (hasCosts) {
      return {
        ...initialValues,
        name: '',
        languageId: '',
        parentUserId: '',
        user_role_id: [],
      }
    }
    return initialValues
  }, [ISO3166Alpha2CountryCode, hasCosts])

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useControlledForm<UserProfileSchema>({
    resolver: zodResolverV4(
      hasCosts ? getFleetUserWithCostsSchema() : getFleetUserSchema(),
    ),
    mode: 'onChange',
    defaultValues: formValues,
  })

  const onSubmit = handleSubmit((data: UserProfileSchema) => {
    createUserMutation.mutate(
      {
        user_name: data.username,
        cell_no:
          data.cellPhone.countryCode && data.cellPhone.number
            ? `+${getCountryCallingCode(data.cellPhone.countryCode as Country)}${
                data.cellPhone.number
              }`
            : '',
        email_address: data.email,
        department_id: data.departmentId,
        client_user_role_id: data.clientUserRoleId,
        parent_id: data.parentUserId,
        name: data.name,
        user_role_id: data.user_role_id,
      },
      {
        onSuccess: (res) => {
          navigateToDetailsPage(res.client_user_id)
          dispatch(fetchUsers())
          closeModal()
        },
      },
    )
  })

  return (
    <Dialog
      open={true}
      onClose={closeModal}
      sx={{
        '& .MuiDialog-paper': {
          minWidth: '800px',
        },
      }}
    >
      <Stack sx={{ height: '100%', overflow: 'hidden' }}>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            px: 3,
            pt: 3,
          }}
        >
          <Typography variant="h6">
            {ctIntl.formatMessage({ id: 'Add new user' })}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
          >
            <IconButton onClick={closeModal}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Stack>
        <Box sx={{ px: 3, pb: 2 }}>
          <UserForm
            isEditing={true}
            control={control}
            userRoles={fetchUserInfoQuery.data?.userRoles ?? []}
            clientUsers={fetchUserInfoQuery.data?.clientUsers ?? []}
          />
        </Box>
      </Stack>
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          px: 3,
          py: 2,
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
        }}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={closeModal}
        >
          {ctIntl.formatMessage({ id: 'Cancel' })}
        </Button>
        <Button
          size="small"
          disabled={!isValid}
          variant="contained"
          onClick={onSubmit}
          loading={createUserMutation.status === 'pending'}
        >
          {ctIntl.formatMessage({ id: 'Add User' })}
        </Button>
      </Stack>
    </Dialog>
  )
}

export default AddUser
