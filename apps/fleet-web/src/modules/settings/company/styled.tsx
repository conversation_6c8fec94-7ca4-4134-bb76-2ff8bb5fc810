import { Box, Stack, styled } from '@karoo-ui/core'

export const SectionContainer = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    backgroundColor: 'white',
    padding: 2,
  }),
)

export const SectionDetail = styled(Box)(() => ({
  paddingTop: '16px',
  display: 'grid',
  gridTemplateColumns: `repeat(2, minmax(300px, 1fr))`,
  gridAutoRows: `min-content`,
  gridColumnGap: '22px',
  gridRowGap: '18px',
  width: '100%',

  '.Mui-disabled:before': {
    borderBottomStyle: 'solid !important',
  },
}))
