import { useMemo, useState } from 'react'
import { Box, Tab, Tabs } from '@karoo-ui/core'
import { useDispatch } from 'react-redux'
import { match } from 'ts-pattern'

import { useIndustrySelectionMetaData } from '@fleet-web/api/industry-selection/queries'
import { getIndustriesDropdownOptions } from '@fleet-web/api/industry-selection/utils'
import PageHeader from '@fleet-web/components/_containers/PageHeader'
import PageWithMainTableContainer from '@fleet-web/components/_containers/PageWithMainTable'
import { getSettings_UNSAFE, handleSaveProfileSuccess } from '@fleet-web/duxs/user'
import { doesCurrentUserHaveAccessFromSetting } from '@fleet-web/duxs/user-sensitive-selectors'
import { DepartmentContainer as UnitSection } from '@fleet-web/modules/carpool/Settings/BookingPermissions/Departments/EditDepartmentPermissionsModal'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { AppState } from '@fleet-web/root-reducer'
import { Spinner } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type {
  CompanyFetchProfileSettings,
  CompanyFetchVehicleSettingsOptions,
} from './api/types'
import useCompanyProfileSettingsQuery from './api/useCompanyProfileSettingsQuery'
import useCompanyVehicleSettingsOptionsQuery from './api/useCompanyVehicleSettingsOptionsQuery'
import useUpdateCompanyProfileSettingsMutation from './api/useUpdateCompanyProfileSettingsMutation'
import FleetSection, { type FleetSchema } from './components/FleetSection'
import FormSection from './components/FormSection'
import OrganizationInfoSection, {
  type OrganizationSchema,
} from './components/OrganizationInfoSection'
import PreferenceSection, {
  type PreferenceSchema,
} from './components/PreferenceSection'
import type { CustomDataSchema } from './components/schema'

export const sections = {
  organizationInfo: {
    label: 'Organization Info',
    id: 'organization-info',
  },
  preferences: {
    label: 'Preferences',
    id: 'preferences',
  },
  vehicles: {
    self: { label: 'Vehicle custom fields', id: 'vehicles' },
    sub: {
      vehiclesCustomFields: {
        label: 'settings.company.vehicles.customFieldsDescription',
        id: 'vehicles-custom-fields',
        selector: (state: AppState) =>
          doesCurrentUserHaveAccessFromSetting(state, 'vehicleCustomDataEntity'),
      },
    },
  },
  drivers: {
    self: {
      label: 'Driver custom fields',
      id: 'drivers',
      selector: (state: AppState) =>
        doesCurrentUserHaveAccessFromSetting(state, 'driverCustomDataEntity'),
    },
    sub: {
      driversCustomFields: {
        label: 'settings.company.drivers.customFieldsDescription',
        id: 'drivers-custom-fields',
        selector: (state: AppState) =>
          doesCurrentUserHaveAccessFromSetting(state, 'driverCustomDataEntity'),
      },
    },
  },
  units: {
    label: 'Departments',
    id: 'departments',
  },
  fleet: { label: 'Fleet', id: 'fleet' },
}

const companyTabs = [
  { value: 0, label: 'General Info' },
  { value: 1, label: 'Departments' },
  { value: 2, label: 'Custom Fields' },
] as const

type CompanyTabsIndex = (typeof companyTabs)[number]['value']

const Company = () => {
  const [tab, setTab] = useState<CompanyTabsIndex>(0)
  const query = useCompanyProfileSettingsQuery()
  const companyVehicleSettingsOptionsQuery = useCompanyVehicleSettingsOptionsQuery()

  const changeTab = (_: React.SyntheticEvent, newValue: CompanyTabsIndex) => {
    setTab(newValue)
  }

  return (
    <PageWithMainTableContainer>
      <PageHeader>
        <PageHeader.Title>{ctIntl.formatMessage({ id: 'Company' })}</PageHeader.Title>
      </PageHeader>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={tab}
          onChange={changeTab}
          aria-label="basic tabs example"
        >
          {companyTabs.map((tab) => (
            <Tab
              key={tab.label}
              label={ctIntl.formatMessage({ id: tab.label })}
              value={tab.value}
            />
          ))}
        </Tabs>
      </Box>
      {match([query, companyVehicleSettingsOptionsQuery])
        .with(
          [{ status: 'success' }, { status: 'success' }],
          ([queryData, companyVehicleSettingsOptionsData]) => (
            <FormContent
              tab={tab}
              data={{
                queryData: queryData.data,
                companyVehicleSettingsOptionsData:
                  companyVehicleSettingsOptionsData.data,
              }}
            />
          ),
        )
        .otherwise(() => (
          <Spinner />
        ))}
    </PageWithMainTableContainer>
  )
}

const FormContent = ({
  tab,
  data: { queryData, companyVehicleSettingsOptionsData },
}: {
  tab: CompanyTabsIndex
  data: {
    queryData: CompanyFetchProfileSettings.Return
    companyVehicleSettingsOptionsData: CompanyFetchVehicleSettingsOptions.Return
  }
}) => {
  const initialValues = queryData

  const initialFormValues = useMemo(
    () => ({
      name: initialValues.name,
      email: initialValues.email,
      telephoneNumber: initialValues.telephoneNumber,
      faxNumber: initialValues.faxNumber,
      mobileNumber: initialValues.mobileNumber,
      industryId: initialValues.industryId,
      roadSpeedTolerance: initialValues.roadSpeedTolerance,
      vehicleEngineTypeId: initialValues.vehicleEngineTypeId,
      vehicleFuelTypeId: initialValues.vehicleFuelTypeId,
      vehicleConsumptionAverageCombined:
        initialValues.vehicleConsumptionAverageCombined,
      vehicleConsumptionTargetCombined: initialValues.vehicleConsumptionTargetCombined,
      vehicleCustomData: initialValues.vehicleCustomData,
      driverCustomData: initialValues.driverCustomData,
    }),
    [initialValues],
  )

  const unit = companyVehicleSettingsOptionsData.consumptionUnits.find(
    (u) =>
      u.vehicleEngineTypeId === initialFormValues.vehicleEngineTypeId &&
      u.vehicleFuelTypeId === initialFormValues.vehicleFuelTypeId,
  )

  const dispatch = useDispatch()

  const settings = useTypedSelector(getSettings_UNSAFE)

  const mutation = useUpdateCompanyProfileSettingsMutation({
    onSuccess() {
      dispatch(handleSaveProfileSuccess())
    },
  })

  const isLoading = mutation.status === 'pending'

  const industrySelectionMetaDataQuery = useIndustrySelectionMetaData()
  const industriesOptions = useMemo(
    () =>
      industrySelectionMetaDataQuery.data?.industries
        ? getIndustriesDropdownOptions(industrySelectionMetaDataQuery.data.industries)
        : [],
    [industrySelectionMetaDataQuery.data],
  )

  const handleSubmit = (
    values: OrganizationSchema | PreferenceSchema | FleetSchema | CustomDataSchema,
  ) => {
    mutation.mutate({
      ...initialFormValues,
      ...values,
    })
  }

  return match(tab)
    .with(0, () => (
      <>
        <OrganizationInfoSection
          onSubmit={handleSubmit}
          industriesOptions={industriesOptions}
          isLoading={isLoading}
          initialValues={initialFormValues}
        />
        <PreferenceSection
          onSubmit={handleSubmit}
          isLoading={isLoading}
          initialValues={initialFormValues}
        />
        <FleetSection
          onSubmit={handleSubmit}
          initialValues={initialFormValues}
          isLoading={isLoading}
          unitConfig={
            unit
              ? {
                  isSanctionedSimpleUnitIdentifier:
                    unit.isSanctionedSimpleUnitIdentifier,
                  unit: unit.consumptionUnit,
                }
              : null
          }
          vehicleEngineTypes={companyVehicleSettingsOptionsData.vehicleEngineTypes}
          vehicleFuelTypes={companyVehicleSettingsOptionsData.vehicleFuelTypes}
        />
      </>
    ))
    .with(1, () => (
      <Box sx={{ height: '100%', overflow: 'hidden', mt: -1, mb: -5 }}>
        <UnitSection hasTitle={false} />
      </Box>
    ))
    .with(2, () => (
      <>
        <FormSection
          hasCustomFields={!!settings.vehicleCustomDataEntity}
          maxCustomDataIndices={initialValues.maxCustomDataIndices}
          onSubmit={handleSubmit}
          initialValues={initialFormValues}
          isLoading={isLoading}
          info={{
            title: sections.vehicles.self.label,
            subtitle: sections.vehicles.sub.vehiclesCustomFields.label,
            formField: 'vehicleCustomData',
          }}
        />
        <FormSection
          hasCustomFields={!!settings.driverCustomDataEntity}
          maxCustomDataIndices={initialValues.maxCustomDataIndices}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          initialValues={initialFormValues}
          info={{
            title: sections.drivers.self.label,
            subtitle: sections.drivers.sub.driversCustomFields.label,
            formField: 'driverCustomData',
          }}
        />
      </>
    ))
    .exhaustive()
}
export default Company
