import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, IconButton, Stack, Typography } from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import { useHistory } from 'react-router-dom'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import { SETTINGS } from '@fleet-web/modules/app/components/routes/settings'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { getRoleSchema, rolesModalParamsSchema, type RoleSchema } from '../shared/utils'
import { useAddRoleMutation } from './useAddRoleMutation'

const AddRole = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  const closeModal = () => {
    setIsModalVisible(false)
  }

  return (
    <>
      <Button
        variant="contained"
        className="ManageRoles-addUser"
        onClick={() => setIsModalVisible(true)}
        startIcon={<AddIcon />}
      >
        {ctIntl.formatMessage({ id: 'Add Role' })}
      </Button>
      {isModalVisible && <AddRoleDialog closeModal={closeModal} />}
    </>
  )
}

const AddRoleDialog = ({ closeModal }: { closeModal: () => void }) => {
  const history = useHistory()
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useControlledForm<RoleSchema>({
    resolver: zodResolverV4(getRoleSchema()),
    mode: 'onChange',
    defaultValues: {
      roleName: '',
    },
  })

  const { mutate, status } = useAddRoleMutation()

  const submit = handleSubmit((data) => {
    mutate(data, {
      onSuccess: (res) => {
        closeModal()
        if ('success' in res) {
          history.push(
            `${
              SETTINGS.subMenusRoutes.MANAGE_ROLES.path
            }?${buildRouteQueryStringKeepingExistingSearchParams({
              location: history.location,
              schema: rolesModalParamsSchema,
              searchParams: {
                rolesModal: {
                  type: 'profile',
                  id: res.ct_fleet_client_user_info.client_user_id,
                },
              },
              options: { shouldJsonStringify: true },
            })}`,
          )
        }
      },
    })
  })

  return (
    <Dialog
      open={true}
      onClose={closeModal}
      sx={{
        '& .MuiDialog-paper': {
          width: '85%',
        },
      }}
    >
      <Stack sx={{ height: '100%', overflow: 'hidden' }}>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            px: 3,
            pt: 3,
            pb: 2,
          }}
        >
          <Typography variant="h6">
            {ctIntl.formatMessage({ id: 'Add new user role' })}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
          >
            <IconButton onClick={closeModal}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Stack>
        <Stack sx={{ p: 2 }}>
          <TextFieldControlled
            ControllerProps={{
              control,
              name: 'roleName',
            }}
            required
            label={ctIntl.formatMessage({ id: 'Role name' })}
            data-testid="NewRole-RoleNameInput"
          />
        </Stack>
        <Stack
          sx={{
            justifyContent: 'end',
            p: 3,
            borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <Button
              data-testid="NewRole-CancelButton"
              variant="outlined"
              color="secondary"
              onClick={closeModal}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              data-testid="NewRole-AddButton"
              variant="contained"
              type="submit"
              disabled={!isValid}
              loading={status === 'pending'}
              onClick={submit}
            >
              {ctIntl.formatMessage({ id: 'Add user role' })}
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </Dialog>
  )
}

export default AddRole
