import { useMutation, useQueryClient } from '@tanstack/react-query'
import { match } from 'ts-pattern'

import { makeMutationErrorHandlerWithSnackbar } from '@fleet-web/api/helpers'
import roleApi from '@fleet-web/api/role'
import type { CreateOrUpdateRoleHandledError } from '@fleet-web/api/role/types'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { rolesQuery } from '../api/queries'

export function useAddRoleMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ roleName }: { roleName: string }) =>
      roleApi.createNewRole({ roleName }),
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler(error) {
        const apiError = error as CreateOrUpdateRoleHandledError
        const errorMessage = match(apiError.roleWithPropertiesAlreadyExists)
          .with(['name'], () => 'Role name already exists')
          .otherwise(() => 'Error creating role')
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: errorMessage,
          }),
          { variant: 'error' },
        )
      },
    }),
    onSuccess: () => {
      queryClient.invalidateQueries(rolesQuery())
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'User role created successfully',
        }),
        {
          variant: 'success',
        },
      )
    },
  })
}
