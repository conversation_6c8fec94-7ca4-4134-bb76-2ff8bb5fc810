import { useMemo, useState } from 'react'
import {
  Box,
  Button,
  CircularProgressDelayedAbsolute,
  Stack,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { match } from 'ts-pattern'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useUserEditingContext } from '../../manage-users/EditUser/UserEditingContext'
import {
  useEditRoleInfoMutation,
  useFetchRoleInfo,
  type RoleInfo,
} from '../api/queries'
import { getRoleSchema, type RoleSchema } from '../shared/utils'

const RoleNameForm = ({ data }: { data: RoleInfo.Return }) => {
  const { userId } = useUserEditingContext()
  const editRoleMutate = useEditRoleInfoMutation()

  const [isEditing, setIsEditing] = useState(false)

  const formValues = useMemo(
    () => ({
      roleName: data.roleName,
    }),
    [data],
  )

  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid, isDirty },
  } = useControlledForm<RoleSchema>({
    resolver: zodResolverV4(getRoleSchema()),
    mode: 'onChange',
    defaultValues: formValues,
  })

  const submit = handleSubmit((data) => {
    editRoleMutate.mutate(
      { userId, profile: data },
      {
        onSuccess: () => {
          setIsEditing(false)
        },
      },
    )
  })

  return (
    <Stack
      sx={{
        backgroundColor: 'white',
        p: 2,
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
      >
        <Typography variant="subtitle2">
          {ctIntl.formatMessage({ id: 'Role Profile' })}
        </Typography>
        <Stack
          direction="row"
          gap={1}
        >
          {isEditing ? (
            <>
              <Button
                size="small"
                variant="outlined"
                color="secondary"
                onClick={() => {
                  setIsEditing(false)
                  reset()
                }}
              >
                {ctIntl.formatMessage({ id: 'Cancel' })}
              </Button>
              <Button
                size="small"
                disabled={!isValid || !isDirty}
                variant="contained"
                onClick={submit}
                loading={editRoleMutate.isPending}
              >
                {ctIntl.formatMessage({ id: 'Save' })}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              variant="outlined"
              size="small"
              startIcon={<EditOutlinedIcon />}
            >
              {ctIntl.formatMessage({ id: 'Edit' })}
            </Button>
          )}
        </Stack>
      </Stack>
      <Box>
        <TextFieldControlled
          ControllerProps={{
            control,
            name: 'roleName',
          }}
          required
          disabled={!isEditing}
          label={ctIntl.formatMessage({ id: 'Role name' })}
          data-testid="EditRole-RoleNameInput"
        />
      </Box>
    </Stack>
  )
}

const RoleName = () => {
  const { userId } = useUserEditingContext()
  const roleInfo = useFetchRoleInfo({ client_user_id: userId })

  return match(roleInfo)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data }) => <RoleNameForm data={data} />)
    .exhaustive()
}

export default RoleName
