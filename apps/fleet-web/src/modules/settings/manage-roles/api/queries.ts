import { useCallback } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { match } from 'ts-pattern'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import {
  makeMutationErrorHandlerWithSnackbar,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import roleApi from '@fleet-web/api/role'
import type {
  CreateOrUpdateRoleHandledError,
  FetchRoles,
} from '@fleet-web/api/role/types'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { minutesToMs } from '@fleet-web/util-functions/functional-utils'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

export const rolesQuery = () =>
  createQuery({
    queryKey: ['manage-roles/all'] as const,
    queryFn: roleApi.fetchRoles,
    staleTime: minutesToMs(15),
    refetchOnMount: 'always', // because redux-saga change it so need to reload
    ...makeQueryErrorHandlerWithToast(),
  })

export function useRolesQuery() {
  return useQuery(rolesQuery())
}

export const useRoleOptionsQuery = () =>
  useQuery({
    ...rolesQuery(),
    select: useCallback(
      (data: FetchRoles.Return) =>
        data.map((r) => ({
          label: r.username,
          value: r.id,
        })),
      [],
    ),
  })

export declare namespace RoleInfo {
  type ApiInput = {
    client_user_id: string
  }

  type ApiOutput = {
    ct_fleet_client_user_info: {
      user_id: string
      client_user_id: string
      user_name: string
      cell_number: string | null
      e_mail: string | null
      is_locked: 'f' | 't'
      client_user_statuses: string
      shared_reminders_allowed: 'f' | 't'
      buy_sms_allowed: 'f' | 't'
      allow_subuser_tosee_mainuser_geofences: 'f' | 't'
      allow_subuser_toedit_mainuser_geofences: 'f' | 't'
      allow_mainuser_tosee_subuser_geofences: 'f' | 't'
      allow_mainuser_toedit_subuser_geofences: 'f' | 't'
      allow_subuser_tosee_mainuser_poi: 'f' | 't'
      allow_subuser_toedit_mainuser_poi: 'f' | 't'
      allow_mainuser_tosee_subuser_poi: 'f' | 't'
      allow_mainuser_toedit_subuser_poi: 'f' | 't'
      department_id: string | null
    }
  }

  type Return = {
    userId: string
    roleName: string
    clientUserId: string
  }
}

export const useFetchRoleInfo = (params: RoleInfo.ApiInput) =>
  useQuery(fetchRoleInfoQuery(params))

const fetchRoleInfoKey = (params: RoleInfo.ApiInput) =>
  ['manage-roles/role-info', params] as const

const fetchRoleInfoQuery = (params: RoleInfo.ApiInput) =>
  createQuery({
    queryKey: fetchRoleInfoKey(params),
    queryFn: async () => fetchRoleInfo(params),
  })

async function fetchRoleInfo(params: RoleInfo.ApiInput) {
  return apiCallerNoX<RoleInfo.ApiOutput>('ct_fleet_client_role_info', params).then(
    (res) => parseRoleInfo(res),
  )
}

const parseRoleInfo = (res: RoleInfo.ApiOutput): RoleInfo.Return => {
  const data = res.ct_fleet_client_user_info
  return {
    userId: data.user_id,
    roleName: data.user_name,
    clientUserId: data.client_user_id,
  }
}

export const useEditRoleInfoMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      userId,
      profile,
    }: {
      userId: string
      profile: { roleName: string }
    }) => roleApi.updateRole(userId, { profile }),
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler(error) {
        const apiError = error as CreateOrUpdateRoleHandledError
        const errorMessage = match(apiError.roleWithPropertiesAlreadyExists)
          .with(['name'], () => 'updateOrCreateUserRole.error.nameAlreadyTaken')
          .otherwise(() => 'We are experiencing a technical error.')
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: errorMessage,
          }),
          { variant: 'error' },
        )
      },
    }),
    onSuccess: () => {
      queryClient.invalidateQueries()
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'User role updated successfully',
        }),
        {
          variant: 'success',
        },
      )
    },
  })
}
