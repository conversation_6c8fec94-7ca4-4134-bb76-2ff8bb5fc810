import { z } from 'zod'

import { generateStringSchema } from '@fleet-web/util-functions/zod-utils'

export const getRoleSchema = () =>
  z.object({
    roleName: generateStringSchema({
      isRequired: true,
      maxLength: 50,
    }),
  })

export type RoleSchema = z.infer<ReturnType<typeof getRoleSchema>>

export const rolesModalParamsSchema = z.object({
  rolesModal: z
    .discriminatedUnion('type', [
      z.object({
        type: z.literal('profile'),
        id: z.string(),
      }),
      z.object({
        type: z.literal('permissions'),
        id: z.string(),
      }),
      z.object({
        type: z.literal('userDataAccess'),
        id: z.string(),
      }),
      z.object({
        type: z.literal('userDataSharing'),
        id: z.string(),
      }),
    ])
    .optional(),
})
