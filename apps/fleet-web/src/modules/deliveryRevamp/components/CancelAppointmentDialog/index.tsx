import { useState } from 'react'
import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
} from '@karoo-ui/core'
import type { Location } from 'history'

import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import useAppointmentCancellationReasonsQuery from '../../api/appointment/useAppointmentCancellationReasonsQuery'
import useCancelAppointmentMutation from '../../api/appointment/useCancelAppointmentMutation'
import { globalCancelAppointmentDialogModalSearchParamsSchema } from '../GlobalModals/schema'
import type { CancelAppointmentParams } from './type'

type CancelAppointmentConfirmDialogProps = {
  onClose: () => void
  appointmentId: number
}

export const getDeliveryCancelAppointmentDialogMainPath = (
  location: Location,
  params: CancelAppointmentParams,
) =>
  `${location.pathname}?${buildRouteQueryStringKeepingExistingSearchParams({
    location,
    schema: globalCancelAppointmentDialogModalSearchParamsSchema,
    searchParams: {
      deliveryGlobalModal: { modal: 'cancelAppointment', params },
    },
    options: { shouldJsonStringify: true },
  })}`

const CancelAppointmentConfirmDialog = ({
  onClose,
  appointmentId,
}: CancelAppointmentConfirmDialogProps) => {
  const [selectedReasonId, setSelectedReasonId] = useState('')
  const [hasSubmitAttempt, setHasSubmitAttempt] = useState(false)

  const cancelAppointmentMutation = useCancelAppointmentMutation()
  const cancellationReasonsQuery = useAppointmentCancellationReasonsQuery()

  const handleReasonChange = (event: SelectChangeEvent<string>) => {
    setSelectedReasonId(event.target.value)
  }

  const handleCancel = () => {
    if (!appointmentId) return

    setHasSubmitAttempt(true)

    if (!selectedReasonId) {
      return
    }

    cancelAppointmentMutation.mutate(
      {
        appointmentId,
        appointmentCancelReasonId: selectedReasonId,
      },
      {
        onSettled: () => {
          onClose()
        },
      },
    )
  }

  const isError = hasSubmitAttempt && !selectedReasonId

  return (
    <Dialog
      open={true}
      onClose={onClose}
      PaperProps={{ sx: { width: 550, height: 250 } }}
    >
      <DialogTitle>{ctIntl.formatMessage({ id: 'Cancel Appointment' })}</DialogTitle>
      <DialogContent>
        <DialogContentText>
          {ctIntl.formatMessage({
            id: 'Are you sure you want to cancel this appointment?',
          })}
        </DialogContentText>

        <FormControl
          required
          fullWidth
          margin="normal"
          error={isError}
        >
          <InputLabel>
            {ctIntl.formatMessage({ id: 'Appointment Cancellation Reason' })}
          </InputLabel>
          <Select
            value={selectedReasonId}
            onChange={handleReasonChange}
            label={ctIntl.formatMessage({ id: 'Appointment Cancellation Reason' })}
            disabled={
              cancelAppointmentMutation.isPending || cancellationReasonsQuery.isLoading
            }
          >
            {cancellationReasonsQuery.isLoading ? (
              <MenuItem
                value=""
                disabled
              >
                {ctIntl.formatMessage({ id: 'Loading...' })}
              </MenuItem>
            ) : (
              cancellationReasonsQuery.data?.map((reason) => (
                <MenuItem
                  key={reason.appointment_cancel_reason_id}
                  value={reason.appointment_cancel_reason_id}
                >
                  {reason.cancel_reason_description}
                </MenuItem>
              ))
            )}
          </Select>
          {isError && (
            <FormHelperText>
              {ctIntl.formatMessage({ id: 'Please select a cancellation reason' })}
            </FormHelperText>
          )}
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          disabled={cancelAppointmentMutation.isPending}
        >
          {ctIntl.formatMessage({ id: 'No' })}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleCancel}
          disabled={
            cancelAppointmentMutation.isPending ||
            !appointmentId ||
            cancellationReasonsQuery.isLoading ||
            !cancellationReasonsQuery.data?.length
          }
          startIcon={
            cancelAppointmentMutation.isPending ? (
              <CircularProgress
                size={16}
                color="inherit"
              />
            ) : null
          }
        >
          {ctIntl.formatMessage({ id: 'Yes' })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default CancelAppointmentConfirmDialog
