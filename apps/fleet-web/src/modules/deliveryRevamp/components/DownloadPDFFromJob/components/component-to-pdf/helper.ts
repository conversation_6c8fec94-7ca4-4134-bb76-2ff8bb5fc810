import { Font } from '@react-pdf/renderer'

import type { CartrackLocale } from '@fleet-web/api/user/types'
import type { FixMeAny } from '@fleet-web/types'

import ArimoBoldItalic from 'assets/delivery/fonts/Arimo/bold-italic.woff'
import ArimoBold from 'assets/delivery/fonts/Arimo/bold.woff'
import ArimoMediumItalic from 'assets/delivery/fonts/Arimo/medium-italic.woff'
import ArimoMedium from 'assets/delivery/fonts/Arimo/medium.woff'
import ArimoRegularItalic from 'assets/delivery/fonts/Arimo/regular-italic.woff'
//Default
import ArimoRegular from 'assets/delivery/fonts/Arimo/regular.woff'
import NotoSansARBold from 'assets/delivery/fonts/NotoSans-AR/bold.woff'
import NotoSansARMedium from 'assets/delivery/fonts/NotoSans-AR/medium.woff'
//AR
import NotoSansARRegular from 'assets/delivery/fonts/NotoSans-AR/regular.woff'
import NotoSansSCBold from 'assets/delivery/fonts/NotoSans-SC/bold.woff'
import NotoSansSCMedium from 'assets/delivery/fonts/NotoSans-SC/medium.woff'
//Chinese  Simplified
import NotoSansSCRegular from 'assets/delivery/fonts/NotoSans-SC/regular.woff'
import NotoSansTCBold from 'assets/delivery/fonts/NotoSans-TC/bold.woff'
import NotoSansTCMedium from 'assets/delivery/fonts/NotoSans-TC/medium.woff'
//Chinese Traditional
import NotoSansTCRegular from 'assets/delivery/fonts/NotoSans-TC/regular.woff'
import NotoSansTHBold from 'assets/delivery/fonts/NotoSans-TH/bold.woff'
import NotoSansTHMedium from 'assets/delivery/fonts/NotoSans-TH/medium.woff'
//TH
import NotoSansTHRegular from 'assets/delivery/fonts/NotoSans-TH/regular.woff'

//types
import type { PODLayout } from '../JobReportModal'

const PDF_REPORT_LOCALES_COUNTRY = ['th', 'me', 'ae'] as const

const handleFontSelection = (locale: CartrackLocale | null | undefined) => {
  switch (locale) {
    case 'th': {
      return handleFontRegistration({
        regular: NotoSansTHRegular,
        medium: NotoSansTHMedium,
        bold: NotoSansTHBold,
        regularItalic: NotoSansTHRegular,
        mediumItalic: NotoSansTHMedium,
        boldItalic: NotoSansTHBold,
      })
    }
    case 'ar': {
      return handleFontRegistration({
        regular: NotoSansARRegular,
        medium: NotoSansARMedium,
        bold: NotoSansARBold,
        regularItalic: NotoSansARRegular,
        mediumItalic: NotoSansARMedium,
        boldItalic: NotoSansARBold,
      })
    }
    case 'zh': {
      return handleFontRegistration({
        regular: NotoSansSCRegular,
        medium: NotoSansSCMedium,
        bold: NotoSansSCBold,
        regularItalic: NotoSansSCRegular,
        mediumItalic: NotoSansSCMedium,
        boldItalic: NotoSansSCBold,
      })
    }
    case 'zh-Hans-HK': {
      return handleFontRegistration({
        regular: NotoSansTCRegular,
        medium: NotoSansTCMedium,
        bold: NotoSansTCBold,
        regularItalic: NotoSansTCRegular,
        mediumItalic: NotoSansTCMedium,
        boldItalic: NotoSansTCBold,
      })
    }
    default: {
      handleFontRegistration({
        regular: ArimoRegular,
        medium: ArimoMedium,
        bold: ArimoBold,
        regularItalic: ArimoRegularItalic,
        mediumItalic: ArimoMediumItalic,
        boldItalic: ArimoBoldItalic,
      })
    }
  }
}

export const handleFontRegistration = ({
  regular,
  medium,
  bold,
  regularItalic,
  boldItalic,
  mediumItalic,
}: any) => {
  Font.register({
    family: 'DeliveryFont',
    fonts: [
      { src: regular },
      { src: medium, fontWeight: 'medium' },
      { src: bold, fontWeight: 'bold' },
      { src: regularItalic, fontStyle: 'italic' },
      { src: boldItalic, fontWeight: 'bold', fontStyle: 'italic' },
      { src: mediumItalic, fontWeight: 'medium', fontStyle: 'italic' },
    ],
  })
}

export const handlePDFReportFont = (
  rawDefaultCountry: string | undefined | null,
  locale: CartrackLocale | null | undefined,
) => {
  const defaultCountry = rawDefaultCountry?.toLowerCase() ?? null
  if (
    defaultCountry &&
    PDF_REPORT_LOCALES_COUNTRY.includes(defaultCountry as FixMeAny)
  ) {
    const country = defaultCountry as (typeof PDF_REPORT_LOCALES_COUNTRY)[number]
    if (country === 'me' || country === 'ae') {
      handleFontSelection('ar')
    } else {
      handleFontSelection(country)
    }
  } else {
    handleFontSelection(locale)
  }
}

export const hyphenationBreakWord = (name: string): Array<string> => [name]

export const handlePODSizeLayout = (layout: PODLayout) => {
  switch (layout) {
    case 'dynamic': {
      return { width: '90px', height: '145px' }
    }
    case 'groupOf2': {
      return { width: '48%', height: '230px' }
    }
    case 'groupOf3': {
      return { width: '31%', height: '180px' }
    }
    default: {
      return { width: '90px', height: '145px' }
    }
  }
}
