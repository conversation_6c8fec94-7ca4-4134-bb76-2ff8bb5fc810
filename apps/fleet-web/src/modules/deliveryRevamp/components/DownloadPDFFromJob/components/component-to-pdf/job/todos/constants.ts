//theme
import { themeType } from '@fleet-web/modules/deliveryRevamp/constants/theme'

export const TODO_STATUS_ID = {
  UNDONE: null,
  CUSTOMER_NOT_SHOW: 1,
  REFUSE_TO_SIGN: 2,
  SIGNATURE_OTHERS: 3,
  NO_RESPONSE: 4,
  NO_PERSON_AT_HOME: 5,
  PHOTO_OTHERS: 6,
  TECHNICAL_ISSUES: 7,
  ATTACH_CODE_OTHERS: 8,
  COMPLETED: 9,
} as const

export const TODOS_CARD_BG = {
  null: themeType.light.styleHoverActiveColor,
  [TODO_STATUS_ID.COMPLETED]: themeType.light.green3,
  [TODO_STATUS_ID.CUSTOMER_NOT_SHOW]: themeType.light.red6,
  [TODO_STATUS_ID.REFUSE_TO_SIGN]: themeType.light.red6,
  [TODO_STATUS_ID.SIGNATURE_OTHERS]: themeType.light.red6,
  [TODO_STATUS_ID.NO_RESPONSE]: themeType.light.red6,
  [TODO_STATUS_ID.NO_PERSON_AT_HOME]: themeType.light.red6,
  [TODO_STATUS_ID.PHOTO_OTHERS]: themeType.light.red6,
  [TODO_STATUS_ID.TECHNICAL_ISSUES]: themeType.light.red6,
  [TODO_STATUS_ID.ATTACH_CODE_OTHERS]: themeType.light.red6,
} as const

export const TODOS_CARD_STATUS_DESCRIPTION_COLOR = {
  null: themeType.light.black2,
  [TODO_STATUS_ID.COMPLETED]: themeType.light.green4,
  [TODO_STATUS_ID.CUSTOMER_NOT_SHOW]: themeType.light.red,
  [TODO_STATUS_ID.REFUSE_TO_SIGN]: themeType.light.red,
  [TODO_STATUS_ID.SIGNATURE_OTHERS]: themeType.light.red,
  [TODO_STATUS_ID.NO_RESPONSE]: themeType.light.red,
  [TODO_STATUS_ID.NO_PERSON_AT_HOME]: themeType.light.red,
  [TODO_STATUS_ID.PHOTO_OTHERS]: themeType.light.red,
  [TODO_STATUS_ID.TECHNICAL_ISSUES]: themeType.light.red,
  [TODO_STATUS_ID.ATTACH_CODE_OTHERS]: themeType.light.red,
} as const
