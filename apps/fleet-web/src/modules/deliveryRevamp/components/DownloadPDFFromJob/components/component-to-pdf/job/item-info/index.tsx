import { isEmpty } from 'lodash'
import { Text, View } from '@react-pdf/renderer'

import {
  FOR_TRANSLATION_KEYWORDS,
  ITEM_TYPE_ID,
  LENGTH_OPTIONS,
  WEIGHT_OPTIONS,
} from '@fleet-web/modules/deliveryRevamp/constants/job'
//theme
import { themeType } from '@fleet-web/modules/deliveryRevamp/constants/theme'
import { concatWithChars } from '@fleet-web/modules/deliveryRevamp/helpers'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

//components
import Card from '../../commons/card'
//styles
import { styles } from '../../style'
//types
import type { JobReportProps } from '../index'
import ItemTodos from '../todos/items'
//helper
import { DefaultItemStatusIcon, itemStatusDescription } from './helpers'

const ItemInfo = ({ data }: { data: JobReportProps }) => (
  <>
    {data.jobItem && !isEmpty(data.jobItem) ? (
      data.jobItem.map((item, idx) => {
        const itemRemarks = item.jobItemTodo
          .map((item) => (item.statusRemarks ? item.statusRemarks : ''))
          .filter(Boolean)
        return (
          <Card
            key={item.jobItemId}
            borderColor={themeType.light.grey1}
            header={
              <Text
                style={{
                  margin: '0 0 10px 5px',
                  textTransform: 'uppercase',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: themeType.light.grey3,
                }}
              >
                {`${ctIntl.formatMessage({ id: 'Item' })} (${idx + 1}/${
                  data.jobItem.length
                })`}
              </Text>
            }
            title={
              <View
                style={[
                  styles.flex,
                  {
                    padding: '6px 8px',
                    color: 'white',
                    backgroundColor: `${themeType.light.grey24}`,
                    borderTopLeftRadius: '7px',
                    borderTopRightRadius: '7px',
                    border: `1px solid ${themeType.light.grey1}`,
                    borderBottom: 'transparent',
                    fontSize: '13px',
                    fontWeight: 'bold',
                  },
                ]}
              >
                <View style={[styles.flex, { width: 'auto' }]}>
                  {DefaultItemStatusIcon(item.itemTypeId)}
                  <Text
                    style={{
                      marginLeft: '5px',
                      color: themeType.light.black,
                    }}
                  >
                    {FOR_TRANSLATION_KEYWORDS.includes(item.description)
                      ? ctIntl.formatMessage({ id: item.description })
                      : item.description}
                  </Text>
                </View>
                <Text>{itemStatusDescription(item.jobItemStatus)}</Text>
              </View>
            }
            body={
              <>
                <View
                  style={[
                    styles.flex,
                    { padding: '7px 15px', alignItems: 'flex-start' },
                  ]}
                >
                  <View>
                    {[ITEM_TYPE_ID.PACKAGE, ITEM_TYPE_ID.PERSON].includes(
                      item.itemTypeId,
                    ) && (
                      <Text style={{ marginBottom: '8px', display: 'flex' }}>
                        <Text style={{ fontWeight: 'medium' }}>
                          {ctIntl.formatMessage({
                            id:
                              item.itemTypeId === ITEM_TYPE_ID.PERSON
                                ? 'Code'
                                : 'Tracking',
                          })}
                          :{' '}
                        </Text>
                        <Text> #{item.trackingNumber}</Text>
                      </Text>
                    )}
                    <Text style={{ marginBottom: '8px', display: 'flex' }}>
                      <Text style={{ fontWeight: 'medium' }}>
                        {ctIntl.formatMessage({ id: 'QTY' })}:{' '}
                      </Text>
                      <Text> {item.quantity}</Text>
                    </Text>
                    {[ITEM_TYPE_ID.PACKAGE, ITEM_TYPE_ID.PERSON].includes(
                      item.itemTypeId,
                    ) && (
                      <Text style={{ marginBottom: '8px', display: 'flex' }}>
                        <Text style={{ fontWeight: 'medium' }}>
                          {ctIntl.formatMessage({ id: 'Weight' })}:{' '}
                        </Text>
                        <Text>
                          {item.weight} {WEIGHT_OPTIONS[0].label}
                        </Text>
                      </Text>
                    )}
                    {[ITEM_TYPE_ID.PACKAGE, ITEM_TYPE_ID.PERSON].includes(
                      item.itemTypeId,
                    ) &&
                      item.quantity > 1 && (
                        <Text style={{ marginBottom: '8px', display: 'flex' }}>
                          <Text style={{ fontWeight: 'medium' }}>
                            {ctIntl.formatMessage({
                              id: 'delivery.package.totalWeight',
                            })}
                            :{' '}
                          </Text>
                          <Text>
                            {(item.weight * item.quantity).toFixed(2)}{' '}
                            {WEIGHT_OPTIONS[0].label}
                          </Text>
                        </Text>
                      )}
                    {item.itemTypeId === ITEM_TYPE_ID.PACKAGE && (
                      <Text style={{ marginBottom: '8px', display: 'flex' }}>
                        <Text style={{ fontWeight: 'medium' }}>
                          {ctIntl.formatMessage({ id: 'Size' })}:{' '}
                        </Text>
                        <Text>
                          {`${item.length || 0}${LENGTH_OPTIONS[0].label} x ${
                            item.width || 0
                          }${LENGTH_OPTIONS[0].label} x ${item.height || 0}${
                            LENGTH_OPTIONS[0].label
                          }`}
                        </Text>
                      </Text>
                    )}
                  </View>
                  <View
                    style={{
                      textAlign: 'right',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-end',
                      flexWrap: 'wrap',
                    }}
                  >
                    <Text style={{ marginBottom: '8px', display: 'flex' }}>
                      <Text style={{ fontWeight: 'medium' }}>
                        {ctIntl.formatMessage({ id: 'Remarks' })}:{' '}
                      </Text>
                      <Text style={{ flexWrap: 'wrap' }}>
                        {itemRemarks && concatWithChars(itemRemarks)}
                      </Text>
                    </Text>
                  </View>
                </View>
                <View style={{ padding: '7px 15px' }}>
                  <ItemTodos
                    jobTypeId={data.jobTypeId}
                    itemTypeId={item.itemTypeId}
                    itemTodos={item.jobItemTodo}
                    options={{
                      pdfTodoTypes: data.pdfTodoTypes,
                      pdfTodoStatuses: data.pdfTodoStatuses,
                    }}
                    trackingNumber={item.trackingNumber}
                    layoutPOD={data.layoutPOD}
                  />
                </View>
              </>
            }
          />
        )
      })
    ) : (
      <View></View>
    )}
  </>
)

export default ItemInfo
