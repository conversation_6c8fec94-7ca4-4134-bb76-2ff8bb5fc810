import { useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogTitle,
  IconButton,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import styled from 'styled-components'

//utils
import { ctIntl } from '@fleet-web/util-components/ctIntl'

//png
import DynamicOption from 'assets/delivery/png/dynamic.png'
import GroupOf2Option from 'assets/delivery/png/group-of-2.png'
import GroupOf3Option from 'assets/delivery/png/group-of-3.png'

type Props = {
  onResult: (selectedLayout: PODLayout) => void
  onClose: () => void
}

export type PODLayout = 'dynamic' | 'groupOf2' | 'groupOf3'

type POD_LAYOUT_OPTIONS = Array<{
  value: PODLayout
  label: string
  img: string
}>

const PODLayoutOption: POD_LAYOUT_OPTIONS = [
  {
    value: 'dynamic',
    label: 'Dynamic',
    img: DynamicOption,
  },
  {
    value: 'groupOf2',
    label: 'Two per row',
    img: GroupOf2Option,
  },
  {
    value: 'groupOf3',
    label: 'Three per row',
    img: GroupOf3Option,
  },
]

const JobReportModal = ({ onResult, onClose }: Props) => {
  const [selectedLayout, setSelectedLayout] = useState<PODLayout>('dynamic')

  return (
    <Dialog
      open
      onClose={onClose}
    >
      <DialogTitle>
        <>
          <Box
            display="flex"
            width="100%"
            justifyContent="space-between"
            alignItems="center"
            fontSize="20px"
            fontWeight="bold"
            marginBottom="15px"
          >
            {ctIntl.formatMessage({
              id: 'deliver.download.jobReport',
            })}
            <IconButton
              onClick={() => {
                onClose()
              }}
              size="small"
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          <Box
            marginBottom="10px"
            fontSize="18px"
          >
            {ctIntl.formatMessage({
              id: 'Customise POD Layout',
            })}
            :
          </Box>
          <Box
            marginBottom="5px"
            color="grey.700"
            fontSize="14px"
          >
            {ctIntl.formatMessage({
              id: `Choose the number of images you'd like to display in a single row`,
            })}
            .
          </Box>
          <Box
            marginBottom="15px"
            color="grey.700"
            fontSize="14px"
          >
            {ctIntl.formatMessage({
              id: `Selecting fewer images will increase their size for better visibility`,
            })}
            :
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            padding="10px 5px 0"
          >
            {PODLayoutOption.map((layout, index) => (
              <Box
                key={layout.value}
                sx={(theme) => ({
                  backgroundColor:
                    selectedLayout === layout.value ? 'action.hover' : 'transparent',
                  cursor: 'pointer',
                  border:
                    selectedLayout === layout.value
                      ? `solid 1px ${theme.palette.primary.main}`
                      : 'transparent',
                })}
                borderRadius="10px"
                padding="5px"
                marginRight={index !== PODLayoutOption.length - 1 ? '10px' : '0'}
                onClick={(e: React.MouseEvent<HTMLElement>) => {
                  e.stopPropagation()
                  setSelectedLayout(layout.value)
                }}
              >
                <Box
                  marginBottom="5px"
                  fontWeight="bold"
                  color="grey.700"
                >
                  {ctIntl.formatMessage({
                    id: layout.label,
                  })}
                </Box>
                <Image src={layout.img} />
              </Box>
            ))}
          </Box>
        </>
      </DialogTitle>
      <DialogActions>
        <Button
          size="small"
          variant="contained"
          onClick={() => onResult(selectedLayout)}
        >
          {ctIntl.formatMessage({
            id: 'Download',
          })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default JobReportModal

const Image = styled.img`
  width: 160px;
  height: 120px;
`
