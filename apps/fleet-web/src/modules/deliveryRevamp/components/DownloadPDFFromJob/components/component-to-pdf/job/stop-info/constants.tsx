//constants

//theme
import { STOP_STATUS_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import { themeType } from '@fleet-web/modules/deliveryRevamp/constants/theme'

export const JOB_STOP_STATUS_LABEL = {
  [STOP_STATUS_ID.CREATED]: 'Not Started',
  [STOP_STATUS_ID.STARTED]: 'Started',
  [STOP_STATUS_ID.ARRIVED]: 'Arrived',
  [STOP_STATUS_ID.COMPLETED]: 'Completed',
  [STOP_STATUS_ID.REJECTED]: 'Rejected',
} as const

export const JOB_REPORT_STOP_STATUS_STOP_COLOR = {
  [STOP_STATUS_ID.CREATED]: themeType.light.grey10,
  [STOP_STATUS_ID.STARTED]: themeType.light.yellow,
  [STOP_STATUS_ID.ARRIVED]: themeType.light.yellow,
  [STOP_STATUS_ID.COMPLETED]: themeType.light.green,
  [STOP_STATUS_ID.REJECTED]: themeType.light.red,
} as const
