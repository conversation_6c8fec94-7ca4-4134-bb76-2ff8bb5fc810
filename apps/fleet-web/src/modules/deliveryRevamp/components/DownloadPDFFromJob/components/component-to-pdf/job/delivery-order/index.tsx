/* eslint-disable no-nested-ternary */
import { isEmpty } from 'lodash'
import { Text, View } from '@react-pdf/renderer'
import moment from 'moment-timezone'

//constants
import {
  JOB_STATUS_ID,
  JOB_STATUS_ID_TO_COLOR,
} from '@fleet-web/modules/deliveryRevamp/constants/job'
//theme
import { themeType } from '@fleet-web/modules/deliveryRevamp/constants/theme'
import { concatWithChars } from '@fleet-web/modules/deliveryRevamp/helpers'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

//components
import Card from '../../commons/card'
//styles
import { styles } from '../../style'
//types
import type { JobReportProps } from '../index'
//helper
import {
  formatSpecialEquipment,
  JobStatusIcon,
  renderStatusDescription,
  renderStatusLabel,
} from './helpers'

const DeliveryOrder = ({ data }: { data: JobReportProps }) => {
  const inProgress = data.inProgress
  const isAssigned = data.jobStatusId === JOB_STATUS_ID.ASSIGNED

  const isInProgress = inProgress && isAssigned

  const pdfJobLabels =
    data.labels && !isEmpty(data.labels)
      ? data.labels.map((jobLabel) => jobLabel.name)
      : []

  const pdfSpecialEquipment =
    data.requiredCapabilities && !isEmpty(data.requiredCapabilities)
      ? data.requiredCapabilities.map((specialEquipment) =>
          formatSpecialEquipment(specialEquipment.name),
        )
      : []

  return (
    <>
      <Card
        borderColor={
          isInProgress
            ? themeType.light.orange4
            : JOB_STATUS_ID_TO_COLOR[data.jobStatusId]
        }
        header={
          <>
            <Text
              style={{
                margin: '0 0 10px 5px',
                textTransform: 'uppercase',
                fontSize: '16px',
                fontWeight: 'bold',
                color: themeType.light.black,
              }}
            >
              {data.accountName}
            </Text>
            <Text
              style={{
                margin: '0 0 10px 5px',
                textTransform: 'uppercase',
                fontSize: '12px',
                fontWeight: 'bold',
                color: themeType.light.grey3,
              }}
            >
              {ctIntl.formatMessage({ id: 'Delivery Order' })}
            </Text>
          </>
        }
        title={
          <View
            style={[
              styles.flex,
              {
                padding: '6px 8px',
                fontWeight: 'bold',
                color: 'white',
                backgroundColor: `${
                  isInProgress
                    ? themeType.light.orange4
                    : JOB_STATUS_ID_TO_COLOR[data.jobStatusId]
                }`,
                borderTopLeftRadius: '7px',
                borderTopRightRadius: '7px',
                borderTop: `1px solid ${
                  isInProgress
                    ? themeType.light.orange4
                    : JOB_STATUS_ID_TO_COLOR[data.jobStatusId]
                }`,
              },
            ]}
          >
            <View style={[styles.flex, { width: 'auto' }]}>
              {JobStatusIcon(data)}
              <Text style={{ marginLeft: '5px', fontSize: '16px' }}>
                {data.referenceNumber || data.orderId}
              </Text>
            </View>
            <View
              style={[
                styles.flex,
                { width: 'auto', flexDirection: 'column', alignItems: 'flex-end' },
              ]}
            >
              <Text
                style={{ fontSize: '14px', marginBottom: isInProgress ? '7px' : '0px' }}
              >
                {isInProgress
                  ? ctIntl.formatMessage({ id: 'In Progress' })
                  : renderStatusLabel(data)}
              </Text>
              <Text style={{ fontSize: '10px' }}>
                {renderStatusDescription(data, isInProgress)}
              </Text>
            </View>
          </View>
        }
        body={
          <View style={[styles.flex, { padding: '7px 15px' }]}>
            <View>
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Completed Time' })}:{' '}
                </Text>
                <Text>
                  {data.completedTs &&
                    data.completedTs !== '-' &&
                    moment(data.completedTs).format('YYYY-MM-DD hh:mm A')}
                </Text>
              </Text>
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Scheduled' })}:{' '}
                </Text>
                <Text>
                  {data.scheduledDeliveryTs &&
                    moment(data.scheduledDeliveryTs).format('YYYY-MM-DD hh:mm A')}
                </Text>
              </Text>
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Assignee' })}:{' '}
                </Text>
                <Text>
                  {data.driver?.firstName} {data.driver?.lastName}
                </Text>
              </Text>
            </View>
            <View
              style={{
                textAlign: 'right',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-end',
                alignItems: 'flex-end',
              }}
            >
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Job' })}#:{' '}
                </Text>
                <Text>{data.orderId}</Text>
              </Text>
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Job Label' })}:{' '}
                </Text>
                <Text style={{ flexWrap: 'wrap' }}>
                  {concatWithChars(pdfJobLabels)}
                </Text>
              </Text>
              <Text style={{ marginBottom: '8px', display: 'flex' }}>
                <Text style={{ fontWeight: 'medium' }}>
                  {ctIntl.formatMessage({ id: 'Special Equipment' })}:{' '}
                </Text>
                <Text style={{ flexWrap: 'wrap' }}>
                  {concatWithChars(pdfSpecialEquipment)}
                </Text>
              </Text>
            </View>
          </View>
        }
      />
    </>
  )
}

export default DeliveryOrder
