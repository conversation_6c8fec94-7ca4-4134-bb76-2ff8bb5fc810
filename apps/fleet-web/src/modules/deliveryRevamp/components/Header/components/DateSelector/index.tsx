import { useMemo, useState } from 'react'
import { <PERSON><PERSON>, Di<PERSON>r, Icon<PERSON>utton, Stack } from '@karoo-ui/core'
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'
import type { DateTime } from 'luxon'

import DatePickerPopper from '@fleet-web/modules/deliveryRevamp/components/DatePickerPopper'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { DeliveryDateTime } from '../../../../utils/deliveryDateTime'

type Props = {
  selectedDate: DateTime | null
  onChange: (newDate: DateTime | null) => void
}

const DateSelector = ({ selectedDate, onChange }: Props) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const isToday = useMemo(
    () =>
      selectedDate !== null &&
      selectedDate.toISODate() === DeliveryDateTime.now().toISODate(),
    [selectedDate],
  )

  const togglePopper = (event?: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event ? event.currentTarget : null)
  }

  return (
    <>
      <Stack
        direction="row"
        divider={
          <Divider
            orientation="vertical"
            flexItem
          />
        }
        sx={(theme) => ({
          height: '36px',
          border: '1px solid',
          borderColor: theme.palette.divider,
          borderRadius: 1,
          minWidth: '250px',
        })}
      >
        <IconButton
          aria-label={ctIntl.formatMessage({
            id: 'tachograph.activity.drawer.previousDate',
          })}
          size="small"
          disabled={selectedDate === null}
          onClick={() => {
            if (selectedDate) {
              onChange(selectedDate.minus({ days: 1 }))
            }
          }}
        >
          <NavigateBeforeIcon sx={{ fontSize: 'inherit' }} />
        </IconButton>

        <Button
          variant="text"
          color="secondary"
          onClick={togglePopper}
          sx={{ flex: 1, whiteSpace: 'nowrap' }}
        >
          {(() => {
            if (selectedDate === null) {
              return ctIntl.formatMessage({ id: 'Unscheduled' })
            }

            if (isToday) {
              return ctIntl.formatMessage({ id: 'Today' })
            }

            return selectedDate.toFormat('D ccc')
          })()}
        </Button>

        <IconButton
          aria-label={ctIntl.formatMessage({
            id: 'tachograph.activity.drawer.nextDate',
          })}
          size="small"
          disabled={selectedDate === null}
          onClick={() => {
            if (selectedDate) {
              onChange(selectedDate.plus({ days: 1 }))
            }
          }}
        >
          <NavigateNextIcon sx={{ fontSize: 'inherit' }} />
        </IconButton>
      </Stack>

      <DatePickerPopper
        value={selectedDate}
        onChange={onChange}
        anchorEl={anchorEl}
        onClose={togglePopper}
      />
    </>
  )
}

export default DateSelector
