import { useState, type Dispatch, type SetStateAction } from 'react'
import { difference, isEmpty } from 'lodash'
import { Box, Button, Divider, Paper, Stack, Tooltip, Typography } from '@karoo-ui/core'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import type { FileRejection, FileWithPath } from 'react-dropzone'
import { useSelector } from 'react-redux'

import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import type { FetchImportJobTemplate } from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useImportJobTemplateList'
import DropzoneItemWrapper from '@fleet-web/modules/deliveryRevamp/components/DropzoneItemWrapper'
import { IMPORT_TYPE } from '@fleet-web/modules/deliveryRevamp/constants'
import { MIME_IMPORT_JOB_OBJECT } from '@fleet-web/modules/deliveryRevamp/constants/app-setting'
import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import useActiveImport from '@fleet-web/modules/deliveryRevamp/hooks/useActiveImport'
import useLookupHook from '@fleet-web/modules/deliveryRevamp/hooks/useLookup'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { useBackwardCompatibleDropzone } from '@fleet-web/util-components/useBackwardCompatibleDropzone'
import { showSupportedFileFormats } from '@fleet-web/util-functions/file-utils'

import type { ImportState } from '..'
import {
  getFileColumns,
  readXLSXFile,
  type CSVXLSObject,
  type ExtraProps,
} from '../parser'
import {
  CONDITIONAL_REQUIRED_FIELDS,
  generateDefaultUserConfig,
  translateColumn,
} from '../parser/constants'
import type { JobImporterError, JobImporterOutPut } from '../parser/types'

export type ParserParamsType = {
  file: string | ArrayBuffer | null
  extraProps: ExtraProps
  fileType: string
}

export type ImportParserType = (
  data: Array<CSVXLSObject>,
  config: Record<string, string>,
  extraProps: ExtraProps,
) => Promise<{
  data: Array<{ jobs: Array<JobImporterOutPut.Job> }>
  errors: Array<JobImporterError.Error | undefined>
  columns: Array<string>
  invalidMapping: boolean
  invalidUserSetting: boolean
  rawData: Array<unknown>
}>

type Props = {
  importState: ImportState
  handleSetImportState: (importState: ImportState) => void
  systemConfig: Array<string>
  parser: ImportParserType
  templatePath: string
  templates: Array<FetchImportJobTemplate.Return>
  setTemplate: Dispatch<SetStateAction<FetchImportJobTemplate.Return>>
}

export type ParserResultType = {
  data: Array<{ jobs: Array<JobImporterOutPut.Job> }>
  errors: Array<JobImporterError.Error | undefined>
  columns: Array<string>
  invalidMapping: boolean
  invalidUserSetting: boolean
  isSpeedyUpload?: boolean
  rawData: Record<string, any>
  parserParams?: ParserParamsType
}

export const defaultParserResult = {
  data: [],
  errors: [],
  columns: [],
  invalidMapping: false,
  invalidUserSetting: false,
  rawData: [],
  parserParams: undefined,
}

const ACCEPTED_IMPORTS = Object.values(MIME_IMPORT_JOB_OBJECT).join(', ')

const useDataUploading = ({
  handleSetImportState,
  systemConfig,
  parser,
  templatePath,
  templates,
  setTemplate,
}: Props) => {
  const { deliveryCapabilities } = useLookupHook()
  const { phonePrefix } = useSelector(getSettings_UNSAFE)
  const { activeImport } = useActiveImport()

  const [result, setResult] = useState<ParserResultType>(defaultParserResult)

  const [errorFileName, setErrorFileName] = useState('')
  const [fileName, setFileName] = useState('')

  const onAcceptedFiles = async (_acceptedFiles: Array<FileWithPath>) => {
    const file = _acceptedFiles[0]
    setErrorFileName(file.name.substring(0, file.name.lastIndexOf('.')))
    setFileName(file.name)

    handleSetImportState({
      stepType: 'data-uploading',
      uploadState: IMPORT_STATUS_TO_ID.LOADING,
      mapDataState: IMPORT_STATUS_TO_ID.PENDING,
      validateState: IMPORT_STATUS_TO_ID.PENDING,
    })

    const isCsv = file.type === 'text/csv'
    const parseType = isCsv ? 'string' : 'array'

    await new Promise((resolve) => window.setTimeout(resolve, 1000))

    try {
      const rawFile = isCsv ? await file.text() : await file.arrayBuffer()
      const data = readXLSXFile(rawFile, parseType)
      const fileColumns = getFileColumns(data)

      const matchedTemplate = templates.find(
        (template) =>
          template.content.userSetting?.fileColumns.length === fileColumns.length &&
          difference(template.content.userSetting?.fileColumns || [], fileColumns)
            .length === 0,
      )

      if (matchedTemplate) setTemplate(matchedTemplate)

      const result = await parser(
        data,
        matchedTemplate && !isEmpty(matchedTemplate.content.userSetting)
          ? matchedTemplate.content.userSetting.config
          : generateDefaultUserConfig(fileColumns),
        {
          phonePrefix,
          deliveryCapabilities,
          ...(activeImport ?? { importType: IMPORT_TYPE.JOB }),
        },
      ).catch((e) => {
        handleSetImportState({
          stepType: 'custom-error',
          importTemplate: {
            footer: {
              status: {
                message: e,
                type: 'error',
              },
            },
          },
          uploadState: IMPORT_STATUS_TO_ID.FAILED,
          mapDataState: IMPORT_STATUS_TO_ID.PENDING,
          validateState: IMPORT_STATUS_TO_ID.PENDING,
        })
      })

      if (!result) return

      setResult((prev) => ({
        ...prev,
        ...result,
        parserParams: {
          file: rawFile,
          extraProps: {
            phonePrefix,
            deliveryCapabilities,
            ...(activeImport ?? { importType: IMPORT_TYPE.JOB }),
          },
          fileType: file.type,
        },
      }))

      handleSetImportState({
        stepType: 'edit-mapping',
      })
    } catch (e) {
      handleSetImportState({
        stepType: 'custom-error',
        importTemplate: {
          footer: {
            status: {
              message: e,
              type: 'error',
            },
          },
        },
        uploadState: IMPORT_STATUS_TO_ID.FAILED,
        mapDataState: IMPORT_STATUS_TO_ID.PENDING,
        validateState: IMPORT_STATUS_TO_ID.PENDING,
      })
    }
  }

  const onRejectedFiles = (rejectedFiles: Array<FileRejection>) => {
    if (rejectedFiles.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'File type should be CSV, XLS, XLSX',
        }),
        {
          variant: 'error',
        },
      )
    }
  }

  const { getRootProps, getInputProps, open } = useBackwardCompatibleDropzone({
    onDropAccepted: onAcceptedFiles,
    onDropRejected: onRejectedFiles,
    noDragEventsBubbling: true,
    noKeyboard: true,
    noClick: true,
    multiple: false,
    accept: ACCEPTED_IMPORTS,
  })

  const initialHeader = (
    <Typography variant="subtitle2">
      {ctIntl.formatMessage({ id: 'Upload your Excel file' })}
    </Typography>
  )

  const initialModalBody = (
    <Stack sx={{ height: '100%' }}>
      <Stack sx={{ flexGrow: 1 }}>
        <Paper
          {...getRootProps()}
          variant="outlined"
          sx={{
            borderBottom: 0,
            borderRadius: '4px 4px 0 0',
            flexGrow: 1,
          }}
        >
          <input {...getInputProps()} />
          <DropzoneItemWrapper>
            <UploadFileIcon
              fontSize="large"
              color="action"
            />
            <Typography variant="body2">
              {ctIntl.formatMessage({ id: 'mifleet.imports.file.drop.title' })}
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => open()}
            >
              {ctIntl.formatMessage({
                id: 'mifleet.imports.file.drop.browse',
              })}
            </Button>
            <Typography
              variant="caption"
              color="text.secondary"
            >
              {ctIntl.formatMessage(
                { id: 'global.upload.supportedFormats' },
                {
                  values: {
                    formats: showSupportedFileFormats(['xls', 'xlsx', 'csv']),
                  },
                },
              )}
            </Typography>
          </DropzoneItemWrapper>
        </Paper>
        <Paper
          variant="outlined"
          sx={{
            borderRadius: '0 0 4px 4px',
            borderColor: 'info.main',
            padding: '16px 16px 8px 16px',
          }}
        >
          <Stack spacing={1}>
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
            >
              <InfoOutlinedIcon color="info" />
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 'medium' }}
              >
                {ctIntl.formatMessage({
                  id: 'Your file should contain the following fields:',
                })}
              </Typography>
            </Stack>
            <Stack
              direction="row"
              useFlexGap
              flexWrap="wrap"
              spacing={1}
              divider={
                <Divider
                  orientation="vertical"
                  flexItem
                />
              }
            >
              {systemConfig
                .filter((column) => column in CONDITIONAL_REQUIRED_FIELDS)
                .map((column) => (
                  <Typography
                    key={column}
                    variant="body2"
                  >
                    {translateColumn(column)}
                    {CONDITIONAL_REQUIRED_FIELDS[column] && (
                      <Tooltip
                        arrow
                        placement="right"
                        title={CONDITIONAL_REQUIRED_FIELDS[column]()}
                      >
                        <Box
                          component="span"
                          sx={{ color: 'warning.main' }}
                        >
                          *
                        </Box>
                      </Tooltip>
                    )}
                  </Typography>
                ))}
            </Stack>
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
            >
              <Stack
                spacing={2}
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{
                  typography: 'body2',
                }}
              >
                <Box>
                  {ctIntl.formatMessage({ id: 'Conditional required fields' })}
                  <Box
                    component="span"
                    sx={{ color: 'warning.main' }}
                  >
                    *
                  </Box>
                </Box>
              </Stack>
              <Button
                href={templatePath}
                color="info"
                size="small"
                startIcon={<FileDownloadOutlinedIcon />}
              >
                {ctIntl.formatMessage({
                  id: 'Download Template',
                })}
              </Button>
            </Stack>
          </Stack>
        </Paper>
      </Stack>
    </Stack>
  )

  const initialFooter = undefined

  return {
    uploadHeader: initialHeader,
    uploadBody: initialModalBody,
    uploadFooter: initialFooter,
    parsedUploadedResult: result,
    errorFileName,
    fileName,
  }
}

export default useDataUploading
