/* eslint-disable no-nested-ternary */
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  Paper,
  Stack,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import * as R from 'remeda'

import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { ImportState } from '..'
import type { JobImporterError, JobImporterOutPut } from '../parser/types'
import type { ParserResultType } from './useDataUploading'

type Props = {
  missingData: Array<JobImporterError.Error | undefined>
  invalidData: Array<JobImporterError.Error | undefined>
  duplicatedData: Array<JobImporterError.Error | undefined>
  alreadyExistedData: Array<JobImporterError.Error | undefined>
  invalidGeoError: Array<JobImporterError.Error | undefined>
  handleSetImportState: (importState: ImportState) => void
  setShowModal: (val: boolean) => void
  isSpeedyUpload?: boolean
  parsedResultData: ParserResultType
  validForUploadData: Array<{ jobs: Array<JobImporterOutPut.Job> }>
  fileName?: string
  isError: boolean
}

const useDataValidation = ({
  missingData,
  invalidData,
  duplicatedData,
  alreadyExistedData,
  invalidGeoError,
  handleSetImportState,
  setShowModal,
  isSpeedyUpload,
  parsedResultData,
  validForUploadData,
  fileName,
  isError,
}: Props) => {
  const handleStateUpdate = () => {
    if (!isSpeedyUpload) {
      handleSetImportState({
        stepType: 'data-upload',
        uploadState: 1,
        mapDataState: 1,
        validateState: 1,
      })
    }
    if (isSpeedyUpload) {
      setShowModal(false)
    }
  }

  const handlePrevious = () => {
    handleSetImportState({
      stepType: 'edit-mapping',
      uploadState: IMPORT_STATUS_TO_ID.DONE,
      mapDataState: IMPORT_STATUS_TO_ID.PENDING,
      validateState: IMPORT_STATUS_TO_ID.PENDING,
    })
  }

  const renderErrorRows = (errors: Array<JobImporterError.Error | undefined>) => (
    <Stack spacing={2}>
      {errors.map((data, index) => (
        <Stack
          // eslint-disable-next-line react/no-array-index-key
          key={index}
          direction="row"
          alignItems="center"
          spacing={3}
        >
          <Typography
            variant="body2"
            sx={{ flex: 1 }}
          >
            {data?.cellLocation && (
              <>
                {R.isArray(data?.cellLocation) &&
                  data?.cellLocation.filter((i) => i !== '').length > 0 &&
                  ctIntl.formatMessage({
                    id: 'Cell',
                  })}
                &nbsp;
                {R.isArray(data?.cellLocation) &&
                  data?.cellLocation.join('-').toString()}
              </>
            )}
            {data?.rowLocation ? (
              <>
                {((R.isArray(data?.rowLocation) &&
                  data?.rowLocation.filter((i) => i !== '').length > 0) ||
                  (!R.isArray(data?.rowLocation) &&
                    data?.rowLocation.toString() !== '')) &&
                  ctIntl.formatMessage({
                    id: 'Rows',
                  })}
                &nbsp;
                {R.isArray(data?.rowLocation) && data?.rowLocation.join(',').toString()}
                {!R.isArray(data?.rowLocation) && data?.rowLocation.toString()}
              </>
            ) : (
              <>
                {data?.rowNumber && (
                  <>
                    {((R.isArray(data?.rowNumber) &&
                      data?.rowNumber.filter((i) => i !== '').length > 0) ||
                      (!R.isArray(data?.rowNumber) &&
                        data?.rowNumber.toString() !== '')) &&
                      ctIntl.formatMessage({
                        id: 'Rows',
                      })}
                    &nbsp;
                    {R.isArray(data?.rowNumber) && data?.rowNumber.join(',').toString()}
                    {!R.isArray(data?.rowNumber) && data?.rowNumber.toString()}
                  </>
                )}
              </>
            )}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ flex: 4, display: 'flex', alignItems: 'center', gap: 0.5 }}
          >
            {data?.message}
            {data?.message &&
              data.message.includes('Failed to find exact locations') && (
                <Tooltip
                  arrow
                  placement="right"
                  title={ctIntl.formatMessage({
                    id: 'Please double-check your uploaded file and ensure information such as GPS coordinates, longitude and latitude, country code, postal code, and address line 1, is correct.',
                  })}
                >
                  <InfoOutlinedIcon
                    color="warning"
                    sx={{ fontSize: 16 }}
                  />
                </Tooltip>
              )}
          </Typography>
        </Stack>
      ))}
    </Stack>
  )

  return {
    header: (
      <Typography variant="subtitle2">
        {ctIntl.formatMessage({ id: 'Check for errors in the uploaded Excel file' })}
      </Typography>
    ),
    body: isError ? (
      <Stack sx={{ height: '100%' }}>
        <Alert
          severity="error"
          sx={{ margin: '0 0 16px 0', fontSize: 'body2.fontSize', fontWeight: 500 }}
        >
          {ctIntl.formatMessage(
            { id: 'Please fix errors on {fileName} and reupload' },
            {
              values: {
                fileName: fileName,
              },
            },
          )}
        </Alert>
        <Stack spacing={1}>
          {missingData.length > 0 && (
            <Paper
              variant="outlined"
              sx={{ padding: '16px' }}
            >
              <Alert
                severity="error"
                sx={{
                  marginBottom: '10px',
                  background: 'none',
                  padding: 0,
                  fontWeight: 500,
                }}
              >
                {ctIntl.formatMessage({ id: 'delivery.import.missingData' })}
              </Alert>
              {renderErrorRows(missingData)}
            </Paper>
          )}
          {invalidData.length +
            alreadyExistedData.length +
            duplicatedData.length +
            invalidGeoError.length >
            0 && (
            <Paper
              variant="outlined"
              sx={{ padding: '16px' }}
            >
              <Alert
                severity="warning"
                sx={{
                  marginBottom: '10px',
                  background: 'none',
                  padding: 0,
                  fontWeight: 500,
                }}
              >
                {ctIntl.formatMessage({ id: 'delivery.import.invalidData' })}
              </Alert>
              {renderErrorRows([
                ...invalidData,
                ...alreadyExistedData,
                ...duplicatedData,
                ...invalidGeoError,
              ])}
            </Paper>
          )}
        </Stack>
      </Stack>
    ) : (
      <Stack sx={{ height: 'auto' }}>
        <Alert
          severity="success"
          sx={{ margin: '0 0 16px 0' }}
        >
          <AlertTitle sx={{ fontSize: 'body2.fontSize' }}>
            {ctIntl.formatMessage({
              id: 'Data is validated! Please proceed to next step',
            })}
          </AlertTitle>
          {ctIntl.formatMessage({ id: 'No errors detected' })}
        </Alert>
      </Stack>
    ),
    footer: {
      status: {
        type: 'normal',
        message:
          isError && validForUploadData.length > 0 ? (
            <Button
              variant="outlined"
              color="secondary"
              onClick={handlePrevious}
            >
              {ctIntl.formatMessage({
                id: 'Previous',
              })}
            </Button>
          ) : (
            ''
          ),
      },
      footerAction: isError ? (
        <Stack
          direction="row"
          spacing={1}
        >
          {validForUploadData.length > 0 ? (
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                handleSetImportState({
                  stepType: 'data-submitting',
                  uploadState: IMPORT_STATUS_TO_ID.DONE,
                  mapDataState: IMPORT_STATUS_TO_ID.DONE,
                  validateState: IMPORT_STATUS_TO_ID.DONE,
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'Import' })} {validForUploadData.length}/
              {parsedResultData.data.length}
            </Button>
          ) : (
            <Button
              variant="outlined"
              color="secondary"
              onClick={handlePrevious}
            >
              {ctIntl.formatMessage({
                id: 'Previous',
              })}
            </Button>
          )}
          <Button
            variant="contained"
            onClick={handleStateUpdate}
          >
            {ctIntl.formatMessage({
              id: 'Reupload',
            })}
          </Button>
        </Stack>
      ) : (
        <Stack
          direction="row"
          spacing={1}
        >
          <Button
            variant="outlined"
            color="secondary"
            onClick={handlePrevious}
          >
            {ctIntl.formatMessage({
              id: 'Previous',
            })}
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              handleSetImportState({
                stepType: 'data-submitting',
                uploadState: IMPORT_STATUS_TO_ID.DONE,
                mapDataState: IMPORT_STATUS_TO_ID.DONE,
                validateState: IMPORT_STATUS_TO_ID.DONE,
              })
            }}
          >
            {ctIntl.formatMessage({
              id: 'Next',
            })}
          </Button>
        </Stack>
      ),
    },
  }
}

export default useDataValidation
