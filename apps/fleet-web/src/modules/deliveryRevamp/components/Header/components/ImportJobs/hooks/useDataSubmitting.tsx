import { useMemo, useState, type Dispatch, type SetStateAction } from 'react'
import { isEqual } from 'lodash'
import { Alert, Box, Button, Stack, Typography } from '@karoo-ui/core'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import { useQueryClient, type UseMutationResult } from '@tanstack/react-query'
import produce from 'immer'
import { useParams } from 'react-router'
import { useHistory, useLocation } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import {
  enqueueSnackbarWithButtonAction,
  enqueueSnackbarWithCloseAction,
} from '@fleet-web/components/Snackbar/Notistack/utils'
import type { CreateImportJobTemplate } from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useCreateImportJobTemplate'
import useImportJobTemplateList, {
  type FetchImportJobTemplate,
} from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useImportJobTemplateList'
import type { UpdateImportJobTemplate } from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useUpdateImportJobTemplate'
import useDeliveryJobListFilter from '@fleet-web/modules/deliveryRevamp/api/jobs/useDeliveryJobListFilter'
import type { ImportDeliveryJobs } from '@fleet-web/modules/deliveryRevamp/api/jobs/useJobImportDeliveryMutation'
import useDeliveryPlanDetailsQuery from '@fleet-web/modules/deliveryRevamp/api/plans/useDeliveryPlanDetailsQuery'
import { useDriversAndRoutesContext } from '@fleet-web/modules/deliveryRevamp/components/MapPanel/DriversAndRoutesProvider'
import { getDeliverySettingsDialogMainPath } from '@fleet-web/modules/deliveryRevamp/components/SettingsDialog/utils'
import { IMPORT_TYPE } from '@fleet-web/modules/deliveryRevamp/constants'
import {
  IMPORT_STATUS_TO_ID,
  JOB_TYPE_ID,
  SCHEDULE_TYPE_ID,
} from '@fleet-web/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import Dialog from '@fleet-web/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { splitArrayIntoChunks } from '@fleet-web/modules/deliveryRevamp/helpers'
import useActiveImport from '@fleet-web/modules/deliveryRevamp/hooks/useActiveImport'
import { useRefreshDeliveryData } from '@fleet-web/modules/deliveryRevamp/hooks/useRefresh'
import { DELIVERY_PAGES } from '@fleet-web/modules/deliveryRevamp/types'
import { DeliveryDateTime } from '@fleet-web/modules/deliveryRevamp/utils/deliveryDateTime'
import parseStopsJobSetup from '@fleet-web/modules/deliveryRevamp/utils/parseStopsJobSetup'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { ImportState } from '..'
import JobStopsSetup from '../../../../JobStopsSetup'
import checkAndParseUnscheduledJobs from '../checkAndParseUnscheduledJobs'
import { CHUNK_LENGTH } from '../parser/constants'
import type { JobImporterOutPut } from '../parser/types'

type Props = {
  setShowModal: (visible: undefined | boolean) => void
  handleSetImportState: (importState: ImportState) => void
  count: number
  total: number
  data: Array<{ jobs: Array<JobImporterOutPut.Job> }>
  isSpeedyUpload?: boolean
  importApi: UseMutationResult<
    Array<ImportDeliveryJobs.Response>,
    Error,
    Array<{ jobs: Array<JobImporterOutPut.Job> }>,
    unknown
  >
  template: FetchImportJobTemplate.Return
  setTemplate: Dispatch<SetStateAction<FetchImportJobTemplate.Return>>
  createTemplate: UseMutationResult<
    CreateImportJobTemplate.Return,
    Error,
    CreateImportJobTemplate.ApiInput
  >
  updateTemplate: UseMutationResult<
    UpdateImportJobTemplate.Return,
    Error,
    UpdateImportJobTemplate.ApiInput
  >
  templates: Array<FetchImportJobTemplate.Return>
  handleExportErrors: () => void
  importState: ImportState
}

const useDataSubmitting = ({
  setShowModal,
  handleSetImportState,
  importState,
  count,
  data,
  importApi,
  template,
  setTemplate,
  createTemplate,
  updateTemplate,
  handleExportErrors,
  templates,
}: Props) => {
  const history = useHistory()
  const location = useLocation()
  const { activeImport } = useActiveImport()
  const [progress, setProgress] = useState<number>(0)
  const { refresh } = useRefreshDeliveryData()
  const { page } = useParams<{ page?: DELIVERY_PAGES }>()
  const { data: mainPageContextData, setData: setMainPageContextData } =
    useDeliveryMainPageContext()
  const { plansList } = useDriversAndRoutesContext()
  const queryClient = useQueryClient()

  const originalTemplate = useMemo(
    () => templates.find((t) => t.templateId === template.templateId),
    [templates, template],
  )

  const isShowMissingItemType = useMemo(() => {
    const isSingleSetupItemsEmpty =
      template.content.stopSetup.singleStop.items.length === 0
    const isDoubleSetupItemsEmpty =
      template.content.stopSetup.doubleStop.items.length === 0
    const hasReplaceableSingleJob = data.some((d) =>
      d.jobs.some(
        (job) =>
          job.jobTypeId === JOB_TYPE_ID.SINGLE &&
          job.items.some((item) => item?.isReplaceable),
      ),
    )
    const hasReplaceableDoubleJob = data.some((d) =>
      d.jobs.some(
        (job) =>
          job.jobTypeId === JOB_TYPE_ID.PD &&
          job.items.some((item) => item?.isReplaceable),
      ),
    )

    return (
      (hasReplaceableSingleJob && isSingleSetupItemsEmpty) ||
      (hasReplaceableDoubleJob && isDoubleSetupItemsEmpty)
    )
  }, [
    data,
    template.content.stopSetup.doubleStop.items.length,
    template.content.stopSetup.singleStop.items.length,
  ])

  //Sample simulation please remove once api integration part is complete
  const [footerMessage, setFooterMessage] = useState(
    `${ctIntl.formatMessage({
      id: 'delivery.import.readyToImport',
    })}`,
  )

  const isMutating = useMemo(
    () => importApi.isPending || updateTemplate.isPending || createTemplate.isPending,
    [createTemplate.isPending, importApi.isPending, updateTemplate.isPending],
  )

  const importMutation = async (
    data: Array<{ jobs: Array<JobImporterOutPut.Job> }>,
    newSavedTemplate?: FetchImportJobTemplate.Return,
    isUpdate?: boolean,
  ) => {
    handleSetImportState({
      isSubmit: true,
    })

    const dataChunks = splitArrayIntoChunks(
      data as Array<{ jobs: Array<JobImporterOutPut.Job> }>,
      CHUNK_LENGTH,
    )

    let completedPromises = 0

    const interval = Math.floor(Math.random() * 1000) + 1000
    let currentProgress = 0
    const fakeProgressInterval = window.setInterval(() => {
      const fakeProgress =
        Math.max(1, 100 / dataChunks.length / 5) + Math.round(Math.random())
      const maxProgress = Math.min(
        100,
        ((completedPromises + 1) / dataChunks.length) * 100,
      )
      if (currentProgress + fakeProgress < maxProgress) {
        currentProgress += fakeProgress
        setProgress(currentProgress)
      }
    }, interval)

    const importMutations = dataChunks.map(async (chunk) => {
      const response = await importApi.mutateAsync(chunk)
      completedPromises++
      currentProgress = (completedPromises / dataChunks.length) * 100
      setProgress(currentProgress)
      return response
    })

    try {
      const allMutationResponse = await Promise.all(importMutations)
      const hasFailure = allMutationResponse
        .flatMap((group) => group)
        .some((item) => item.success === false)

      if (hasFailure) {
        throw new Error('We are experiencing a technical error.')
      }

      if (activeImport?.importType === IMPORT_TYPE.PLAN) {
        queryClient.invalidateQueries({
          queryKey: useDeliveryPlanDetailsQuery.createKey({
            planId: activeImport.planId,
          }),
        })
        queryClient.invalidateQueries({
          queryKey: useDeliveryJobListFilter.createKey({
            filters: { planId: activeImport.planId },
          }),
        })
      }
    } catch (e) {
      setFooterMessage(
        JSON.stringify(e?.message || e) ||
          ctIntl.formatMessage({
            id: 'We are experiencing a technical error.',
          }),
      )
      handleSetImportState({
        stepType: 'data-submitting-error',
        isSubmit: false,
        uploadState: IMPORT_STATUS_TO_ID.FAILED,
        mapDataState: IMPORT_STATUS_TO_ID.FAILED,
        validateState: IMPORT_STATUS_TO_ID.FAILED,
      })
      window.clearInterval(fakeProgressInterval)
      setProgress(0)
      return
    }

    match(page)
      .with(DELIVERY_PAGES.MAP, () => refresh())
      .with(
        DELIVERY_PAGES.TABLE,
        () =>
          // TODO: when implement grid view
          null,
      )

    window.clearInterval(fakeProgressInterval)

    handleExportErrors()
    handleSetImportState({
      isSubmit: false,
    })

    let unscheduledWithoutDriverCount = 0
    let unscheduledWithDriverCount = 0
    const jobsWithDates = new Map<string | null, number>()
    const jobsWithPlanName = new Map<string, number>()

    for (const job of data.flatMap((d) => d.jobs)) {
      const planName = match(job)
        .with({ planName: P.nonNullable }, ({ planName }) => planName)
        .with({ planId: P.nonNullable }, ({ planId }) => plansList?.byId[planId]?.name)
        .otherwise(() => undefined)
      if (planName) {
        jobsWithPlanName.set(planName, (jobsWithPlanName.get(planName) || 0) + 1)
      } else if (job.scheduleTypeId === SCHEDULE_TYPE_ID.UNSCHEDULE) {
        if (job.deliveryDriverId || job.driverName) {
          unscheduledWithDriverCount += 1
        } else {
          unscheduledWithoutDriverCount += 1
        }
      } else {
        jobsWithDates.set(
          job.scheduledDeliveryTs,
          (jobsWithDates.get(job.scheduledDeliveryTs) || 0) + 1,
        )
      }
    }

    enqueueSnackbarWithCloseAction(
      <div>
        <Typography
          variant="subtitle2"
          fontWeight={500}
        >
          {ctIntl.formatMessage(
            { id: '{count} jobs imported successfully!' },
            { values: { count } },
          )}
        </Typography>

        {unscheduledWithoutDriverCount > 0 && (
          <Typography variant="body2">
            {ctIntl.formatMessage(
              { id: '{count} unscheduled jobs imported' },
              {
                values: {
                  count: unscheduledWithoutDriverCount,
                },
              },
            )}
            <Button
              variant="text"
              size="small"
              sx={{ color: 'white', p: 0 }}
              onClick={() => {
                setMainPageContextData({
                  ...mainPageContextData,
                  selectedDateRange: null,
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'View' })}
            </Button>
          </Typography>
        )}
        {unscheduledWithDriverCount > 0 && (
          <Typography variant="body2">
            {ctIntl.formatMessage(
              {
                id: `{count} unscheduled jobs imported and assigned to drivers' routes`,
              },
              {
                values: {
                  count: unscheduledWithDriverCount,
                },
              },
            )}
            <Button
              variant="text"
              size="small"
              sx={{ color: 'white', p: 0 }}
              onClick={() => {
                setMainPageContextData({
                  selectedDateRange: null,
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'View' })}
            </Button>
          </Typography>
        )}

        {Array.from(jobsWithDates.entries()).map(([dateStr, count]) => {
          const dateTime = dateStr
            ? DeliveryDateTime.fromISO(dateStr)
            : DeliveryDateTime.now().startOf('day')
          const isToday = !dateStr
          return (
            <Typography
              key={dateStr}
              variant="body2"
            >
              {ctIntl.formatMessage(
                { id: '{count} jobs imported for {date}' },
                {
                  values: {
                    count,
                    date: `${
                      isToday ? ctIntl.formatMessage({ id: 'Today' }) + ', ' : ''
                    }${dateTime.toFormat('D')}`,
                  },
                },
              )}
              <Button
                variant="text"
                size="small"
                sx={{ color: 'white', p: 0 }}
                onClick={() => {
                  setMainPageContextData({
                    selectedDateRange: {
                      start: dateTime.startOf('day'),
                      end: dateTime.endOf('day'),
                    },
                  })
                }}
              >
                {ctIntl.formatMessage({ id: 'View' })}
              </Button>
            </Typography>
          )
        })}

        {Array.from(jobsWithPlanName.entries()).map(([name, count]) => (
          <Typography
            key={name}
            variant="body2"
          >
            {ctIntl.formatMessage(
              { id: '{count} jobs imported for {name}' },
              {
                values: {
                  count,
                  name,
                },
              },
            )}
          </Typography>
        ))}
      </div>,
      { variant: 'success', persist: true },
    )

    if (newSavedTemplate) {
      if (isUpdate) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage(
            {
              id: `Successfully updated ‘{templateName}’`,
            },
            {
              values: {
                templateName: newSavedTemplate.displayName,
              },
            },
          ),
          { variant: 'success' },
        )
      } else {
        enqueueSnackbarWithButtonAction({
          message: ctIntl.formatMessage(
            {
              id: `Successfully saved ‘{templateName}’`,
            },
            {
              values: {
                templateName: newSavedTemplate.displayName,
              },
            },
          ),
          snackBarOptions: { variant: 'success' },
          buttonAction: () => {
            history.push(
              getDeliverySettingsDialogMainPath(location, 'IMPORT_JOB_TEMPLATES'),
            )
          },
          buttonText: ctIntl.formatMessage({ id: 'Open Settings' }),
        })
      }
    }
    setProgress(0)
    setShowModal(false)
  }

  const handleCompleteImport = async () => {
    let dataWithSetup = template.content.stopSetup
      ? parseStopsJobSetup({
          data: data as Array<{ jobs: Array<JobImporterOutPut.Job> }>,
          singleJobSetup: template.content.stopSetup.singleStop,
          doubleJobSetup: template.content.stopSetup.doubleStop,
        })
      : data

    try {
      if (!activeImport?.importType || activeImport?.importType === IMPORT_TYPE.JOB) {
        dataWithSetup = await checkAndParseUnscheduledJobs(dataWithSetup)
      }
    } catch {
      return
    }

    if (template.templateId) {
      if (isEqual(originalTemplate, template)) {
        importMutation(dataWithSetup as Array<{ jobs: Array<JobImporterOutPut.Job> }>)
      } else {
        Dialog.alert({
          hideCloseIcon: true,
          title: ctIntl.formatMessage(
            {
              id: 'Update ‘{templateName}’?',
            },
            {
              values: {
                templateName: template.displayName,
              },
            },
          ),
          content: ctIntl.formatMessage(
            {
              id: `We've noticed some new changes in mapping and/or job setups. Would you like to apply these updates to ‘{templateName}’ for future use?`,
            },
            {
              values: {
                templateName: template.displayName,
              },
            },
          ),
          confirmButtonLabel: ctIntl.formatMessage({
            id: 'Update template & import',
          }),
          rejectButtonLabel: ctIntl.formatMessage({
            id: 'Keep original & import',
          }),
          onResult: () => {
            updateTemplate.mutate(template, {
              onSuccess: (data) => {
                importMutation(
                  dataWithSetup as Array<{
                    jobs: Array<JobImporterOutPut.Job>
                  }>,
                  data,
                  true,
                )
              },
            })
          },
          onClose: () =>
            importMutation(
              dataWithSetup as Array<{ jobs: Array<JobImporterOutPut.Job> }>,
            ),
        })
      }
    } else {
      Dialog.alert({
        withInput: true,
        hideCloseIcon: true,
        inputPlaceholder: ctIntl.formatMessage({
          id: 'Type in a template name',
        }),
        title: ctIntl.formatMessage({ id: 'Save as new template?' }),
        content: (
          <>
            <Typography variant="body2">
              {ctIntl.formatMessage({
                id: 'Save the mapping and job setup as a new template for future imports',
              })}
              .
            </Typography>
            <Typography
              variant="body2"
              sx={{ marginTop: '20px' }}
            >
              {ctIntl.formatMessage({
                id: 'Access your template in settings once it is saved.',
              })}
            </Typography>
          </>
        ),
        confirmButtonLabel: ctIntl.formatMessage({ id: 'Save & import' }),
        rejectButtonLabel: ctIntl.formatMessage({ id: 'Skip & import' }),
        onResult: (displayName: string) => {
          createTemplate.mutate(
            {
              ...template,
              displayName: displayName as string,
            },
            {
              onSuccess: (data) => {
                queryClient.setQueryData(useImportJobTemplateList.createKey(), [
                  data,
                  ...templates,
                ])
                importMutation(
                  dataWithSetup as Array<{
                    jobs: Array<JobImporterOutPut.Job>
                  }>,
                  data,
                )
              },
            },
          )
        },
        onClose: () =>
          importMutation(
            dataWithSetup as Array<{ jobs: Array<JobImporterOutPut.Job> }>,
          ),
      })
    }
  }

  return {
    sheader: (
      <>
        <Typography variant="subtitle2">
          {ctIntl.formatMessage({
            id: 'Specify required to-dos and item deliveries at each job stop for drivers to complete',
          })}
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
        >
          {ctIntl.formatMessage({
            id: 'This setup will serve as a default, but any specifics included in the uploaded',
          })}
          <b>
            {' '}
            {ctIntl.formatMessage({ id: 'Excel sheet will always take precedence' })}
          </b>
        </Typography>
      </>
    ),
    sbody: (
      <Stack sx={{ height: '100%' }}>
        <Stack
          spacing={1}
          sx={{ paddingBottom: '1px' }}
        >
          <JobStopsSetup
            jobTypeId={JOB_TYPE_ID.SINGLE}
            value={template.content.stopSetup.singleStop}
            defaultValue={
              originalTemplate?.content.stopSetup.singleStop || {
                items: [],
                todos: [],
              }
            }
            onChange={(value) => {
              setTemplate(
                produce((draft) => {
                  draft.content.stopSetup.singleStop = value
                }),
              )
            }}
          />
          <JobStopsSetup
            jobTypeId={JOB_TYPE_ID.PD}
            defaultValue={
              originalTemplate?.content.stopSetup.doubleStop || {
                items: [],
                todos: [],
              }
            }
            value={template.content.stopSetup.doubleStop}
            onChange={(value) => {
              setTemplate(
                produce((draft) => {
                  draft.content.stopSetup.doubleStop = value
                }),
              )
            }}
          />
        </Stack>
      </Stack>
    ),
    sfooter: {
      status: {
        type: 'normal',
        message: isShowMissingItemType ? (
          <Stack
            direction="row"
            alignItems="center"
          >
            <WarningAmberOutlinedIcon
              color="warning"
              sx={{ marginRight: '16px' }}
            />
            <Alert
              icon={false}
              severity="warning"
              sx={{
                backgroundColor: 'transparent',
                padding: 0,
              }}
            >
              <Box
                component="span"
                sx={{ fontWeight: 'bold' }}
              >
                {ctIntl.formatMessage({
                  id: `Missing 'package' item types on all jobs`,
                })}
              </Box>
              <br />
              {ctIntl.formatMessage({
                id: `The item types added here should match those item details in your mapped information, or else it may lead to inaccurate job details`,
              })}
            </Alert>
          </Stack>
        ) : (
          ''
        ),
      },
      footerAction: (
        <Stack
          direction="row"
          spacing={1}
          sx={{
            flexShrink: 0,
          }}
        >
          <Button
            variant="outlined"
            color="secondary"
            disabled={isMutating || importState.isSubmit}
            onClick={() => {
              handleSetImportState({
                stepType: 'validation-report',
                uploadState: IMPORT_STATUS_TO_ID.DONE,
                mapDataState: IMPORT_STATUS_TO_ID.DONE,
                validateState: IMPORT_STATUS_TO_ID.DONE,
              })
            }}
          >
            {ctIntl.formatMessage({
              id: 'Previous',
            })}
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (isShowMissingItemType) {
                Dialog.alert({
                  hideCloseIcon: true,
                  title: ctIntl.formatMessage({
                    id: 'Potential job creation without item types & their to-dos',
                  }),
                  content: ctIntl.formatMessage({
                    id: 'It seems there is a mismatch between the indicated item type and the item details in your mapped information. Proceeding could result in job creation without accurate item details. To correct this, edit your items in the job setup step or review your Excel file and mapping to ensure correct types are added and mapped.',
                  }),
                  rejectButtonLabel: ctIntl.formatMessage({ id: 'Back' }),
                  confirmButtonLabel: ctIntl.formatMessage({
                    id: 'Proceed to complete',
                  }),
                  confirmButtonType: 'error',
                  onResult: () => {
                    handleCompleteImport()
                  },
                })
              } else {
                handleCompleteImport()
              }
            }}
            loading={isMutating || importState.isSubmit}
            loadingPosition="start"
            startIcon={<FileUploadOutlinedIcon />}
          >
            {importState.isSubmit
              ? `${ctIntl.formatMessage({
                  id: 'delivery.import.importing',
                })} ${Math.round(progress)}%`
              : ctIntl.formatMessage({
                  id: 'Complete Import',
                })}
          </Button>
        </Stack>
      ),
    },
    errorMessage: footerMessage,
  }
}

export default useDataSubmitting
