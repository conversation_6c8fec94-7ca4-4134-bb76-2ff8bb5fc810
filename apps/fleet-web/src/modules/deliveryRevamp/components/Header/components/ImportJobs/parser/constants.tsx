import { Typography } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

// Some translations have double meaning, so need to reflect to our translation
export const SpecialWordsMap = {
  'Order Number': 'Job Reference Number',
  'Send Date Time': 'Scheduled Delivery Date and Allowed Start',
  'Time Window': 'Scheduled arrival time',
  Duration: 'Scheduled Duration',
  'Item Size': 'Item Dimensions',
  'Item Size Unit': 'Item Dimensions Unit',
  State: 'delivery.import.state',
  Note: 'delivery.import.note',
  'Plan Name': 'Plan/Route Name',
} as const

export const IMPORT_COLUMNS = [
  'Order Number',
  'Customer Name',
  'Phone',
  'Customer ID',
  'Customer Reference',
  'GPS',
  'Lat',
  'Lng',
  'Country Code',
  'Postal Code',
  'Address Line 1',
  'Address Line 2',
  'Tracking Code',
  'City',
  'State',
  'Phone Country Code',
  'Email',
  'Stop Type',
  'Stop No',
  'Send Date Time',
  'Priority',
  'Driver Name',
  'Plan Name',
  'Special Requirements',
  'Note',
  'Time Window',
  'Duration',
  'Stop Todos',
  'Stop Signature',
  'Stop Note',
  'Stop POD',
  'Item Todos',
  'Item Type',
  'Item Name',
  'Item Quantity',
  'Item Weight',
  'Item Weight Unit',
  'Item Size',
  'Item Size Unit',
  'Item Signature',
  'Item POD',
  'Item Note',
  'Job Labels',
  'SKU',
  'UPC',
]

export const CHUNK_LENGTH = 1000 as const

export function translateColumn(term: string) {
  return ctIntl.formatMessage({
    id:
      term in SpecialWordsMap
        ? SpecialWordsMap[term as keyof typeof SpecialWordsMap]
        : term,
  })
}

export function generateDefaultUserConfig(
  uploadedFileColumns: Array<string>,
): Record<string, string> {
  const obj: Record<string, string> = {}
  for (const config of IMPORT_COLUMNS) {
    const foundFileCol = uploadedFileColumns.find((col) =>
      [
        translateColumn(config).trim().toLowerCase(),
        config.trim().toLowerCase(),
        ctIntl.formatMessage({ id: config }),
      ].includes(col.trim().toLowerCase()),
    )
    if (foundFileCol) {
      obj[config] = foundFileCol
    }
  }
  return obj
}

export enum UngroupType {
  TRUE = 'true',
  FALSE = 'false',
  YES = 'yes',
  NO = 'no',
  BLANK = '',
}

// () => ReactNode means conditional. null means required
export const CONDITIONAL_REQUIRED_FIELDS: Record<string, () => React.ReactNode> = {
  'Customer ID': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Map the customer ID (customer ID generated on delivery platform) to retrieve the customer’s name, contact details, and location information from your address book for mapping and import purposes.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, please map the individual fields, including name, phone country code, phone number, email, GPS coordinates (or latitude and longitude), country code, postal code, and address lines 1 and 2. These details are necessary for creating a job.',
        })}
      </Typography>
    </>
  ),
  'Customer Reference': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'If a valid customer reference is entered in this field, the system will automatically retrieve all details from the matching customer record. You can set this field for every customer in your address book.',
        })}
      </Typography>
    </>
  ),
  Phone: () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Map the phone number to enable customers to receive tracking notifications via SMS and facilitate communication for drivers during delivery.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: `Without phone number added, driver can’t contact the customer during delivery. The customer also won’t receive tracking notifications via SMS.`,
        })}
      </Typography>
    </>
  ),
  GPS: () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'GPS coordinates retrieve the exact location for job stops. Map GPS or latitude and longitude independently to pinpoint the exact job stop locations.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, the country code, postal code, address lines 1 and 2 will be used to determine the locations. Without locations, jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  Lat: () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'GPS coordinates retrieve the exact location for job stops. Map GPS or latitude and longitude independently to pinpoint the exact job stop locations.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, the country code, postal code, address lines 1 and 2 will be used to determine the locations. Without locations, jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  Lng: () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'GPS coordinates retrieve the exact location for job stops. Map GPS or latitude and longitude independently to pinpoint the exact job stop locations.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, the country code, postal code, address lines 1 and 2 will be used to determine the locations. Without locations, jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  'Country Code': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Country code helps identify the destination location coordinates. It will be used in conjunction with the postal code or address line 1 and other location information to determine the location coordinates.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Without a country code, we can’t identify a location coordinates properly, especially in cases where GPS coordinates are not present, and jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  'Postal Code': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Postal code helps identify the destination location coordinates in some countries (e.g. Singapore). It will be used in conjunction with the country code and other location information to determine the location coordinates.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, GPS or longitude and latitude coordinates, country code, and address lines 1 and 2 will be used to determine the locations. Without locations, jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  'Address Line 1': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Address line 1 helps identify the destination location coordinates. It will be used in conjunction with the country code and other location information to determine the location coordinates.',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: 'Otherwise, GPS or longitude and latitude coordinates, country code, and address lines 1 and 2 will be used to determine the locations. Without locations, jobs cannot be created.',
        })}
      </Typography>
    </>
  ),
  'Customer Name': () => (
    <>
      <Typography sx={{ fontSize: '11px' }}>
        {ctIntl.formatMessage({
          id: 'Customer names help you and your driver know to whom a job should be delivered',
        })}
      </Typography>
      <Typography sx={{ fontSize: '11px', marginTop: '10px' }}>
        {ctIntl.formatMessage({
          id: "Alternatively, map the customer ID to retrieve the customer's name from your address book.",
        })}
      </Typography>
    </>
  ),
}
