import type {
  ITEM_TYPE_ID,
  JOB_TYPE_ID,
} from '@fleet-web/modules/deliveryRevamp/constants/job'

declare namespace JobImporterOutPut {
  export type Todo = {
    todoTypeId?: number
    stopTypeId?: string | number
    cellLocation?: string | Array<string>
    isRequired?: boolean
    tag?: string
    description?: string
  }

  export type Stop = {
    customerId?: string
    clientReference?: string
    stopTypeId?: number
    stopNo?: number
    rowLocation?: string
    scheduledDeliveryTs?: string | null
    customerName?: string
    addressLine1?: string
    addressLine2?: string
    postalCode?: string | null
    countryId?: string
    email?: string
    contactCode?: string
    contactNumber?: string
    priority?: number
    duration?: number
    deliveryWindows?: Array<{
      timeFrom: string
      timeTo: string
    }>
    note?: string
    saveToAddressBook?: boolean
    todos: Array<Todo>
    latitude?: string
    longitude?: string
  }

  export type Item = {
    description?: string
    weight?: number
    length?: number
    width?: number
    height?: number
    quantity?: number
    rowLocation?: string
    itemTypeId?: ITEM_TYPE_ID
    trackingNumber?: string
    todos: Array<Todo>
    isReplaceable?: boolean // If the item generates by system. If true, it means itemType is empty but filled other item related fields in the Excel
    sku?: string
    upc?: string
  }

  export type Job = {
    deliveryDriverId?: string
    driverName?: string
    planName?: string
    planId?: number
    scheduleTypeId?: number
    jobTypeId?: JOB_TYPE_ID
    rowLocation: string
    scheduledDeliveryTs: string | null
    referenceNumber?: null | string
    stops: Array<Stop>
    items: Array<Item>
    ungroup?: string
    labels?: Array<string>
    requiredCapabilities?: Array<string>
  }
}
declare namespace JobImporterError {
  export type errorType =
    | 'required'
    | 'invalid'
    | 'invalidGeo'
    | 'uniqueRequired'
    | 'existed'
  export type Error = {
    message?: string
    cellLocation?: Array<string>
    columnName?: string
    rowLocation?: string | Array<string>
    errorType: errorType
    rowNumber?: number
  }

  export type AggregatedErrors = {
    [key: string]: Array<Error>
  }
}

declare namespace JobImporterInput {
  export type ParsedObject = {
    [key: string]: any
    orderNumber: string
    driverName?: string
    planName?: string
    sendDateTime: string
    priority?: string
    requiredCapabilities?: string
    specialRequirements?: string
    stopType: string
    stopNo?: string
    stopTodos?: string
    customerName: string
    customerId?: string
    customerReference?: string
    phoneCountryCode?: string
    phone: string
    email: string
    addressLine1: string
    addressLine2: string
    lat?: string
    lng?: string
    city: string
    state: string
    gps: string
    postalCode: string
    countryCode: string
    note: string
    deliveryWindows?: string
    timeWindow?: string
    duration?: string
    stopSignature: string
    stopPod: string
    stopNote: string
    itemType: string
    itemSize: string
    itemSizeUnit: string
    itemName: string
    itemQuantity: string
    itemWeight: string
    itemWeightUnit: string
    trackingCode: string
    itemTodos: string
    itemSignature: string
    itemPod: string
    itemNote: string
    jobLabels: string
    labels: string
    sku: string
    upc: string
  }
}

declare namespace MappingConfig {
  export type Config = {
    column: string
    required: boolean
    excelColumn?: string
  }
}
