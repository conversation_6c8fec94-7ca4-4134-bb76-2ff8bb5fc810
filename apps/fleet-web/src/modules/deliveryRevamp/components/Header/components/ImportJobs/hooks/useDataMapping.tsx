import { useMemo, type Dispatch, type SetStateAction } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from '@karoo-ui/core'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import produce from 'immer'

import type { FetchImportJobTemplate } from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useImportJobTemplateList'
import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { ImportState } from '..'
import EditMapping from '../../../../EditMapping'
import useMappingConfig from '../../../../EditMapping/hooks/useMappingConfig'

type Props = {
  systemConfig: Array<string>
  userfileColumns: Array<string>
  defaultUserConfig: Record<string, string>
  setShowModal: (visible: undefined | boolean) => void
  uploadedFileColumns: Array<string>
  template: FetchImportJobTemplate.Return
  previewExcelData: Record<string, string>
  setImportState: Dispatch<SetStateAction<ImportState>>
  setTemplate: Dispatch<SetStateAction<FetchImportJobTemplate.Return>>
  templates: Array<FetchImportJobTemplate.Return>
  isTemplateCreation?: boolean
}

const useDataMapping = ({
  systemConfig,
  userfileColumns,
  // defaultUserConfig,
  uploadedFileColumns,
  template,
  previewExcelData,
  setImportState,
  setTemplate,
  templates,
  isTemplateCreation,
}: Props) => {
  const originalUserConfig = useMemo(
    () =>
      templates.find((t) => t.templateId === template.templateId)?.content.userSetting
        .config,
    [templates, template],
  )

  const {
    parsedDefaultUserConfig,
    filteredFileColumns,
    userConfig,
    mappedFieldsCount,
    handleFieldUpdate,
    handleResetMissingField,
    handleResetAll,
    isTheTemplateEdited,
  } = useMappingConfig({
    systemConfig,
    userfileColumns,
    defaultUserConfig: originalUserConfig || {},
    uploadedFileColumns,
    invalidMapping: !template.templateId,
    originalUserConfig,
  })
  const Footer = {
    status: {
      type: 'normal',
      message: userConfig['Phone'] ? (
        ''
      ) : (
        <Stack
          direction="row"
          alignItems="center"
        >
          <WarningAmberOutlinedIcon
            color="warning"
            sx={{ marginRight: '8px' }}
          />
          {ctIntl.formatMessage({
            id: `Without phone number added, driver can’t contact the customer during delivery. The customer also won’t receive tracking notifications via SMS.`,
          })}
        </Stack>
      ),
    },
    footerAction: (
      <Button
        variant="contained"
        onClick={() => {
          setTemplate(
            produce((draft) => {
              draft.content.userSetting.fileColumns = uploadedFileColumns
              draft.content.userSetting.config = userConfig
            }),
          )

          if (isTemplateCreation) {
            setImportState({
              stepType: 'data-submitting',
              uploadState: IMPORT_STATUS_TO_ID.DONE,
              mapDataState: IMPORT_STATUS_TO_ID.DONE,
              validateState: IMPORT_STATUS_TO_ID.DONE,
            })
          } else {
            setImportState({
              stepType: 'data-mapping',
              uploadState: IMPORT_STATUS_TO_ID.DONE,
              mapDataState: IMPORT_STATUS_TO_ID.LOADING,
              validateState: IMPORT_STATUS_TO_ID.PENDING,
            })
          }
        }}
        sx={{ textWrap: 'nowrap', flexShrink: 0 }}
        disabled={!mappedFieldsCount}
      >
        {ctIntl.formatMessage(
          { id: 'Import {count} Columns' },
          {
            values: {
              count: mappedFieldsCount,
            },
          },
        )}
      </Button>
    ),
  }

  const Header = (
    <Typography variant="subtitle2">
      {ctIntl.formatMessage({
        id: 'Map delivery fields to the columns in your file for data import',
      })}
    </Typography>
  )

  const Body = (
    <Stack sx={{ height: '100%', paddingBottom: '1px' }}>
      {!!template.templateId && !isTheTemplateEdited && (
        <Alert
          severity="success"
          sx={{ margin: '0 0 16px 0' }}
        >
          <AlertTitle sx={{ fontSize: 'body2.fontSize' }}>
            {ctIntl.formatMessage(
              {
                id: 'Uploaded file matched ‘{templateName}’. {count} columns mapped automatically',
              },
              {
                values: {
                  templateName: template.displayName,
                  count: mappedFieldsCount,
                },
              },
            )}
            .
          </AlertTitle>
          {ctIntl.formatMessage(
            {
              id: 'Any changes made can be updated to ‘{templateName}’ at the end of the import process',
            },
            {
              values: {
                templateName: template.displayName,
              },
            },
          )}
          .
        </Alert>
      )}
      {!template.templateId && !isTemplateCreation && (
        <Alert
          severity="info"
          sx={{ margin: '0 0 16px 0' }}
        >
          <AlertTitle sx={{ fontSize: 'body2.fontSize' }}>
            {ctIntl.formatMessage({
              id: 'New file detected',
            })}
            {'. '}
            {ctIntl.formatMessage({
              id: 'Please map your columns.',
            })}
          </AlertTitle>
          {ctIntl.formatMessage({
            id: 'Mapping can be saved as a template for future use at the end of the import process',
          })}
          .
        </Alert>
      )}
      <EditMapping
        systemConfig={systemConfig}
        parsedDefaultUserConfig={parsedDefaultUserConfig}
        filteredFileColumns={filteredFileColumns}
        userConfig={userConfig}
        mappedFieldsCount={mappedFieldsCount}
        handleFieldUpdate={handleFieldUpdate}
        handleResetMissingField={handleResetMissingField}
        handleResetAll={handleResetAll}
        previewExcelData={previewExcelData}
        isMatchedTemplate={!!template.templateId}
        isTheTemplateEdited={isTheTemplateEdited}
      />
    </Stack>
  )

  return {
    header: Header,
    body: Body,
    footer: Footer,
    isTheTemplateEdited,
  }
}

export default useDataMapping
