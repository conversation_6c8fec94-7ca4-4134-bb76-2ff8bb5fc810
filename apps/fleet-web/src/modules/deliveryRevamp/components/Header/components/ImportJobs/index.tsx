import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import groupBy from 'lodash/groupBy'
import isEmpty from 'lodash/isEmpty'
import {
  But<PERSON>,
  IconButton,
  Stack,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  Stepper,
  styled,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import type { ValueOf } from 'type-fest'
import XLSX from 'xlsx'

import useCreateImportJobTemplate from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useCreateImportJobTemplate'
import type { FetchImportJobTemplate } from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useImportJobTemplateList'
import useUpdateImportJobTemplate from '@fleet-web/modules/deliveryRevamp/api/import-job-template/useUpdateImportJobTemplate'
import useJobImportDeliveryMutation from '@fleet-web/modules/deliveryRevamp/api/jobs/useJobImportDeliveryMutation'
import { useValidateJobImportDeliveryMutation } from '@fleet-web/modules/deliveryRevamp/api/jobs/useValidateJobImportDeliveryMutation'
import BatchImportModal from '@fleet-web/modules/deliveryRevamp/components/BatchImportModal'
import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import Dialog from '@fleet-web/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import {
  extractValidForUploadJobs,
  formatErrorLocation,
  getGroupedErrors,
  getLocaleLanguageForImportTemplate,
} from './helpers'
import useDataImporting from './hooks/useDataImporting'
import useDataMapping from './hooks/useDataMapping'
import useDataSubmittingError from './hooks/useDataSubmitError'
import useDataSubmitting from './hooks/useDataSubmitting'
import useDataUploading, {
  defaultParserResult,
  type ParserResultType,
} from './hooks/useDataUploading'
import useDataValidation from './hooks/useDataValidation'
import parser from './parser'
import { IMPORT_COLUMNS } from './parser/constants'
import type { JobImporterError, JobImporterOutPut } from './parser/types'

type ModalFooterStatus = { message: string; type: string }

type ModalHeaderProps = {
  title?: React.ReactNode | string
  headerAction?: React.ReactNode
}

type ModalFooterProps = {
  status?: ModalFooterStatus
  footerAction?: React.ReactNode
}

export type Step =
  | 'data-upload'
  | 'data-uploading'
  | 'data-mapping'
  | 'data-validating'
  | 'data-submitting'
  | 'edit-mapping'
  | 'validation-report'
  | 'data-submitting-error'
  | 'custom-error'

export type ImportState = {
  stepType?: Step
  importTemplate?: {
    header?: ModalHeaderProps
    body?: React.ReactNode
    footer?: ModalFooterProps
  }
  uploadState?: ValueOf<typeof IMPORT_STATUS_TO_ID>
  mapDataState?: ValueOf<typeof IMPORT_STATUS_TO_ID>
  validateState?: ValueOf<typeof IMPORT_STATUS_TO_ID>
  isSubmit?: boolean
}

type Props = {
  showModal: boolean
  setShowModal: (visible: undefined | boolean) => void
  templates: Array<FetchImportJobTemplate.Return>
  predefinedImportState?: ImportState
  isSpeedyUpload?: boolean
  speedyUploadResult?: ParserResultType
  speedyImportStateHandler?: (importState: any) => void
  speedyErrorFileName?: string
  matchedTemplateFromSpeedyUpload?: FetchImportJobTemplate.Return
}

export type StepperType = {
  steps: Array<Step>
  label: string
}

const STEPPER_ARRAY: Array<StepperType> = [
  { steps: ['data-upload', 'data-uploading'], label: 'Upload' },
  { steps: ['edit-mapping', 'data-mapping'], label: 'Mapping' },
  { steps: ['data-validating', 'validation-report'], label: 'Validation' },
  { steps: ['data-submitting'], label: 'Job Setup' },
] as const

export const DEFAULT_TEMPLATE = {
  templateId: 0,
  displayName: '',
  userId: 0,
  createTs: '',
  updateTs: '',
  content: {
    userSetting: {
      fileColumns: [],
      config: {},
    },
    stopSetup: {
      singleStop: {
        todos: [],
        items: [],
      },
      doubleStop: {
        todos: [],
        items: [],
      },
    },
  },
}

const DeliveryImport = ({
  showModal,
  setShowModal,
  templates,
  isSpeedyUpload,
  predefinedImportState,
  speedyUploadResult,
  speedyImportStateHandler,
  speedyErrorFileName,
  matchedTemplateFromSpeedyUpload,
}: Props) => {
  const createTemplate = useCreateImportJobTemplate()
  const updateTemplate = useUpdateImportJobTemplate()
  const [template, setTemplate] =
    useState<FetchImportJobTemplate.Return>(DEFAULT_TEMPLATE)

  const locale =
    getLocaleLanguageForImportTemplate(localStorage.getItem('locale') || 'en') || ''
  const language = locale && locale?.includes('-') ? locale.split('-')[0] : locale
  const [importState, setImportState] = useState<ImportState>(
    predefinedImportState
      ? predefinedImportState
      : {
          stepType: 'data-upload',
          uploadState: 1,
          mapDataState: 1,
          validateState: 1,
        },
  )
  const [fileParserResult, setFileParserResult] =
    useState<ParserResultType>(defaultParserResult)
  const validateMutation = useValidateJobImportDeliveryMutation()

  useEffect(() => {
    if (isSpeedyUpload && predefinedImportState) {
      setImportState(predefinedImportState)
    }
  }, [predefinedImportState, isSpeedyUpload])

  useEffect(() => {
    if (isSpeedyUpload) {
      setTemplate(matchedTemplateFromSpeedyUpload || DEFAULT_TEMPLATE)
    }
  }, [matchedTemplateFromSpeedyUpload, isSpeedyUpload])

  const handleSetImportState = (importStateUpdates: ImportState) => {
    if (!isSpeedyUpload) {
      setImportState((prevState) => ({
        ...prevState,
        ...importStateUpdates,
      }))
    }
    if (isSpeedyUpload && speedyImportStateHandler) {
      speedyImportStateHandler((prevState: ImportState) => ({
        ...prevState,
        ...importStateUpdates,
      }))
    }
  }

  const {
    uploadHeader,
    uploadBody,
    uploadFooter,
    parsedUploadedResult,
    errorFileName,
    fileName,
  } = useDataUploading({
    importState,
    handleSetImportState,
    systemConfig: IMPORT_COLUMNS,
    parser,
    templatePath: `/assets/templates-delivery/${language}/ImportJobs_V2.0.0.zip`,
    templates: templates || [],
    setTemplate,
  })

  const forValidationErrorFileName = speedyErrorFileName || errorFileName

  const importer = useJobImportDeliveryMutation()

  const frontendErrors = useMemo(() => {
    const parsedErrors =
      fileParserResult.errors &&
      fileParserResult.errors.map((error) => ({
        ...error,
        ...(error &&
          (error.rowNumber || error.rowNumber === 0) && {
            rowNumber: error.rowNumber + 2,
          }),
      }))

    const errors = groupBy(
      parsedErrors || [],
      'errorType',
    ) as JobImporterError.AggregatedErrors

    for (const key of Object.keys(errors)) {
      const value = errors[key]
      errors[key] = value ? getGroupedErrors(value) : []
    }

    return errors
  }, [fileParserResult])

  const {
    header: dataMappingHeader,
    body: dataMappingBody,
    footer: dataMappingFooter,
    isTheTemplateEdited,
  } = useDataMapping({
    systemConfig: IMPORT_COLUMNS,
    userfileColumns:
      template && !isEmpty(template.content.userSetting)
        ? template.content.userSetting.fileColumns
        : [],
    defaultUserConfig:
      template && !isEmpty(template.content.userSetting)
        ? template.content.userSetting.config
        : {},
    setShowModal,
    uploadedFileColumns: fileParserResult.columns,
    template,
    previewExcelData: fileParserResult?.rawData?.[0] || {},
    setTemplate,
    setImportState,
    templates: templates || [],
  })

  const extractvalidForUploads = (
    data: Array<{ jobs: Array<JobImporterOutPut.Job> }>,
    uniqueErrorRows: Array<string>,
  ): Array<{ jobs: Array<JobImporterOutPut.Job> }> =>
    extractValidForUploadJobs(
      data as Array<{ jobs: Array<JobImporterOutPut.Job> }>,
      uniqueErrorRows,
    )

  //Fetch rowLocation of errors
  const { errors, data, rawData, columns } = fileParserResult
  const uniqueErrorRows: Array<string> = []

  for (const err of errors) {
    if (err && err.rowLocation) {
      uniqueErrorRows.push(...formatErrorLocation(err.rowLocation))
    }
    if (err && err.cellLocation) {
      uniqueErrorRows.push(...formatErrorLocation(err.cellLocation))
    }
    if (err && err.rowNumber) {
      const mapRowNumberToExcel = err.rowNumber + 1
      uniqueErrorRows.push(mapRowNumberToExcel.toString())
    }
  }

  const validForUploadData = extractvalidForUploads(data, [...new Set(uniqueErrorRows)])

  //Export error data to .xlxs file
  const handleExportErrors = () => {
    //Build for export error data
    const exportErrorData =
      rawData &&
      rawData.filter((raw: Record<string, any>) =>
        uniqueErrorRows.includes(String(raw.__rowNum__ + 1)),
      )

    if (exportErrorData && exportErrorData.length > 0) {
      const exportData = exportErrorData.map((errData: Record<string, any>) => {
        let obj = {}
        for (const [idx] of Object.values(errData).entries()) {
          if (columns[idx]) {
            obj = {
              ...obj,
              [columns[idx]]: errData[columns[idx]],
            }
          }
        }
        return obj
      })

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, `Sheet1`)
      XLSX.writeFile(workbook, `ERROR-${forValidationErrorFileName}.xlsx`)
    }
  }

  const {
    header: dataValidationHeader,
    body: dataValidationBody,
    footer: dataValidationFooter,
  } = useDataValidation({
    setShowModal,
    missingData: frontendErrors['required'] || [],
    invalidData: frontendErrors['invalid'] || [],
    duplicatedData: frontendErrors['uniqueRequired'] || [],
    alreadyExistedData: frontendErrors['existed'] || [],
    invalidGeoError: frontendErrors['invalidGeo'] || [],
    handleSetImportState,
    isSpeedyUpload,
    parsedResultData: fileParserResult,
    validForUploadData,
    fileName: speedyErrorFileName || fileName,
    isError: Object.keys(frontendErrors).length > 0,
  })

  const { iheader, ibody, ifooter, uploadError, parsedResult } = useDataImporting({
    setShowModal,
    importState,
    handleSetImportState,
    validateMutation,
    fileParserResult,
    parser,
    template,
    setTemplate,
    fileName: speedyErrorFileName || fileName,
    isTheTemplateEdited,
  })

  const { sheader, sfooter, sbody, errorMessage } = useDataSubmitting({
    setShowModal,
    handleSetImportState,
    importState,
    count: validForUploadData.length,
    total: fileParserResult.data.length,
    data: validForUploadData.length > 0 ? validForUploadData : fileParserResult.data,
    importApi: importer,
    template,
    setTemplate,
    createTemplate,
    updateTemplate,
    templates: templates || [],
    handleExportErrors,
  })

  const { submitErrorFooter } = useDataSubmittingError({
    setShowModal,
    handleSetImportState,
    error: uploadError || errorMessage,
  })

  useEffect(() => {
    if (
      importState.uploadState === IMPORT_STATUS_TO_ID.DONE &&
      importState.mapDataState === IMPORT_STATUS_TO_ID.DONE
    ) {
      setFileParserResult(parsedResult)
    } else if (isSpeedyUpload && speedyUploadResult) {
      setFileParserResult(speedyUploadResult)
    } else {
      setFileParserResult(parsedUploadedResult)
    }
  }, [
    speedyUploadResult,
    isSpeedyUpload,
    parsedResult,
    parsedUploadedResult,
    importState,
  ])

  const { footer, header, body } = useMemo(
    () => {
      switch (importState.stepType) {
        case 'edit-mapping': {
          return {
            footer: dataMappingFooter,
            header: dataMappingHeader,
            body: dataMappingBody,
          }
        }
        case 'validation-report': {
          return {
            footer: dataValidationFooter,
            header: dataValidationHeader,
            body: dataValidationBody,
          }
        }
        case 'data-upload': {
          return {
            footer: uploadFooter,
            header: uploadHeader,
            body: uploadBody,
          }
        }
        case 'data-uploading':
        case 'data-mapping':
        case 'data-validating': {
          return {
            footer: ifooter,
            header: uploadHeader,
            body: ibody,
          }
        }
        case 'data-submitting': {
          return {
            footer: sfooter,
            header: sheader,
            body: sbody,
          }
        }
        case 'data-submitting-error': {
          return {
            footer: submitErrorFooter,
            header: iheader,
            body: ibody,
          }
        }
        case 'custom-error': {
          return {
            footer: {
              status: importState.importTemplate?.footer?.status,
              footerAction: (
                <Stack
                  direction="row"
                  spacing={1}
                >
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={() => setShowModal(false)}
                  >
                    {ctIntl.formatMessage({
                      id: 'delivery.import.cancel',
                    })}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => {
                      if (!isSpeedyUpload) {
                        handleSetImportState({
                          stepType: 'data-upload',
                          uploadState: 1,
                          mapDataState: 1,
                          validateState: 1,
                        })
                      }
                      if (isSpeedyUpload) {
                        setShowModal(false)
                      }
                    }}
                  >
                    {ctIntl.formatMessage({ id: 'Reupload' })}
                  </Button>
                </Stack>
              ),
            },
            header: iheader,
            body: ibody,
          }
        }
        default: {
          return {
            footer: uploadFooter,
            header: uploadHeader,
            body: uploadBody,
          }
        }
      }
    },
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [importState.stepType, importState.isSubmit, dataMappingBody, dataMappingFooter],
  )

  return (
    showModal && (
      <BatchImportModal
        header={{
          title: ctIntl.formatMessage({ id: 'Import jobs' }),
          headerAction: (
            <IconButton
              onClick={() => {
                if (importState.stepType === 'data-upload') {
                  setShowModal(false)
                  return
                }
                Dialog.alert({
                  title: ctIntl.formatMessage({ id: 'Discard unsaved changes?' }),
                  content: ctIntl.formatMessage({
                    id: 'This page contains unsaved changes. Do you still wish to leave?',
                  }),
                  confirmButtonLabel: ctIntl.formatMessage({ id: 'Discard and Leave' }),
                  rejectButtonLabel: ctIntl.formatMessage({ id: 'Back' }),
                  onResult: () => {
                    validateMutation.abort()
                    setShowModal(false)
                  },
                })
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          ),
        }}
        stepper={
          <>
            <StyledStepper
              activeStep={STEPPER_ARRAY.findIndex(
                (step) =>
                  importState.stepType && step.steps.includes(importState.stepType),
              )}
            >
              {STEPPER_ARRAY.map((step) => (
                <Step key={step.label}>
                  <StepLabel>{ctIntl.formatMessage({ id: step.label })}</StepLabel>
                </Step>
              ))}
            </StyledStepper>
            {header && <Stack sx={{ marginTop: '16px' }}>{header}</Stack>}
          </>
        }
        body={body}
        footer={footer}
      />
    )
  )
}

export default DeliveryImport

export const StyledStepper = styled(Stepper)(({ theme }) => ({
  marginTop: '10px',
  '& .MuiStep-root:first-child': {
    paddingLeft: 0,
  },
  '& .MuiStepLabel-iconContainer > .Mui-completed': {
    color: theme.palette.success.main,
  },
}))
