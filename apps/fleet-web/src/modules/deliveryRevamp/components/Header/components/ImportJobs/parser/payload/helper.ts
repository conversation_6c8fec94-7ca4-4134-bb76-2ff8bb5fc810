import { format, set } from 'date-fns'

import {
  PRIORITY_OPTIONS,
  TODO_TYPE_ID,
} from '@fleet-web/modules/deliveryRevamp/constants/job'

import type { JobImporterInput } from '../types'

export const jobTypeVerify = (
  pickUpArr: Array<JobImporterInput.ParsedObject>,
  dropOffArr: Array<JobImporterInput.ParsedObject>,
) => {
  const isPickUpDropOff = pickUpArr.length > 0 && pickUpArr.length === 1
  const isSingleJobPickupDropOff = isPickUpDropOff && dropOffArr.length === 1

  const isMultiJobPickupDropOff = isPickUpDropOff && dropOffArr.length > 1

  const isSingleStopJob = !isPickUpDropOff && dropOffArr.length === 1
  const isMultiJobSingleStop = !isPickUpDropOff && dropOffArr.length > 1

  return {
    isPickUpDropOff,
    isSingleJobPickupDropOff,
    isMultiJobPickupDropOff,
    isSingleStopJob,
    isMultiJobSingleStop,
  }
}

export const getTodoTypeId = (todoTypeId: TODO_TYPE_ID) =>
  Object.values(TODO_TYPE_ID).includes(+todoTypeId)
    ? +todoTypeId
    : TODO_TYPE_ID.SIGNATURE

export const getPriority = (value: string | undefined) => {
  const REGULAR =
    PRIORITY_OPTIONS.find((priority) => priority.label === 'Regular')?.value || 5
  if (!value) {
    return REGULAR
  } else {
    const priorityFromValue = PRIORITY_OPTIONS.find(
      (priority) => priority.label.toLowerCase() === value.toString().toLowerCase(),
    )?.value
    return priorityFromValue ? priorityFromValue : REGULAR
  }
}

/** This accepts up to four values seperated by comma
 * e.g. 2:30PM, 4:30PM
 * If only one value was provided, then the time window will be startTime-startTime
 * If two values were provided, then they will be treated as startTime-endTime.
 * */
export const getDeliveryWindows = (windows: string | undefined) => {
  if (!windows?.trim()) {
    return []
  } else {
    const splittedTime = windows
      .replaceAll(' ', '')
      .split(',')
      .map((time) => formatTime(time))
    if (splittedTime.length === 1) {
      return [
        {
          timeFrom: splittedTime[0],
          timeTo: splittedTime[0],
        },
      ]
    } else if (splittedTime.length > 1) {
      return [
        {
          timeFrom: splittedTime[0],
          timeTo: splittedTime[1],
        },
      ]
    } else {
      return []
    }
  }
}

// Change 12:30AM to 00:30:08+00 format if GMT+8 Singapore Time
const formatTime = (time: string) => {
  const timeSplitted = time.split('')
  const checkAMorPM = timeSplitted.slice(timeSplitted.length - 2).join('')
  const isAM = checkAMorPM.toUpperCase() === 'AM'
  const isPM = checkAMorPM.toUpperCase() === 'PM'

  // eslint-disable-next-line sonarjs/concise-regex
  const regexForTimeWithMin = /^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?/g
  // eslint-disable-next-line sonarjs/concise-regex
  const regexForTimeNoMin = /^([0-9]|0[0-9]|1[0-9]|2[0-3])/g
  const matchingRegexForTime =
    time.match(regexForTimeWithMin)?.slice(0, 1).shift() ||
    time.match(regexForTimeNoMin)?.slice(0, 1).shift() ||
    '23:59:00'
  const [hour = 0, minute = 0] = matchingRegexForTime.split(':')
  let hourOffset = 0
  if (isAM) {
    hourOffset = +hour > 11 ? -12 : hourOffset //if 12:30AM change to 00:30
  } else if (isPM && +hour < 12) {
    hourOffset = 12
  }
  return format(
    set(new Date(), {
      hours: +hour + hourOffset,
      minutes: +minute,
      seconds: 0,
    }),
    'HH:mm:ssxxx',
  )
}
