import { forwardRef, useCallback, useImperative<PERSON>and<PERSON>, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Popper, Slide } from '@karoo-ui/core'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'

import useAssignJob from '@fleet-web/modules/deliveryRevamp/api/jobs/useAssignJob'
import {
  DROPPABLE_TYPE,
  useDndMonitor,
} from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryDndJobsContext'
import { useDriverMigrationBanner } from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryDriverMigrationBannerContext'
import { useSwitchVersion } from '@fleet-web/modules/deliveryRevamp/contexts/DeliverySwitchVersionContext'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { DELIVERY_DRIVER_MIGRATION_BANNER_HEIGHT_IN_PX } from '../../../DriverMigrationBanner'
import { FOOTER_HEIGHT_IN_PX } from '../../../Footer/constants'
import { DELIVERY_VERSION_SWITCH_FRAME_HEIGHT_IN_PX } from '../../../VersionSwitchFrame'
import { HEADER_HEIGHT_IN_PX } from '../../constants'
import RecurringList from './components/RecurringList'

type Props = {
  anchor?: HTMLElement | null
  onOpen?: () => void
}

export type RecurringPopperHandles = {
  close: () => void
}

const RecurringPopper = forwardRef<RecurringPopperHandles, Props>(
  ({ anchor, onOpen }, ref) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
    const { shouldDisplaySwitchFrame } = useSwitchVersion()
    const { isDriverMigrationBannerDisplayed } = useDriverMigrationBanner()

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
      const el = anchorEl ? null : anchor || event.currentTarget
      setAnchorEl(el)
      if (el) onOpen?.()
    }
    const open = Boolean(anchorEl)

    const close = useCallback(() => {
      setAnchorEl(null)
    }, [])

    useImperativeHandle(ref, () => ({
      close,
    }))

    const { mutate } = useAssignJob()

    useDndMonitor({
      onDragEnd({ over, active }) {
        const activeData = active.data.current
        const overData = over?.data.current
        if (activeData && overData && overData.type === DROPPABLE_TYPE.RECURRING_LIST) {
          mutate({
            routeId: `plan_${overData.plan.planId}`,
            jobIds: activeData.jobIdsWillBeAssigned,
          })
        }
      },
    })

    return (
      <>
        <Button
          startIcon={<LoopOutlinedIcon sx={{ fontSize: 'inherit' }} />}
          color="secondary"
          variant="text"
          onClick={handleClick}
        >
          {ctIntl.formatMessage({ id: 'Manage Recurring' })}
        </Button>
        <Popper
          open={open}
          anchorEl={anchorEl}
          transition
          placement="bottom-end"
          sx={(theme) => ({ zIndex: theme.zIndex.drawer })}
          modifiers={[
            {
              name: 'flip',
              enabled: false,
            },
          ]}
        >
          {({ TransitionProps }) => (
            <Slide
              {...TransitionProps}
              direction="left"
            >
              <Card
                sx={{
                  height: `calc(100vh - ${FOOTER_HEIGHT_IN_PX}px - ${HEADER_HEIGHT_IN_PX}px - ${
                    shouldDisplaySwitchFrame
                      ? DELIVERY_VERSION_SWITCH_FRAME_HEIGHT_IN_PX
                      : 0
                  }px - ${
                    isDriverMigrationBannerDisplayed
                      ? DELIVERY_DRIVER_MIGRATION_BANNER_HEIGHT_IN_PX
                      : 0
                  }px)`,
                  width: '320px',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <RecurringList onClose={handleClick} />
              </Card>
            </Slide>
          )}
        </Popper>
      </>
    )
  },
)

export default RecurringPopper
