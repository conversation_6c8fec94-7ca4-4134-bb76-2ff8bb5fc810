import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  type DateRange,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import FlashOnOutlinedIcon from '@mui/icons-material/FlashOnOutlined'
import PersonOutlinedIcon from '@mui/icons-material/PersonOutlined'
import type { DateTime } from 'luxon'

import { useExportDeliveryDrivers } from '@fleet-web/modules/deliveryRevamp/api/drivers/useExportDrivers'
import { useDeliveryTableExportReportMutation } from '@fleet-web/modules/deliveryRevamp/Pages/Table/DataGrid/components/ReportsDownload/useDeliveryTableExportReportMutation'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { DeliveryDateTime } from '../../../../utils/deliveryDateTime'
import { ASSIGN_STATUS } from '../../../Filters/filtersSchemas'
import ReportItem from './ReportItem'

type Props = {
  onClose: () => void
}

const ExportButton: React.FC<Props> = ({ onClose }) => {
  const [jobDateRange, setJobDateRange] = useState<DateRange<DateTime>>([
    DeliveryDateTime.now(),
    DeliveryDateTime.now(),
  ])

  const [driverDateRange, setDriverDateRange] = useState<DateRange<DateTime>>([
    DeliveryDateTime.now(),
    DeliveryDateTime.now(),
  ])

  const exportDeliveryJobsMutation = useDeliveryTableExportReportMutation()
  const exportDriverMutation = useExportDeliveryDrivers()

  const handleDownloadJobs = () => {
    if (!jobDateRange[0]?.isValid || !jobDateRange[1]?.isValid) return

    const url = window.location.href
    const payload = {
      filters: {
        scheduledDeliveryTs: {
          from: jobDateRange[0].startOf('day').toISO({
            suppressMilliseconds: true,
          }),
          to: jobDateRange[1]
            .endOf('day')
            .startOf('second')
            .toISO({ suppressMilliseconds: true }),
        },
        assignment: ASSIGN_STATUS.ALL,
        groupStops: true,
      },
      sort: {},
      returnData: false,
      url: url.includes('/delivery') ? url.substring(0, url.length - 9) : url,
    }

    exportDeliveryJobsMutation.mutate(payload)
  }

  const handleDownloadDrivers = () => {
    if (!driverDateRange[0]?.isValid || !driverDateRange[1]?.isValid) return

    const payload = {
      filters: {
        scheduledDeliveryTs: {
          from: driverDateRange[0].startOf('day').toISO({
            suppressMilliseconds: true,
          }),
          to: driverDateRange[1]
            .endOf('day')
            .startOf('second')
            .toISO({ suppressMilliseconds: true }),
        },
      },
      order: {
        createTs: 'desc',
      },
    }

    exportDriverMutation.mutate(payload)
  }

  const handleCloseModal = () => {
    onClose()
  }

  return (
    <Dialog
      open
      onClose={handleCloseModal}
      fullWidth
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        {ctIntl.formatMessage({
          id: 'deliver.download.report',
        })}
        <IconButton
          size="small"
          onClick={handleCloseModal}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Stack
          display="flex"
          justifyContent="center"
          direction="column"
        >
          <ReportItem
            label={ctIntl.formatMessage({
              id: 'deliver.download.jobReport',
            })}
            handleOnDateChange={(value) => setJobDateRange(value)}
            dateRange={jobDateRange}
            handleDownload={handleDownloadJobs}
            Icon={FlashOnOutlinedIcon}
            isLoading={exportDeliveryJobsMutation.isPending}
          />
          <ReportItem
            label={ctIntl.formatMessage({
              id: 'deliver.download.driverReport',
            })}
            handleOnDateChange={(value) => setDriverDateRange(value)}
            dateRange={driverDateRange}
            handleDownload={handleDownloadDrivers}
            Icon={PersonOutlinedIcon}
            isLoading={exportDriverMutation.isPending}
          />
        </Stack>
      </DialogContent>
    </Dialog>
  )
}

export default ExportButton
