import { Button, Stack } from '@karoo-ui/core'
import RefreshIcon from '@mui/icons-material/Refresh'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { ImportState } from '..'

type Props = {
  setShowModal: (visible: undefined | boolean) => void
  handleSetImportState: (importState: ImportState) => void
  error: string
  isSpeedyUpload?: boolean
}

const useDataSubmittingError = ({
  setShowModal,
  handleSetImportState,
  error,
  isSpeedyUpload,
}: Props) => {
  const submitFooter = {
    status: {
      message: error,
      type: 'normal',
    },
    footerAction: (
      <Stack
        direction="row"
        spacing={1}
      >
        <Button
          color="secondary"
          onClick={() => {
            setShowModal?.(false)
          }}
        >
          {ctIntl.formatMessage({
            id: 'Cancel',
          })}
        </Button>
        <Button
          variant="outlined"
          color="secondary"
          startIcon={<RefreshIcon />}
          onClick={() => {
            if (isSpeedyUpload) {
              setShowModal?.(false)
            }

            if (!isSpeedyUpload) {
              handleSetImportState({
                stepType: 'data-upload',
                uploadState: 3,
                mapDataState: 3,
                validateState: 2,
              })
            }
          }}
        >
          {ctIntl.formatMessage({
            id: 'Restart',
          })}
        </Button>
      </Stack>
    ),
  }

  return {
    submitErrorFooter: submitFooter,
  }
}

export default useDataSubmittingError
