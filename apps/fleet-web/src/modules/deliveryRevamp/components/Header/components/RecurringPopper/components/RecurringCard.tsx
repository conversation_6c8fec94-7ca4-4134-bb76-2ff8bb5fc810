import { useMemo, useState } from 'react'
import {
  Checkbox,
  CircularProgressD<PERSON>yed,
  IconButton,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  OverflowTypography,
  Stack,
  Tooltip,
  Typography,
  type MenuItemProps,
} from '@karoo-ui/core'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import KeyboardDoubleArrowRightOutlinedIcon from '@mui/icons-material/KeyboardDoubleArrowRightOutlined'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import PersonAddAltOutlinedIcon from '@mui/icons-material/PersonAddAltOutlined'
import PersonOutlinedIcon from '@mui/icons-material/PersonOutlined'
import WarningIcon from '@mui/icons-material/Warning'
import WarningOutlinedIcon from '@mui/icons-material/WarningOutlined'
import { useHistory, useLocation } from 'react-router'
import { match, P } from 'ts-pattern'

import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { useCachedDriversList } from '@fleet-web/modules/deliveryRevamp/api/drivers/useDriversList'
import type { PlanDetailReturn } from '@fleet-web/modules/deliveryRevamp/api/plans/types'
import { useConfirmDeliveryPlanMutation } from '@fleet-web/modules/deliveryRevamp/api/plans/useConfirmPlanMutation'
import { useDeleteDeliveryPlanMutation } from '@fleet-web/modules/deliveryRevamp/api/plans/useDeletePlanMutation'
import Popover from '@fleet-web/modules/deliveryRevamp/components/Popover'
import { getDeliveryRecurringDialogMainPath } from '@fleet-web/modules/deliveryRevamp/components/RecurringDialog/helpers'
import DotDivider from '@fleet-web/modules/deliveryRevamp/components/RouteCard/component/DotDivider'
import { RepeatDays } from '@fleet-web/modules/deliveryRevamp/components/RouteCard/component/RecurringTooltip'
import { FORM_STATE } from '@fleet-web/modules/deliveryRevamp/constants/job'
import {
  DROPPABLE_TYPE,
  useDroppable,
} from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryDndJobsContext'
import Dialog from '@fleet-web/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { useRefreshDeliveryData } from '@fleet-web/modules/deliveryRevamp/hooks/useRefresh'
import { DeliveryDateTime } from '@fleet-web/modules/deliveryRevamp/utils/deliveryDateTime'
import { getConfirmPlanError } from '@fleet-web/modules/deliveryRevamp/utils/error-message-translation-keys'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type Props = {
  plan: PlanDetailReturn
  withDroppable?: boolean
  hideMenu?: boolean
  selected?: boolean
  onClick?: () => void
  showCheckbox?: boolean
  hideNextRun?: boolean
}

export default function RecurringCard({
  plan,
  withDroppable,
  hideMenu,
  selected,
  onClick,
  showCheckbox,
  hideNextRun,
}: Props) {
  const location = useLocation()
  const history = useHistory()
  const [isHover, setIsHover] = useState(false)

  const driverLists = useCachedDriversList()
  const { refresh } = useRefreshDeliveryData()
  const deletePlanMutation = useDeleteDeliveryPlanMutation()
  const assignNowMutation = useConfirmDeliveryPlanMutation()

  const handleHoverInDriver = () => {
    setIsHover(true)
  }

  const handleHoverOutDriver = () => {
    setIsHover(false)
  }

  const { setNodeRef, isOver } = useDroppable({
    id: plan.planId,
    disabled: !withDroppable,
    data: {
      type: DROPPABLE_TYPE.RECURRING_LIST,
      plan,
    },
  })

  const showRecurrenceExpired = useMemo(() => {
    if (!plan.recurrence.dtstart) return false

    if (!plan.nextOccurrence) return true

    const isExplicitlyExpired =
      plan.isRecurrenceExpired &&
      (!plan.targetDriverId || plan.orderedJobIds.length === 0)

    if (isExplicitlyExpired) return true

    const now = DeliveryDateTime.now()
    const nextOccurrence = DeliveryDateTime.fromJSDate(new Date(plan.nextOccurrence))
    const createdOrUpdatedAt = DeliveryDateTime.fromJSDate(
      new Date(plan.updateTs || plan.createTs),
    )

    const isSameDay = nextOccurrence.hasSame(now, 'day')
    const isInPast = nextOccurrence < now

    if (!isSameDay && isInPast) return true

    if (isSameDay && isInPast) {
      const wasCreatedAfter = createdOrUpdatedAt > nextOccurrence
      const twoMinutesPassed = createdOrUpdatedAt.plus({ minutes: 2 }) < now
      return !wasCreatedAfter || twoMinutesPassed
    }

    return false
  }, [
    plan.updateTs,
    plan.createTs,
    plan.targetDriverId,
    plan.nextOccurrence,
    plan.recurrence.dtstart,
    plan.isRecurrenceExpired,
    plan.orderedJobIds.length,
  ])

  const isDisabledAssignNow = useMemo(
    () =>
      assignNowMutation.isPending ||
      showRecurrenceExpired ||
      !plan.targetDriverId ||
      plan.orderedJobIds.length === 0,
    [
      showRecurrenceExpired,
      plan.targetDriverId,
      plan.orderedJobIds.length,
      assignNowMutation.isPending,
    ],
  )

  const planDriverInfo = useMemo(() => {
    if (!driverLists || !plan.targetDriverId) return null
    return driverLists.byId[plan.targetDriverId]
  }, [driverLists, plan.targetDriverId])

  const menuItems: Array<MenuItemProps> = useMemo(
    () => [
      {
        key: 'assignNow',
        disabled: isDisabledAssignNow,
        children: (
          <Stack
            direction="row"
            alignItems="center"
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              {assignNowMutation.isPending ? (
                <CircularProgressDelayed size={20} />
              ) : (
                <PersonAddAltOutlinedIcon />
              )}
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'delivery.assignNowRecurringRoute' })}
            </ListItemText>
          </Stack>
        ),
        onClick: () =>
          assignNowMutation.mutate(
            { planId: plan.planId },
            {
              onSuccess: () => {
                enqueueSnackbarWithCloseAction(
                  ctIntl.formatMessage({
                    id: 'delivery.assignNowRecurringRoute.successful',
                  }),
                  { variant: 'success' },
                )
                refresh()
              },
              onError: (...args) => {
                const errorKey = args[0].message
                enqueueSnackbarWithCloseAction(
                  ctIntl.formatMessage({
                    id: getConfirmPlanError(errorKey),
                  }),
                  { variant: 'error' },
                )
              },
            },
          ),
      },
      {
        key: 'duplicateRecurring',
        children: (
          <Stack
            direction="row"
            alignItems="center"
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              <ContentCopyIcon />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'global.duplicate' })}
            </ListItemText>
          </Stack>
        ),
        onClick: () =>
          history.push(
            getDeliveryRecurringDialogMainPath(location, {
              formParams: {
                existingFormField: { ...plan, formState: FORM_STATE.DUPLICATE },
                additionalFormField: {
                  jobIds: plan.orderedJobIds,
                },
              },
            }),
          ),
      },
      {
        key: 'deleteRecurring',
        children: (
          <ListItemText sx={{ color: 'error.main' }}>
            {ctIntl.formatMessage({ id: 'delivery.deleteRecurringRoute' })}
          </ListItemText>
        ),
        onClick: () =>
          Dialog.alert({
            title: ctIntl.formatMessage({
              id: 'delivery.deleteRecurringRoute.confirmation.title',
            }),
            content: ctIntl.formatMessage({
              id: 'delivery.deleteRecurringRoute.confirmation.body',
            }),
            confirmButtonLabel: ctIntl.formatMessage({ id: 'Confirm' }),
            rejectButtonLabel: ctIntl.formatMessage({ id: 'Cancel' }),
            onResult: () => {
              deletePlanMutation.mutate(
                { planId: plan.planId },
                {
                  onSuccess() {
                    refresh()
                  },
                },
              )
            },
          }),
      },
    ],
    [
      refresh,
      plan,
      history,
      location,
      assignNowMutation,
      deletePlanMutation,
      isDisabledAssignNow,
    ],
  )

  return (
    <ListItemButton
      ref={setNodeRef}
      disableGutters
      divider
      sx={{
        py: 0,
        overflow: 'hidden',
        ...(isOver && {
          backgroundColor: 'action.hover',
        }),
      }}
      onMouseEnter={handleHoverInDriver}
      onMouseLeave={handleHoverOutDriver}
      onClick={() => {
        if (onClick) {
          onClick()
        } else {
          history.push(
            getDeliveryRecurringDialogMainPath(location, { id: plan.planId }),
          )
        }
      }}
      selected={selected}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        gap={1}
        flexGrow={1}
        sx={{ py: 1, px: 2, overflow: 'hidden' }}
      >
        <Stack
          direction="row"
          alignItems="center"
          gap={1}
          sx={{ overflow: 'hidden' }}
        >
          {showCheckbox && (
            <Checkbox
              checked={selected}
              disableRipple
              size="small"
            />
          )}
          <Tooltip title={ctIntl.formatMessage({ id: 'Recurring route' })}>
            <LoopOutlinedIcon
              sx={{
                fontSize: 16,
                color: 'text.secondary',
                visibility: plan.recurrence.freq ? 'visible' : 'hidden',
              }}
            />
          </Tooltip>
          <Stack sx={{ overflow: 'hidden' }}>
            <Typography variant="body2">{plan.name}</Typography>
            <OverflowTypography>
              {({ renderedIn }) => {
                const notInTooltip = renderedIn !== 'tooltip'
                return (
                  <>
                    <Typography
                      variant="caption"
                      sx={{
                        ...(notInTooltip && { color: 'text.secondary' }),
                        textTransform: 'lowercase',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {`${plan.orderedJobIds?.length ?? 0} ${ctIntl.formatMessage({
                        id: 'Jobs',
                      })}`}
                    </Typography>

                    {plan.recurrence.freq && (
                      <>
                        <DotDivider />
                        <RepeatDays
                          recurrence={plan.recurrence}
                          isDarkBackground={!notInTooltip}
                        />
                      </>
                    )}
                  </>
                )
              }}
            </OverflowTypography>
            {plan.scheduledTime &&
              plan.orderedJobIds.length > 0 &&
              plan.targetDriverId &&
              plan.nextOccurrence &&
              new Date(plan.nextOccurrence) > new Date() &&
              !hideNextRun && (
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={0.5}
                >
                  <KeyboardDoubleArrowRightOutlinedIcon
                    sx={{ fontSize: 12, color: 'text.secondary' }}
                  />
                  <Typography
                    variant="caption"
                    sx={{ color: 'text.secondary' }}
                  >
                    {`${ctIntl.formatMessage({
                      id: 'Next run',
                    })}: ${DeliveryDateTime.fromJSDate(
                      new Date(plan.nextOccurrence),
                    ).toFormat('D ccc')}`}
                  </Typography>
                </Stack>
              )}
            {showRecurrenceExpired && (
              <Tooltip
                title={match(plan)
                  .with({ orderedJobIds: [] }, () =>
                    ctIntl.formatMessage({
                      id: 'The recurring start date is overdue, and the process has not started because there’s no jobs added',
                    }),
                  )
                  .with({ targetDriverId: P.nullish }, () =>
                    ctIntl.formatMessage({
                      id: 'The recurring start date is overdue, and the process has not started because there’s no driver assigned. Please assign a driver or change the start date.',
                    }),
                  )
                  .otherwise(() =>
                    ctIntl.formatMessage({
                      id: 'This recurring route stopped running due to a technical issue. Please contact support for further investigation.',
                    }),
                  )}
              >
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={0.5}
                >
                  <WarningOutlinedIcon sx={{ fontSize: 12, color: 'error.main' }} />
                  <Typography
                    variant="caption"
                    sx={{ color: 'error.main' }}
                  >
                    {match(plan)
                      .with(
                        {
                          orderedJobIds: [],
                        },
                        {
                          targetDriverId: P.nullish,
                        },
                        () =>
                          ctIntl.formatMessage({
                            id: 'Overdue',
                          }),
                      )
                      .otherwise(() =>
                        ctIntl.formatMessage({
                          id: 'Stopped running – Error',
                        }),
                      )}
                  </Typography>
                </Stack>
              </Tooltip>
            )}
            {!plan.scheduledTime && (
              <Tooltip
                title={ctIntl.formatMessage({
                  id: `The recurring start date is not set, so the route hasn't begun. Please enter a start date.`,
                })}
              >
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={0.5}
                >
                  <WarningIcon sx={{ fontSize: 14, color: 'warning.main' }} />
                  <Typography
                    variant="caption"
                    sx={{ color: 'warning.main' }}
                  >
                    {ctIntl.formatMessage({ id: 'Unscheduled' })}
                  </Typography>
                </Stack>
              </Tooltip>
            )}
            <Stack
              direction="row"
              alignItems="center"
            >
              {planDriverInfo && (
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={0.5}
                >
                  <PersonOutlinedIcon sx={{ fontSize: 12, color: 'text.secondary' }} />
                  <Typography
                    variant="caption"
                    sx={{ color: 'text.secondary', whiteSpace: 'nowrap' }}
                  >
                    {planDriverInfo.fullName}
                  </Typography>
                </Stack>
              )}
            </Stack>
          </Stack>
        </Stack>
        {isHover && !hideMenu && (
          <Popover
            content={menuItems.map((props) => (
              <MenuItem
                {...props}
                dense
                key={props.key}
                sx={{ paddingX: 2, paddingY: 1 }}
              >
                {props.children}
              </MenuItem>
            ))}
            onClose={() => setIsHover(false)}
          >
            <IconButton size="small">
              <MoreVertIcon sx={{ fontSize: 'inherit' }} />
            </IconButton>
          </Popover>
        )}
      </Stack>
    </ListItemButton>
  )
}
