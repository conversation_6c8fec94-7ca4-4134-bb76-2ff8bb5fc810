import {
  Box,
  Button,
  DateRangePicker,
  SingleInputDateRangeField,
  Stack,
  type DateRange,
  type SvgIconProps,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

type Props = {
  handleOnDateChange: (value: DateRange<DateTime>) => void
  dateRange: DateRange<DateTime>
  handleDownload: () => void
  label: string
  Icon: React.ComponentType<SvgIconProps>
  isLoading: boolean
}

const ReportItem = ({
  handleOnDateChange,
  dateRange,
  handleDownload,
  label,
  Icon,
  isLoading,
}: Props) => (
  <Stack
    gap={2}
    direction="row"
    justifyContent="center"
    alignItems="center"
    sx={{ padding: '15px' }}
  >
    <Icon
      sx={{
        height: '30px',
        width: '30px',
      }}
    />
    <Box>
      <DateRangePicker
        label={ctIntl.formatMessage({
          id: label,
        })}
        slots={{ field: SingleInputDateRangeField }}
        slotProps={{
          textField: {
            sx: { minWidth: '224px' },
          },
        }}
        value={dateRange}
        onChange={(newValue) => {
          handleOnDateChange([
            newValue[0]?.startOf('day') || null,
            newValue[1]?.endOf('day') || null,
          ])
        }}
      />
    </Box>
    <Button
      onClick={handleDownload}
      loading={isLoading}
      variant="contained"
      disabled={!dateRange[0]?.isValid || !dateRange[1]?.isValid}
    >
      {ctIntl.formatMessage({
        id: 'Download',
      })}
    </Button>
  </Stack>
)

export default ReportItem
