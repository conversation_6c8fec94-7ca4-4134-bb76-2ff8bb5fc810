import { useMemo, useState } from 'react'
import {
  <PERSON>ton,
  ClickAwayListener,
  Divider,
  Fade,
  IconButton,
  Paper,
  Popper,
  RangeValueUntilAccept,
  Stack,
  StaticDateRangePicker,
  Typography,
  type DateRange,
  type PopperProps,
} from '@karoo-ui/core'
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'
import type { DateTime } from 'luxon'

import { useDateRangeShortcutItems } from '@fleet-web/hooks/useDateRangeShortcutItems'
import type { DeliveryMainPageContextType } from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type Props = {
  selectedDateRange: DeliveryMainPageContextType['data']['selectedDateRange']
  onChange: (
    newDateRange: DeliveryMainPageContextType['data']['selectedDateRange'],
  ) => void
}

const DateRangeSelector = ({ selectedDateRange, onChange }: Props) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const togglePopper = (event?: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event ? event.currentTarget : null)
  }

  const dateRangePickerValue = useMemo<DateRange<DateTime>>(() => {
    if (selectedDateRange === null) return [null, null]

    return [selectedDateRange.start, selectedDateRange.end]
  }, [selectedDateRange])

  return (
    <>
      <Stack
        direction="row"
        divider={
          <Divider
            orientation="vertical"
            flexItem
          />
        }
        sx={(theme) => ({
          height: '36px',
          border: '1px solid',
          borderColor: theme.palette.divider,
          borderRadius: 1,
          minWidth: '250px',
          width: '100%',
        })}
      >
        <IconButton
          aria-label={ctIntl.formatMessage({
            id: 'tachograph.activity.drawer.previousDate',
          })}
          size="small"
          disabled={selectedDateRange === null}
          onClick={() => {
            if (selectedDateRange) {
              onChange({
                start: selectedDateRange.start.minus({ days: 1 }),
                end: selectedDateRange.end.minus({ days: 1 }),
              })
            }
          }}
        >
          <NavigateBeforeIcon sx={{ fontSize: 'inherit' }} />
        </IconButton>

        <Button
          variant="text"
          color="secondary"
          onClick={togglePopper}
          sx={{ flex: 1, whiteSpace: 'nowrap' }}
        >
          {(() => {
            if (selectedDateRange === null) {
              return ctIntl.formatMessage({ id: 'Unscheduled' })
            }

            return `${selectedDateRange.start.toFormat(
              'D',
            )} - ${selectedDateRange.end.toFormat('D')}`
          })()}
        </Button>

        <IconButton
          aria-label={ctIntl.formatMessage({
            id: 'tachograph.activity.drawer.nextDate',
          })}
          size="small"
          disabled={selectedDateRange === null}
          onClick={() => {
            if (selectedDateRange) {
              onChange({
                start: selectedDateRange.start.plus({ days: 1 }),
                end: selectedDateRange.end.plus({ days: 1 }),
              })
            }
          }}
        >
          <NavigateNextIcon sx={{ fontSize: 'inherit' }} />
        </IconButton>
      </Stack>

      <DateRangePickerPopper
        value={dateRangePickerValue}
        onChange={(newDateRange) => {
          if (newDateRange === null) {
            onChange(null)
          } else if (newDateRange[0] && newDateRange[1]) {
            onChange({
              start: newDateRange[0],
              end: newDateRange[1],
            })
          }
        }}
        anchorEl={anchorEl}
        onClose={togglePopper}
      />
    </>
  )
}

export default DateRangeSelector

export function generateSlots() {
  return {
    actionBar: () => null,
    toolbar: () => (
      <Typography
        variant="subtitle2"
        sx={{
          paddingY: 2,
          paddingX: 3,
        }}
      >
        {ctIntl.formatMessage({ id: 'delivery.scheduledDeliveryDate' })}
      </Typography>
    ),
  }
}

function DateRangePickerPopper({
  value,
  onChange,
  anchorEl,
  onClose,
  popperProps,
}: {
  value: DateRange<DateTime>
  onChange: (newDate: DateRange<DateTime> | null) => void
  anchorEl: HTMLElement | null
  onClose: () => void
  popperProps?: Partial<PopperProps>
}) {
  const isOpen = Boolean(anchorEl)

  const shortcuts = useDateRangeShortcutItems()

  const shortcutsItems = useMemo(
    () => [
      {
        label: ctIntl.formatMessage({ id: 'Unscheduled' }),
        getValue: (): [null, null] => [null, null],
      },
      shortcuts.today,
      shortcuts.last7Days,
      shortcuts.last30Days,
      shortcuts.last90Days,
      shortcuts.next7Days,
    ],
    [shortcuts],
  )

  return (
    <Popper
      id="calendar-popper"
      open={isOpen}
      anchorEl={anchorEl}
      transition
      disablePortal
      sx={{ zIndex: (theme) => theme.zIndex.modal }}
      {...popperProps}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps}>
          <Paper elevation={6}>
            <ClickAwayListener
              onClickAway={() => {
                if (isOpen) {
                  onClose()
                }
              }}
            >
              <Stack>
                <RangeValueUntilAccept value={value}>
                  {(bag) => (
                    <StaticDateRangePicker
                      calendars={2}
                      disableAutoMonthSwitching
                      {...bag}
                      onAccept={(newDate: DateRange<DateTime>) => {
                        onClose()
                        if (newDate && newDate[0] === null && newDate[1] === null) {
                          onChange(null)
                        } else {
                          onChange(newDate)
                        }
                      }}
                      slots={{
                        toolbar: () => (
                          <Typography
                            variant="subtitle2"
                            sx={{
                              paddingY: 2,
                              paddingLeft: 2,
                            }}
                          >
                            {ctIntl.formatMessage({
                              id: 'delivery.scheduledDeliveryDate',
                            })}
                          </Typography>
                        ),
                      }}
                      onClose={onClose}
                      slotProps={{
                        shortcuts: { items: shortcutsItems },
                        toolbar: { hidden: true },
                      }}
                    />
                  )}
                </RangeValueUntilAccept>
              </Stack>
            </ClickAwayListener>
          </Paper>
        </Fade>
      )}
    </Popper>
  )
}
