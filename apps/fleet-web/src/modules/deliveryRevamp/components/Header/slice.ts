import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { ReadonlyDeep } from 'type-fest/source/readonly-deep'

import type { AppState } from '@fleet-web/root-reducer'

type State = ReadonlyDeep<{
  focusedDriverId: string | null
}>

const initialState: State = {
  focusedDriverId: null,
}

const slice = createSlice({
  name: 'deliveryRevamp-header',
  initialState,
  reducers: {
    setFocusedDriverId(
      draft,
      { payload: focusedDriverId }: PayloadAction<State['focusedDriverId']>,
    ) {
      draft.focusedDriverId = focusedDriverId
    },
  },
})

export const { setFocusedDriverId } = slice.actions

export default slice.reducer

const getState = (state: AppState) => state.deliveryRevamp.header

export const getDeliveryRevampHeaderState = (state: AppState) => getState(state)

export const getFocusedDriverId = (state: AppState) =>
  getDeliveryRevampHeaderState(state).focusedDriverId
