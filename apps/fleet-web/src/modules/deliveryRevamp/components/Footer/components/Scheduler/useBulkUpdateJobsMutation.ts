import { useMutation } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { makeMutationErrorHandlerWithSnackbar } from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import type { UpdateDeliveryJobs } from '@fleet-web/modules/deliveryRevamp/api/jobs/useUpdateDeliveryJobMutation'
import { useRefreshDeliveryData } from '@fleet-web/modules/deliveryRevamp/hooks/useRefresh'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

export declare namespace BulkUpdateJobs {
  type ApiInput = {
    jobs: Array<UpdateDeliveryJobs.ApiInput>
  }
  type ApiOutput = FixMeAny
}

export const useBulkUpdateJobsMutation = (withoutMessage?: boolean) => {
  const { refresh } = useRefreshDeliveryData()
  return useMutation({
    mutationFn: (data: BulkUpdateJobs.ApiInput) => bulkUpdateJobs(data),
    ...makeMutationErrorHandlerWithSnackbar(),
    onSuccess: () => {
      refresh()

      if (!withoutMessage) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'deliver.scheduler.successBulkUpdate',
          }),
          { variant: 'success' },
        )
      }
    },
  })
}

const bulkUpdateJobs = async (data: BulkUpdateJobs.ApiInput) =>
  apiCallerNoX<BulkUpdateJobs.ApiOutput>('delivery_update_job_bulk', { data }).then(
    (res) => res,
  )
