import { memo } from 'react'
import { Tooltip, type AvatarProps } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import Avatar from '..'

type Props = {
  username: string
} & AvatarProps

const SubUserAvatar = ({ username, ...avatarProps }: Props) => (
  <Tooltip
    title={ctIntl.formatMessage(
      { id: 'Managed by {subuserName}' },
      { values: { subuserName: username } },
    )}
    placement="top"
  >
    <span>
      <Avatar
        sx={{ width: 20, height: 20, fontSize: '12px' }}
        {...avatarProps}
      >
        {username}
      </Avatar>
    </span>
  </Tooltip>
)

export default memo(SubUserAvatar)
