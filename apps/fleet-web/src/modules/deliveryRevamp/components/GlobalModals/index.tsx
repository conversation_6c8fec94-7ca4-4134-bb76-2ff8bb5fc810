import { memo } from 'react'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import { useValidatedJsonifiableSearchParams } from '@fleet-web/hooks/useValidatedSearchParams'
import DeliveryRecurringDialog from '@fleet-web/modules/deliveryRevamp/components/RecurringDialog'

import CancelAppointmentConfirmDialog from '../CancelAppointmentDialog'
import DriverLoginInfoDialog from '../DriverLoginInfoDialog'
import { deliveryGlobalModalsSearchParamsSchema } from './schema'

const GlobalModals = () => {
  const history = useHistory()
  const searchParams = new URLSearchParams(history.location.search)
  const deliveryGlobalModalSearchParam = searchParams.get('deliveryGlobalModal')

  const validatedParams = useValidatedJsonifiableSearchParams(
    () => deliveryGlobalModalsSearchParamsSchema,
  )

  if (deliveryGlobalModalSearchParam === null) {
    return null
  } else if (validatedParams.status === 'invalid') {
    if (ENV.NODE_ENV === 'development') {
      console.error(
        `[Cartrack] - Delivery Global Modal - Invalid search params:`,
        deliveryGlobalModalSearchParam,
        `\nPlease make sure the modal property is valid and the params are valid with the modal property.`,
      )
    }

    return null
  }

  const handleCloseGlobalModal = () => {
    history.push({
      search: buildRouteQueryStringKeepingExistingSearchParams({
        location: history.location,
        schema: deliveryGlobalModalsSearchParamsSchema,
        searchParams: {
          deliveryGlobalModal: undefined,
        },
      }),
    })
  }

  return match(validatedParams.data.deliveryGlobalModal)
    .with(undefined, () => null)
    .with({ modal: 'recurring' }, ({ params }) => (
      <DeliveryRecurringDialog
        {...params}
        onClose={handleCloseGlobalModal}
      />
    ))
    .with({ modal: 'driversInfo' }, ({ params }) => (
      <DriverLoginInfoDialog
        {...params}
        onClose={handleCloseGlobalModal}
      />
    ))
    .with({ modal: 'cancelAppointment' }, ({ params }) => (
      <CancelAppointmentConfirmDialog
        {...params}
        onClose={handleCloseGlobalModal}
      />
    ))
    .exhaustive()
}

export default memo(GlobalModals)
