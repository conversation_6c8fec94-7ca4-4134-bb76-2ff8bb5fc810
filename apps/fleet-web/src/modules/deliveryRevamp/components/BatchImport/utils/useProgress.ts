import { useRef, useState } from 'react'

import { useUnmount } from '@fleet-web/modules/deliveryRevamp/hooks/common-hooks'

export default function useProgress() {
  const [progress, setProgress] = useState<number | null>(null)
  const completedRef = useRef(0)
  const totalRef = useRef(0)
  const currentProgressRef = useRef(0)
  const intervalRef = useRef<number>()

  const cleanup = () => {
    if (intervalRef.current) {
      window.clearInterval(intervalRef.current)
      intervalRef.current = undefined
    }
  }

  const reset = () => {
    cleanup()
    completedRef.current = 0
    totalRef.current = 0
    currentProgressRef.current = 0
    setProgress(null)
  }

  const startProgress = (totalTasks: number) => {
    reset()
    if (totalTasks <= 0) return

    totalRef.current = totalTasks
    setProgress(0)

    const interval = Math.floor(Math.random() * 1000) + 1000
    intervalRef.current = window.setInterval(() => {
      const fakeProgress = Math.max(1, 100 / totalTasks / 5) + Math.round(Math.random())
      const maxProgress = Math.min(100, ((completedRef.current + 1) / totalTasks) * 100)

      if (currentProgressRef.current + fakeProgress < maxProgress) {
        currentProgressRef.current += fakeProgress
        setProgress(currentProgressRef.current)
      }
    }, interval)
  }

  const markOneCompleted = () => {
    if (!totalRef.current) return
    completedRef.current += 1
    const newProgress = (completedRef.current / totalRef.current) * 100
    currentProgressRef.current = newProgress
    setProgress(newProgress)

    if (completedRef.current >= totalRef.current) {
      cleanup()
      window.setTimeout(() => reset(), 500)
    }
  }

  useUnmount(() => reset())

  return { progress, startProgress, markOneCompleted, reset }
}
