import { isDate, isString, keys, max, pickBy } from 'lodash'
import XLSX, { type ParsingOptions } from 'xlsx'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { ColumnsType, CSVXLSObject, RawData, UserSetting } from '../types'

export const readXLSXFile = (
  file: string | ArrayBuffer | null,
  type: ParsingOptions['type'],
) => {
  const wb = XLSX.read(file, {
    type,
    cellDates: true,
    raw: type === 'string',
  })
  // Some sheet may not be named Sheet1 so we just get the first initial sheet in
  // these other instances based on what comes up first in wb.Sheets.keys
  const firstSheetName = wb.SheetNames[0]
  const sheetData = wb.Sheets[firstSheetName]
  const date1904 = wb.Workbook?.WBProps?.date1904

  function getDataRange(data: XLSX.WorkSheet) {
    const dataWithValues = pickBy(data, (value) => !!value.v)
    const cellNamesWithValues = keys(dataWithValues)
    const cellsWithValues = cellNamesWithValues.map((cell) =>
      XLSX.utils.decode_cell(cell),
    )
    const maxRow = max(cellsWithValues.map((cell) => cell.r))
    const maxColumn = max(cellsWithValues.map((cell) => cell.c))
    const lastCellName = XLSX.utils.encode_cell({
      c: maxColumn as number,
      r: maxRow as number,
    })
    return `A1:${lastCellName}`
  }

  sheetData['!ref'] = getDataRange(sheetData)

  return XLSX.utils
    .sheet_to_json(sheetData, {
      blankrows: false,
      defval: '',
    })
    .filter((object) =>
      Object.values(object as Record<string, any>).some(
        (val) => isString(val) && val.trim(),
      ),
    )
    .map((object) => {
      const result = {} as CSVXLSObject
      // Avoid the time offset issue of Date1904
      for (const [key, value] of Object.entries(object as CSVXLSObject)) {
        if (isDate(value)) {
          result[key] = XLSX.SSF.format('YYYY-MM-DD hh:mm', value, { date1904 })
        }
      }

      return Object.assign(object as CSVXLSObject, result)
    }) as RawData
}

export const exportDataToExcel = (data: RawData, fileName: string) => {
  if (data.length > 0) {
    const worksheet = XLSX.utils.json_to_sheet(data)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, `Sheet1`)
    XLSX.writeFile(workbook, `ERROR-${fileName}.xlsx`)
  }
}

export const normalizeStr = (str: string) => str.replace(/[\s*]+/g, '').toLowerCase()

export const getFileColumns = (rawData: RawData) => {
  if (rawData.length > 0) {
    const keys = Object.keys(rawData[0] as any)
      .filter((item) => !item.includes('EMPTY'))
      .map((col) => col.trim())
    // Deduplicate. E.g. xxx and xxx_1 are duplicate
    return keys.map((col) =>
      col.replace(
        /_(\d+)$/,
        `(${ctIntl.formatMessage({
          id: 'global.duplicate',
        })})`,
      ),
    )
  }

  return []
}

export function generateDefaultUserConfig(
  uploadedFileColumns: Array<string>,
  importColumns: ColumnsType,
): Record<string, string> {
  const obj: Record<string, string> = {}
  for (const [systemCol, config] of importColumns) {
    const foundFileCol = uploadedFileColumns.find((col) =>
      [ctIntl.formatMessage({ id: config.label }), systemCol, config.label]
        .map(normalizeStr)
        .includes(normalizeStr(col)),
    )
    if (foundFileCol) {
      obj[systemCol] = foundFileCol
    }
  }
  return obj
}

export function excelColName(n: number) {
  const ordA = 'a'.codePointAt(0) ?? 97
  const ordZ = 'z'.codePointAt(0) ?? 122
  const len = ordZ - ordA + 1
  let coIndex = n
  let s = ''
  while (coIndex >= 0) {
    s = String.fromCodePoint((coIndex % len) + ordA) + s
    coIndex = Math.floor(coIndex / len) - 1
  }
  return s
}

type MapKey<T extends Map<any, any>> = T extends Map<infer K, any> ? K : never
type SystemColToExcelVal<T extends ColumnsType> = {
  systemColToExcelVal: Array<Record<MapKey<T>, any> & { __rowNum__: number }>
  systemColToColAlphabet: Record<MapKey<T>, string>
}
export function systemColMapToExcelVal<T extends ColumnsType>({
  rawData,
  userConfig,
  importColumns,
}: {
  rawData: RawData
  userConfig: UserSetting['config']
  importColumns: T
}): SystemColToExcelVal<T> {
  const excelCols = getFileColumns(rawData)
  // Make them still matched if import col is 'systemCol' and config is { 'System Col': 'File Col' }
  const normalizedImportColToImportColMap = new Map<string, string>()
  const normalizedImportColumns = Array.from(importColumns.keys()).map((col) => {
    const normalized = normalizeStr(col)
    normalizedImportColToImportColMap.set(normalized, col)
    return normalized
  })
  const userConfigWithNormalizedKeys = Object.fromEntries(
    Object.entries(userConfig).map(([key, value]) => [normalizeStr(key), value]),
  )
  const excelToSystemMap = new Map<string, string>()
  for (const normalizedSystemCol of normalizedImportColumns) {
    const excelCol = userConfigWithNormalizedKeys[normalizedSystemCol]
    if (excelCol) {
      excelToSystemMap.set(
        normalizeStr(excelCol),
        normalizedImportColToImportColMap.get(normalizedSystemCol) as string,
      )
    }
  }

  const systemColToColAlphabet = {} as SystemColToExcelVal<T>['systemColToColAlphabet']

  const systemColToExcelVal: SystemColToExcelVal<T>['systemColToExcelVal'] =
    rawData.map((row) => {
      const mappedRow = {
        __rowNum__: row.__rowNum__,
      } as SystemColToExcelVal<T>['systemColToExcelVal'][number]
      for (const [excelCol, value] of Object.entries(row)) {
        const excelColIndex = excelCols.indexOf(excelCol)
        const normalized = normalizeStr(excelCol)
        const systemCol = excelToSystemMap.get(normalized)
        if (systemCol) {
          mappedRow[systemCol as MapKey<T>] =
            typeof value === 'string' ? value.trim() : value
          systemColToColAlphabet[systemCol as MapKey<T>] = excelColName(excelColIndex)
        }
      }
      return mappedRow
    })

  return {
    systemColToExcelVal,
    systemColToColAlphabet,
  }
}
