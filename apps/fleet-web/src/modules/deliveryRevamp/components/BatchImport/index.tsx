import { useCallback, useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Stepper, styled } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import type { Except } from 'type-fest'

import useEffectExceptOnMount from '@fleet-web/hooks/useEffectExceptOnMount'
import Dialog from '@fleet-web/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import BatchImportModal from '../BatchImportModal'
import Mapping from './components/Mapping'
import Upload from './components/Upload'
import Validation from './components/Validation'
import { BatchImportContext } from './ContextProvider'
import type { BatchImportContextType, StepConfig, UserSetting } from './types'
import createFlow from './utils/createFlow'

export type Props<T> = {
  showModal: boolean
  setShowModal: (visible: undefined | boolean) => void
  title: string
  steps?: [StepConfig, ...StepConfig[]]
} & Except<BatchImportContextType<T>, 'setUserSetting' | 'onClose'>

export const DEFAULT_USER_SETTING = {
  fileColumns: [],
  config: {},
} satisfies UserSetting

export const DEFAULT_STEPS = [
  { component: Upload, label: 'Upload' },
  { component: Mapping, label: 'Mapping' },
  { component: Validation, label: 'Validation' },
] satisfies [StepConfig, ...StepConfig[]]

export default function BatchImport<T>({
  showModal,
  setShowModal,
  title,
  importColumns,
  userSetting,
  templatePath,
  validateMutation,
  importMutation,
  parser,
  steps = DEFAULT_STEPS,
  onBeforeImport,
  filterErrorData,
}: Props<T>) {
  const [stepIndex, setStepIndex] = useState(0)
  const [userSettingState, setUserSettingState] = useState<UserSetting>(
    userSetting || DEFAULT_USER_SETTING,
  )

  useEffectExceptOnMount(() => {
    setUserSettingState(userSetting)
  }, [userSetting])

  const onClose = useCallback(() => {
    if (stepIndex === 0) {
      setShowModal(false)
      return
    }
    Dialog.alert({
      title: ctIntl.formatMessage({ id: 'Discard unsaved changes?' }),
      content: ctIntl.formatMessage({
        id: 'This page contains unsaved changes. Do you still wish to leave?',
      }),
      confirmButtonLabel: ctIntl.formatMessage({
        id: 'Discard and Leave',
      }),
      rejectButtonLabel: ctIntl.formatMessage({ id: 'Back' }),
      onResult: () => {
        validateMutation.abort?.()
        setShowModal(false)
      },
    })
  }, [setShowModal, stepIndex, validateMutation])

  const ImportFlow = useMemo(() => createFlow(steps), [steps])

  const contextValue = useMemo<BatchImportContextType<T>>(
    () => ({
      importColumns,
      userSetting: userSettingState,
      setUserSetting: setUserSettingState,
      templatePath,
      validateMutation,
      importMutation,
      parser,
      onClose,
      onBeforeImport,
      filterErrorData,
    }),
    [
      importColumns,
      parser,
      templatePath,
      userSettingState,
      validateMutation,
      importMutation,
      onClose,
      onBeforeImport,
      filterErrorData,
    ],
  )

  return (
    <BatchImportContext.Provider value={contextValue}>
      {showModal && (
        <BatchImportModal
          header={{
            title,
            headerAction: (
              <IconButton onClick={onClose}>
                <CloseIcon fontSize="inherit" />
              </IconButton>
            ),
          }}
          stepper={
            <StyledStepper activeStep={stepIndex}>
              {steps.map((step) => (
                <Step key={step.label}>
                  <StepLabel>{ctIntl.formatMessage({ id: step.label })}</StepLabel>
                </Step>
              ))}
            </StyledStepper>
          }
          body={<ImportFlow onStepChange={setStepIndex} />}
        />
      )}
    </BatchImportContext.Provider>
  )
}

export const StyledStepper = styled(Stepper)(({ theme }) => ({
  marginTop: '10px',
  '& .MuiStep-root:first-child': {
    paddingLeft: 0,
  },
  '& .MuiStepLabel-iconContainer > .Mui-completed': {
    color: theme.palette.success.main,
  },
}))
