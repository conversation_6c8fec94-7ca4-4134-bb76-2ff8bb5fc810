import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  <PERSON>ert,
  Alert<PERSON><PERSON>le,
  Button,
  Paper,
  Stack,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { useSelector } from 'react-redux'
import { isArray } from 'remeda'
import { match, P } from 'ts-pattern'

import { getSettings } from '@fleet-web/duxs/user'
import { useEffectEvent } from '@fleet-web/hooks/useEventHandler'
import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import { splitArrayIntoChunks } from '@fleet-web/modules/deliveryRevamp/helpers'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { useBatchImportContext } from '../ContextProvider'
import type {
  MappingOutputData,
  RawData,
  StepComponent,
  ValidationOutputData,
} from '../types'
import { exportDataToExcel } from '../utils'
import useProgress from '../utils/useProgress'
import Loading from './Loading'

const CHUNK_LENGTH = 1000

type ValidationProps = Parameters<
  StepComponent<MappingOutputData, ValidationOutputData>
>[0] & {
  importButtonLabel?: string
}

const Validation: React.FC<ValidationProps> = ({
  data: { fileName, userSetting, rawData },
  onError,
  onRetry,
  onPrevious,
  importButtonLabel = 'Import',
}) => {
  const { phonePrefix } = useSelector(getSettings)
  const {
    parser,
    validateMutation,
    importMutation,
    onClose,
    onBeforeImport,
    filterErrorData,
  } = useBatchImportContext()
  const [parsedData, setParsedData] = useState<Array<FixMeAny>>([])
  const [validForUploadData, setValidForUploadData] = useState<Array<FixMeAny>>([])
  const [errorData, setErrorData] = useState<RawData>([])
  const [extraErrorSections, setExtraErrorSections] = useState<
    Array<React.ReactNode> | undefined
  >()
  const [validationErrors, setValidationErrors] = useState<
    Array<JobImporterError.Error>
  >([])
  const { progress, startProgress, markOneCompleted } = useProgress()

  const [missingData, invalidData] = useMemo(() => {
    const missing = []
    const invalid = []
    for (const error of validationErrors) {
      if (error.errorType === 'required') {
        missing.push(error)
      } else {
        invalid.push(error)
      }
    }
    return [missing, invalid]
  }, [validationErrors])

  const parseAndValidate = useEffectEvent(async () => {
    try {
      const errors: Array<JobImporterError.Error> = []
      const { parsedData, errors: feErrors } = parser(rawData, userSetting.config, {
        phonePrefix: phonePrefix as number,
      })
      setParsedData(parsedData)
      errors.push(...feErrors)

      const dataChunks = splitArrayIntoChunks(parsedData, CHUNK_LENGTH)
      startProgress(dataChunks.length)

      const validationMutations = dataChunks.map(async (chunk) => {
        const err = await validateMutation.mutateAsync(chunk)
        markOneCompleted()
        errors.push(...err)
      })

      await Promise.all(validationMutations)
      setValidationErrors(errors)

      const { errorData, validData, extraErrorSections } = filterErrorData({
        errors,
        rawData,
        parsedData,
      })

      setExtraErrorSections(extraErrorSections)
      setValidForUploadData(validData)
      setErrorData(errorData)
    } catch (e) {
      onError(e)
    }
  })

  useEffect(() => {
    parseAndValidate()
  }, [userSetting.config])

  const handleImport = useCallback(async () => {
    if (onBeforeImport) {
      await onBeforeImport(userSetting)
    }
    try {
      exportDataToExcel(errorData, fileName)
      const dataChunks = splitArrayIntoChunks(validForUploadData, CHUNK_LENGTH)
      startProgress(dataChunks.length)

      const importMutations = dataChunks.map(async (chunk) => {
        await importMutation.mutateAsync(chunk)
        markOneCompleted()
      })

      await Promise.all(importMutations)
    } catch (e) {
      onError(e)
    }
  }, [
    errorData,
    fileName,
    importMutation,
    markOneCompleted,
    onBeforeImport,
    onError,
    startProgress,
    userSetting,
    validForUploadData,
  ])

  if (validateMutation.isPending) {
    return (
      <Loading
        fileName={fileName}
        uploadState={IMPORT_STATUS_TO_ID.DONE}
        mapDataState={IMPORT_STATUS_TO_ID.DONE}
        validateState={IMPORT_STATUS_TO_ID.LOADING}
        onCancel={onClose}
        progress={progress}
      />
    )
  }

  return (
    <Stack
      sx={{ height: '100%' }}
      gap={2}
    >
      <Typography variant="subtitle2">
        {ctIntl.formatMessage({ id: 'Check for errors in the uploaded Excel file' })}
      </Typography>
      <Stack
        gap={2}
        sx={{ flexGrow: 1, overflowY: 'auto' }}
      >
        {validationErrors.length === 0 ? (
          <Alert
            severity="success"
            sx={{ margin: '0 0 16px 0' }}
          >
            <AlertTitle sx={{ fontSize: 'body2.fontSize' }}>
              {ctIntl.formatMessage({
                id: 'Data is validated! Please proceed to next step',
              })}
            </AlertTitle>
            {ctIntl.formatMessage({ id: 'No errors detected' })}
          </Alert>
        ) : (
          <>
            <Alert
              severity="error"
              sx={{ fontSize: 'body2.fontSize', fontWeight: 500 }}
            >
              {ctIntl.formatMessage(
                { id: 'Please fix errors on {fileName} and reupload' },
                {
                  values: {
                    fileName,
                  },
                },
              )}
            </Alert>
            {missingData.length > 0 && (
              <Paper
                variant="outlined"
                sx={{ padding: '16px' }}
              >
                <Alert
                  severity="error"
                  sx={{
                    marginBottom: '10px',
                    background: 'none',
                    padding: 0,
                    fontWeight: 500,
                  }}
                >
                  {ctIntl.formatMessage({ id: 'delivery.import.missingData' })}
                </Alert>
                {renderErrorRows(missingData)}
              </Paper>
            )}
            {invalidData.length > 0 && (
              <Paper
                variant="outlined"
                sx={{ padding: '16px' }}
              >
                <Alert
                  severity="warning"
                  sx={{
                    marginBottom: '10px',
                    background: 'none',
                    padding: 0,
                    fontWeight: 500,
                  }}
                >
                  {ctIntl.formatMessage({ id: 'delivery.import.invalidData' })}
                </Alert>
                {renderErrorRows(invalidData)}
              </Paper>
            )}
            {extraErrorSections}
          </>
        )}
      </Stack>
      <Stack
        flexDirection="row-reverse"
        justifyContent="space-between"
      >
        {validationErrors.length > 0 ? (
          <Stack
            direction="row"
            spacing={1}
          >
            {validForUploadData.length > 0 ? (
              <Button
                size="small"
                variant="outlined"
                color="secondary"
                loading={importMutation.isPending}
                loadingIndicator={`${progress}%`}
                onClick={handleImport}
              >
                {ctIntl.formatMessage({ id: importButtonLabel })}{' '}
                {validForUploadData.length}/{parsedData.length}
              </Button>
            ) : (
              <Button
                size="small"
                variant="outlined"
                color="secondary"
                onClick={onPrevious}
                disabled={importMutation.isPending}
              >
                {ctIntl.formatMessage({
                  id: 'Previous',
                })}
              </Button>
            )}
            <Button
              size="small"
              variant="contained"
              onClick={onRetry}
            >
              {ctIntl.formatMessage({
                id: 'Reupload',
              })}
            </Button>
          </Stack>
        ) : (
          <Stack
            direction="row"
            spacing={1}
          >
            <Button
              size="small"
              variant="outlined"
              color="secondary"
              onClick={onPrevious}
              disabled={importMutation.isPending}
            >
              {ctIntl.formatMessage({
                id: 'Previous',
              })}
            </Button>
            <Button
              size="small"
              variant="contained"
              loading={importMutation.isPending}
              loadingIndicator={`${progress}%`}
              onClick={handleImport}
            >
              {ctIntl.formatMessage({
                id: importButtonLabel,
              })}{' '}
              ({validForUploadData.length})
            </Button>
          </Stack>
        )}

        {validationErrors.length > 0 && validForUploadData.length > 0 && (
          <Button
            size="small"
            variant="outlined"
            color="secondary"
            onClick={onPrevious}
            disabled={importMutation.isPending}
          >
            {ctIntl.formatMessage({
              id: 'Previous',
            })}
          </Button>
        )}
      </Stack>
    </Stack>
  )
}

export default Validation

export function renderErrorRows(errors: Array<JobImporterError.Error>) {
  return (
    <Stack spacing={2}>
      {errors.map((data, index) => (
        <Stack
          // eslint-disable-next-line react/no-array-index-key
          key={index}
          direction="row"
          alignItems="center"
          spacing={3}
        >
          <Typography
            variant="body2"
            sx={{ flex: 1 }}
          >
            {match(data)
              .with({ cellLocation: P.nonNullable }, ({ cellLocation }) => (
                <>
                  {isArray(cellLocation) &&
                    cellLocation.some((i) => i !== '') &&
                    ctIntl.formatMessage({
                      id: 'Cell',
                    })}
                  &nbsp;
                  {isArray(cellLocation) && cellLocation.join('-').toString()}
                  {typeof cellLocation === 'string' &&
                    `${ctIntl.formatMessage({
                      id: 'Cell',
                    })} ${cellLocation}`}
                </>
              ))
              .with({ rowLocation: P.nonNullable }, ({ rowLocation }) => (
                <>
                  {((isArray(rowLocation) && rowLocation.some((i) => i !== '')) ||
                    (!isArray(rowLocation) && rowLocation.toString() !== '')) &&
                    ctIntl.formatMessage({
                      id: 'Rows',
                    })}
                  &nbsp;
                  {isArray(rowLocation) && rowLocation.join(',').toString()}
                  {!isArray(rowLocation) && rowLocation.toString()}
                </>
              ))
              .with({ rowNumber: P.nonNullable }, ({ rowNumber }) => (
                <>
                  {((isArray(rowNumber) && rowNumber.some((i) => i !== '')) ||
                    (!isArray(rowNumber) && rowNumber.toString() !== '')) &&
                    ctIntl.formatMessage({
                      id: 'Rows',
                    })}
                  &nbsp;
                  {isArray(rowNumber) && rowNumber.join(',').toString()}
                  {!isArray(rowNumber) && rowNumber.toString()}
                </>
              ))
              .otherwise(() => null)}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ flex: 4, display: 'flex', alignItems: 'center', gap: 0.5 }}
          >
            {data?.message}
            {data?.message &&
              data.message.includes('Failed to find exact locations') && (
                <Tooltip
                  arrow
                  placement="right"
                  title={ctIntl.formatMessage({
                    id: 'Please double-check your uploaded file and ensure information such as GPS coordinates, longitude and latitude, country code, postal code, and address line 1, is correct.',
                  })}
                >
                  <InfoOutlinedIcon
                    color="warning"
                    sx={{ fontSize: 16 }}
                  />
                </Tooltip>
              )}
          </Typography>
        </Stack>
      ))}
    </Stack>
  )
}
