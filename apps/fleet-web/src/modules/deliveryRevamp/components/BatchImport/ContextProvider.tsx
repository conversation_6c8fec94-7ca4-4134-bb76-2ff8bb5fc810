import { createContext, useContext } from 'react'

import type { FixMeAny } from '@fleet-web/types'

import type { BatchImportContextType } from './types'

export const BatchImportContext =
  createContext<BatchImportContextType<FixMeAny> | null>(null)

export const useBatchImportContext = () => {
  const context = useContext(BatchImportContext)
  if (!context) {
    throw new Error('useBatchImportContext must be used within BatchImportProvider')
  }
  return context
}
