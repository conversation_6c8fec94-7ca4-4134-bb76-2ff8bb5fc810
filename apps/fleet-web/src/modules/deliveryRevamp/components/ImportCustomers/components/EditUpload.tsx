import { Button, Paper, Stack, Typography } from '@karoo-ui/core'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'

import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import useCustomersExportDeliveryMutation from '@fleet-web/modules/deliveryRevamp/api/customers/useCustomersExportDeliveryMutation'
import { IMPORT_STATUS_TO_ID } from '@fleet-web/modules/deliveryRevamp/constants/job'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import Loading from '../../BatchImport/components/Loading'
import { useFileDropZone, type Props } from '../../BatchImport/components/Upload'

const EditUpload: Props = ({ onNext, onError }) => {
  const exportDeliveryCustomersMutation = useCustomersExportDeliveryMutation({
    onError: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'delivery.download.job.report.fail',
        }),
        { variant: 'error' },
      )
    },
  })
  const { isUploading, fileName, FileDropZone } = useFileDropZone({ onNext, onError })

  if (isUploading) {
    return (
      <Loading
        fileName={fileName}
        uploadState={IMPORT_STATUS_TO_ID.LOADING}
      />
    )
  }

  return (
    <Stack
      sx={{ height: '100%' }}
      gap={2}
    >
      <Stack>
        <Typography
          variant="subtitle1"
          sx={{ fontWeight: '500' }}
        >
          1.{' '}
          {ctIntl.formatMessage({ id: 'Make changes to the existing customer file' })}
        </Typography>
        <Typography
          variant="body2"
          sx={{ color: 'text.secondary' }}
        >
          {ctIntl.formatMessage({
            id: 'Export an excel sheet of all existing customers within your address book. Please use this file to make necessary changes.',
          })}
        </Typography>
      </Stack>

      <Button
        variant="outlined"
        size="small"
        startIcon={<FileDownloadOutlinedIcon />}
        loading={exportDeliveryCustomersMutation.isPending}
        loadingPosition="start"
        sx={{ alignSelf: 'flex-start' }}
        onClick={() => {
          exportDeliveryCustomersMutation.mutate()
        }}
      >
        {ctIntl.formatMessage({
          id: 'delivery.settings.exportCustomers',
        })}
      </Button>

      <Typography
        variant="subtitle1"
        sx={{ fontWeight: '500' }}
      >
        2. {ctIntl.formatMessage({ id: 'Upload your updated file' })}
      </Typography>

      <Paper
        variant="outlined"
        sx={{ flexGrow: 1 }}
      >
        {FileDropZone}
      </Paper>
    </Stack>
  )
}

export default EditUpload
