import * as React from 'react'
import {
  Button,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  type ButtonProps,
  type SvgIconProps,
} from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export type ButtonMenuOptions = {
  value: any
  label: string
  icon?: React.ReactElement<SvgIconProps>
  onClick?: () => void
  disabled?: boolean
}

type Props = {
  buttonProps: ButtonProps
  options: Array<ButtonMenuOptions>
  onChange: (value: any) => void
}

export default function ButtonMenu({ buttonProps, options, onChange }: Props) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <div>
      <Button
        onClick={handleClick}
        {...buttonProps}
      >
        {buttonProps.children}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        {options.map((item) => (
          <MenuItem
            key={item.label}
            dense
            disabled={item.disabled}
            onClick={(event: React.MouseEvent) => {
              event.stopPropagation()
              item.onClick?.()
              onChange(item.value)
              handleClose()
            }}
          >
            {item.icon && <ListItemIcon>{item.icon}</ListItemIcon>}
            <ListItemText>
              {ctIntl.formatMessage({
                id: item.label,
              })}
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}
