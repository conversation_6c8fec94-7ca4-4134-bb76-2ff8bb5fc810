import { useMemo } from 'react'
import {
  Box,
  CircularProgressDelayedAbsolute,
  DataGrid,
  IconButton,
  LinearProgress,
  OverflowTypography,
  Paper,
  Stack,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import MovingOutlinedIcon from '@mui/icons-material/MovingOutlined'
import TimerOutlinedIcon from '@mui/icons-material/TimerOutlined'
import WarningIcon from '@mui/icons-material/Warning'
import { match, P } from 'ts-pattern'

import DataStatePlaceholder from '@fleet-web/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import useRouteLegs from '@fleet-web/modules/deliveryRevamp/api/routes/useRouteLegs'
import type { RoutesList } from '@fleet-web/modules/deliveryRevamp/api/routes/useRoutesList'
import {
  STOP_STATUS_ID,
  STOP_STATUS_ID_TRANSLATION,
  STOP_TYPE_ID,
} from '@fleet-web/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from '@fleet-web/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import useSelectDriver from '@fleet-web/modules/deliveryRevamp/hooks/useSelectDriver'
import useSelectRoute from '@fleet-web/modules/deliveryRevamp/hooks/useSelectRoute'
import { DeliveryDateTime } from '@fleet-web/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DriverChip from '../../../DriverChip'
import DateDiff from '../../../JobCard/components/DateDiff'
import JobStatusChip from '../../../JobCard/components/JobStatusChip'
import StopCard from '../../../JobCard/StopCard'
import DotDivider from '../../../RouteCard/component/DotDivider'
import { useDriversAndRoutesContext } from '../../DriversAndRoutesProvider'

export const ROUTE_DETAILS_WIDTH_IN_PX = 327
export const ROUTE_DETAILS_MARGIN = 8
export const ROUTE_DETAILS_HEIGHT_RATIO = 0.5

const RouteDetailsContent = ({
  routeInfo,
}: {
  routeInfo: RoutesList.Return[number]
}) => {
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()
  const columnHelper = useDataGridColumnHelper<
    RoutesList.Return[number]['orderedStops'][number]
  >({
    filterMode: 'client',
  })
  const { focusedDriverId, handleSelectDriver } = useSelectDriver()
  const { selectedRouteId, handleSelectRoute } = useSelectRoute()
  const { driversList } = useDriversAndRoutesContext()
  const { data: routeLegs } = useRouteLegs(routeInfo.routeId)

  const totalDistanceAndTime = useMemo(() => {
    if (!routeLegs) return null

    return routeLegs.stopLegs.reduce(
      (acc, cur) =>
        Object.assign(acc, {
          totalDistance: acc.totalDistance + cur.distance,
          totalTime: acc.totalTime + cur.lastEstimatedTravelTime,
        }),
      {
        totalDistance: 0,
        totalTime: 0,
      },
    )
  }, [routeLegs])

  const columns = useMemo(
    () => [
      columnHelper.string((_, row) => row.addressLine1, {
        field: 'addressLine1',
        maxWidth: 280,
        headerName: ctIntl.formatMessage({ id: 'delivery.jobStops' }),
        renderCell: ({ row }) => (
          <StopCard.Stop
            stop={row}
            ordering={row.ordering}
            showCheckbox={false}
            selected={false}
            hideStatusChip
          />
        ),
      }),
      columnHelper.string((_, row) => STOP_STATUS_ID_TRANSLATION[row.stopStatusId], {
        field: 'stopStatusId',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <Box>
            <JobStatusChip
              stopStatusId={row.stopStatusId}
              stopTypeId={row.stopTypeId}
              // activityTimestamp={row[STOP_STATUS_ID_TO_TIME_KEY[row.stopStatusId]]}
              activityTimestamp={null}
              isInUnassignedRoute={!routeInfo.deliveryDriverId}
              formattedTimeWindow={null}
              latestTimeWindowInDateTime={null}
              deliveryDate={null}
            />
            {row.hasLateRiskBasedOnPreStop && (
              <Stack
                direction="row"
                alignItems="center"
                gap={0.2}
                sx={{ mt: 0.5 }}
              >
                <Tooltip
                  title={
                    <>
                      <Box>{ctIntl.formatMessage({ id: 'Late risk' })}</Box>
                      <Box>
                        {ctIntl.formatMessage({
                          id: `The driver might arrive later than scheduled, based on the previous job's completion or arrival time.`,
                        })}
                      </Box>
                    </>
                  }
                >
                  <WarningIcon
                    color="warning"
                    sx={{ fontSize: 'inherit' }}
                  />
                </Tooltip>

                <Box
                  sx={{ color: 'text.secondary', fontSize: 10, whiteSpace: 'nowrap' }}
                >
                  {ctIntl.formatMessage({ id: 'might arrive late' })}
                </Box>
              </Stack>
            )}
          </Box>
        ),
      }),
      columnHelper.dateTime({
        field: 'activityArrivedTs',
        headerName: ctIntl.formatMessage({ id: 'delivery.routeDetails.actualArrival' }),
        valueGetter: (_, row) =>
          row.activityArrivedTs !== null ? new Date(row.activityArrivedTs) : null,
        renderCell: ({
          row: { activityArrivedTs, isLate, stopStatusId, stopTypeId },
        }) => {
          if (!activityArrivedTs) return '-'
          return (
            <Tooltip
              title={match({ stopStatusId, stopTypeId, isLate })
                .with({ isLate: true, stopStatusId: STOP_STATUS_ID.ARRIVED }, () =>
                  ctIntl.formatMessage({
                    id: 'The driver arrived later than scheduled.',
                  }),
                )
                .with(
                  {
                    isLate: true,
                    stopStatusId: STOP_STATUS_ID.COMPLETED,
                    stopTypeId: STOP_TYPE_ID.PICKUP,
                  },
                  () =>
                    ctIntl.formatMessage({
                      id: 'Driver picked up the job later than scheduled.',
                    }),
                )
                .with(
                  {
                    isLate: true,
                    stopStatusId: STOP_STATUS_ID.COMPLETED,
                  },
                  () =>
                    ctIntl.formatMessage({
                      id: 'The driver completed the job later than scheduled, possibly due to a delayed arrival or extended duration',
                    }),
                )
                .otherwise(() => '')}
            >
              <Box sx={{ ...(isLate && { color: 'error.main' }) }}>
                {DeliveryDateTime.fromJSDate(new Date(activityArrivedTs)).toFormat(
                  'HH:mm',
                )}
                <DateDiff
                  start={selectedDateRange?.start}
                  end={activityArrivedTs}
                />
              </Box>
            </Tooltip>
          )
        },
        flex: 1,
      }),
      columnHelper.dateTime({
        field: 'scheduledArrival',
        headerName: ctIntl.formatMessage({
          id: 'delivery.routeDetails.scheduledArrival',
        }),
        valueGetter: (_, row) => {
          if (row.expectedDurationInMinutes && row.activityStartedTs) {
            const scheduledArrival = DeliveryDateTime.fromJSDate(
              new Date(row.activityStartedTs),
            ).plus({
              minutes: row.expectedDurationInMinutes,
            })

            return scheduledArrival.toJSDate()
          }

          return null
        },
        renderCell: ({ row: { formattedTimeWindow } }) => (
          <Box sx={{ color: 'text.secondary' }}>{formattedTimeWindow || '-'}</Box>
        ),
        flex: 1,
      }),
      columnHelper.number((_, row) => row.actualDurationInMinutes, {
        field: 'actualDurationInMinutes',
        headerName: ctIntl.formatMessage({ id: 'Actual Duration' }),
        flex: 1,
        renderCell: ({
          row: { actualDurationInMinutes, expectedDurationInMinutes },
        }) => {
          if (!actualDurationInMinutes) return '-'
          const isExceeded =
            expectedDurationInMinutes &&
            actualDurationInMinutes > expectedDurationInMinutes
          return (
            <Tooltip
              title={
                isExceeded
                  ? ctIntl.formatMessage({
                      id: 'The actual duration spent at the location exceeded the scheduled duration.',
                    })
                  : ''
              }
            >
              <Box sx={{ ...(isExceeded && { color: 'error.main' }) }}>
                {ctIntl.formatDuration(actualDurationInMinutes * 60)}
              </Box>
            </Tooltip>
          )
        },
      }),
      columnHelper.number((_, row) => row.expectedDurationInMinutes, {
        field: 'expectedDurationInMinutes',
        headerName: ctIntl.formatMessage({ id: 'Scheduled Duration' }),
        flex: 1,
        renderCell: ({ row: { expectedDurationInMinutes } }) => {
          if (!expectedDurationInMinutes) return '-'
          return (
            <Box sx={{ color: 'text.secondary' }}>
              {ctIntl.formatDuration(expectedDurationInMinutes * 60)}
            </Box>
          )
        },
      }),
      columnHelper.number((_, row) => row.itemsWeightInKg, {
        field: 'itemsWeightInKg',
        maxWidth: 280,
        headerName: ctIntl.formatMessage({ id: 'delivery.itemWeight.label' }),
      }),
      columnHelper.number((_, row) => row.itemsVolumeInCubicCm, {
        field: 'itemsVolumeInCubicCm',
        maxWidth: 280,
        headerName: ctIntl.formatMessage({ id: 'delivery.itemVolume.label' }),
      }),
    ],
    [columnHelper, routeInfo.deliveryDriverId, selectedDateRange],
  )

  const getRowId = useCallbackBranded(
    (row: (typeof routeInfo)['orderedStops'][number]) => row.stopId,
    [],
  )

  const getRowHeight = useCallbackBranded(() => 'auto' as const, [])

  if (!selectedRouteId) return null
  return (
    <Paper
      sx={{
        position: 'absolute',
        width: 'calc(100% - 2 * 8px)',
        bottom: 0,
        height: `calc(100% * ${ROUTE_DETAILS_HEIGHT_RATIO})`,
        margin: `${ROUTE_DETAILS_MARGIN}px`,
        borderRadius: 1,
        background: 'background.paper',
        zIndex: 1,
      }}
    >
      <Stack sx={{ height: '100%' }}>
        <Stack
          p={2}
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ overflow: 'hidden' }}
        >
          <Stack
            direction="row"
            gap={0.2}
            alignItems="center"
            sx={{ overflowY: 'hidden' }}
          >
            <Box sx={{ flexShrink: 0, alignSelf: 'flex-start' }}>
              <DriverChip
                driver={
                  routeInfo.deliveryDriverId
                    ? driversList?.byId[routeInfo.deliveryDriverId]
                    : undefined
                }
                canExpand={false}
              />
            </Box>
            <OverflowTypography>
              {({ renderedIn }) => (
                <>
                  {routeInfo.routeName && (
                    <>
                      {renderedIn === 'body' && <DotDivider />}
                      <Typography
                        variant="caption"
                        sx={{ whiteSpace: 'nowrap' }}
                      >
                        {routeInfo.routeName}
                      </Typography>
                    </>
                  )}
                  {totalDistanceAndTime && (
                    <>
                      {totalDistanceAndTime.totalDistance > 0 && (
                        <>
                          <MovingOutlinedIcon
                            sx={{
                              fontSize: 16,
                              verticalAlign: 'sub',
                              ml: 2,
                              ...(renderedIn !== 'tooltip' && { color: 'grey.600' }),
                            }}
                          />
                          &nbsp;
                          <Typography
                            variant="caption"
                            sx={{
                              ...(renderedIn !== 'tooltip' && {
                                color: 'text.secondary',
                              }),
                            }}
                          >
                            {ctIntl.formatMessage({ id: 'Total Distance' })}&nbsp;
                          </Typography>
                          <Typography variant="caption">
                            {(totalDistanceAndTime.totalDistance / 1000).toFixed(2)}km
                          </Typography>
                        </>
                      )}
                      {totalDistanceAndTime.totalTime > 0 && (
                        <>
                          <TimerOutlinedIcon
                            sx={{
                              fontSize: 16,
                              verticalAlign: 'sub',
                              ml: 2,
                              ...(renderedIn !== 'tooltip' && { color: 'grey.600' }),
                            }}
                          />
                          &nbsp;
                          <Typography
                            variant="caption"
                            sx={{
                              ...(renderedIn !== 'tooltip' && {
                                color: 'text.secondary',
                              }),
                            }}
                          >
                            {ctIntl.formatMessage({ id: 'Total Time' })}&nbsp;
                          </Typography>
                          <Typography variant="caption">
                            {ctIntl.formatDuration(totalDistanceAndTime.totalTime)}
                          </Typography>
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </OverflowTypography>
          </Stack>
          <IconButton
            size="small"
            onClick={() => {
              handleSelectRoute({ routeId: null, jobsFromRoute: [] })
              if (focusedDriverId) {
                handleSelectDriver(null)
              }
            }}
          >
            <CloseIcon sx={{ fontSize: 'inherit' }} />
          </IconButton>
        </Stack>
        <UserDataGridWithSavedSettingsOnIDB
          Component={DataGrid}
          getRowHeight={getRowHeight}
          rows={routeInfo.orderedStops}
          getRowId={getRowId}
          columns={columns}
          autosizeOnMount
          dataGridId="delivery-revamp-route-details"
          disableRowSelectionOnClick
          loading={false}
          hideFooter
          disableColumnSorting
          sortModel={[]} // Some users are having sortModel in their IndexDB, just in case
          sx={{
            flex: 1,
            '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': { py: 1 },
            '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': { py: 1.5 },
            '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': { py: 2 },
          }}
          slots={{
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
          }}
        />
      </Stack>
    </Paper>
  )
}

const RouteDetails = () => {
  const { normalizedRoutesList, isLoading } = useDriversAndRoutesContext()
  const { selectedRouteId } = useSelectRoute()

  return match({ normalizedRoutesList, isLoading, selectedRouteId })
    .with({ isLoading: true }, () => <CircularProgressDelayedAbsolute />)
    .with(
      {
        normalizedRoutesList: P.nonNullable,
        selectedRouteId: P.nonNullable,
      },
      ({ normalizedRoutesList, selectedRouteId }) => {
        const selectedRoute = normalizedRoutesList.byId[selectedRouteId] //Should be its own api call
        return selectedRoute ? <RouteDetailsContent routeInfo={selectedRoute} /> : null
      },
    )
    .otherwise(() => null)
}

export default RouteDetails
