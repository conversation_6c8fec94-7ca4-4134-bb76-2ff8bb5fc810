import {
  MOBILE_APP_DATA,
  ROUTING_PROFILE,
  ROUTING_PROVIDER_API,
  SUB_USER_ACCESS_SCOPE,
} from '../../constants/app-setting'
import {
  APPEARANCE_VALUE,
  GRID_STYLE,
  MAP_STYLE,
  VIEWS,
} from '../../constants/appearance'
import type { SettingsData } from './types'

export const deliveryUserSettingsDefault: SettingsData = {
  appearanceDefaultView: VIEWS.MAP,
  appearanceMapStyle: MAP_STYLE.DARK,
  appearanceMode: APPEARANCE_VALUE.SIMPLE,
  appearanceTableViewDensity: GRID_STYLE.COMPACT,
  contractDriversEnabled: '1',
  contractMaxDriversEnabled: '20',
  driverLabel: 'Sample driver #1',
  driverMaxStopsBusy: '25',
  driverMobileAddItems: false,
  driverMobileNavigationCustomEnabled: false,
  driverMobileNavigationCustomUrlName: '',
  driverMobileNavigationCustomUrlTemplate: '',
  driverMobileRejectJobs: false,
  driverMobileStopReorder: false,
  driverShowPlannedJobs: false,
  driverTimeoutOffline: '216',
  driverTrackLocations: true,
  enableShowDriverActualRoutes: false,
  fieldService: true,
  importCustomerMapping: null,
  importJobMapping: null,
  inetCustomReportTemplate: null,
  jobImportDefaultCountryEnabled: false,
  jobImportDefaultCountryValue: null,
  jobTemplate: '',
  jobFormTemplate: '',
  jobType: '13',
  jobViewPreviousDaysActive: true,
  jobViewPreviousNumDays: '30',
  mobileAppDownloadUrl: MOBILE_APP_DATA.DOWNLOAD_URL,
  mobileAppName: MOBILE_APP_DATA.NAME,
  mobileAppQrCodeImage: MOBILE_APP_DATA.QR_CODE,
  notifCustomerEmailExtraRecipients: null,
  notifCustomerSendEmail: true,
  notifCustomerSendSms: false,
  notifCustomerSmsExtraRecipients: null,
  picupMaxJobs: '0',
  routificMaxJobs: '0',
  routingAvoidTolls: false,
  routingBalance: false,
  routingConsiderRoadTraffic: false,
  routingCurrentProfile: ROUTING_PROFILE.DEFAULT,
  routingDynamicBalance: false,
  routingMaxVehicleOvertime: '0',
  routingMaxVisitLateness: '0',
  routingMinVehicles: '1',
  routingMinVisitsPerVehicle: '1',
  routingProfileCustomAvoidTolls: false,
  routingProfileCustomBalance: false,
  routingProfileCustomConsiderRoadTraffic: false,
  routingProfileCustomDynamicBalance: false,
  routingProfileCustomMaxVehicleOvertime: '0',
  routingProfileCustomMaxVisitLateness: '0',
  routingProfileCustomMinVehicles: '1',
  routingProfileCustomMinVisitsPerVehicle: '1',
  routingProfileCustomShortestDistance: false,
  routingProfileCustomSquashDurations: '2',
  routingProfileCustomTraffic: 'normal',
  routingProfileCustomUseSavedDriverLocations: false,
  routingProviderApi: ROUTING_PROVIDER_API.PICUP,
  routingShortestDistance: true,
  routingSquashDurations: '2',
  routingTraffic: 'normal',
  routingUseSavedDriverLocations: true,
  subusersDataAccessScope: SUB_USER_ACCESS_SCOPE.OWNED_PLUS_ADMIN,
  designStyleCurrent: 'old',
  designStyleSwitchAllowed: false,
  feedbackLastSubmissionTs: null,
  newjobsPanelRejectedPosition: 'top',
  driverEnforceStopArrivalRangeEnabled: false,
  driverEnforceStopArrivalRangeRadius: '0',
  autoplanningDeliveryBasicAllowed: false,
}
