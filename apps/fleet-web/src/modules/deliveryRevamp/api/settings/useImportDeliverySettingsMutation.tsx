import { useMutation, type UseMutationOptions } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'

export declare namespace FetchSetDeliverySettings {
  export type ApiOutput = {
    key: string
    enable: string
    value: string
  }

  export type Job = {
    setting_key: string
    setting_value: string
    setting_type: string
  }

  export type Driver = {
    setting_key: string
    setting_value: string
    setting_type: string
  }

  export type SettingObjectType = {
    job: Array<Job>
    driver: Array<Driver>
    [key: string]: any
  }

  export type ImportFormat = {
    key: string
    enable: string
    value: string
  }

  type ApiInput = Array<ImportFormat>

  type Return = Array<ApiOutput>
}

const createKey = (input?: FetchSetDeliverySettings.ApiInput) =>
  ['deliveryRevamp/setDeliverySettings', { input }] as const

const useDeliveryExportSettingsMutation = <Context,>(
  query?: FetchSetDeliverySettings.ApiInput,
  options?: UseMutationOptions<
    FetchSetDeliverySettings.Return,
    Error,
    FetchSetDeliverySettings.ApiInput | undefined,
    Context
  >,
) =>
  useMutation<
    FetchSetDeliverySettings.Return,
    Error,
    FetchSetDeliverySettings.ApiInput | undefined,
    Context
  >({
    mutationKey: createKey(query),
    mutationFn: (input?: FetchSetDeliverySettings.ApiInput) =>
      fetchSetDeliverySettings(input),
    ...options,
  })

const fetchSetDeliverySettings = async (data?: FetchSetDeliverySettings.ApiInput) =>
  apiCallerNoX<FetchSetDeliverySettings.Return>('delivery_set_user_settings', {
    data: data || {},
  }).then((res) => res)

export default useDeliveryExportSettingsMutation
