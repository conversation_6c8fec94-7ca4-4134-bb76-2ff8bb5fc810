import { useMutation, type UseMutationOptions } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'

import { camelize } from '../../helpers'

export declare namespace FetchGetDeliverySettings {
  export type ApiInput = {
    filters: {
      type: string
    }
  }

  export type Job = {
    settingKey: string
    settingValue: string
    settingType: string
  }

  export type Driver = {
    settingKey: string
    settingValue: string
    settingType: string
  }

  export type ApiOutput = {
    job: Array<Job>
    driver: Array<Driver>
    [key: string]: any
  }

  type Return = ReturnType<typeof parseDeliverySettings> & {
    [key: string]: any
  }
}

const createKey = (input?: FetchGetDeliverySettings.ApiInput) =>
  ['deliveryRevamp/getDeliverySettings', { input }] as const

const useExportDeliverySettingsMutation = <Context,>(
  query?: FetchGetDeliverySettings.ApiInput,
  options?: UseMutationOptions<
    FetchGetDeliverySettings.Return,
    Error,
    FetchGetDeliverySettings.ApiInput | undefined,
    Context
  >,
) =>
  useMutation<
    FetchGetDeliverySettings.Return,
    Error,
    FetchGetDeliverySettings.ApiInput | undefined,
    Context
  >({
    mutationKey: createKey(query),
    mutationFn: (input?: FetchGetDeliverySettings.ApiInput) =>
      FetchGetDeliverySettings(input),
    ...options,
  })

const parseDeliverySettings = (settingsData: FetchGetDeliverySettings.ApiOutput) => ({
  job: settingsData.job.map((item) => camelize(item)),
  driver: settingsData.driver.map((item) => camelize(item)),
})

const FetchGetDeliverySettings = async (data?: FetchGetDeliverySettings.ApiInput) =>
  apiCallerNoX<FetchGetDeliverySettings.Return>('delivery_user_settings', {
    data: data || {},
  }).then((res) => parseDeliverySettings(res))

export default useExportDeliverySettingsMutation
