import type {
  ROUTING_PROFILE,
  ROUTING_PROVIDER_API,
  ROUTING_TRAFFIC_VALUES,
  SUB_USER_ACCESS_SCOPE,
} from '../../constants/app-setting'
import type {
  APPEARANCE_VALUE,
  GRID_STYLE,
  MAP_STYLE,
  VIEWS,
} from '../../constants/appearance'

export type SettingsData = {
  appearanceDefaultView: VIEWS
  appearanceMapStyle: MAP_STYLE
  appearanceMode: APPEARANCE_VALUE
  appearanceTableViewDensity: GRID_STYLE
  contractDriversEnabled: string
  contractMaxDriversEnabled: string
  driverLabel: string
  driverMaxStopsBusy: string
  driverMobileAddItems: boolean
  driverMobileNavigationCustomEnabled: boolean
  driverMobileNavigationCustomUrlName: string
  driverMobileNavigationCustomUrlTemplate: string
  driverMobileRejectJobs: boolean
  driverMobileStopReorder: boolean
  driverShowPlannedJobs: boolean
  driverTimeoutOffline: string
  driverTrackLocations: boolean
  enableShowDriverActualRoutes: boolean
  fieldService: boolean
  importCustomerMapping: string | null
  importJobMapping: string | null
  inetCustomReportTemplate: string | null
  jobImportDefaultCountryEnabled: boolean
  jobImportDefaultCountryValue: string | null
  jobTemplate: string
  jobFormTemplate: string
  jobType: string
  jobViewPreviousDaysActive: boolean
  jobViewPreviousNumDays: string
  mobileAppDownloadUrl: string
  mobileAppName: string
  mobileAppQrCodeImage: string
  notifCustomerEmailExtraRecipients: string | null
  notifCustomerSendEmail: boolean
  notifCustomerSendSms: boolean
  notifCustomerSmsExtraRecipients: string | null
  picupMaxJobs: string
  routificMaxJobs: string
  routingAvoidTolls: boolean
  routingBalance: boolean
  routingConsiderRoadTraffic: boolean
  routingCurrentProfile: ROUTING_PROFILE
  routingDynamicBalance: boolean
  routingMaxVehicleOvertime: string
  routingMaxVisitLateness: string
  routingMinVehicles: string
  routingMinVisitsPerVehicle: string
  routingProfileCustomAvoidTolls: boolean
  routingProfileCustomBalance: boolean
  routingProfileCustomConsiderRoadTraffic: boolean
  routingProfileCustomDynamicBalance: boolean
  routingProfileCustomMaxVehicleOvertime: string
  routingProfileCustomMaxVisitLateness: string
  routingProfileCustomMinVehicles: string
  routingProfileCustomMinVisitsPerVehicle: string
  routingProfileCustomShortestDistance: boolean
  routingProfileCustomSquashDurations: string
  routingProfileCustomTraffic: ROUTING_TRAFFIC_VALUES
  routingProfileCustomUseSavedDriverLocations: boolean
  routingProviderApi: ROUTING_PROVIDER_API
  routingShortestDistance: boolean
  routingSquashDurations: string
  routingTraffic: ROUTING_TRAFFIC_VALUES
  routingUseSavedDriverLocations: boolean
  subusersDataAccessScope: SUB_USER_ACCESS_SCOPE
  designStyleCurrent: 'old' | 'new'
  designStyleSwitchAllowed: boolean
  feedbackLastSubmissionTs: string | null
  newjobsPanelRejectedPosition: 'top' | 'bottom'
  driverEnforceStopArrivalRangeEnabled: boolean
  driverEnforceStopArrivalRangeRadius: string
  autoplanningDeliveryBasicAllowed: boolean
}
