import { snakeCase } from 'lodash'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { SnakeCasedProperties } from 'type-fest'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { SettingsData } from './types'
import {
  deliverySettingsQueryKey,
  type DeliverySettings,
} from './useDeliverySettingsQuery'

export declare namespace UpdateDeliverySettings {
  type ApiOutput = {
    result: string
    error: string
  }

  type MutationInput = Array<{
    key: keyof SettingsData
    value: unknown
  }>

  type ApiInput = Array<{
    key: keyof SnakeCasedProperties<SettingsData>
    value: unknown
  }>

  type Return = ReturnType<typeof parseUpdateDeliverySettings>
}

const updateDeliverySettings = async (data: UpdateDeliverySettings.MutationInput) =>
  apiCallerNoX<UpdateDeliverySettings.ApiOutput>('delivery_set_user_settings', {
    data: normalizeUpdateDeliverySettingsMutationInput(data),
  }).then(parseUpdateDeliverySettings)

const normalizeUpdateDeliverySettingsMutationInput = (
  input: UpdateDeliverySettings.MutationInput,
): UpdateDeliverySettings.ApiInput =>
  input.map((setting) => ({
    key: snakeCase(setting.key) as UpdateDeliverySettings.ApiInput[number]['key'],
    value: setting.value,
  }))

const parseUpdateDeliverySettings = (
  output: UpdateDeliverySettings.ApiOutput,
): UpdateDeliverySettings.ApiOutput => ({
  result: output.result,
  error: output.error,
})

export const useUpdateDeliverySettingsMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateDeliverySettings,
    onMutate: async (newSettings) => {
      await queryClient.cancelQueries({ queryKey: deliverySettingsQueryKey })

      const previousSettings = queryClient.getQueryData(deliverySettingsQueryKey)

      queryClient.setQueryData<DeliverySettings.Return | undefined>(
        deliverySettingsQueryKey,
        (oldSettings) => {
          if (oldSettings !== undefined) {
            return {
              ...oldSettings,
              ...newSettings.reduce(
                (acc, { key, value }) => {
                  acc[key] = value
                  return acc
                },
                {} as Record<keyof SettingsData, any>,
              ),
            }
          }

          return oldSettings
        },
      )

      return { previousSettings }
    },
    onError: (_error, _newSettings, context) => {
      if (context !== undefined) {
        queryClient.setQueryData(deliverySettingsQueryKey, context.previousSettings)
      }

      enqueueSnackbarWithCloseAction(
        //TODO: DELIVERY REVAMP - Translate
        ctIntl.formatMessage({
          id: 'There was an error updating the settings',
        }),
        { variant: 'error' },
      )
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: deliverySettingsQueryKey,
      })
    },
  })
}
