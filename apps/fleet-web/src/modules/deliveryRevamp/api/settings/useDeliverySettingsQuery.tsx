import { mapValues } from 'lodash'
import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import type { Except } from 'type-fest'

import { apiCallerNoX } from '@fleet-web/api/api-caller'

import { camelize } from '../../helpers'
import type { SettingsData } from './types'

export declare namespace DeliverySettings {
  type ApiOutput = SettingsData

  type Return = ReturnType<typeof parseDeliverySettings>
}

export const deliverySettingsQueryKey = ['deliveryRevamp/userSettings'] as const

const parseDeliverySettingValue = <
  const FallbackValue extends boolean | string | null | undefined,
>(
  value: unknown,
  {
    fallback,
  }: {
    fallback: FallbackValue
  },
): boolean | FallbackValue => {
  switch (value) {
    case true:
    case 'true': {
      return true
    }
    case false:
    case 'false': {
      return false
    }
    default: {
      return fallback
    }
  }
}

function useDeliverySettingsQuery<TData = DeliverySettings.Return>(
  options?: Except<
    UseQueryOptions<
      DeliverySettings.Return,
      Error,
      TData,
      typeof deliverySettingsQueryKey
    >,
    'queryKey' | 'queryFn'
  >,
) {
  return useQuery<
    DeliverySettings.Return,
    Error,
    TData,
    typeof deliverySettingsQueryKey
  >({
    queryKey: deliverySettingsQueryKey,
    queryFn: () => fetchDeliverySettings(),
    ...options,
  })
}

const fetchDeliverySettings = () =>
  apiCallerNoX<DeliverySettings.ApiOutput>('delivery_user_settings', {
    data: {},
  }).then((res) => {
    const deliverySettings = parseDeliverySettings(res)
    localStorage.setItem('deliverySettings', JSON.stringify(deliverySettings))
    return deliverySettings
  })

const parseDeliverySettings = (settingsData: DeliverySettings.ApiOutput) =>
  camelize(
    mapValues(settingsData, (value) =>
      parseDeliverySettingValue(value, { fallback: value }),
    ) as SettingsData,
  )

export default useDeliverySettingsQuery
