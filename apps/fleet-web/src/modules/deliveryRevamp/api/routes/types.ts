import type { CommonStopApiOutput } from '@fleet-web/modules/deliveryRevamp/api/jobs/types'
import type { PlanRecurrence } from '@fleet-web/modules/deliveryRevamp/api/plans/types'

import type { SCHEDULE_TYPE_ID } from '../../constants/job'

type PlanIdAndRecurrence =
  | {
      recurrence: PlanRecurrence
      planId: number
    }
  | {
      recurrence: null
      planId: number | null
    }

export type OrderedStopApiOutput = CommonStopApiOutput & {
  jobId: number
  deliveryDate: string | null
  scheduledDeliveryTs: string | null
  activityStartedTs: string | null
  activityArrivedTs: string | null
  activityCompletedTs: string | null
  referenceNumber: string | null
  subuserId: number | null
  deliveryDriverId: string
  orderId: string
  sendToDriverAt: string | null
  allowedToStartAt: string | null
  etaInSeconds: number | null
  planName: string | null
  itemsWeightInKg: number
  itemsVolumeInCubicCm: number
} & PlanIdAndRecurrence

export type RouteId = `driver_${string}` | `plan_${string}`

export type RouteApiOutput = {
  routeId: RouteId
  routeName: string | null
  orderedStops: Array<OrderedStopApiOutput>
  planIds: Array<number>
  driverFullname: string
  fromTs: string | null
  toTs: string | null
  scheduleTypeId: SCHEDULE_TYPE_ID
}

export type Leg = {
  legId: number
  startStopId: number | null
  endStopId: number | null
  createTs: string
  updateTs: string
  projectedPolyline: string
  distance: number
  lastEstimatedTravelTime: number
  driverId: string | null
  driverTravelPolyline: string | null
  driverTravelDistance: number | null
  driverTravelTime: number | null
  userId: number
  isDeleted: boolean
}
