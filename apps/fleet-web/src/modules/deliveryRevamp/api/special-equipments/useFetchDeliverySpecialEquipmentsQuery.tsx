import { useCallback } from 'react'
import { useQuery, type QueryClient, type UseQueryOptions } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import {
  DEFAULT_SPECIAL_EQUIPMENT_OPTIONS,
  DEFAULT_SPECIAL_EQUIPMENTS,
} from '../../constants/driver'
import { formatSpecialEquipments } from '../../helpers'
import type { SpecialEquipmentsApiOutput, SpecialEquipmentsReturn } from './types'

export declare namespace FetchDeliverySpecialEquipments {
  type ApiOutput = {
    data: Array<SpecialEquipmentsApiOutput>
  }
  type Return = Array<SpecialEquipmentsReturn>
}

export const fetchDeliverySpecialEquipmentsKey = () =>
  ['deliveryRevamp/specialRequirementLists'] as const

export const prefetchDeliverySpecialEquipments = async (
  queryClient: QueryClient,
  options?: UseQueryOptions<FetchDeliverySpecialEquipments.Return, Error>,
) => {
  await queryClient.prefetchQuery<FetchDeliverySpecialEquipments.Return, Error>({
    ...specialEquipmentQuery(),
    ...options,
  })
}

const fetchDeliverySpecialEquipments = async () =>
  apiCallerNoX<FetchDeliverySpecialEquipments.ApiOutput>(
    'delivery_user_delivery_capability_list',
    { data: [] },
  ).then((res) => parseCreateSpecialEquipments(res.data))

export const specialEquipmentQuery = () =>
  createQuery({
    queryKey: fetchDeliverySpecialEquipmentsKey(),
    queryFn: () => fetchDeliverySpecialEquipments(),
  })

const parseCreateSpecialEquipments = (
  data: FetchDeliverySpecialEquipments.ApiOutput['data'],
): FetchDeliverySpecialEquipments.Return =>
  data.map((requirements) => ({
    id: requirements.capabilityId,
    name: requirements.capabilityName,
    userId: requirements.userId,
  }))

function useFetchDeliverySpecialEquipments(
  options?: UseQueryOptions<FetchDeliverySpecialEquipments.Return, Error>,
) {
  return useQuery<FetchDeliverySpecialEquipments.Return, Error>({
    ...specialEquipmentQuery(),
    ...options,
  })
}

export default Object.assign(useFetchDeliverySpecialEquipments, {
  createKey: fetchDeliverySpecialEquipmentsKey,
})

export const parseSpecialEquipmentOptions = (
  specialOption: FetchDeliverySpecialEquipments.Return,
) => {
  const specialEquipmentsData = specialOption
    .sort((a, b) => Number(a.id) - Number(b.id))
    .sort((a, b) => Number(a.userId) - Number(b.userId))

  return specialEquipmentsData.map((specialEquipments) => ({
    value: Number(specialEquipments.id),
    label: DEFAULT_SPECIAL_EQUIPMENTS.includes(specialEquipments.name)
      ? formatSpecialEquipments(specialEquipments.name)
      : specialEquipments.name,
    name: DEFAULT_SPECIAL_EQUIPMENTS.includes(specialEquipments.name)
      ? formatSpecialEquipments(specialEquipments.name)
      : specialEquipments.name,
    userId: specialEquipments.userId,
  }))
}

export const useSpecialEquipmentsOptions = () =>
  useQuery({
    ...specialEquipmentQuery(),
    select: useCallback(
      (data: FetchDeliverySpecialEquipments.Return) => ({
        data: data
          ? parseSpecialEquipmentOptions(data)
          : DEFAULT_SPECIAL_EQUIPMENT_OPTIONS.map((option) => ({
              ...option,
              name: ctIntl.formatMessage({ id: option.name }),
              label: ctIntl.formatMessage({ id: option.label }),
            })),
      }),
      [],
    ),
  })
