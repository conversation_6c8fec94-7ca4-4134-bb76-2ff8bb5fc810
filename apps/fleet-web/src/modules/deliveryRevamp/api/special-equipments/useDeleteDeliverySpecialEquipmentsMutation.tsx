import {
  useMutation,
  useQueryClient,
  type UseMutationOptions,
} from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import type { FixMeAny } from '@fleet-web/types'

import useFetchDeliverySpecialEquipments, {
  type FetchDeliverySpecialEquipments,
} from './useFetchDeliverySpecialEquipmentsQuery'

declare namespace DeleteSpecialEquipments {
  type ApiInput = { capabilityIds: Array<number> }
  type ApiOutput = { id: number; result?: FixMeAny; error: string }
  type Return = ReturnType<typeof parseDeleteSpecialEquipments>
}

const createKey = () => ['deliveryRevamp/DeleteSpecialEquipments'] as const

const deleteDeliverySpecialEquipments = async (
  data: DeleteSpecialEquipments.ApiInput,
) =>
  apiCallerNoX<DeleteSpecialEquipments.ApiOutput>(
    'delivery_custom_delivery_capability_remove',
    { data },
  ).then((res) => res)

export const parseDeleteSpecialEquipments = (
  output: DeleteSpecialEquipments.ApiOutput,
): DeleteSpecialEquipments.ApiOutput => ({
  id: output.id,
  result: output.result,
  error: output.error,
})

export const useDeleteDeliverySpecialEquipmentsMutation = (
  options?: UseMutationOptions<
    DeleteSpecialEquipments.Return,
    Error,
    DeleteSpecialEquipments.ApiInput
  >,
) => {
  const queryClient = useQueryClient()
  return useMutation<
    DeleteSpecialEquipments.Return,
    Error,
    DeleteSpecialEquipments.ApiInput,
    FetchDeliverySpecialEquipments.Return
  >({
    mutationKey: createKey(),
    mutationFn: deleteDeliverySpecialEquipments,
    ...options,
    onMutate: async (payload) => {
      await queryClient.cancelQueries({
        queryKey: useFetchDeliverySpecialEquipments.createKey(),
      })
      const previousData =
        queryClient.getQueryData<FetchDeliverySpecialEquipments.Return>(
          useFetchDeliverySpecialEquipments.createKey(),
        )
      // Optimistically update to the new value
      if (previousData) {
        queryClient.setQueryData(
          useFetchDeliverySpecialEquipments.createKey(),
          previousData.filter((se) => !payload.capabilityIds.includes(se.id)),
        )
      }
      return previousData
    },
    onSuccess: async (...args) => {
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }
    },
    onError: (error, _, context) => {
      queryClient.setQueryData(useFetchDeliverySpecialEquipments.createKey(), context)
      if (options?.onError) {
        options?.onError(error, _, context)
      }
    },
    onSettled: (...args) => {
      if (options?.onSettled) {
        options?.onSettled(...args)
      }
    },
  })
}
