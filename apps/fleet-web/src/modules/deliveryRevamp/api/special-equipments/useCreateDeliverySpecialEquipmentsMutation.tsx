import {
  useMutation,
  useQueryClient,
  type UseMutationOptions,
} from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { getSpecialEquipmentError } from '../../utils/error-message-translation-keys'
import type { SpecialEquipmentsApiOutput, SpecialEquipmentsReturn } from './types'
import useFetchDeliverySpecialEquipments from './useFetchDeliverySpecialEquipmentsQuery'

declare namespace CreateSpecialEquipments {
  type ApiInput = { capabilityNames: Array<string> }
  type ApiOutput = {
    data: Array<SpecialEquipmentsApiOutput>
  }
  type Return = Array<SpecialEquipmentsReturn>
}

const createKey = () => ['deliveryRevamp/CreateSpecialEquipments'] as const

const createDeliverySpecialEquipments = async (
  data: CreateSpecialEquipments.ApiInput,
) =>
  apiCallerNoX<CreateSpecialEquipments.ApiOutput>(
    'delivery_custom_delivery_capability_add',
    { data },
  ).then((res) => parseCreateSpecialEquipments(res.data))

const parseCreateSpecialEquipments = (
  data: CreateSpecialEquipments.ApiOutput['data'],
): CreateSpecialEquipments.Return =>
  data.map((requirements) => ({
    id: requirements.capabilityId,
    name: requirements.capabilityName,
    userId: requirements.userId,
  }))

export const useCreateDeliverySpecialEquipmentsMutation = (
  options?: UseMutationOptions<
    CreateSpecialEquipments.Return,
    Error,
    CreateSpecialEquipments.ApiInput
  >,
) => {
  const queryClient = useQueryClient()

  return useMutation<
    CreateSpecialEquipments.Return,
    Error,
    CreateSpecialEquipments.ApiInput
  >({
    mutationKey: createKey(),
    mutationFn: createDeliverySpecialEquipments,
    ...options,
    onSuccess: async (...args) => {
      if (options?.onSuccess) {
        options.onSuccess(...args)
      }

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'delivery.addSpecialEquipment.success',
        }),
        {
          variant: 'success',
        },
      )
    },
    onError: (...args) => {
      if (options?.onError) {
        options?.onError(...args)
      }

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: getSpecialEquipmentError(args[0].message),
        }),
        {
          variant: 'error',
        },
      )
    },
    onSettled: (...args) => {
      if (options?.onSettled) {
        options?.onSettled(...args)
      }

      queryClient.invalidateQueries({
        queryKey: useFetchDeliverySpecialEquipments.createKey(),
      })
    },
  })
}
