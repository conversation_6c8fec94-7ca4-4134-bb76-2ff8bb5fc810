import { useMutation, useQuery } from '@tanstack/react-query'
import type { DateTime } from 'luxon'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { makeQueryErrorHandlerWithToast } from '@fleet-web/api/helpers'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import type { JobId } from '../jobs/types'

export declare namespace SchedulerStats {
  type ApiInput = {
    date: DateTime | null
  }
  type ApiOutput = object //TODO: add type
}

const schedulerStatsKey = (payload: SchedulerStats.ApiInput) =>
  payload.date
    ? ['deliveryRevamp/schedulerStats', payload]
    : ['deliveryRevamp/schedulerStats']

export function useSchedulerStatsQuery(payload: SchedulerStats.ApiInput) {
  return useQuery(schedulerStatsQuery(payload))
}

function schedulerStatsQuery(payload: SchedulerStats.ApiInput) {
  return createQuery({
    queryKey: schedulerStatsKey(payload),
    queryFn: () => fetchSchedulerStats(payload),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchSchedulerStats(payload: SchedulerStats.ApiInput) {
  return apiCallerNoX<SchedulerStats.ApiOutput>('delivery_scheduler_stats', {
    data: payload,
  })
}

export declare namespace BulkUpdateScheduledJobs {
  type ApiInput = {
    jobIds: Array<JobId>
    date: DateTime | null
  }
  type ApiOutput = object //TODO: add type
}

const bulkUpdateScheduledJobsKey = (payload: BulkUpdateScheduledJobs.ApiInput) => [
  'deliveryRevamp/bulkUpdateScheduledJobs',
  payload,
]

export function useBulkUpdateScheduledJobsMutation(
  payload: BulkUpdateScheduledJobs.ApiInput,
) {
  return useMutation({
    mutationKey: bulkUpdateScheduledJobsKey(payload),
    mutationFn: () => bulkUpdateScheduledJobs(payload),
  })
}

async function bulkUpdateScheduledJobs(payload: BulkUpdateScheduledJobs.ApiInput) {
  return apiCallerNoX<BulkUpdateScheduledJobs.ApiOutput>(
    'delivery_bulk_update_scheduled_jobs',
    {
      data: payload,
    },
  )
}
