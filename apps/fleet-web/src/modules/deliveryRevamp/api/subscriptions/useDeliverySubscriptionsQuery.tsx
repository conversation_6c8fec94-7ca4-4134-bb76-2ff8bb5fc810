import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from '@fleet-web/api/api-caller'
import { makeQueryErrorHandlerWithToast } from '@fleet-web/api/helpers'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

//constants
import { CONTRACT_TYPE, SUBSCRIPTION_TYPE } from './constants'
//types
import type { SubscriptionsApiOutput, SubscriptionsReturn } from './types'

export declare namespace FetchDeliverySubscriptions {
  type ApiOutput = {
    subscriptions: SubscriptionsApiOutput
  }
  type Return = SubscriptionsReturn
}

export const deliverySubscriptionsQueryKey = [
  'deliveryRevamp',
  'subscriptions',
] as const

export const deliverySubscriptionsQuery = () =>
  createQuery({
    queryKey: deliverySubscriptionsQueryKey,
    queryFn: fetchDeliverySubscriptions,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useDeliverySubscriptionsQuery = () =>
  useQuery(deliverySubscriptionsQuery())

const fetchDeliverySubscriptions = async () =>
  apiCallerNoX<FetchDeliverySubscriptions.ApiOutput>(
    'delivery_user_subscription_list',
    { data: [] },
  ).then((res) => parseSubscriptions(res.subscriptions))

const parseSubscriptions = (
  data: FetchDeliverySubscriptions.ApiOutput['subscriptions'],
): FetchDeliverySubscriptions.Return => ({
  standalone: data?.standalone?.map((standalone) => ({
    contractStartDate: standalone.contract_start_date,
    contractEndDate: standalone.contract_end_date,
    maxDrivers: standalone.max_drivers,
    contractTerm: standalone.contract_term,
    type: SUBSCRIPTION_TYPE.STAND_ALONE,
    description: standalone.description || null,
    availableDrivers: standalone.available_drivers,
    contractType: standalone.contract_type,
  })),
  productPackages: data?.productPackages?.map((productPackage) => ({
    contractStartDate: productPackage.contract_start_date,
    contractEndDate: productPackage.contract_end_date,
    maxDrivers: productPackage.max_drivers,
    contractTerm: productPackage.contract_term,
    type: SUBSCRIPTION_TYPE.PRODUCT_PACKAGES,
    description: productPackage.description,
    availableDrivers: productPackage.available_drivers,
    contractType: productPackage.contract_type,
  })),
  productOptions: data?.productOptions?.map((productPackage) => ({
    contractStartDate: productPackage.contract_start_date,
    contractEndDate: productPackage.contract_end_date,
    maxDrivers: productPackage.max_drivers,
    contractTerm: productPackage.contract_term,
    type: SUBSCRIPTION_TYPE.PRODUCT_OPTIONS,
    description: productPackage.description,
    availableDrivers: productPackage.available_drivers,
    contractType: productPackage.contract_type,
  })),
  appUserSettings: data?.appUserSettings?.map((appUserSetting) => ({
    contractStartDate: appUserSetting.contract_start_date,
    contractEndDate: appUserSetting.contract_end_date,
    maxDrivers: appUserSetting.max_drivers,
    contractTerm: appUserSetting.contract_term,
    description: appUserSetting.description,
    contractType: CONTRACT_TYPE.PLUS,
  })),
})

export default useDeliverySubscriptionsQuery
