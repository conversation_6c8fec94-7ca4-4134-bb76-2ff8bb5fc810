import type { FixMeAny, ValueOf } from '@fleet-web/types'

import type { CONTRACT_TYPE, SUBSCRIPTION_TYPE } from './constants'

export type SubscriptionReturnType = {
  contractStartDate: string
  contractEndDate: string | null
  maxDrivers: string
  contractTerm?: string | null
  type: ValueOf<typeof SUBSCRIPTION_TYPE>
  description: string | null
  availableDrivers: number
  contractType: ValueOf<typeof CONTRACT_TYPE>
}

type StandAloneType = {
  contract_start_date: string
  contract_end_date: string | null
  max_drivers: string
  contract_term: string | null
  description: string | null
  available_drivers: number
  contract_type: ValueOf<typeof CONTRACT_TYPE>
}

type ProductPackagesType = {
  contract_start_date: string
  contract_end_date: string | null
  max_drivers: string
  contract_term: string | null
  product_package_id: string
  description: string
  available_drivers: number
  contract_type: ValueOf<typeof CONTRACT_TYPE>
}

type AppUserSettingsType = {
  contract_start_date: string
  contract_end_date: string | null
  max_drivers: string
  contract_term: string | null
  description: string
  contract_type: ValueOf<typeof CONTRACT_TYPE>
}

type AppUserSettingsReturnType = {
  contractStartDate: string
  contractEndDate: string | null
  maxDrivers: string
  contractTerm: string | null
  description: string
  contractType: ValueOf<typeof CONTRACT_TYPE>
}

type ProductOptionsType = FixMeAny // TODO: Will eventually update the types once determine the object values

export type SubscriptionsApiOutput = {
  productPackages?: Array<ProductPackagesType>
  productOptions?: Array<ProductOptionsType>
  standalone?: Array<StandAloneType>
  appUserSettings?: Array<AppUserSettingsType>
}

export type SubscriptionsReturn = {
  productPackages?: Array<SubscriptionReturnType>
  productOptions?: Array<SubscriptionReturnType>
  standalone?: Array<SubscriptionReturnType>
  appUserSettings?: Array<AppUserSettingsReturnType>
}
