import { useMemo } from 'react'
import { CircularProgress, Dialog, Stack, Typography } from '@karoo-ui/core'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'
import { z } from 'zod'

import type { ClientUserId } from '@fleet-web/api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import NavigationTabs from '@fleet-web/components/_tabs/NavigationTabs'
import NavigationPanel from '@fleet-web/components/Panel/Navigation'
import { useValidatedJsonifiableSearchParams } from '@fleet-web/hooks/useValidatedSearchParams'
import GlobalModalContentContainer from '@fleet-web/modules/app/GlobalModals/components/ContentContainer'

import { useFetchRoleInfo } from '../../settings/manage-roles/api/queries'
import { rolesModalParamsSchema } from '../../settings/manage-roles/shared/utils'
import { UserEditingContextProvider } from '../../settings/manage-users/EditUser/UserEditingContext'
import useEditRoleModalTabs from './useEditRoleModalTabs'

const ROLES_MODAL_TABS = [
  'profile',
  'permissions',
  'userDataAccess',
  'userDataSharing',
] as const

export const rolesModalTabsSchema = z.enum(ROLES_MODAL_TABS)

export type RoleBaseState = z.infer<typeof rolesModalTabsSchema>

function EditRoleWrapper() {
  const { data } = useValidatedJsonifiableSearchParams(() => rolesModalParamsSchema)

  if (!data || !('rolesModal' in data)) return null

  return <EditRoleContent validatedSearchParams={data} />
}

type Props = {
  validatedSearchParams: z.infer<typeof rolesModalParamsSchema>
}

function EditRoleContent({ validatedSearchParams }: Props) {
  const history = useHistory()

  const roleModalSection = useMemo(
    () => validatedSearchParams.rolesModal?.type,
    [validatedSearchParams],
  )

  const userId = validatedSearchParams.rolesModal?.id

  const roleInfo = useFetchRoleInfo({ client_user_id: userId as string })

  const { visibleTabsArray } = useEditRoleModalTabs(userId as string)

  const activeVisibleTab = useMemo(() => {
    if (roleModalSection === undefined) {
      return undefined
    }

    return visibleTabsArray.find((t) => t.id === roleModalSection)
  }, [roleModalSection, visibleTabsArray])

  const closeDialog = () => {
    history.push({
      search: buildRouteQueryStringKeepingExistingSearchParams({
        location: history.location,
        schema: rolesModalParamsSchema,
        searchParams: {
          rolesModal: undefined,
        },
        options: { shouldJsonStringify: true },
      }),
    })
  }

  return (
    <>
      {roleModalSection && userId && (
        <Dialog
          open={true}
          onClose={closeDialog}
          sx={{
            '& .MuiDialog-paper': {
              maxWidth: 'unset',
              height: '80%',
              width: '85%',
            },
          }}
        >
          <Stack
            direction="row"
            width="100%"
            height="100%"
            overflow="hidden"
          >
            <NavigationPanel>
              <Typography
                sx={{
                  px: 2,
                  pb: 2,
                  pt: 4,
                }}
                variant="subtitle2"
              >
                {match(roleInfo)
                  .with({ status: 'pending' }, () => <CircularProgress />)
                  .with({ status: 'error' }, () => null)
                  .with({ status: 'success' }, ({ data }) => data.roleName)
                  .exhaustive()}
              </Typography>
              <NavigationTabs
                activeTab={roleModalSection}
                tabs={visibleTabsArray}
                schema={rolesModalTabsSchema}
              />
            </NavigationPanel>
            {activeVisibleTab !== undefined && (
              <UserEditingContextProvider
                value={{ userId: userId as ClientUserId, editType: 'role' }}
              >
                <GlobalModalContentContainer
                  id={ROLES_GLOBAL_MODAL_CONTENT_CONTAINER_ID}
                  title={activeVisibleTab.label}
                  subLabel={activeVisibleTab.subLabel}
                  onCloseButtonClick={closeDialog}
                >
                  <activeVisibleTab.component />
                </GlobalModalContentContainer>
              </UserEditingContextProvider>
            )}
          </Stack>
        </Dialog>
      )}
    </>
  )
}

export default EditRoleWrapper

export const ROLES_GLOBAL_MODAL_CONTENT_CONTAINER_ID =
  'roles-global-modal-content-container'
