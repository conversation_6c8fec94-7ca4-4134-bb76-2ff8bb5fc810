import { useState, type ChangeEvent } from 'react'
import { compact, filter, identity, isEmpty, isNil, pickBy } from 'lodash'
import type { History } from 'history'
import { DateTime } from 'luxon'
import memoizeOne from 'memoize-one'
import { connect } from 'react-redux'

import { selectors } from '@fleet-web/duxs/mifleet/regulatory'
import { MiFleetDatePicker } from '@fleet-web/modules/mifleet/components/date-picker/MiFleetDatePicker'
import TableTotals from '@fleet-web/modules/mifleet/components/utils/table-header-totals'
import type { AppState } from '@fleet-web/root-reducer'
import type { DateTimeRange, FixMeAny } from '@fleet-web/types'
import type { TypedColumns } from '@fleet-web/types/extended/react-table'
import {
  AdvancedTable,
  Button,
  MinimizableSearchBar,
  Spinner,
  TextInput,
  useMifleetFormattedNumber,
} from '@fleet-web/util-components'
import { fuzzySearch } from '@fleet-web/util-functions'
import { createFormattedDateHeader } from '@fleet-web/util-functions/mifleet'
import { addDays } from '@fleet-web/util-functions/time-utils'

import calculateTableRowCount from '../components/utils/calculate-table-row-count'
import filterTableDataByDate from '../components/utils/table-date-filter'
import { checkNumberPositive } from '../operational/shared/operational-validation-positive-number'
import type { OperationalShare } from '../operational/shared/type'
import type { RegulatoryInsurance } from './shared/type'

const { allRegulatoryInsurance, allRegulatoryInsuranceTotals, getRegulatoryLoading } =
  selectors

const defaultFilters = {
  searchInput: '',
  dateRangeInput: {
    from: addDays(new Date(), -60),
    to: new Date(),
  },
  minValueInput: undefined,
  maxValueInput: undefined,
}

// Defined as an interface because it "type" causes issues when used with lodash pickBy
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
interface FilterType {
  dateRangeInput?: DateTimeRange
  maxValueInput?: string
  minValueInput?: string
  searchInput?: string
}

type InsuranceType = {
  isLoading: boolean
  automationId?: string
  history: History
  regulatoryInsurance: Array<RegulatoryInsurance>
  regulatoryInsuranceTotals: OperationalShare.TotalCost
  viewDocumentClick: (
    documentId: string,
    filter?: FilterType,
    activeMenu?: string,
  ) => void
  onlyShowFilteredTotals: boolean
}
const Insurance = ({
  isLoading,
  automationId = undefined,
  history,
  regulatoryInsurance,
  regulatoryInsuranceTotals,
  viewDocumentClick,
  onlyShowFilteredTotals = false,
}: InsuranceType) => {
  const [filters, setFilters] = useState<FilterType>({
    searchInput: '',
    dateRangeInput: {
      from: addDays(new Date(), -60),
      to: new Date(),
    },
    minValueInput: undefined,
    maxValueInput: undefined,
  })

  const formatNumber = useMifleetFormattedNumber()

  const shouldShowClearFilters = () => {
    const check = filter(filters, (o) => !isNil(o) && !isEmpty(o))

    return !isEmpty(check)
  }

  const getTableColumns = (filters?: FilterType): TypedColumns<RegulatoryInsurance> => [
    createFormattedDateHeader({
      Header: 'Date',
      accessor: 'document_date',
      style: { cursor: 'default' },
    }),
    {
      Header: 'Vehicle',
      accessor: 'plate',
      style: { cursor: 'default' },
    },
    {
      Header: 'Insurance Type',
      accessor: 'insurance_type',
      Cell: (cell) => cell.value || '',
      style: { cursor: 'default' },
    },
    {
      Header: 'Policy Number',
      accessor: 'policy_number',
      Cell: (cell) => cell.value || '',
      style: { cursor: 'default' },
    },
    {
      Header: 'Total Value',
      id: 'total_value',
      accessor: (original) => Number(original.total_value),
      Cell: (cell) => formatNumber(cell.value),
      className: 'util-tableEnd',
      style: { cursor: 'default' },
    },
    {
      Header: 'Contract',
      Cell: (cell) => (
        <Button
          icon="list-alt"
          tooltipMessage="View Contract"
          onClick={() =>
            history.push(
              `/mifleet/contracts/insurance-edit/${cell.original.contract_insurance_id}`,
            )
          }
          disabled={!cell.original.contract_insurance_id}
          className={'apply'}
          square
        />
      ),
      className: 'Regulatory-centeredItem',
      maxWidth: 120,
      sortable: false,
      style: { cursor: 'default' },
    },
    {
      Header: 'View Document',
      Cell: (cell) => (
        <Button
          id={automationId ? `view-doc-${automationId}-btn` : undefined}
          icon="eye"
          tooltipMessage="View Document"
          onClick={(e) => {
            e.stopPropagation()
            viewDocumentClick(
              cell.original.document_id,
              {
                ...filters,
                dateRangeInput: [
                  DateTime.fromJSDate(new Date(filters?.dateRangeInput?.from as any)),
                  DateTime.fromJSDate(new Date(filters?.dateRangeInput?.to as any)),
                ] as any,
              },
              'miscConcept',
            )
          }}
          className={'apply'}
          square
          withTooltip
          small
        />
      ),
      className: 'Regulatory-centeredItem',
      style: { cursor: 'default' },
      width: 120,
      sortable: false,
    },
  ]

  const handleSearchInputChange = (e: ChangeEvent<HTMLInputElement>) =>
    setFilters({ ...filters, searchInput: e.target.value })

  const valueRangeChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (checkNumberPositive(e)) {
      setFilters({
        ...filters,
        [e.currentTarget.id]: e.currentTarget.value,
      })
    }
  }

  const handleDatePickerChange = (e: DateTimeRange) =>
    setFilters({
      ...filters,
      dateRangeInput: e,
    })

  const makeTableData = memoizeOne(
    (insurance: Array<RegulatoryInsurance>, filters: FilterType) => {
      const activeFilters = pickBy(filters, identity)

      let items = insurance

      if (activeFilters.searchInput) {
        const f = activeFilters.searchInput
        items = items.filter(
          (o) =>
            fuzzySearch(o.plate, f) ||
            fuzzySearch(o.insurance_type, f) ||
            fuzzySearch(o.policy_number, f),
        )
      }

      if (activeFilters.dateRangeInput) {
        items = filterTableDataByDate(items, activeFilters.dateRangeInput)
      }

      if (activeFilters.minValueInput) {
        const f = Number.parseFloat(activeFilters.minValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) >= f)
      }

      if (activeFilters.maxValueInput) {
        const f = Number.parseFloat(activeFilters.maxValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) <= f)
      }

      return compact(items)
    },
  )

  if (!regulatoryInsurance || isLoading) {
    return <Spinner />
  }

  const memoData = makeTableData(regulatoryInsurance, {
    ...filters,
  })

  return (
    <div className="Regulatory-permits">
      <div className="Regulatory-header">
        <MinimizableSearchBar
          id={automationId ? `search-${automationId}-bar` : undefined}
          onChange={handleSearchInputChange}
          value={filters?.searchInput}
          placeholder={'Search'}
          forceOriginalValue
        />
        <MiFleetDatePicker
          value={filters?.dateRangeInput}
          onChange={handleDatePickerChange}
          isRange
          placeholder={{
            from: 'Start Date',
            to: 'End Date',
          }}
          disableMultipleInput={false}
        />
        <TextInput
          placeholder="Minimum Value"
          id="minValueInput"
          value={filters?.minValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        <TextInput
          placeholder="Maximum Value"
          id="maxValueInput"
          value={filters?.maxValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        {shouldShowClearFilters() && (
          <Button
            square
            icon={['fas', 'eraser']}
            onClick={() => setFilters(defaultFilters)}
          />
        )}
        {(!isEmpty(regulatoryInsuranceTotals) || onlyShowFilteredTotals) && filters && (
          <TableTotals
            filtersAreActive={
              !isEmpty(filters.searchInput) ||
              !isEmpty(filters.dateRangeInput) ||
              !isEmpty(filters.minValueInput) ||
              !isEmpty(filters.maxValueInput)
            }
            data={memoData}
            totals={regulatoryInsuranceTotals}
            showTotal
            onlyShowFilteredTotals={onlyShowFilteredTotals}
          />
        )}
      </div>
      <AdvancedTable
        columns={getTableColumns(filters)}
        data={memoData}
        showPagination
        defaultPageSize={calculateTableRowCount(memoData) || 20}
        minRows={calculateTableRowCount(memoData) || 20}
        getRowProps={() => ({ className: 'Regulatory-tableTr' })}
      />
    </div>
  )
}

const mapStateToProps = (state: AppState) => ({
  isLoading: getRegulatoryLoading(state),
  regulatoryInsurance: allRegulatoryInsurance(state),
  regulatoryInsuranceTotals: allRegulatoryInsuranceTotals(state),
})

const mergeProps = (
  stateProps: FixMeAny,
  dispatchProps: FixMeAny,
  ownProps: FixMeAny,
) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps,
})

export default connect(mapStateToProps, null, mergeProps)(Insurance)
