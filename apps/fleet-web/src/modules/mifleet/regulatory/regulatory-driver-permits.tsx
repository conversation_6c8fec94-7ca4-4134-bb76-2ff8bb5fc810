import { useEffect, useState, type ChangeEvent } from 'react'
import { Form, Formik, type FormikProps } from 'formik'
import { compact, filter, identity, isEmpty, isNil, pickBy } from 'lodash'
import memoizeOne from 'memoize-one'
import { FormattedMessage } from 'react-intl'
import { connect } from 'react-redux'
import type { RowInfo } from 'react-table'

import { actions, selectors } from '@fleet-web/duxs/mifleet/regulatory'
import { MiFleetDatePicker } from '@fleet-web/modules/mifleet/components/date-picker/MiFleetDatePicker'
import TableTotals from '@fleet-web/modules/mifleet/components/utils/table-header-totals'
import type { AppState } from '@fleet-web/root-reducer'
import { generateMetaProps } from '@fleet-web/shared/formik'
import type { DateTimeRange, FixMeAny } from '@fleet-web/types'
import type { TypedColumns } from '@fleet-web/types/extended/react-table'
import {
  AdvancedTable,
  Button,
  InputDropdown,
  Minimizable<PERSON>ear<PERSON><PERSON><PERSON>,
  Spinner,
  TextInput,
  useMifleetFormattedNumber,
} from '@fleet-web/util-components'
import { connectedCtIntl } from '@fleet-web/util-components/connectedCtIntl'
import { createFormattedDateHeader } from '@fleet-web/util-functions/mifleet'

import type { DocumentStatus } from '../api/costInput/shared/types'
import allowByDocumentStatus from '../components/utils/allow-by-document-status'
import calculateTableRowCount from '../components/utils/calculate-table-row-count'
import filterTableDataByDate from '../components/utils/table-date-filter'
import filterTableData from '../components/utils/table-search-filter'
import { checkNumberPositive } from '../operational/shared/operational-validation-positive-number'
import type { OperationalShare } from '../operational/shared/type'
import { regulatoryDriverPermitsValidationSchema } from './shared/regulatory-validation-schema'
import type {
  RegulatoryDriverPermits,
  RegulatoryDriverPermitsTypes,
  SubRegulatoryDriverPermits,
} from './shared/type'

const { updateRegulatoryDriverPermits } = actions

const {
  allRegulatoryDriverPermits,
  allRegulatoryDriverPermitsTotals,
  allRegulatoryDriverPermitsTypes,
  getRegulatoryLoading,
} = selectors

const defaultFilters = {
  searchInput: undefined,
  dateRangeInput: undefined,
  minValueInput: undefined,
  maxValueInput: undefined,
}

// Defined as an interface because it "type" causes issues when used with lodash pickBy
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
interface FilterType {
  dateRangeInput?: { from: DateTimeRange; to: DateTimeRange }
  maxValueInput?: string
  minValueInput?: string
  searchInput?: string
}

type DriverPermitsType = {
  regulatoryDriverPermits: Array<RegulatoryDriverPermits>
  regulatoryDriverPermitsTotals: OperationalShare.TotalCost
  regulatoryDriverPermitsTypes: Array<RegulatoryDriverPermitsTypes>
  updateRegulatoryDriverPermits: () => void
  viewDocumentClick: (documentId: string, filter?: FilterType) => void
  isLoading: boolean
  automationId?: string
  setFilterByHistory: (
    item1: 'search' | 'date' | 'minValueInput' | 'maxValueInput',
    item2: undefined,
  ) => FixMeAny
}

type Props = DriverPermitsType &
  typeof actionCreators &
  ReturnType<typeof mapStateToProps>

const DriverPermits = ({
  regulatoryDriverPermits,
  regulatoryDriverPermitsTotals,
  regulatoryDriverPermitsTypes,
  updateRegulatoryDriverPermits,
  viewDocumentClick,
  isLoading,
  automationId = undefined,
  setFilterByHistory,
}: Props) => {
  const [expanded, setExpanded] = useState<{ [key: number]: boolean }>({})
  const [filters, setFilters] = useState<FilterType>()

  const formatNumber = useMifleetFormattedNumber()

  useEffect(() => {
    setFilters({
      searchInput: setFilterByHistory('search', undefined),
      dateRangeInput: setFilterByHistory('date', undefined),
      minValueInput: setFilterByHistory('minValueInput', undefined),
      maxValueInput: setFilterByHistory('maxValueInput', undefined),
    })
  }, [setFilterByHistory])

  const shouldShowClearFilters = () => {
    const check = filter(filters, (o) => !isNil(o) && !isEmpty(o))

    return !isEmpty(check)
  }

  const getTableColumns = (
    filters?: FilterType,
  ): TypedColumns<RegulatoryDriverPermits> => [
    {
      expander: true,
      width: 45,
      Expander: ({ isExpanded }) => (
        <i
          id={automationId ? `exp-${automationId}-btn` : undefined}
          className={`fa  ${isExpanded ? 'fa-angle-down' : 'fa-angle-right'}`}
          style={{ fontSize: '20px' }}
        />
      ),
      className: 'Regulatory-centeredItem',
    },
    createFormattedDateHeader({
      Header: 'Date',
      accessor: 'document_date',
    }),
    {
      Header: 'Driver',
      accessor: 'driver_name',
    },
    {
      Header: 'License Type',
      accessor: 'driver_permit_type_id',
      Cell: (cell) => {
        const type = regulatoryDriverPermitsTypes.find(
          (o: RegulatoryDriverPermitsTypes) => o.value === cell.value,
        )

        return type ? type.name : ''
      },
    },
    {
      Header: 'License Number',
      accessor: 'permit_number',
      className: 'util-tableEnd',
    },
    {
      Header: 'Begin Date',
      accessor: 'begin_date',
      Cell: (cell) => cell.value && connectedCtIntl.formatDate(cell.value).formatted,
    },
    {
      Header: 'Expiration Date',
      accessor: 'expiration_date',
      Cell: (cell) =>
        cell.value ? connectedCtIntl.formatDate(cell.value).formatted : '',
    },
    {
      Header: 'Total Value',
      id: 'total_value',
      accessor: (original) => Number(original.total_value),
      Cell: (cell) => formatNumber(cell.value),
      className: 'util-tableEnd',
    },
    {
      Header: 'View Document',
      Cell: (cell) => (
        <Button
          id={automationId ? `view-doc-${automationId}-btn` : undefined}
          icon="eye"
          tooltipMessage="View Document"
          onClick={(e) => {
            e.stopPropagation()
            viewDocumentClick(cell.original.document_id, filters)
          }}
          className={'apply'}
          square
          withTooltip
          small
        />
      ),
      className: 'Regulatory-centeredItem',
      style: { cursor: 'default' },
      width: 120,
      sortable: false,
    },
  ]

  const handleSearchInputChange = (e: ChangeEvent<HTMLInputElement>) =>
    setFilters({ ...filters, searchInput: e.target.value })

  const valueRangeChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (checkNumberPositive(e)) {
      setFilters({ ...filters, [e.currentTarget.id]: e.currentTarget.value })
    }
  }

  const handleDatePickerChange = (e: { from: DateTimeRange; to: DateTimeRange }) =>
    setFilters({ ...filters, dateRangeInput: e })

  const makeTableData = memoizeOne(
    (driverPermits: Array<RegulatoryDriverPermits>, filters: FilterType) => {
      const activeFilters = pickBy(filters, identity)

      let items = driverPermits

      if (activeFilters.searchInput) {
        const filterOptions = [
          { filter: 'document_date', isDate: true },
          { filter: 'driver_name' },
          { filter: 'driver_permit_type' },
          { filter: 'permit_number' },
          { filter: 'begin_date', isDate: true },
          { filter: 'expiration_date', isDate: true },
          { filter: 'total_value' },
        ]
        const searchInput = activeFilters.searchInput

        items = filterTableData(filterOptions, searchInput, items)
      }

      if (activeFilters.dateRangeInput) {
        items = filterTableDataByDate(items, activeFilters.dateRangeInput)
      }

      if (activeFilters.minValueInput) {
        const f = Number.parseFloat(activeFilters.minValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) >= f)
      }

      if (activeFilters.maxValueInput) {
        const f = Number.parseFloat(activeFilters.maxValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) <= f)
      }

      return compact(items)
    },
  )

  const handleRowClick = (_: string, row: RowInfo) => {
    if (expanded[row.viewIndex]) {
      setExpanded({})
    } else {
      setExpanded({ [row.viewIndex]: true })
    }
  }

  if (!regulatoryDriverPermits || isLoading) {
    return <Spinner />
  }

  const memoData = makeTableData(regulatoryDriverPermits, {
    ...filters,
  })

  return (
    <div className="Regulatory-permits">
      <div className="Regulatory-header">
        <MinimizableSearchBar
          id={automationId ? `search-${automationId}-bar` : undefined}
          onChange={handleSearchInputChange}
          value={filters?.searchInput}
          placeholder={'Search'}
          forceOriginalValue
        />
        <MiFleetDatePicker
          value={filters?.dateRangeInput}
          onChange={handleDatePickerChange}
          disabledDays="future"
          isRange
          placeholder={{
            from: 'Start Date',
            to: 'End Date',
          }}
          disableMultipleInput={false}
        />
        <TextInput
          placeholder="Minimum Value"
          id="minValueInput"
          value={filters?.minValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        <TextInput
          placeholder="Maximum Value"
          id="maxValueInput"
          value={filters?.maxValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        {shouldShowClearFilters() && (
          <Button
            square
            icon={['fas', 'eraser']}
            onClick={() => setFilters(defaultFilters)}
          />
        )}
        {!isEmpty(regulatoryDriverPermitsTotals) && filters && (
          <TableTotals
            filtersAreActive={
              !isEmpty(filters.searchInput) ||
              !isEmpty(filters.dateRangeInput) ||
              !isEmpty(filters.minValueInput) ||
              !isEmpty(filters.maxValueInput)
            }
            data={memoData}
            totals={regulatoryDriverPermitsTotals}
            showTotal
          />
        )}
      </div>
      <AdvancedTable
        columns={getTableColumns(filters)}
        data={memoData}
        showPagination
        defaultPageSize={calculateTableRowCount(memoData) || 20}
        minRows={calculateTableRowCount(memoData) || 20}
        getRowProps={() => ({ className: 'Regulatory-tableTr' })}
        onRowClick={handleRowClick}
        expanded={expanded}
        SubComponent={(row: RowInfo) => (
          <DriverPermitsSubForm
            automationId={automationId}
            item={row}
            isLoading={isLoading}
            closeForm={() => setExpanded({})}
            driverPermitsTypes={regulatoryDriverPermitsTypes}
            updateRegulatoryDriverPermits={updateRegulatoryDriverPermits}
          />
        )}
      />
    </div>
  )
}

type DriverPermitsSubFormType = {
  closeForm: () => void
  item: RowInfo
  driverPermitsTypes: Array<RegulatoryDriverPermitsTypes>
  updateRegulatoryDriverPermits: ({
    payload,
  }: {
    payload: SubRegulatoryDriverPermits
  }) => void
  isLoading: boolean
  automationId?: string
}
const DriverPermitsSubForm = ({
  closeForm,
  item,
  driverPermitsTypes,
  updateRegulatoryDriverPermits,
  isLoading,
  automationId = undefined,
}: DriverPermitsSubFormType) => {
  const [editing, setEditing] = useState<boolean>(false)

  const setFormikValue = (
    form: FormikProps<SubRegulatoryDriverPermits>,
    fieldName: string,
    fieldValue: FixMeAny,
  ) => {
    form.setFieldTouched(fieldName, true)
    form.setFieldValue(fieldName, fieldValue)
  }

  const clickedEditButton = (action: 'edit' | 'close') => {
    if (action === 'edit') {
      setEditing(true)
    }

    if (action === 'close') {
      setEditing(false)
      closeForm()
    }
  }

  const renderEditButtons = (
    form: FormikProps<SubRegulatoryDriverPermits>,
    documentStatus: DocumentStatus,
  ) => (
    <div className="RegulatoryForms-editButtonsWrapper">
      <Button
        id={automationId ? `close-${automationId}-btn` : undefined}
        icon="times"
        tooltipMessage="Close Row"
        onClick={() => clickedEditButton('close')}
        square
        small
      />
      {/* eslint-disable-next-line no-nested-ternary */}
      {allowByDocumentStatus(documentStatus) ? (
        editing ? (
          <Button
            id={automationId ? `save-${automationId}-btn` : undefined}
            icon="check"
            tooltipMessage="Apply"
            type="submit"
            className={'apply'}
            square
            small
            disabled={!isEmpty(form.errors) || !form.isValid}
          />
        ) : (
          <Button
            id={automationId ? `edit-${automationId}-btn` : undefined}
            icon="pencil-alt"
            tooltipMessage="Edit"
            onClick={(e) => {
              e.preventDefault()
              clickedEditButton('edit')
            }}
            square
            small
          />
        )
      ) : null}
    </div>
  )
  const handleFormSubmit = (values: SubRegulatoryDriverPermits) => {
    const changeType = Object.keys(values) as Array<keyof SubRegulatoryDriverPermits>
    const changesChecker = changeType.filter(
      (key) => item.original[key] !== values[key],
    )

    if (compact(changesChecker).length > 0) {
      updateRegulatoryDriverPermits({
        payload: { ...item.original, ...values },
      })
    }
    setEditing(false)
  }

  const { original } = item

  const {
    driver_permit_type_id,
    description,
    begin_date,
    expiration_date,
    permit_id,
    permit_number,
    document_line_id,
    document_status,
  } = original

  const driverPermitsInitialValues = {
    driver_permit_type_id,
    description,
    begin_date,
    expiration_date,
    permit_id,
    permit_number,
    document_line_id,
  }

  return (
    <div className="RegulatoryForms util-p15">
      {isLoading ? (
        <Spinner />
      ) : (
        <Formik
          isInitialValid={false} // Check if form is valid on load
          initialValues={driverPermitsInitialValues}
          enableReinitialize
          validationSchema={regulatoryDriverPermitsValidationSchema}
          onSubmit={(values) => handleFormSubmit(values)}
          render={(form) => (
            <Form className="RegulatoryForms-formik col-xs-12">
              <div className="RegulatoryForms-leftSection col-xs-4 row">
                <InputDropdown
                  required
                  placeholder="License Type"
                  id="driver_permit_type_id"
                  name={'driver_permit_type_id'}
                  disableInput
                  disabled={!editing}
                  sortItem={false}
                  options={driverPermitsTypes}
                  onBlur={form.handleBlur}
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                  activeOption={driverPermitsTypes.find(
                    (o) =>
                      o.driver_permit_type_id === form.values.driver_permit_type_id,
                  )}
                  onChange={(e: string) =>
                    setFormikValue(form, 'driver_permit_type_id', e)
                  }
                />
                <TextInput
                  placeholder="License Number"
                  disabled={!editing}
                  value={form.values.permit_number}
                  id="permit_number"
                  forceOriginalValue
                  type="number"
                  onChange={form.handleChange}
                  onBlur={form.handleBlur}
                  meta={generateMetaProps(form, 'permit_number')}
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                  required
                />
                {form.errors.driver_permit_type_id && (
                  <div className="RegulatoryForms-leftSection-error col-xs-5">
                    <FormattedMessage
                      id={form.errors.driver_permit_type_id as string}
                      defaultMessage={form.errors.driver_permit_type_id as string}
                    />
                  </div>
                )}
                <MiFleetDatePicker
                  placeholder="Begin Date"
                  disabled={!editing}
                  value={form.values.begin_date}
                  id="begin_date"
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-8',
                  }}
                  onChange={(fromDate: string) => {
                    setFormikValue(form, 'begin_date', fromDate)
                  }}
                  meta={generateMetaProps(form, 'begin_date')}
                  required
                />
                <MiFleetDatePicker
                  placeholder="End Date"
                  disabled={!editing}
                  value={form.values.expiration_date}
                  id="expiration_date"
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-8',
                  }}
                  onChange={(toDate: string) => {
                    setFormikValue(form, 'expiration_date', toDate)
                  }}
                  required
                  meta={generateMetaProps(form, 'expiration_date')}
                />
              </div>
              <div className="RegulatoryForms-centerSection col-xs-7">
                <TextInput
                  placeholder={'Description'}
                  id={automationId ? `desc-${automationId}-txi` : undefined}
                  name="description"
                  value={form.values.description}
                  onChange={form.handleChange}
                  disabled={!editing}
                  textArea
                  forceOriginalValue
                  extraClassNames={{
                    containerClassNames: 'RegulatoryForms-centerSection-description',
                    inputClassNames: 'RegulatoryForms-centerSection-description',
                  }}
                />
              </div>
              <div className={'RegulatoryForms-rightSection col-xs-1'}>
                {renderEditButtons(form, document_status)}
              </div>
            </Form>
          )}
        />
      )}
    </div>
  )
}

const actionCreators = {
  updateRegulatoryDriverPermits,
}

const mapStateToProps = (state: AppState) => ({
  regulatoryDriverPermits: allRegulatoryDriverPermits(state),
  regulatoryDriverPermitsTotals: allRegulatoryDriverPermitsTotals(state),
  regulatoryDriverPermitsTypes: allRegulatoryDriverPermitsTypes(state),
  isLoading: getRegulatoryLoading(state),
})

export default connect(mapStateToProps, actionCreators)(DriverPermits)
