import type { DateTimeRange, FixMeAny } from '@fleet-web/types'

import type { DocumentStatus } from '../../api/costInput/shared/types'

type ShareRegulatory = {
  company_id: string
  document_date: string
  document_id: string
  document_status: DocumentStatus
  net_value: string
  tax_deductable_value: string
  tax_non_deductable_value: string
  total_value: string
}
export type SubRegulatoryTax = {
  begin_date: string
  description: FixMeAny
  document_line_id: string
  expiration_date: string
  tax_id: string
  tax_type_id: string
}

export type RegulatoryTax = {
  plate: string
  tax_type: string
  vehicle_id: string
} & ShareRegulatory &
  SubRegulatoryTax

export type RegulatoryInsurance = {
  contract_insurance_id: string | null
  insurance_type: string | null
  insurance_type_id: string | null
  plate: string
  policy_number: string | null
  vehicle_id: string
  document_line_id: string
} & ShareRegulatory

export type SubRegulatoryPermits = {
  begin_date: string
  begin_odometer: string | null
  description: string | null
  document_line_id: string
  end_odometer: string | null
  expiration_date: string
  permit_id: string
  vehicle_permit_type_id: string
}
export type RegulatoryPermits = {
  plate: string
  vehicle_id: string
  vehicle_permit_type: string
} & ShareRegulatory &
  SubRegulatoryPermits

export type SubRegulatoryDriverPermits = {
  begin_date: string
  description: string
  driver_permit_type_id: string
  expiration_date: string
  permit_id: string
  permit_number: string
  document_line_id: string
}
export type RegulatoryDriverPermits = {
  driver_id: string
  driver_name: string
  driver_permit_type: string
} & ShareRegulatory &
  SubRegulatoryDriverPermits

type ShareRegulatoryTypes = {
  company_id: string
  is_deleted: 't' | 'f'
  name: string
  value: string
}
export type RegulatoryTaxTypes = {
  default_tax_value: string
  has_scales: 'f' | 't'
  is_percentage_scaled: 'f' | 't'
  is_vat_tax: 'f' | 't'
  is_vehicle_tax: 'f' | 't'
  tax_name: string
  tax_type_id: string
} & ShareRegulatoryTypes
export type RegulatoryPermitsTypes = {
  permit_type: string
  vehicle_permit_type_id: string
} & ShareRegulatoryTypes

export type RegulatoryDriverPermitsTypes = {
  driver_permit_type_id: string

  permit_type: string
} & ShareRegulatoryTypes

// Defined as an interface because it "type" causes issues when used with lodash pickBy
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export interface RegulatoryFilterType {
  dateRangeInput?: DateTimeRange
  maxValueInput?: string
  minValueInput?: string
  searchInput?: string
}
