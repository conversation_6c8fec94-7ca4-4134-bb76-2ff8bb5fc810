import * as yup from 'yup'

import { higherDateThanSchema, lowerDateThanSchema } from '@fleet-web/shared/formik'
import { messages } from '@fleet-web/shared/forms/messages'

const regulatoryPermitsValidationSchema = yup.object().shape({
  vehicle_permit_type_id: yup.string().required(messages.required).nullable(),
  begin_date: lowerDateThanSchema(
    'expiration_date',
    'Begin Date must be lower than End Date',
  )
    .required('Begin Date is required')
    .nullable(),
  expiration_date: higherDateThanSchema(
    'begin_date',
    'End Date must be higher than Begin Date',
  )
    .required('End Date is required')
    .nullable(),
})

const regulatoryTaxesValidationSchema = yup.object().shape({
  tax_type_id: yup.string().required(messages.required).nullable(),
  begin_date: lowerDateThanSchema(
    'expiration_date',
    'Begin Date must be lower than End Date',
  )
    .typeError(messages.dateValid)
    .nullable(),
  expiration_date: higherDateThanSchema(
    'begin_date',
    'End Date must be higher than Begin Date',
  )
    .typeError(messages.dateValid)
    .nullable(),
})

const regulatoryDriverPermitsValidationSchema = yup.object().shape({
  driver_permit_type_id: yup.string().required(messages.required).nullable(),
  permit_number: yup
    .number()
    .typeError(messages.validNumber)
    .required(messages.required),
  begin_date: lowerDateThanSchema(
    'expiration_date',
    'Begin Date must be lower than End Date',
  ).required(messages.required),
  expiration_date: higherDateThanSchema(
    'begin_date',
    'End Date must be higher than Begin Date',
  ).required('End Date is required'),
})

export {
  regulatoryPermitsValidationSchema,
  regulatoryTaxesValidationSchema,
  regulatoryDriverPermitsValidationSchema,
}
