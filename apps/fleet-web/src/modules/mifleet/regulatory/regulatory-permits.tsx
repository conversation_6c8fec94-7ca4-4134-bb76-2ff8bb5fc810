import { useState, type ChangeEvent } from 'react'
import { Form, Formik, type FormikProps } from 'formik'
import { compact, filter, identity, isEmpty, isNil, pickBy } from 'lodash'
import { DateTime } from 'luxon'
import memoizeOne from 'memoize-one'
import { FormattedMessage } from 'react-intl'
import { connect } from 'react-redux'
import type { RowInfo } from 'react-table'

import { actions, selectors } from '@fleet-web/duxs/mifleet/regulatory'
import { UserFormattedLengthInKmOrMiles } from '@fleet-web/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { MiFleetDatePicker } from '@fleet-web/modules/mifleet/components/date-picker/MiFleetDatePicker'
import TableTotals from '@fleet-web/modules/mifleet/components/utils/table-header-totals'
import type { AppState } from '@fleet-web/root-reducer'
import { generateMetaProps } from '@fleet-web/shared/formik'
import type { DateTimeRange, FixMeAny } from '@fleet-web/types'
import type { TypedColumns } from '@fleet-web/types/extended/react-table'
import {
  AdvancedTable,
  Button,
  InputDropdown,
  MinimizableSearchBar,
  Spinner,
  TextInput,
  useMifleetFormattedNumber,
} from '@fleet-web/util-components'
import { connectedCtIntl } from '@fleet-web/util-components/connectedCtIntl'
import { createFormattedDateHeader } from '@fleet-web/util-functions/mifleet'
import { addDays } from '@fleet-web/util-functions/time-utils'

import type { DocumentStatus } from '../api/costInput/shared/types'
import allowByDocumentStatus from '../components/utils/allow-by-document-status'
import calculateTableRowCount from '../components/utils/calculate-table-row-count'
import filterTableDataByDate from '../components/utils/table-date-filter'
import filterTableData from '../components/utils/table-search-filter'
import { checkNumberPositive } from '../operational/shared/operational-validation-positive-number'
import type { OperationalShare } from '../operational/shared/type'
import { regulatoryPermitsValidationSchema } from './shared/regulatory-validation-schema'
import type {
  RegulatoryFilterType,
  RegulatoryPermits,
  RegulatoryPermitsTypes,
  SubRegulatoryPermits,
} from './shared/type'

const { updateRegulatoryPermits } = actions

const {
  allRegulatoryPermits,
  allRegulatoryPermitsTypes,
  allRegulatoryPermitsTotals,
  getRegulatoryLoading,
} = selectors

const defaultFilters = {
  searchInput: '',
  dateRangeInput: {
    from: addDays(new Date(), -60),
    to: new Date(),
  },
  minValueInput: undefined,
  maxValueInput: undefined,
}

type PermitsType = {
  isLoading: boolean
  automationId?: string
  regulatoryPermits: Array<RegulatoryPermits>
  regulatoryPermitsTotals: OperationalShare.TotalCost
  regulatoryPermitsTypes: Array<RegulatoryPermitsTypes>
  updateRegulatoryPermits: () => void
  viewDocumentClick: (
    documentid: string,
    filters?: RegulatoryFilterType,
    activeMenu?: string,
  ) => void

  onlyShowFilteredTotals: boolean
}
type Props = PermitsType & typeof actionCreators & ReturnType<typeof mapStateToProps>

const Permits = ({
  isLoading,
  automationId = undefined,
  regulatoryPermits,
  regulatoryPermitsTotals,
  regulatoryPermitsTypes,
  updateRegulatoryPermits,
  viewDocumentClick,
  onlyShowFilteredTotals = false,
}: Props) => {
  const [expanded, setExpanded] = useState<{ [key: number]: boolean }>({})
  const [filters, setFilters] = useState<RegulatoryFilterType>({
    searchInput: '',
    dateRangeInput: {
      from: addDays(new Date(), -60),
      to: new Date(),
    },
    minValueInput: undefined,
    maxValueInput: undefined,
  })

  const formatNumber = useMifleetFormattedNumber()

  const shouldShowClearFilters = () => {
    const check = filter(filters, (o) => !isNil(o) && !isEmpty(o))

    return !isEmpty(check)
  }

  const getTableColumns = (
    filters?: RegulatoryFilterType,
  ): TypedColumns<RegulatoryPermits> => [
    {
      expander: true,
      width: 45,
      Expander: ({ isExpanded }) => (
        <i
          id={automationId ? `exp-${automationId}-btn` : undefined}
          className={`fa  ${isExpanded ? 'fa-angle-down' : 'fa-angle-right'}`}
          style={{ fontSize: '20px' }}
        />
      ),
      className: 'Regulatory-centeredItem',
    },
    createFormattedDateHeader({
      Header: 'Date',
      accessor: 'document_date',
    }),
    {
      Header: 'Vehicle',
      accessor: 'plate',
    },
    {
      Header: 'Vehicle Licenses',
      accessor: 'vehicle_permit_type_id',
      Cell: (cell) => {
        const type = regulatoryPermitsTypes.find(
          (o: RegulatoryPermitsTypes) => o.value === cell.value,
        )

        return type ? type.name : ''
      },
    },
    {
      Header: 'Begin Date',
      accessor: 'begin_date',
      Cell: (cell) =>
        cell.value ? connectedCtIntl.formatDate(cell.value).formatted : '',
    },
    {
      Header: 'Expiration Date',
      accessor: 'expiration_date',
      Cell: (cell) =>
        cell.value ? connectedCtIntl.formatDate(cell.value).formatted : '',
    },
    {
      Header: 'Begin Odometer',
      accessor: 'begin_odometer',
      Cell: (row) => (
        <UserFormattedLengthInKmOrMiles
          valueInKm={row.value}
          transformValueBeforeFormatting={Math.round}
        />
      ),
      className: 'util-tableEnd',
    },
    {
      Header: 'End Odometer',
      accessor: 'end_odometer',
      Cell: (row) => (
        <UserFormattedLengthInKmOrMiles
          valueInKm={row.value}
          transformValueBeforeFormatting={Math.round}
        />
      ),
      className: 'util-tableEnd',
    },
    {
      Header: 'Total Value',
      id: 'total_value',
      accessor: (original) => Number(original.total_value),
      Cell: (cell) => formatNumber(cell.value),
      className: 'util-tableEnd',
    },
    {
      Header: 'View Document',
      Cell: (cell) => (
        <Button
          id={automationId ? `view-doc-${automationId}-btn` : undefined}
          icon="eye"
          tooltipMessage="View Document"
          onClick={(e) => {
            e.stopPropagation()
            viewDocumentClick(
              cell.original.document_id,
              {
                ...filters,
                dateRangeInput: [
                  DateTime.fromJSDate(new Date(filters?.dateRangeInput?.from as any)),
                  DateTime.fromJSDate(new Date(filters?.dateRangeInput?.to as any)),
                ] as any,
              },
              'miscConcept',
            )
          }}
          className={'apply'}
          square
          withTooltip
          small
        />
      ),
      className: 'Regulatory-centeredItem',
      style: { cursor: 'default' },
      width: 120,
      sortable: false,
    },
  ]

  const handleSearchInputChange = (e: ChangeEvent<HTMLInputElement>) =>
    setFilters({
      ...filters,
      searchInput: e.target.value,
    })

  const valueRangeChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (checkNumberPositive(e)) {
      setFilters({
        ...filters,
        [e.currentTarget.id]: e.currentTarget.value,
      })
    }
  }

  const handleDatePickerChange = (e: DateTimeRange) =>
    setFilters({
      ...filters,
      dateRangeInput: e,
    })

  const makeTableData = memoizeOne(
    (permits: Array<RegulatoryPermits>, filters: RegulatoryFilterType) => {
      const activeFilters = pickBy(filters, identity)

      let items = permits

      if (activeFilters.searchInput) {
        const filterOptions = [
          { filter: 'document_date', isDate: true },
          { filter: 'plate' },
          { filter: 'vehicle_permit_type' },
          { filter: 'begin_date', isDate: true },
          { filter: 'expiration_date', isDate: true },
          { filter: 'begin_odometer' },
          { filter: 'end_odometer' },
          { filter: 'total_value' },
        ]
        const searchInput = activeFilters.searchInput

        items = filterTableData(filterOptions, searchInput, items)
      }

      if (activeFilters.dateRangeInput) {
        items = filterTableDataByDate(items, activeFilters.dateRangeInput)
      }

      if (activeFilters.minValueInput) {
        const f = Number.parseFloat(activeFilters.minValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) >= f)
      }

      if (activeFilters.maxValueInput) {
        const f = Number.parseFloat(activeFilters.maxValueInput)
        items = items.filter((o) => Number.parseFloat(o.total_value) <= f)
      }

      return compact(items)
    },
  )

  const handleRowClick = (_: string, row: RowInfo) => {
    if (expanded[row.viewIndex]) {
      setExpanded({})
    } else {
      setExpanded({ [row.viewIndex]: true })
    }
  }

  if (!regulatoryPermits || isLoading) {
    return <Spinner />
  }

  const memoData = makeTableData(regulatoryPermits, {
    ...filters,
  })

  return (
    <div className="Regulatory-permits">
      <div className="Regulatory-header">
        <MinimizableSearchBar
          id={automationId ? `search-${automationId}-bar` : undefined}
          onChange={handleSearchInputChange}
          value={filters?.searchInput}
          placeholder={'Search'}
          forceOriginalValue
        />
        <MiFleetDatePicker
          value={filters?.dateRangeInput}
          onChange={handleDatePickerChange}
          isRange
          placeholder={{
            from: 'Start Date',
            to: 'End Date',
          }}
          disableMultipleInput={false}
        />
        <TextInput
          placeholder="Minimum Value"
          id="minValueInput"
          value={filters?.minValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        <TextInput
          placeholder="Maximum Value"
          id="maxValueInput"
          value={filters?.maxValueInput}
          onChange={valueRangeChange}
          forceOriginalValue
          type="number"
          className="numberInput"
          min="0"
        />
        {shouldShowClearFilters() && (
          <Button
            square
            icon={['fas', 'eraser']}
            onClick={() => setFilters(defaultFilters)}
          />
        )}
        {(!isEmpty(regulatoryPermitsTotals) || onlyShowFilteredTotals) && filters && (
          <TableTotals
            filtersAreActive={
              !isEmpty(filters.searchInput) ||
              !isEmpty(filters.dateRangeInput) ||
              !isEmpty(filters.minValueInput) ||
              !isEmpty(filters.maxValueInput)
            }
            data={memoData}
            totals={regulatoryPermitsTotals}
            showTotal
            onlyShowFilteredTotals={onlyShowFilteredTotals}
          />
        )}
      </div>
      <AdvancedTable
        columns={getTableColumns(filters)}
        data={memoData}
        showPagination
        defaultPageSize={calculateTableRowCount(memoData) || 20}
        minRows={calculateTableRowCount(memoData) || 20}
        getRowProps={() => ({ className: 'Regulatory-tableTr' })}
        onRowClick={handleRowClick}
        expanded={expanded}
        SubComponent={(row: RowInfo) =>
          automationId && (
            <PermitsSubForm
              item={row}
              automationId={automationId}
              isLoading={isLoading}
              closeForm={() => setExpanded({})}
              permitTypes={regulatoryPermitsTypes}
              updateRegulatoryPermits={updateRegulatoryPermits}
            />
          )
        }
      />
    </div>
  )
}

type PermitsSubType = {
  isLoading: boolean
  automationId?: string
  closeForm: () => void
  item: RowInfo
  permitTypes: Array<RegulatoryPermitsTypes>
  updateRegulatoryPermits: ({ payload }: { payload: SubRegulatoryPermits }) => void
}
const PermitsSubForm = ({
  isLoading,
  automationId = undefined,
  closeForm,
  item,
  permitTypes,
  updateRegulatoryPermits,
}: PermitsSubType) => {
  const [editing, setEditing] = useState<boolean>(false)

  const setFormikValue = (
    form: FormikProps<SubRegulatoryPermits>,
    fieldName: string,
    fieldValue: FixMeAny,
  ) => {
    form.setFieldTouched(fieldName, true)
    form.setFieldValue(fieldName, fieldValue)
  }

  const clickedEditButton = (action: 'edit' | 'close') => {
    if (action === 'edit') {
      setEditing(true)
    }

    if (action === 'close') {
      setEditing(false)
      closeForm()
    }
  }

  const renderEditButtons = (
    form: FormikProps<SubRegulatoryPermits>,
    documentStatus: DocumentStatus,
  ) => (
    <div className="RegulatoryForms-editButtonsWrapper">
      <Button
        id={automationId ? `close-${automationId}-btn` : undefined}
        icon="times"
        tooltipMessage="Close Row"
        onClick={() => clickedEditButton('close')}
        square
        small
      />
      {/* eslint-disable-next-line no-nested-ternary */}
      {allowByDocumentStatus(documentStatus) ? (
        editing ? (
          <Button
            id={automationId ? `save-${automationId}-btn` : undefined}
            icon="check"
            tooltipMessage="Apply"
            type="submit"
            className={'apply'}
            square
            small
            disabled={!isEmpty(form.errors) || !form.isValid}
          />
        ) : (
          <Button
            id={automationId ? `edit-${automationId}-btn` : undefined}
            icon="pencil-alt"
            tooltipMessage="Edit"
            onClick={(e) => {
              e.preventDefault()
              clickedEditButton('edit')
            }}
            square
            small
          />
        )
      ) : null}
    </div>
  )
  const handleFormSubmit = (values: SubRegulatoryPermits) => {
    const changeType = Object.keys(values) as Array<keyof SubRegulatoryPermits>
    const changesChecker = changeType.filter(
      (key) => item.original[key] !== values[key],
    )

    if (compact(changesChecker).length > 0) {
      const vehicle_permit_type =
        permitTypes.find(
          (permitType) =>
            permitType.vehicle_permit_type_id === values.vehicle_permit_type_id,
        )?.permit_type ?? item.original.vehicle_permit_type

      updateRegulatoryPermits({
        payload: {
          ...item.original,
          ...values,
          vehicle_permit_type,
        },
      })
    }
    setEditing(false)
  }

  const { original } = item

  const {
    vehicle_permit_type_id,
    description,
    begin_date,
    expiration_date,
    begin_odometer,
    end_odometer,
    permit_id,
    document_line_id,
    document_status,
  } = original

  const permitsInitialValues = {
    vehicle_permit_type_id,
    description,
    begin_date,
    expiration_date,
    begin_odometer,
    end_odometer,
    permit_id,
    document_line_id,
  }

  return (
    <div className="RegulatoryForms util-p15">
      {isLoading ? (
        <Spinner />
      ) : (
        <Formik
          isInitialValid={false} // Check if form is valid on load
          initialValues={permitsInitialValues}
          enableReinitialize
          validationSchema={regulatoryPermitsValidationSchema}
          onSubmit={(values) => handleFormSubmit(values)}
          render={(form) => (
            <Form className="RegulatoryForms-formik col-xs-12">
              <div className="RegulatoryForms-leftSection col-xs-6 row">
                <InputDropdown
                  required
                  placeholder="Vehicle Licenses"
                  id="vehicle_permit_type_id"
                  name={'vehicle_permit_type_id'}
                  disableInput
                  disabled={!editing}
                  sortItem={false}
                  options={permitTypes}
                  onBlur={form.handleBlur}
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-8',
                  }}
                  activeOption={permitTypes.find(
                    (o) =>
                      o.vehicle_permit_type_id === form.values.vehicle_permit_type_id,
                  )}
                  onChange={(e: string) =>
                    setFormikValue(form, 'vehicle_permit_type_id', e)
                  }
                />
                {form.errors.vehicle_permit_type_id && (
                  <div className="RegulatoryForms-leftSection-error col-xs-8">
                    <FormattedMessage
                      id={form.errors.vehicle_permit_type_id as string}
                      defaultMessage={form.errors.vehicle_permit_type_id as string}
                    />
                  </div>
                )}
                <MiFleetDatePicker
                  placeholder="Begin Date"
                  disabled={!editing}
                  value={form.values.begin_date}
                  id="begin_date"
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                  onChange={(fromDate: string) => {
                    setFormikValue(form, 'begin_date', fromDate)
                  }}
                  required
                  meta={generateMetaProps(form, 'begin_date')}
                />
                <MiFleetDatePicker
                  placeholder="End Date"
                  disabled={!editing}
                  value={form.values.expiration_date}
                  id="expiration_date"
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                  onChange={(toDate: string) => {
                    setFormikValue(form, 'expiration_date', toDate)
                  }}
                  required
                  meta={generateMetaProps(form, 'expiration_date')}
                />
                <TextInput
                  placeholder="Begin Odometer"
                  disabled={!editing}
                  value={form.values.begin_odometer}
                  id="begin_odometer"
                  forceOriginalValue
                  type="number"
                  onChange={form.handleChange}
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                />
                <TextInput
                  placeholder="End Odometer"
                  disabled={!editing}
                  value={form.values.end_odometer}
                  id="end_odometer"
                  forceOriginalValue
                  type="number"
                  onChange={form.handleChange}
                  extraClassNames={{
                    containerClassNames: 'util-marginBottom col-xs-5',
                  }}
                />
              </div>
              <div className="RegulatoryForms-centerSection col-xs-5">
                <TextInput
                  placeholder={'Description'}
                  id={automationId ? `desc-${automationId}-txi` : undefined}
                  name="description"
                  value={form.values.description}
                  onChange={form.handleChange}
                  disabled={!editing}
                  textArea
                  forceOriginalValue
                  extraClassNames={{
                    containerClassNames: 'RegulatoryForms-centerSection-description',
                    inputClassNames: 'RegulatoryForms-centerSection-description',
                  }}
                />
              </div>
              <div className={'RegulatoryForms-rightSection col-xs-1'}>
                {renderEditButtons(form, document_status)}
              </div>
            </Form>
          )}
        />
      )}
    </div>
  )
}

const actionCreators = {
  updateRegulatoryPermits,
}

const mapStateToProps = (state: AppState) => ({
  isLoading: getRegulatoryLoading(state),
  regulatoryPermits: allRegulatoryPermits(state),
  regulatoryPermitsTotals: allRegulatoryPermitsTotals(state),
  regulatoryPermitsTypes: allRegulatoryPermitsTypes(state),
})

const mergeProps = (
  stateProps: FixMeAny,
  dispatchProps: FixMeAny,
  ownProps: FixMeAny,
) => ({
  ...stateProps,
  ...dispatchProps,
  ...ownProps,
})

export default connect(mapStateToProps, actionCreators, mergeProps)(Permits)
