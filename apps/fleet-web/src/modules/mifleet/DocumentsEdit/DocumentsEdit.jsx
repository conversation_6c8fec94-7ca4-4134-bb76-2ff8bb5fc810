import { Component } from 'react'
import { filter, isEmpty, isNil, trim } from 'lodash'
import { bool, func, shape } from 'prop-types'
import { connect } from 'react-redux'
import styled from 'styled-components'

import { addToast } from '@fleet-web/duxs/toast'
import { COSTS } from '@fleet-web/modules/app/components/routes/costs'
import { AdminHeader, Button, EditButton, Spinner } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import UploadFiles from '@fleet-web/util-components/mifleet/DragAndDrop/UploadFiles'

import { formatDate } from '../../../util-functions/moment-helper'
import {
  allowEditDocumentButtonRender,
  getFormConfiguration,
} from '../components/documents/details-form-configuration'
import DocumentsDetails from '../components/documents/DocumentsDetails'
import DocumentsDetailsForm from '../components/documents/DocumentsDetailsForm'
import DocumentsPaymentsForm from '../components/documents/DocumentsPaymentsForm'
import DocumentsTable from '../components/documents/DocumentsTable'
import { SupplierFormModal } from '../components/suppliers/supplier-form/supplier-form-modal'
import SectionHeaderAlt from '../components/utils/section-header-alt'
import {
  deleteDocumentLine,
  deleteDocumentsFile,
  downloadDocumentsFile,
  fetchDocument,
  fetchDocumentArrayTypes,
  fetchDocuments,
  getArrayTypes,
  getDocument,
  getLoading,
  getProcessTotalFlag,
  resetProcessTotal,
  resetShouldUpdateDoc,
  updateDocument,
  uploadDocumentsFile,
} from './slice.ts'

class DocumentsEdit extends Component {
  static propTypes = {
    fetchDocument: func.isRequired,
    fetchDocumentArrayTypes: func.isRequired,
    fetchDocuments: func.isRequired,
    item: shape({}).isRequired,
    match: shape({}).isRequired,
    loading: bool.isRequired,
    history: shape({
      push: func.isRequired,
      goBack: func.isRequired,
    }).isRequired,
    addToast: func.isRequired,
    updateDocument: func.isRequired,
    deleteDocumentLine: func.isRequired,
    resetProcessTotal: func.isRequired,
    uploadDocumentsFile: func.isRequired,
    deleteDocumentsFile: func.isRequired,
    downloadDocumentsFile: func.isRequired,
    processTotal: bool.isRequired,
    shouldUpdateDoc: bool.isRequired,
    resetShouldUpdateDoc: func.isRequired,
    arrayTypes: shape({}).isRequired,
    fromInnerImportData: bool.isRequired,
    user: shape({}),
  }

  getProperId = (props) => {
    if (props.fromInnerImportData) {
      return 'new'
    } else {
      const {
        match: {
          params: { id },
        },
      } = props

      return id
    }
  }

  constructor(props) {
    super(props)

    this.state = {
      currentItem: {},
      editable: this.getProperId(props) === 'new',
      files: [],
      isModalOpen: false,
      saveDisabled: false,
    }
  }

  componentDidMount() {
    const { fetchDocument, fetchDocumentArrayTypes } = this.props

    const id = this.getProperId(this.props)

    if (id && isEmpty(this.state.currentItem)) {
      fetchDocumentArrayTypes()
      fetchDocument({ id })
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const id = this.getProperId(this.props)

    if (
      id === 'new' &&
      !isEmpty(this.props.item) &&
      !isEmpty(this.props.item.document_id)
    ) {
      this.props.history.push(`/mifleet/input/${this.props.item.document_id}`)
    }

    const { item, shouldUpdateDoc, resetShouldUpdateDoc, resetProcessTotal, history } =
      this.props

    const { currentItem } = this.state

    if (
      (isEmpty(currentItem) && !isEmpty(item)) ||
      shouldUpdateDoc ||
      prevState.currentItem.document_id !== item.document_id
    ) {
      this.setState(
        {
          currentItem: item,
          files: [],
        },
        () => {
          resetShouldUpdateDoc()
          resetProcessTotal()
          this.handleProcessTotal(currentItem)
        },
      )
    }

    if (prevState.toUpdate && !isEmpty(currentItem) && !isEmpty(item)) {
      this.setState({ toUpdate: false }, () =>
        history.push(`/mifleet/input/${item.document_id}/line/create`),
      )
    }
  }

  handleOnClickEditDocument = () => this.setState({ editable: !this.state.editable })

  handleOnClickCancelDocument = () => {
    const id = this.getProperId(this.props)
    this.props.fetchDocument({ id })
    this.handleOnClickEditDocument()
  }

  handleChange = (changeObj) => {
    const newObj = { ...this.state.currentItem }
    Object.keys(changeObj).map((o) => {
      if (changeObj[o] === null || changeObj[o] === undefined) {
        // eslint-disable-next-line sonarjs/no-nested-assignment
        return (newObj[o] = changeObj[o])
      }

      // eslint-disable-next-line sonarjs/no-nested-assignment
      return (newObj[o] = changeObj[o].toString())
    })

    const saveDisabled =
      (newObj.document_status_id === '5' &&
        this.state.editable &&
        isEmpty(trim(newObj.cancelled_reason))) ||
      isEmpty(trim(newObj.document_date))

    this.setState({ currentItem: newObj, saveDisabled })
  }

  getBase64 = (file, cb) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => cb(reader.result)
  }

  handleFileInput = (files) => {
    const { uploadDocumentsFile, addToast } = this.props
    const { currentItem } = this.state

    if (!this.validateDocumentFields() || !currentItem.document_id) {
      addToast('error', 'costs.input.file.upload.id.error', 3000)
      return
    }

    this.setState({ files }, () => {
      this.getBase64(files[0], (result) =>
        uploadDocumentsFile({
          file: result,
          name: files[0].name,
          document: currentItem,
        }),
      )
    })
  }

  validateDocumentFields() {
    const { currentItem: doc } = this.state

    const keys = ['document_date', 'document_status_id', 'document_type_id']

    if (doc['document_status_id'] === '5') {
      keys.push('cancelled_reason')
    }

    const check = filter(keys, (key) => isNil(doc[key]) || isEmpty(doc[key]))

    return isEmpty(check)
  }

  handleToggleModal = () => {
    const { addToast, updateDocument, history } = this.props
    const { currentItem } = this.state

    if (!this.validateDocumentFields()) {
      addToast('error', 'Fill all the document fields before adding a new line', 3000)
      return
    }

    if (!currentItem.document_id) {
      const item = currentItem

      this.setState({ ...this.state, toUpdate: true }, () => {
        updateDocument({ item })
      })

      return
    }

    history.push({
      pathname: `/mifleet/input/${currentItem.document_id}/line/create`,
      state: {
        originalPath: history?.location?.state
          ? history.location.state.originalPath
          : '',

        filterValues: history?.location?.state
          ? history.location.state.filterValues
          : '',
      },
    })
  }

  handleDeleteDocumentLine = (line) => {
    const item = this.state.currentItem
    this.props.deleteDocumentLine({ item, line })
  }

  handleUpdateDocument = () => {
    const item = this.state.currentItem

    if (!this.validateDocumentFields()) {
      this.props.addToast('error', 'Fill all the document fields before saving', 3000)
      return
    }

    this.props.updateDocument({ item })
    this.handleOnClickEditDocument()
  }

  renderEditButton = () => (
    <EditButton
      editButtonId="edit-docs-btn"
      saveButtonId="save-docs-btn"
      cancelButtonId="canc-docs-btn"
      editing={this.state.editable}
      onEditClick={this.handleOnClickEditDocument}
      onSaveClick={this.handleUpdateDocument}
      onCancelClick={this.handleOnClickCancelDocument}
      disableSaveButton={this.state.saveDisabled}
      disableCancelButton={this.getProperId(this.props) === 'new'}
      deactivate={false}
      label="Document"
    />
  )

  handleProcessTotal = (item) => {
    const lines = item.documentLines || []
    let gross_totals = 0
    let net_totals = 0
    let ndvat = 0
    let dvat = 0

    for (const line of lines) {
      net_totals += Number(line.net_value)
      ndvat += Number(line.tax_non_deductable_total)
      dvat += Number(line.tax_deductable_total)
      gross_totals += Number(line.total_value)
    }
    const newObj = {
      ...item,
      net_total: net_totals,
      tax_non_deductable_total: ndvat,
      tax_deductable_total: dvat,
      gross_total: gross_totals,
      check_total: item.check_total ? Number(item.check_total) : 0,
      documentLines: lines,
    }
    this.setState({
      ...this.state,
      currentItem: newObj,
    })
  }

  renderPayments = (editable = true) => {
    const { currentItem } = this.state

    return (
      <DocumentsPaymentsForm
        item={currentItem}
        onFormChange={this.handleChange}
        editable={editable}
        type={this.getProperId(this.props) === 'new' ? 'create' : 'update'}
      />
    )
  }

  renderDropZone = (editable = true) => {
    const { files, currentItem } = this.state

    // Sort to show latest created document first by use of document id
    let docFiles = []
    if (currentItem.documentFiles && currentItem.documentFiles.length > 0) {
      docFiles = [...currentItem.documentFiles]
      docFiles.sort((a, b) => b.file_document_id - a.file_document_id)
    }

    const newDocFile = this.getProperId(this.props) === 'new'

    return (
      <DocumentsDropzoneWrapper>
        <UploadFiles
          defaultFiles={docFiles}
          files={files}
          newDocFile={newDocFile}
          onDeleteFile={(file) => this.props.deleteDocumentsFile(file)}
          onDownloadFile={(file) => this.props.downloadDocumentsFile(file)}
          onDropAccepted={this.handleFileInput}
          disabled={!editable}
          noDrag={!editable}
          noClick={!editable}
          noKeyboard={!editable}
        />
      </DocumentsDropzoneWrapper>
    )
  }

  render() {
    const { loading, arrayTypes, history } = this.props

    const { currentItem, editable } = this.state

    if (isEmpty(currentItem) || loading) {
      return <Spinner />
    }

    const breadcrumbPaths = []

    if (!this.props.fromInnerImportData) {
      breadcrumbPaths.unshift({
        label: 'Back',
        onClick: () => {
          /*fetchDocuments()
          if (history.location.state && history.location.state.originalPath) {
            history.push({
              pathname: history.location.state.originalPath,
              state: {
                filterValues: history.location.state.filterValues || undefined,
              },
            })
          } else {
            history.push(COSTS.DOCUMENTS.path)
          }*/

          history.push({
            pathname:
              this.props.history.location.state.originalPath ||
              COSTS.LITE_DASHBOARD.path,
            state: {
              filterValue: this.props.history.location.state?.filterValue,
              activeMenu: this.props.history.location.state?.activeMenu,
            },
          })
        },
        backButtonId: 'back-docs-btn',
      })
    }

    const formConfiguration = getFormConfiguration(this.props.item.document_status_id)

    const isEditable = formConfiguration.editable && editable
    const isDetailsEditable =
      editable && getFormConfiguration(currentItem.document_status_id).editable

    const isStatusEditable = editable

    const filteredStatusOptions = arrayTypes.documentStatus.filter((option) => {
      const index = formConfiguration.statusOptions.indexOf(option.document_status_id)
      return (
        index !== -1 || option.document_status_id === currentItem.document_status_id
      )
    })

    return (
      <div className="DocumentsEdit">
        <AdminHeader
          breadcrumbs={breadcrumbPaths}
          renderButtonComponent={
            allowEditDocumentButtonRender(currentItem.document_status) ||
            this.getProperId(this.props) === 'new'
              ? this.renderEditButton
              : undefined
          }
        />
        <div className="grid grid-container">
          <SectionHeaderAlt
            className="-withSub"
            label={'Last Updated'}
            labelAfterValue={`: ${formatDate(
              currentItem.update_timestamp,
              ctIntl.formatMessage({ id: 'util.dateFormat' }),
            )}`}
          ></SectionHeaderAlt>
          <StyledSection>
            <StyledDocumentForm>
              <SupplierFormModal
                isOpen={this.state.isModalOpen}
                onClose={() => {
                  this.setState({ isModalOpen: false })
                }}
                onSuccess={() => {
                  this.setState({ isModalOpen: false })
                  this.props.fetchDocumentArrayTypes()
                }}
                actionType="create"
                user={this.props.user}
              />

              <DocumentsDetailsForm
                item={currentItem}
                suppliers={arrayTypes.suppliers}
                documentStatusOptions={filteredStatusOptions}
                documentTypes={arrayTypes.documentTypes}
                onFormChange={this.handleChange}
                editable={isEditable}
                isStatusEditable={isStatusEditable}
                type={this.getProperId(this.props) === 'new' ? 'create' : 'update'}
                onSupplierCreateClick={() => {
                  this.setState({ isModalOpen: true })
                }}
              />
            </StyledDocumentForm>
            <StyledDocumentForm>
              <DocumentsDetails item={currentItem} />
            </StyledDocumentForm>
          </StyledSection>
          <SectionHeaderAlt
            label="Details"
            contentAfterLabel={
              isDetailsEditable && (
                <Button
                  id="add-doc-line-btn"
                  icon="plus"
                  label="Add New"
                  action
                  onClick={this.handleToggleModal}
                />
              )
            }
            rowClassName="util-flex util-flexGrow"
          />
          <DocumentsTable
            optionsLineId={'opt-doc-line-btn'}
            document={currentItem}
            isLoading={loading}
            editable={isEditable}
            ProcessTotal={this.handleProcessTotal}
            onDeleteRow={this.handleDeleteDocumentLine}
            history={this.props.history}
          />
          {this.renderPayments(isEditable)}
          {this.renderDropZone(isEditable)}
        </div>
      </div>
    )
  }
}

const mapStateToProps = (state) => ({
  item: getDocument(state),
  loading: getLoading(state),
  processTotal: getProcessTotalFlag(state),
  shouldUpdateDoc: state.mifleetDocuments.shouldUpdateDoc,
  arrayTypes: getArrayTypes(state),
  user: state.user.user,
})

const actionCreators = {
  fetchDocument,
  fetchDocumentArrayTypes,
  fetchDocuments,
  updateDocument,
  deleteDocumentLine,
  addToast,
  resetProcessTotal,
  uploadDocumentsFile,
  deleteDocumentsFile,
  resetShouldUpdateDoc,
  downloadDocumentsFile,
}

export default connect(mapStateToProps, actionCreators)(DocumentsEdit)

const StyledDocumentForm = styled.div`
  margin: 0 auto;
  width: 30%;

  @media (max-width: 1370px) {
    width: 35%;
  }

  @media (max-width: 1200px) {
    width: 40%;
  }
`

const StyledSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
`

const DocumentsDropzoneWrapper = styled.div`
  margin-bottom: 40px;
`
