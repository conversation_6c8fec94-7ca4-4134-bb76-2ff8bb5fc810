import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'
import { isTrue } from '@fleet-web/util-functions/validation'

import type { DocumentEditTypes, DocumentLine } from '../components/documents/types'

const prefix = 'mifleetDocuments'

type DocumentsTotals = {
  grossTotal: number | null
  netTotal: number | null
  taxNonDeductableTotal: number | null
  taxDeductableTotal: number | null
}

type State = {
  items: Array<Record<string, FixMeAny>>
  item: DocumentEditTypes | Record<string, FixMeAny>
  loading: boolean
  processTotal: boolean
  shouldUpdateDoc: boolean
  documentsTotals: DocumentsTotals | Record<string, FixMeAny>
  arrayTypes: {
    accessoryTypes: Array<Record<string, FixMeAny>>
    breakdownTypes: Array<Record<string, FixMeAny>>
    cleaningTypes: Array<Record<string, FixMeAny>>
    documentConcepts: Array<{
      document_concept_id: FixMeAny
      value: FixMeAny
      name: FixMeAny
    }>
    documentStatus: Array<Record<string, FixMeAny>>
    documentTypes: Array<Record<string, FixMeAny>>
    driverCostTypes: Array<Record<string, FixMeAny>>
    driverPermitTypes: Array<Record<string, FixMeAny>>
    fineTypes: Array<Record<string, FixMeAny>>
    fuelCards: Array<Record<string, FixMeAny>>
    fuelTransactionTypes: Array<Record<string, FixMeAny>>
    incidentTypes: Array<Record<string, FixMeAny>>
    maintenanceTypes: Array<Record<string, FixMeAny>>
    paymentMethods: Array<Record<string, FixMeAny>>
    paymentTerms: Array<{
      payment_term_id: FixMeAny
      due_days: FixMeAny
      name: FixMeAny
    }>
    permitTypes: Array<Record<string, FixMeAny>>
    suppliers: Array<Record<string, FixMeAny>>
    drivers: Array<{ name: FixMeAny; value: FixMeAny }>
    allDrivers: Array<Record<string, FixMeAny>>
    taxTypes: Array<{
      tax_type_id: FixMeAny
      is_vat_tax: FixMeAny
      tax_value: FixMeAny
    }>
    tireLocations: Array<Record<string, FixMeAny>>
    tireOperations: Array<Record<string, FixMeAny>>
    vehicles: Array<{
      driver_id: number | null
      is_deleted: boolean | string
      name: string
      plate: string
      value: string
      vehicle_id: number
    }>
    oilTypes: Array<Record<string, FixMeAny>>
  }
  vatDeductionValues?: {
    tax_deductable_value: string | number
    tax_non_deductable_value: string | number
    tax_value?: string | number
    total_value?: string | number
    vat_deduction_percentage?: string | number
  }
  loadingVat: boolean
}

const initialState: State = {
  items: [],
  item: {},
  loading: false,
  processTotal: false,
  shouldUpdateDoc: false,
  loadingVat: false,
  documentsTotals: {},
  vatDeductionValues: {
    tax_deductable_value: 0,
    tax_non_deductable_value: 0,
  },
  arrayTypes: {
    accessoryTypes: [],
    breakdownTypes: [],
    cleaningTypes: [],
    documentConcepts: [],
    documentStatus: [],
    documentTypes: [],
    driverCostTypes: [],
    driverPermitTypes: [],
    fineTypes: [],
    fuelCards: [],
    fuelTransactionTypes: [],
    incidentTypes: [],
    maintenanceTypes: [],
    paymentMethods: [],
    paymentTerms: [],
    permitTypes: [],
    suppliers: [],
    drivers: [],
    allDrivers: [],
    taxTypes: [],
    tireLocations: [],
    tireOperations: [],
    vehicles: [],
    oilTypes: [],
  },
}

const slice = createSlice({
  name: prefix,
  initialState,
  reducers: {
    fetchDocument: (draft, _: PayloadAction<string>) => {
      draft.loading = true
    },
    onFetchDocument: (
      draft,
      { payload: { item } }: PayloadAction<Record<string, FixMeAny>>,
    ) => {
      draft.item = item
      draft.loading = false
      draft.shouldUpdateDoc = true
    },
    fetchDocuments: (draft) => {
      draft.loading = true
    },
    fetchDocumentArrayTypes: (draft) => {
      draft.loading = true
    },
    onFetchDocuments: (draft, { payload: { documentList, totals } }) => {
      draft.items = documentList
      draft.documentsTotals = totals
      draft.loading = false
    },
    updateDocument: (draft, _: PayloadAction<FixMeAny>) => {
      draft.loading = false
    },
    onUpdateDocument: (draft, { payload: { updatedItem } }) => {
      const index = draft.items.findIndex((item) =>
        item.document_id === updatedItem.document_id ? updatedItem : item,
      )

      if (index !== -1) {
        draft.items.splice(index, updatedItem)
      }

      draft.item = updatedItem
      draft.loading = false
      draft.shouldUpdateDoc = true
    },
    deleteDocumentLine: (_draft, _: PayloadAction<FixMeAny>) => {},
    onDeleteDocumentLine: (draft, { payload: { item, line } }) => {
      draft.item = {
        ...item,
        documentLines: draft.item.documentLines.filter(
          (docLine: DocumentLine) => docLine.document_line_id !== line.document_line_id,
        ),
      }
      draft.loading = false
      draft.shouldUpdateDoc = true
    },
    resetProcessTotal: () => {},
    onResetProcessTotal: (draft) => {
      draft.processTotal = false
    },
    uploadDocumentsFile: (_draft, _: PayloadAction<Record<string, FixMeAny>>) => {},
    onUploadDocumentsFile: (draft, { payload: { document, objectList } }) => {
      draft.item = {
        ...document,
        documentFiles: objectList
          ? [...draft.item.documentFiles, objectList]
          : draft.item.documentFiles,
      }
      draft.shouldUpdateDoc = true
    },
    deleteDocumentsFile: (_draft, _: PayloadAction<Record<string, FixMeAny>>) => {},
    onDeleteDocumentsFile: (draft, { payload }) => {
      const index = draft.item.documentFiles.findIndex(
        (f: FixMeAny) => f.file_document_id === payload.file_document_id,
      )
      if (index !== -1) {
        draft.item.documentFiles.splice(index, 1)
      }
      draft.shouldUpdateDoc = true
    },
    resetShouldUpdateDoc: (draft) => {
      draft.shouldUpdateDoc = false
    },
    downloadDocumentsFile: (_draft, _: PayloadAction<Record<string, FixMeAny>>) => {},
    updateArrayTypes: (
      draft,
      { payload: arrayTypes }: PayloadAction<State['arrayTypes']>,
    ) => {
      draft.arrayTypes = arrayTypes
      draft.loading = false
    },
    deleteTireDetail: (_draft, _: PayloadAction<Record<string, FixMeAny>>) => {},
    createDocumentLine: (draft, _: PayloadAction<FixMeAny>) => {
      draft.loading = true
    },
    onCreateDocumentLine: (
      draft,
      { payload: line }: PayloadAction<DocumentEditTypes>,
    ) => {
      if (!draft.item.documentLines) {
        draft.item.documentLines = []
      }

      draft.item.documentLines.unshift(line)
      draft.loading = false
    },
    fetchVatDeductionValues: (draft, _: PayloadAction<Record<string, FixMeAny>>) => {
      draft.loadingVat = true
    },
    onGetVatDeductionValues: (
      draft,
      { payload: { objectList } }: PayloadAction<Record<string, FixMeAny>>,
    ) => {
      draft.loadingVat = false
      draft.vatDeductionValues = objectList
    },
    createDocument: (draft) => {
      draft.loading = true
    },
    onCreateDocument: (draft, { payload: item }: PayloadAction<DocumentEditTypes>) => {
      draft.loading = false
      draft.item = item
    },
    updateDocumentLine: (draft, _: PayloadAction<FixMeAny>) => {
      draft.loading = true
    },
    onUpdateDocumentLine: (
      draft,
      { payload }: PayloadAction<Record<string, FixMeAny>>,
    ) => {
      const line = payload
      const lineId = line.document_line_id
      const index = draft.item.documentLines.findIndex(
        (line: FixMeAny) => line.document_line_id === lineId,
      )

      if (index !== -1) {
        draft.item.documentLines.splice(index, 1, line)
      }

      draft.loading = false
      draft.processTotal = true
      draft.shouldUpdateDoc = true
    },
    onDocumentsFailure: (draft) => {
      draft.loading = false
    },
    resetItem: (draft) => {
      draft.item = {}
    },
    setItem: (draft, { payload }) => {
      draft.item = payload
    },
  },
})

export const {
  fetchDocument,
  onFetchDocument,
  fetchDocuments,
  onFetchDocuments,
  updateDocument,
  onUpdateDocument,
  deleteDocumentLine,
  onDeleteDocumentLine,
  resetProcessTotal,
  onResetProcessTotal,
  uploadDocumentsFile,
  onUploadDocumentsFile,
  deleteDocumentsFile,
  onDeleteDocumentsFile,
  resetShouldUpdateDoc,
  downloadDocumentsFile,
  fetchDocumentArrayTypes,
  updateArrayTypes,
  deleteTireDetail,
  createDocumentLine,
  onCreateDocumentLine,
  fetchVatDeductionValues,
  onGetVatDeductionValues,
  createDocument,
  onCreateDocument,
  updateDocumentLine,
  onUpdateDocumentLine,
  onDocumentsFailure,
  resetItem,
  setItem,
} = slice.actions

export default slice.reducer

// Selectors
const getState = (state: AppState) => state.mifleetDocuments

export const getDocuments = (state: AppState) => getState(state).items
export const getDocumentsTotals = (state: AppState) => getState(state).documentsTotals
export const getDocument = (state: AppState) => getState(state).item
export const getLoading = (state: AppState) => getState(state).loading
export const getProcessTotalFlag = (state: AppState) => getState(state).processTotal
export const getArrayTypes = (state: AppState) => getState(state).arrayTypes
export const getValidTaxTypes = (state: AppState) =>
  getState(state).arrayTypes.taxTypes.filter((tax) => isTrue(tax.is_vat_tax))

export const getVatDeductionValuesUpdated = (state: AppState) =>
  getState(state).vatDeductionValues
export const getVatDeductionLoading = (state: AppState) => getState(state).loadingVat
