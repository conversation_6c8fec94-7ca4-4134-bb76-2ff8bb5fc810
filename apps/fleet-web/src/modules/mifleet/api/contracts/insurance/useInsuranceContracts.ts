import { useQuery } from '@tanstack/react-query'

import type { ContractStatus } from '@fleet-web/modules/mifleet/components/contracts/shared/types'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import listDataApi from '../../../../../api/mifleet/list-data'
import type { ContractStatusId } from '../database-constants'

export declare namespace FetchContracts {
  type ContractStatusInfo = {
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    is_deleted: boolean
    name: ContractStatus
    value: ContractStatusId
  }
  type InsuranceCondition = {
    contract_insurance_condition_id: string
    contract_insurance_id: null | string | number
    insurance_condition: string
    is_deleted: string
    isNew?: boolean
  }
  type InsuranceContract = {
    accounting_chart_number: null | number
    company_id: number
    contract_date?: string
    contract_end_date: string
    contract_insurance_id: number
    contract_id: number
    contract_start_date: string
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    create_timestamp: number
    create_user_id: number
    create_user_name: string
    driver_id: null | string
    driver_name: null | string
    fleet_odometer: null | number
    franchise_percentage: number
    franchise_value: null | number // DOUBLE CHECK
    insurance_type?: string
    insurance_type_id?: number
    net_value: number
    notes: null | string
    payment_method: null | string
    payment_method_id: null | string
    payment_term: null | string
    payment_term_id: null | string
    plate: string
    policy_number: null | string // DOUBLE CHECK
    supplier?: string
    supplier_id: number | undefined
    tax_deductable_value: number
    tax_non_deductable_value: string
    tax_type_id: number
    total_value?: number
    update_timestamp: number
    update_user_id: number
    update_user_name: string
    vehicle_id: number | undefined
    insuranceConditions: Array<InsuranceCondition>
    history: Array<InsuranceContract>
    succeeded_by_contract_id: number | string | null
    history_count: number
  }
  type APIReturn = [
    {
      vehicleContractInsurances: Array<InsuranceContract>
    },
  ]

  export type Status = {
    ok: boolean
    intlMessage: string
  }
}

// Contracts
const ENDPOINT = 'vehicles/vehicleContractInsuranceJSON.php?action=read'
export const MIFLEET_CONTRACTS_INSURANCE_QUERY_KEY = 'miFleetContractsInsurance'

// Insurance Contract Conditions
const CONDITIONS_ENDPOINT = 'vehicles/vehicleContractInsuranceConditionJSON.php'
export const MIFLEET_CONTRACTS_INSURANCE_CONDITIONS_QUERY_KEY =
  'miFleetContractsInsuranceConditions'

// Fetch All Contracts
export default function useInsuranceContracts() {
  return useQuery({
    queryKey: [MIFLEET_CONTRACTS_INSURANCE_QUERY_KEY],
    queryFn: () => fetchContracts(),
  })
}

async function fetchContracts(): Promise<Array<FetchContracts.InsuranceContract>> {
  return listDataApi.getListData(ENDPOINT).then((res: FetchContracts.APIReturn) => {
    const data = res[0]
    return data.vehicleContractInsurances
  })
}

// Fetch Single Insurance Contract

export const singleInsuranceContractQuery = (contractId: string) =>
  createQuery({
    queryKey: [MIFLEET_CONTRACTS_INSURANCE_QUERY_KEY, contractId],
    queryFn: contractId ? () => fetchSingleInsuranceContract(contractId) : undefined,
    enabled: !!contractId,
  })

export const useSingleInsuranceContract = (contractId: string) =>
  useQuery(singleInsuranceContractQuery(contractId))

async function fetchSingleInsuranceContract(
  contractId: string,
): Promise<FetchContracts.InsuranceContract> {
  return listDataApi
    .postListData(ENDPOINT, {
      contract_id: contractId,
    })
    .then((res: { objectList: FetchContracts.InsuranceContract }) => res.objectList)
}

// Fetch Contract Conditions

export const insuranceContractConditionsQuery = (contractId: string) =>
  createQuery({
    queryKey: [MIFLEET_CONTRACTS_INSURANCE_CONDITIONS_QUERY_KEY, contractId],
    queryFn: () => fetchContractConditions(contractId),
    enabled: !!contractId,
  })

export function useInsuranceContractConditionsQuery(contractId: string) {
  return useQuery(insuranceContractConditionsQuery(contractId))
}

async function fetchContractConditions(
  contractId: string,
): Promise<Array<FetchContracts.InsuranceCondition>> {
  return listDataApi
    .getListData(`${CONDITIONS_ENDPOINT}&contract=${contractId}`)
    .then((res: Array<FetchContracts.InsuranceCondition>) => res)
}
