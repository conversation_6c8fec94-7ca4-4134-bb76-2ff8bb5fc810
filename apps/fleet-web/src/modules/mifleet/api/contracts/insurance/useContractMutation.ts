import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as R from 'remeda'

import type { InsuranceContractSchema } from '@fleet-web/modules/mifleet/components/contracts/shared/contract-validation-schema'
import { ctToast } from '@fleet-web/util-components'

import listDataApi from '../../../../../api/mifleet/list-data'
import {
  insuranceContractConditionsQuery,
  singleInsuranceContractQuery,
  type FetchContracts,
} from './useInsuranceContracts'

export declare namespace UpdateContract {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create' | 'hardDelete'
  type ApiInput = {
    contractData:
      | FetchContracts.InsuranceContract
      | { contract_id: ContractId; hard_delete?: boolean }
      | InsuranceContractSchema
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.InsuranceContract
}

export declare namespace UpdateInsuranceCondition {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create'
  type ApiInput = {
    contract_id: ContractId
    conditionData: {
      contract_id?: number | string
      contract_insurance_condition_id?: number
      insurance_condition: string
    }
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.InsuranceCondition
}

const ENDPOINT = 'vehicles/vehicleContractInsuranceJSON.php'
const CONDITIONS_ENDPOINT = 'vehicles/vehicleContractInsuranceConditionJSON.php'

enum SuccessMessages {
  create = 'Insurance contract created successfully',
  update = 'Insurance contract updated successfully',
  delete = 'Insurance contract cancelled successfully',
  hardDelete = 'Insurance contract deleted successfully',
}

enum ErrorMessages {
  create = 'Failed to create insurance contract, please try again later',
  update = 'Failed to update insurance contract, please try again later',
  delete = 'Failed to cancel insurance contract, please try again later',
  hardDelete = 'Failed to delete insurance contract, please try again later',
}

enum ConditionSuccessMessages {
  create = 'Insurance condition created successfully',
  update = 'Insurance condition updated successfully',
  delete = 'Insurance condition cancelled successfully',
  hardDelete = 'Insurance condition deleted successfully',
}

enum ConditionErrorMessages {
  create = 'Failed to create insurance condition, please try again later',
  update = 'Failed to update insurance condition, please try again later',
  delete = 'Failed to cancel insurance condition, please try again later',
  hardDelete = 'Failed to delete insurance condition, please try again later',
}

export default function useContractMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateContract,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          singleInsuranceContractQuery(`${data.contract_id}`).queryKey,
          data,
        )
        if (!(variables.contractData as Record<string, any>).displayRenewMessage)
          ctToast.fire('success', SuccessMessages[variables.mutationType])
      }
    },
    onError: (error: Error, variables) => {
      if (error) {
        ctToast.fire('error', ErrorMessages[variables.mutationType])
      }
    },
    onSettled: (data, _, variables) => {
      if (data?.contract_id && variables.mutationType !== 'hardDelete') {
        queryClient.invalidateQueries(
          singleInsuranceContractQuery(`${data.contract_id}`),
        )
      }
    },
  })
}

const updateContract = async ({
  contractData,
  mutationType,
}: UpdateContract.ApiInput) =>
  listDataApi
    .postListData(
      `${ENDPOINT}?action=${mutationType === 'hardDelete' ? 'delete' : mutationType}`,
      contractData,
    )
    .then((res) => res.objectList)

// Update Contract Insurance Conditions
export function useInsuranceConditionMutation() {
  const queryClient = useQueryClient()

  return useMutation<
    UpdateInsuranceCondition.Return,
    Error,
    UpdateInsuranceCondition.ApiInput
  >({
    mutationFn: updateInsuranceCondition,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          insuranceContractConditionsQuery(`${variables.contract_id}`).queryKey,
          R.isArray(data) ? data : [{ ...data }],
        )
        ctToast.fire('success', ConditionSuccessMessages[variables.mutationType])
      }
    },
    onError: (error, variables) => {
      if (error) {
        ctToast.fire('error', ConditionErrorMessages[variables.mutationType])
      }
    },
    onSettled: (_data, _, variables) => {
      if (variables?.contract_id) {
        queryClient.invalidateQueries(
          insuranceContractConditionsQuery(`${variables.contract_id}`),
        )
      }
    },
  })
}

const updateInsuranceCondition = async ({
  conditionData,
  mutationType,
}: UpdateInsuranceCondition.ApiInput) =>
  listDataApi
    .postListData(`${CONDITIONS_ENDPOINT}?action=${mutationType}`, conditionData)
    .then((res) => res.objectList)
