import { useQuery } from '@tanstack/react-query'

import type { ContractStatus } from '@fleet-web/modules/mifleet/components/contracts/shared/types'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import listDataApi from '../../../../../api/mifleet/list-data'
import type { ContractStatusId } from '../database-constants'

export declare namespace FetchContracts {
  type ContractStatusInfo = {
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    is_deleted: boolean
    name: ContractStatus
    value: ContractStatusId
  }
  type Plafond = {
    contract_fuel_card_id: number
    contract_fuel_card_plafond_id: string
    start_date: string
    end_date: string
    is_deleted: string | boolean
    period_type: string
    period_type_id: number
    total_value: number
    isNew?: boolean
  }
  type FuelCardContract = {
    accounting_chart_number: null | number
    card_number: string
    company_id: number
    contract_date?: string
    contract_end_date: string
    contract_fuel_card_id: number
    contract_id: number
    contract_start_date: string
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    create_timestamp: number
    create_user_id: number
    create_user_name: string
    driver_id: null | string
    driver_name: null | string
    fleet_odometer: null | number
    net_value: number
    notes: null | string
    payment_method: null | string
    payment_method_id: null | string
    payment_term: null | string
    payment_term_id: null | string
    plate: string
    supplier?: string
    supplier_id: number | undefined
    target_object: string
    tax_deductable_value: number
    tax_non_deductable_value: number
    tax_type_id: number
    total_value?: number
    update_timestamp: number
    update_user_id: number
    update_user_name: string
    vehicle_id: string | number | undefined
    history: Array<FuelCardContract>
    succeeded_by_contract_id: number | string | null
    history_count: number
  }
  type APIReturn = [
    {
      vehicleContractFuelCards: Array<FuelCardContract>
    },
  ]

  export type Status = {
    ok: boolean
    intlMessage: string
  }
}

// Contracts
const ENDPOINT = 'vehicles/vehicleContractFuelCardJSON.php?action=read'
export const MIFLEET_CONTRACTS_FUEL_CARDS_QUERY_KEY = 'miFleetContractsFuelCards'

// Fuel Card Plafond
const PLAFOND_ENDPOINT = 'vehicles/vehicleContractFuelCardPlafondJSON.php'
export const MIFLEET_CONTRACTS_FUEL_CARD_PLAFOND_QUERY_KEY =
  'miFleetContractsFuelCardPlafond'

// Fetch All Contracts
export default function useFuelCardContracts() {
  return useQuery({
    queryKey: [MIFLEET_CONTRACTS_FUEL_CARDS_QUERY_KEY],
    queryFn: () => fetchContracts(),
  })
}

async function fetchContracts(): Promise<Array<FetchContracts.FuelCardContract>> {
  return listDataApi.getListData(ENDPOINT).then((res: FetchContracts.APIReturn) => {
    const data = res[0]
    return data.vehicleContractFuelCards
  })
}

// Fetch Single Contract

export const singleFuelCardContractQuery = (contractId: string) =>
  createQuery({
    queryKey: [MIFLEET_CONTRACTS_FUEL_CARDS_QUERY_KEY, contractId],
    queryFn: contractId ? () => fetchSingleContract(contractId) : undefined,
    enabled: !!contractId,
  })

export function useSingleFuelCardContractQuery(contractId: string) {
  return useQuery(singleFuelCardContractQuery(contractId))
}

async function fetchSingleContract(
  contractId: string,
): Promise<FetchContracts.FuelCardContract> {
  return listDataApi
    .postListData(ENDPOINT, {
      contract_id: contractId,
    })
    .then((res: { objectList: FetchContracts.FuelCardContract }) => res.objectList)
}

// Fetch Fuel Card Plafond

export const contractPlafondQuery = (contractId: string) =>
  createQuery({
    queryKey: [MIFLEET_CONTRACTS_FUEL_CARD_PLAFOND_QUERY_KEY, contractId],
    queryFn: () => fetchContractPlafond(contractId),
    enabled: !!contractId,
  })

export function useContractPlafond(contractId: string) {
  return useQuery(contractPlafondQuery(contractId))
}

async function fetchContractPlafond(
  contractId: string,
): Promise<Array<FetchContracts.Plafond>> {
  return listDataApi
    .getListData(`${PLAFOND_ENDPOINT}&contract=${contractId}`)
    .then((res: Array<FetchContracts.Plafond>) => res)
}
