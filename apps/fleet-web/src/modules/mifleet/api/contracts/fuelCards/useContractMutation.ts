import { isNil } from 'lodash'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as R from 'remeda'

import type { FuelContractSchema } from '@fleet-web/modules/mifleet/components/contracts/shared/contract-validation-schema'
import { ctToast } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import listDataApi from '../../../../../api/mifleet/list-data'
import {
  contractPlafondQuery,
  singleFuelCardContractQuery,
  type FetchContracts,
} from './useFuelCardContracts'

export declare namespace UpdateContract {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create' | 'hardDelete'
  type ApiInput = {
    contractData:
      | FetchContracts.FuelCardContract
      | { contract_id: ContractId; hard_delete?: boolean }
      | FuelContractSchema
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.FuelCardContract
}

export declare namespace UpdateFuelCardPlafond {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create'
  type ApiInput = {
    contract_id: ContractId
    plafondData: {
      contract_id?: ContractId
      contract_fuel_card_id?: null | string | number
      contract_fuel_card_plafond_id?: string
      end_date: string
      period_type?: string
      period_type_id: number
      start_date: string
      total_value: number
    }
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.Plafond
}

const ENDPOINT = 'vehicles/vehicleContractFuelCardJSON.php'
const PLAFOND_ENDPOINT = 'vehicles/vehicleContractFuelCardPlafondJSON.php'

enum SuccessMessages {
  create = 'Fuel Card contract created successfully',
  update = 'Fuel Card contract updated successfully',
  delete = 'Fuel Card contract cancelled successfully',
  hardDelete = 'Fuel Card contract deleted successfully',
}

enum ErrorMessages {
  create = 'Failed to create Fuel Card contract, please try again later',
  update = 'Failed to update Fuel Card contract, please try again later',
  delete = 'Failed to cancel Fuel Card contract, please try again later',
  hardDelete = 'Failed to delete Fuel Card contract, please try again later',
}

enum PlafondSuccessMessages {
  create = 'Fuel Card plafond created successfully',
  update = 'Fuel Card plafond updated successfully',
  delete = 'Fuel Card plafond cancelled successfully',
}

enum PlafondErrorMessages {
  create = 'Failed to create Fuel Card plafond, please try again later',
  update = 'Failed to update Fuel Card plafond, please try again later',
  delete = 'Failed to cancel Fuel Card plafond, please try again later',
}

export default function useContractMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateContract,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          singleFuelCardContractQuery(`${data.contract_id}`).queryKey,
          data,
        )
        if (!(variables.contractData as Record<string, any>).displayRenewMessage)
          ctToast.fire('success', SuccessMessages[variables.mutationType])
      }
    },
    onError: (error: Error, variables) => {
      if (error) {
        let errorMessage = ''
        const contractIsLinked = error.message.includes(':')
        if (contractIsLinked) {
          const parsedErrorObject = JSON.parse(error.message)
          const translatedString = Object.keys(parsedErrorObject)
            .map(
              (key: string) =>
                `${parsedErrorObject[key]} ${ctIntl.formatMessage({
                  id: `mifleet.delete.listdata.item.error.${key}`,
                })}`,
            )
            .join(', ')

          errorMessage = `${ctIntl.formatMessage({
            id: 'Fuel Card',
          })} ${ctIntl.formatMessage({
            id: `mifleet.${variables.mutationType}.listdata.item.error.links`,
          })} ${translatedString}`
        }
        ctToast.fire(
          'error',
          contractIsLinked && !isNil(errorMessage)
            ? errorMessage
            : ErrorMessages[variables.mutationType],
        )
      }
    },
    onSettled: (data, _, variables) => {
      if (data?.contract_id && variables.mutationType !== 'hardDelete') {
        queryClient.invalidateQueries(
          singleFuelCardContractQuery(`${data.contract_id}`),
        )
      }
    },
  })
}

const updateContract = async ({
  contractData,
  mutationType,
}: UpdateContract.ApiInput) =>
  listDataApi
    .postListData(
      `${ENDPOINT}?action=${mutationType === 'hardDelete' ? 'delete' : mutationType}`,
      contractData,
    )
    .then((res) => res.objectList)

// Update Contract Plafond
export function useFuelCardPlafondMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateFuelCardPlafond,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          contractPlafondQuery(`${variables.contract_id}`).queryKey,
          R.isArray(data) ? data : [{ ...data }],
        )
        ctToast.fire('success', PlafondSuccessMessages[variables.mutationType])
      }
    },
    onError: (error: Error, variables) => {
      if (error) {
        ctToast.fire('error', PlafondErrorMessages[variables.mutationType])
      }
    },
    onSettled: (_data, _, variables) => {
      if (variables?.contract_id) {
        queryClient.invalidateQueries(contractPlafondQuery(`${variables.contract_id}`))
      }
    },
  })
}

const updateFuelCardPlafond = async ({
  plafondData,
  mutationType,
}: UpdateFuelCardPlafond.ApiInput) =>
  listDataApi
    .postListData(`${PLAFOND_ENDPOINT}?action=${mutationType}`, plafondData)
    .then((res) => res.objectList)
