import { useMutation, useQueryClient } from '@tanstack/react-query'

import type { MaintenanceContractSchema } from '@fleet-web/modules/mifleet/components/contracts/shared/contract-validation-schema'
import { ctToast } from '@fleet-web/util-components'

import listDataApi from '../../../../../api/mifleet/list-data'
import {
  singleMaintenanceContractQuery,
  type FetchContracts,
} from './useMaintenanceContracts'

export declare namespace UpdateContract {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create' | 'hardDelete'
  type ApiInput = {
    contractData:
      | FetchContracts.MaintenanceContract
      | { contract_id: ContractId; hard_delete?: boolean }
      | MaintenanceContractSchema
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.MaintenanceContract
}

const ENDPOINT = 'vehicles/vehicleContractMaintenanceJSON.php'

enum SuccessMessages {
  create = 'Maintenance contract created successfully',
  update = 'Maintenance contract updated successfully',
  delete = 'Maintenance contract cancelled successfully',
  hardDelete = 'Maintenance contract deleted successfully',
}

enum ErrorMessages {
  create = 'Failed to create maintenance contract, please try again later',
  update = 'Failed to update maintenance contract, please try again later',
  delete = 'Failed to cancel maintenance contract, please try again later',
  hardDelete = 'Failed to delete maintenance contract, please try again later',
}

export default function useContractMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateContract,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          singleMaintenanceContractQuery(`${data.contract_id}`).queryKey,
          data,
        )
        if (!(variables.contractData as Record<string, any>).displayRenewMessage)
          ctToast.fire('success', SuccessMessages[variables.mutationType])
      }
    },
    onError: (error: Error, variables) => {
      if (error) {
        ctToast.fire('error', ErrorMessages[variables.mutationType])
      }
    },
    onSettled: (data, _, variables) => {
      if (data?.contract_id && variables.mutationType !== 'hardDelete') {
        queryClient.invalidateQueries(
          singleMaintenanceContractQuery(`${data.contract_id}`),
        )
      }
    },
  })
}

const updateContract = async ({
  contractData,
  mutationType,
}: UpdateContract.ApiInput) =>
  listDataApi
    .postListData(
      `${ENDPOINT}?action=${mutationType === 'hardDelete' ? 'delete' : mutationType}`,
      contractData,
    )
    .then((res) => res.objectList)
