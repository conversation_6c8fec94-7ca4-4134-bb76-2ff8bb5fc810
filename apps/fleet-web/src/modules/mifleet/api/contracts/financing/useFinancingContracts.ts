import { useQuery } from '@tanstack/react-query'

import type { ContractStatus } from '@fleet-web/modules/mifleet/components/contracts/shared/types'
import { createQuery } from '@fleet-web/util-functions/react-query-utils'

import listDataApi from '../../../../../api/mifleet/list-data'
import type { ContractStatusId } from '../database-constants'

export declare namespace FetchContracts {
  type ContractStatusInfo = {
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    is_deleted: boolean
    name: ContractStatus
    value: ContractStatusId
  }
  type FinancingContract = {
    accounting_chart_number: string
    company_id: number
    contract_date?: string
    contract_end_date: string
    contract_financing_id: number
    contract_id: number
    contract_start_date: string
    contract_status: ContractStatus
    contract_status_id: ContractStatusId
    create_timestamp: number
    create_user_id: number
    create_user_name: string
    driver_id: null | string
    driver_name: null | string
    financing_type?: string
    fleet_odometer: string
    interest: null | string
    net_value: string
    notes: null | string
    odometer_limit: string
    payment_method: null | string
    payment_method_id: null | string
    payment_term: null | string
    payment_term_id: null | string
    plate: string
    residual_value: null | string
    supplier?: string
    supplier_id: string
    tax_deductable_value: number
    tax_non_deductable_value: string
    tax_type_id: string
    total_value?: string
    update_timestamp: number
    update_user_id: number
    update_user_name: string
    vehicle_financing_type_id: string
    vehicle_id: string
    history: Array<FinancingContract>
    succeeded_by_contract_id: number | string | null
    history_count: number
  }
  type APIReturn = [
    {
      vehicleContractFinancings: Array<FinancingContract>
    },
  ]

  export type Status = {
    ok: boolean
    intlMessage: string
  }
}

export const MIFLEET_CONTRACTS_FINANCING_QUERY_KEY = 'miFleetContractsFinancing'
const ENDPOINT = 'vehicles/vehicleContractFinancingJSON.php?action=read'

export default function useFinancingContracts() {
  return useQuery({
    queryKey: [MIFLEET_CONTRACTS_FINANCING_QUERY_KEY],
    queryFn: () => fetchContracts(),
  })
}

async function fetchContracts(): Promise<Array<FetchContracts.FinancingContract>> {
  return listDataApi.getListData(ENDPOINT).then((res: FetchContracts.APIReturn) => {
    const data = res[0]
    return data.vehicleContractFinancings
  })
}

// Fetch Single Financing Contract

export const singleFinancingContractsQuery = (contractId: string) =>
  createQuery({
    queryKey: [MIFLEET_CONTRACTS_FINANCING_QUERY_KEY, contractId],
    queryFn: contractId ? () => fetchSingleFinancingContract(contractId) : undefined,
    enabled: !!contractId,
  })

export function useSingleFinancingContractsQuery(contractId: string) {
  return useQuery(singleFinancingContractsQuery(contractId))
}

async function fetchSingleFinancingContract(
  contractId: string,
): Promise<FetchContracts.FinancingContract> {
  return listDataApi
    .postListData(ENDPOINT, {
      contract_id: contractId,
    })
    .then((res: { objectList: FetchContracts.FinancingContract }) => res.objectList)
}
