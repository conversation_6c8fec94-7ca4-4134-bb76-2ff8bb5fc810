import { useMutation, useQueryClient } from '@tanstack/react-query'

import type { FinancingContractSchema } from '@fleet-web/modules/mifleet/components/contracts/shared/contract-validation-schema'
import { ctToast } from '@fleet-web/util-components'

import listDataApi from '../../../../../api/mifleet/list-data'
import {
  singleFinancingContractsQuery,
  type FetchContracts,
} from './useFinancingContracts'

export declare namespace UpdateContract {
  type ContractId = number
  type ApiParameters = 'delete' | 'update' | 'create' | 'hardDelete'
  type ApiInput = {
    contractData:
      | FetchContracts.FinancingContract
      | { contract_id: ContractId; hard_delete?: boolean }
      | FinancingContractSchema
    mutationType: ApiParameters
  }
  type ApiOutput = Document
  type Return = FetchContracts.FinancingContract
}

const ENDPOINT = 'vehicles/vehicleContractFinancingJSON.php'

enum SuccessMessages {
  create = 'Financing contract created successfully',
  update = 'Financing contract updated successfully',
  delete = 'Financing contract cancelled successfully',
  hardDelete = 'Financing contract deleted successfully',
}

enum ErrorMessages {
  create = 'Failed to create financing contract, please try again later',
  update = 'Failed to update financing contract, please try again later',
  delete = 'Failed to cancel financing contract, please try again later',
  hardDelete = 'Failed to delete financing contract, please try again later',
}

export default function useContractMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateContract,
    onSuccess: (data, variables) => {
      if (data) {
        queryClient.setQueryData(
          singleFinancingContractsQuery(`${data.contract_id}`).queryKey,
          data,
        )

        if (!(variables.contractData as Record<string, any>).displayRenewMessage)
          ctToast.fire('success', SuccessMessages[variables.mutationType])
      }
    },
    onError: (error: Error, variables) => {
      if (error) {
        ctToast.fire('error', ErrorMessages[variables.mutationType])
      }
    },
    onSettled: (data, _, variables) => {
      if (data?.contract_id && variables.mutationType !== 'hardDelete') {
        queryClient.invalidateQueries(
          singleFinancingContractsQuery(`${data.contract_id}`),
        )
      }
    },
  })
}

const updateContract = async ({
  contractData,
  mutationType,
}: UpdateContract.ApiInput) =>
  listDataApi
    .postListData(
      `${ENDPOINT}?action=${mutationType === 'hardDelete' ? 'delete' : mutationType}`,
      contractData,
    )
    .then((res) => res.objectList)
