import { useMemo } from 'react'
import {
  Box,
  IconButton,
  SearchTextField,
  Stack,
  useSearchTextField,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/EditOutlined'
import { useHistory, useRouteMatch } from 'react-router'
import * as R from 'remeda'

import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import IntlTypography from '@fleet-web/util-components/IntlTypography'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from '@fleet-web/util-functions/search-utils'

import {
  ListDataGeneralSettingsSubItems,
  ListDataVehicleSettingsSubItems,
  type ListDataSettingType,
} from './data/list-data'
import ListDataDrawer from './setting/shared/ListDataDrawer'
import { listDataSearchParamsSchema } from './shared/utils'

const ListData = () => {
  const { url } = useRouteMatch()
  const history = useHistory()
  const searchProps = useSearchTextField('')

  const sortedListData = R.sort(
    [...ListDataGeneralSettingsSubItems, ...ListDataVehicleSettingsSubItems],
    (i1, i2) =>
      ctIntl
        .formatMessage({ id: i1.label })
        .localeCompare(ctIntl.formatMessage({ id: i2.label })),
  )

  const filteredListData = useMemo(() => {
    const searchFilters: Filters<ListDataSettingType> = {
      search: [(item) => ctIntl.formatMessage({ id: item.label })],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )

    return sortedListData.filter((i) => itemMatchesWithTextAndFilters(i, searchFilters))
  }, [searchProps.value, sortedListData])

  return (
    <Box
      px={2.5}
      py={4}
    >
      <IntlTypography
        variant="h6"
        msgProps={{ id: 'List Data' }}
        mb={3}
      />
      <SearchTextField
        {...searchProps}
        placeholder={ctIntl.formatMessage({ id: 'Search' })}
        sx={{
          mb: 2,
        }}
      />

      <Stack
        sx={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: 2,
        }}
      >
        {filteredListData.map((item) => (
          <Stack
            key={item.id}
            sx={{
              height: '48px',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              boxShadow: 2,
              width: 'calc((100% - 32px)/3)',
              borderRadius: '3px',
              px: 1,
              '&:nth-child(3n)': {
                mr: 0,
              },
            }}
          >
            <IntlTypography
              sx={{
                pointerEvents: 'none',
              }}
              msgProps={{ id: item.label }}
            />

            <IconButton
              size="small"
              onClick={() => {
                history.push(
                  `${url}?${buildRouteQueryStringKeepingExistingSearchParams({
                    location: history.location,
                    schema: listDataSearchParamsSchema,
                    searchParams: { type: 'edit', id: item.id },
                  })}`,
                )
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Stack>
        ))}
      </Stack>
      <ListDataDrawer />
    </Box>
  )
}

export default ListData
