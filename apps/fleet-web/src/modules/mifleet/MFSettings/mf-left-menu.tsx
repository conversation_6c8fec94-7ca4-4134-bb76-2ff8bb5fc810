import { memo } from 'react'
import { Box, styled } from '@karoo-ui/core'
import { rgba } from 'polished'

import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { settingsMenuTree } from './constants'

export type MifleetSubMenuSelectedSetting =
  | (typeof settingsMenuTree)[number]['id']
  | null

type Props = {
  handleNavItemClick(selectedSetting: MifleetSubMenuSelectedSetting): void
  isActiveToggle: boolean
  item: FixMeAny
}

function LeftMenu({ handleNavItemClick, isActiveToggle, item }: Props) {
  return (
    <Box>
      <MenuHolder
        isActiveToggle={isActiveToggle}
        onClick={() => handleNavItemClick(item.id)}
      >
        <StyledMenuContainer>
          <StyledMenuTitle>{ctIntl.formatMessage({ id: item.label })}</StyledMenuTitle>
        </StyledMenuContainer>
      </MenuHolder>
    </Box>
  )
}

export default memo(LeftMenu)

const MenuHolder = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isActiveToggle',
})<{ isActiveToggle: boolean }>(({ isActiveToggle, theme: { palette } }) => ({
  cursor: 'pointer',
  height: '40px',
  lineHeight: '40px',
  background: isActiveToggle
    ? rgba(palette.primary.main, palette.action.selectedOpacity)
    : `unset`,
  ':hover': {
    borderLeft: `2px ${rgba(
      palette.primary.main,
      palette.action.activatedOpacity,
    )} solid`,
  },
}))

const StyledMenuContainer = styled('div')`
  display: flex;
  flex-direction: row;
  padding: 0px 16px;
`
const StyledMenuTitle = styled('span', {
  shouldForwardProp: (prop) => prop !== 'isSub',
})<{ isSub?: boolean }>(({ isSub }) => ({
  flex: '1',
  display: 'block',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  padding: isSub ? `0px 24px` : `0 16px 0 0`,
}))
