import { useEffect, useState } from 'react'
import { head, isEmpty } from 'lodash'
import { Divider, Typography } from '@karoo-ui/core'
import { generatePath, useHistory } from 'react-router'
import styled from 'styled-components'

import useCollapseSidebar from '@fleet-web/modules/app/components/MainSideNavbar/useCollapseSidebar'
import { COSTS } from '@fleet-web/modules/app/components/routes/costs'
//Content
import CostCentres from '@fleet-web/modules/mifleet/costCentres/cost-centres'
import FiscalConfigurations from '@fleet-web/modules/mifleet/setting/fiscal-configurations/fiscal-configurations'
import FiscalConfigurationsDetails from '@fleet-web/modules/mifleet/setting/fiscal-configurations/fiscal-configurations-details'
import FuelValidation from '@fleet-web/modules/mifleet/setting/fuel-validation'
import VehicleMapping from '@fleet-web/modules/mifleet/setting/vehicle-mapping'
import Suppliers from '@fleet-web/modules/mifleet/suppliers'
import TaxTypes from '@fleet-web/modules/mifleet/tax-types'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import ListData from '../ListData'
import { settingsMenuTree } from './constants'
import LeftMenu, { type MifleetSubMenuSelectedSetting } from './mf-left-menu'

const MiFleetSettings = () => {
  const history = useHistory()
  const [detailsId, setDetailsId] = useState()

  const getDefaultOpenedSetting = () => {
    const pathChecker = settingsMenuTree.find(
      (item) =>
        generatePath(COSTS.MIFLEET_SETTINGS.path, { subMenu: item.url }) ===
        history.location.pathname,
    )

    return pathChecker?.id || head(settingsMenuTree)?.id || null
  }

  const [selectedSetting, setSelectedSetting] = useState<MifleetSubMenuSelectedSetting>(
    getDefaultOpenedSetting(),
  )

  // collapse the main sidebar when open the mifleet settings since it has its own sidebar
  useCollapseSidebar()

  useEffect(() => {
    const historyState = history.location.state as { from: string }

    const fullSetting = settingsMenuTree.find((item) => item.id === selectedSetting)

    const path = generatePath(COSTS.MIFLEET_SETTINGS.path, {
      subMenu: fullSetting?.url || '',
    })

    history.push(path, {
      from: historyState?.from,
    })
  }, [history, selectedSetting])

  const handleNavItemClick = (selectedSetting: MifleetSubMenuSelectedSetting) => {
    if (isEmpty(selectedSetting)) {
      return
    }

    setSelectedSetting(selectedSetting)
    setDetailsId(undefined)
  }

  const getSelectedContent = () => {
    switch (selectedSetting) {
      case 'costcentres-category': {
        return <CostCentres />
      }
      case 'suppliers-category': {
        return <Suppliers />
      }
      case 'taxes-category': {
        return <TaxTypes setTaxTypesId={setDetailsId} />
      }
      case 'fiscal-configurations-category': {
        if (!detailsId) {
          return <FiscalConfigurations />
        } else {
          return <FiscalConfigurationsDetails fiscalConfigurationId={detailsId} />
        }
      }
      case 'listdata-category': {
        return <ListData />
      }
      case 'vehicle-mapping-category': {
        return <VehicleMapping />
      }

      case 'fuel-validation-category': {
        return <FuelValidation />
      }
      default: {
        return null
      }
    }
  }

  const navItemsSorted: FixMeAny = (item: Record<string, any>) => {
    const translateLabel = item.subItems
      ?.map((element: Record<string, any>) =>
        element.label
          ? { ...element, label: ctIntl.formatMessage({ id: element.label }) }
          : '',
      )
      .sort((item1: FixMeAny, item2: FixMeAny) =>
        item1.label.localeCompare(item2.label),
      )
    const sortedItems: Record<string, any> = {
      ...item,
      subItems: translateLabel,
    }
    return sortedItems
  }

  const renderNavItems = () =>
    settingsMenuTree.map((item) => {
      const isActiveToggle = selectedSetting === item.id
      const sortedItem = navItemsSorted(item)
      return (
        <LeftMenu
          handleNavItemClick={handleNavItemClick}
          isActiveToggle={isActiveToggle}
          item={sortedItem}
          key={sortedItem.id}
        />
      )
    })

  return (
    <StyledMFSettingsWrapper>
      <StyledLeftMenuWrapper>
        <Typography
          variant="h5"
          sx={{ p: '0 24px 24px 16px' }}
        >
          {ctIntl.formatMessage({ id: 'Settings' })}
        </Typography>
        {renderNavItems()}
      </StyledLeftMenuWrapper>
      <Divider orientation="vertical" />
      <StyledSettingsContainer>{getSelectedContent()}</StyledSettingsContainer>
    </StyledMFSettingsWrapper>
  )
}

export default MiFleetSettings

const StyledMFSettingsWrapper = styled.div`
  display: flex;
  flex-direction: row;
  height: 100%;
`

const StyledLeftMenuWrapper = styled.div`
  padding: 24px 0px;
  width: 300px;
  overflow-y: auto;
`

const StyledSettingsContainer = styled.div`
  flex: 1;
  overflow: auto;
`
