import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import { isNil } from 'lodash'
import {
  Button,
  CircularProgress,
  DataGrid,
  DatePicker,
  GridActionsCellItem,
  GridRowEditStopReasons,
  GridRowModes,
  LinearProgress,
  Stack,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridEventListener,
  type GridRowHeightParams,
  type GridRowId,
  type GridRowModesModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { Controller, useForm, useWatch } from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { messages } from '@fleet-web/shared/forms/messages'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DeleteSettingsDialog from '../../shared/DeleteSettingsDialog'
import type { SettingsTypes } from '../../shared/type'
import useMiFleetPHRulesQuery, {
  useCreateMiFleetPHRulesQuery,
  useDeleteMiFleetPHRulesQuery,
  useUpdateMiFleetPHRulesQuery,
} from './api/useMifleetHolidayRules'

const NewRowTemporaryId = 'new_row'

const initialFormValues = {
  public_holiday: new Date(),
  description: '',
  fiscal_configuration_public_holiday_id: '',
}
const formikSchema = z.object({
  public_holiday: z.date({ error: () => messages.required }),
  description: z.string().min(1, { message: messages.required }),
  fiscal_configuration_public_holiday_id: z.string(),
})
type ValidSchema = z.infer<typeof formikSchema>
type Props = { fiscalConfigurationId: string }
type FiscalPHRulesProps = {
  fiscalConfigurationId: string
  phRules: Array<SettingsTypes.phRules>
  refetchPHRulesQuery: () => void
}
const FiscalPhRules = ({ fiscalConfigurationId }: Props) => {
  const fetchPHRulesQuery = useMiFleetPHRulesQuery({
    fiscalConfigurationId: fiscalConfigurationId,
  })
  return match(fetchPHRulesQuery)
    .with({ status: 'pending', fetchStatus: 'fetching' }, () => (
      <Stack
        sx={{
          height: '400px',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data }) => (
      <FiscalPHRulesComponent
        fiscalConfigurationId={fiscalConfigurationId}
        phRules={data}
        refetchPHRulesQuery={fetchPHRulesQuery.refetch}
      />
    ))
    .with({ status: 'error' }, () => null)
    .otherwise(() => null)
}
const FiscalPHRulesComponent = ({
  fiscalConfigurationId,
  phRules = [],
  refetchPHRulesQuery,
}: FiscalPHRulesProps) => {
  const [phRulesList, setPhRulesList] = useState<Array<SettingsTypes.phRules>>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeItemToDelete, setActiveItemToDelete] = useState<
    SettingsTypes.phRules | undefined
  >(undefined)
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})
  const createFiscalPHRuleQuery = useCreateMiFleetPHRulesQuery()
  const updateFiscalPHRuleQuery = useUpdateMiFleetPHRulesQuery()
  const deleteFiscalPHRuleQuery = useDeleteMiFleetPHRulesQuery()
  const columnHelper = useDataGridColumnHelper<SettingsTypes.phRules>({
    filterMode: 'client',
  })
  useEffect(() => {
    setPhRulesList(phRules)
  }, [phRules])

  const {
    control,
    formState: { errors, isValid },
    reset,
    setValue: setFormValue,
    getValues,
  } = useForm<ValidSchema>({
    resolver: zodResolverV4(formikSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialFormValues,
  })
  const watchPhId = useWatch({
    name: 'fiscal_configuration_public_holiday_id',
    control,
  })

  const errorsLength = Object.keys(errors).length

  const handleCreateItem = () => {
    const createChecker = phRulesList.filter((o) =>
      o.fiscal_configuration_public_holiday_id.includes(NewRowTemporaryId),
    )
    if (createChecker.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'You should save the line first!' }),
        { variant: 'error' },
      )
      return
    }
    const defaultObj = {
      ...initialFormValues,
      fiscal_configuration_public_holiday_id: NewRowTemporaryId,
      fiscal_configuration_id: fiscalConfigurationId,
      is_deleted: 'f',
    }
    setPhRulesList((oldRows) => [
      {
        ...defaultObj,
      },
      ...oldRows,
    ])
    setRowModesModel({
      [NewRowTemporaryId]: { mode: GridRowModes.Edit, fieldToFocus: 'name' },
    })
    reset({
      ...initialFormValues,
      fiscal_configuration_public_holiday_id: NewRowTemporaryId,
    })
  }

  const handleResetItem = useCallback(
    (row: SettingsTypes.phRules) => {
      setPhRulesList(
        phRulesList.filter(
          (item) => item.fiscal_configuration_public_holiday_id !== NewRowTemporaryId,
        ),
      )
      setRowModesModel((prevRowModesModel) => ({
        ...prevRowModesModel,
        [row.fiscal_configuration_public_holiday_id]: {
          mode: GridRowModes.View,
          ignoreModifications: true,
        },
      }))
      reset(initialFormValues)
    },
    [phRulesList, reset],
  )

  const handleEditItem = useCallback(
    (row: SettingsTypes.phRules) => {
      if (watchPhId.includes(NewRowTemporaryId)) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'mifleet.settings.save.form.first' }),
          { variant: 'error' },
        )

        return
      }
      setRowModesModel((oldModel) => ({
        ...R.mapValues(oldModel, (value, key) => {
          if (key === row.fiscal_configuration_public_holiday_id) {
            return value
          }
          return { mode: GridRowModes.View, ignoreModifications: true }
        }),
        [row.fiscal_configuration_public_holiday_id]: { mode: GridRowModes.Edit },
      }))
      reset({
        ...row,
        public_holiday: new Date(row.public_holiday),
      })
    },
    [reset, watchPhId],
  )
  const handleSaveItem = useCallback(() => {
    const data = {
      ...getValues(),
      fiscal_configuration_id: fiscalConfigurationId,
      is_deleted: 'f',
    }
    if (watchPhId.includes(NewRowTemporaryId)) {
      createFiscalPHRuleQuery.mutate(data, {
        onSuccess() {
          refetchPHRulesQuery()
        },
      })
    } else {
      updateFiscalPHRuleQuery.mutate(data, {
        onSuccess() {
          refetchPHRulesQuery()
        },
      })
    }
    setRowModesModel({
      ...rowModesModel,
      [watchPhId]: { mode: GridRowModes.View },
    })
    reset(initialFormValues)
  }, [
    createFiscalPHRuleQuery,
    fiscalConfigurationId,
    getValues,
    refetchPHRulesQuery,
    reset,
    rowModesModel,
    updateFiscalPHRuleQuery,
    watchPhId,
  ])
  const openDeletionModal = (row: SettingsTypes.phRules) => {
    setActiveItemToDelete(row)
    setIsModalOpen(true)
  }

  const handleDeleteItem = () => {
    if (activeItemToDelete) {
      const { fiscal_configuration_public_holiday_id, fiscal_configuration_id } =
        activeItemToDelete
      deleteFiscalPHRuleQuery.mutate(
        {
          fiscal_configuration_public_holiday_id,
          fiscal_configuration_id,
        },
        {
          onSuccess() {
            refetchPHRulesQuery()
          },
        },
      )
      setActiveItemToDelete(undefined)
      setIsModalOpen(false)
    }
  }

  const renderFiscalCellDescription = useCallback(
    (key: 'description') => (
      <TextFieldControlled
        fullWidth
        ControllerProps={{
          name: key,
          control,
        }}
        sx={{
          input: {
            backgroundColor: '#fff',
          },
        }}
        onKeyDown={(event) => {
          // https://stackoverflow.com/questions/71055614/why-is-the-space-key-being-filtered-out-by-muis-text-field-component
          event.stopPropagation()
        }}
        required
      />
    ),
    [control],
  )
  const renderFiscalCellDate = useCallback(
    (key: 'public_holiday') => {
      const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
        e.preventDefault()
        e.stopPropagation()
      }
      return (
        <Controller
          control={control}
          name={key}
          render={({ field, fieldState }) => (
            <DatePicker
              value={
                isNil(field.value) ? field.value : DateTime.fromJSDate(field.value)
              }
              onChange={(newValue) =>
                setFormValue(field.name, newValue ? newValue.toJSDate() : new Date(), {
                  shouldValidate: true,
                })
              }
              slotProps={{
                textField: {
                  required: true,
                  error: !!fieldState.error,
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  onKeyDownCapture: onKeyDown,
                  sx: {
                    '.MuiInputBase-root': {
                      backgroundColor: '#fff',
                    },
                  },
                },
              }}
            />
          )}
        />
      )
    },
    [control, setFormValue],
  )
  const columns = useMemo(
    (): Array<GridColDef<SettingsTypes.phRules>> => [
      columnHelper.date({
        headerName: ctIntl.formatMessage({ id: 'Public Holiday' }),
        field: 'public_holiday',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueGetter: (_, row) =>
          row.public_holiday ? new Date(row.public_holiday) : null,
        renderEditCell: () => renderFiscalCellDate('public_holiday'),
      }),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        minWidth: 140,
        flex: 1,
        editable: true,
        renderEditCell: () => renderFiscalCellDescription('description'),
      }),
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const isInEditMode =
            rowModesModel[row.fiscal_configuration_public_holiday_id]?.mode ===
            GridRowModes.Edit
          return isInEditMode
            ? [
                <GridActionsCellItem
                  key="saveEdit"
                  icon={<CheckOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Update' })}
                  onClick={handleSaveItem}
                  disabled={!isValid}
                  material={{ color: 'success' }}
                />,
                <GridActionsCellItem
                  key="cancelEdit"
                  icon={<CloseOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Close' })}
                  onClick={() => handleResetItem(row)}
                  material={{ color: 'error' }}
                />,
              ]
            : [
                <GridActionsCellItem
                  key="edit"
                  icon={<ModeEditOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => handleEditItem(row)}
                />,
                <GridActionsCellItem
                  key="delete"
                  icon={<DeleteOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={() => openDeletionModal(row)}
                />,
              ]
        },
      },
    ],
    [
      columnHelper,
      handleEditItem,
      handleResetItem,
      handleSaveItem,
      isValid,
      renderFiscalCellDate,
      renderFiscalCellDescription,
      rowModesModel,
    ],
  )
  const handleRowEditStop: GridEventListener<'rowEditStop'> = (params, event) => {
    if (params.reason === GridRowEditStopReasons.rowFocusOut) {
      // eslint-disable-next-line no-param-reassign
      event.defaultMuiPrevented = true
    }
  }
  const editedRow = Object.entries(rowModesModel).find(
    ([_, value]) => value.mode === 'edit',
  )

  const rowSelectionModel = useMemo(
    (): ReadonlySet<GridRowId> => (editedRow ? new Set([editedRow[0]]) : new Set()),
    [editedRow],
  )
  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        sx={(theme) => ({
          '.MuiDataGrid-virtualScroller': {
            minHeight: '400px',
            height: '100%',
          },
          '.MuiDataGrid-row.Mui-selected': {
            backgroundColor: rgba(
              theme.palette.primary.main,
              theme.palette.action.selectedOpacity,
            ),
            '.MuiDataGrid-cell': {
              alignItems: () =>
                errorsLength > 0 ? 'flex-start !important' : 'inherit',
              padding: '8px 10px',
            },
          },
        })}
        getRowHeight={useCallbackBranded(
          ({ id }: GridRowHeightParams<SettingsTypes.phRules>) => {
            if (errorsLength > 0 && id === watchPhId) {
              return 75
            }
            return null
          },
          [errorsLength, watchPhId],
        )}
        Component={DataGrid}
        dataGridId="fiscalPhRules"
        disableRowSelectionOnClick
        pagination
        rows={phRulesList}
        getRowId={useCallbackBranded(
          (row: (typeof phRulesList)[number]) =>
            row.fiscal_configuration_public_holiday_id,
          [],
        )}
        columns={columns}
        autoPageSize
        rowSelectionModel={rowSelectionModel}
        editMode="row"
        rowModesModel={rowModesModel}
        onRowEditStop={handleRowEditStop}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          filterPanel: {
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  size="small"
                  color="primary"
                  onClick={handleCreateItem}
                >
                  {ctIntl.formatMessage({
                    id: 'Add New',
                  })}
                </Button>
              ),
            },
          }),
        }}
      />

      {isModalOpen && (
        <DeleteSettingsDialog
          onClose={() => {
            setActiveItemToDelete(undefined)
            setIsModalOpen(false)
          }}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Public Holidays',
          }}
        />
      )}
    </>
  )
}

export default FiscalPhRules
