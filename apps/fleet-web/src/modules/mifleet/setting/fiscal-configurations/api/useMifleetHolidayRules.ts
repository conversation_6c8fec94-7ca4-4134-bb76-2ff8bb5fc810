import { useMutation, useQuery } from '@tanstack/react-query'

import {
  makeMutationErrorHandlerWithSnackbar,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import listDataApi from '@fleet-web/modules/mifleet/lite/api/MiFleetApi'
import type { SettingsTypes } from '@fleet-web/modules/mifleet/shared/type'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type params = { fiscalConfigurationId: string }
export const MIFLEET_ARRAY_PH_CONFIGURAION = 'mifleetPHRulesConfiguration'
const ENDPOINT = 'setupGeneral/fiscalConfigurationPublicHolidayJSON.php'
const NewRowTemporaryId = 'new_row'

export default function useMiFleetPHRulesQuery({ fiscalConfigurationId }: params) {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_PH_CONFIGURAION, fiscalConfigurationId],
    queryFn: () =>
      fetchMiFleetPHRules({
        fiscalConfigurationId: fiscalConfigurationId,
      }),
    enabled: !!(fiscalConfigurationId && fiscalConfigurationId !== NewRowTemporaryId),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetPHRules(params: params) {
  return listDataApi
    .getListData(
      `${ENDPOINT}?action=read&fiscalConfigurationId=${params.fiscalConfigurationId}`,
    )
    .then((response: Array<SettingsTypes.phRules>) => response)
}

export const useCreateMiFleetPHRulesQuery = () =>
  useMutation({
    mutationFn: createPHRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal public holiday rule created',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function createPHRules(params: SettingsTypes.phRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=create`, params)
    .then((response: { objectList: SettingsTypes.phRules }) => response.objectList)
}

export const useUpdateMiFleetPHRulesQuery = () =>
  useMutation({
    mutationFn: updatePHRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal public holiday rule updated',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function updatePHRules(params: SettingsTypes.phRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=update`, params)
    .then((response: { objectList: SettingsTypes.phRules }) => response.objectList)
}

export const useDeleteMiFleetPHRulesQuery = () =>
  useMutation({
    mutationFn: deletePHRuels,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal public holiday rule deleted',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function deletePHRuels(params: {
  fiscal_configuration_public_holiday_id: string
  fiscal_configuration_id: string
}) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=delete`, params)
    .then((response: { objectList: SettingsTypes.phRules }) => response.objectList)
}
