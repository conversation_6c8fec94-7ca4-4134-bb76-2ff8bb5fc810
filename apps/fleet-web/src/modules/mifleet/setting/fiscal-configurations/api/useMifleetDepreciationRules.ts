import { useMutation, useQuery } from '@tanstack/react-query'

import {
  makeMutationErrorHandlerWithSnackbar,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import listDataApi from '@fleet-web/modules/mifleet/lite/api/MiFleetApi'
import type { SettingsTypes } from '@fleet-web/modules/mifleet/shared/type'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type params = { fiscalConfigurationId: string }
export const MIFLEET_ARRAY_DEP_CONFIGURAION = 'mifleetDepRulesConfiguration'
const ENDPOINT = 'setupVehicle/fiscalConfigurationDepreciationRuleJSON.php'
const NewRowTemporaryId = 'new_row'

export default function useMiFleetDepRulesQuery({ fiscalConfigurationId }: params) {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_DEP_CONFIGURAION, fiscalConfigurationId],
    queryFn: () =>
      fetchMiFleetDepRules({
        fiscalConfigurationId: fiscalConfigurationId,
      }),
    enabled: !!(fiscalConfigurationId && fiscalConfigurationId !== NewRowTemporaryId),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetDepRules(params: params) {
  return listDataApi
    .getListData(
      `${ENDPOINT}?action=read&fiscalConfigurationId=${params.fiscalConfigurationId}`,
    )
    .then((response: Array<SettingsTypes.depRules>) => response)
}

export const useCreateMiFleetDepRulesQuery = () =>
  useMutation({
    mutationFn: createDepRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal depreciation rule created',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function createDepRules(params: SettingsTypes.depRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=create`, params)
    .then((response: { objectList: SettingsTypes.depRules }) => response.objectList)
}

export const useUpdateMiFleetDepRulesQuery = () =>
  useMutation({
    mutationFn: updateDepRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal depreciation rule updated',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function updateDepRules(params: SettingsTypes.depRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=update`, params)
    .then((response: { objectList: SettingsTypes.depRules }) => response.objectList)
}

export const useDeleteMiFleetDepRulesQuery = () =>
  useMutation({
    mutationFn: deleteDepRuels,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal depreciation rule deleted',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function deleteDepRuels(params: {
  fiscal_configuration_depreciation_rule_id: string
  fiscal_configuration_id: string
}) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=delete`, params)
    .then((response: { objectList: SettingsTypes.depRules }) => response.objectList)
}
