import { useMutation, useQuery } from '@tanstack/react-query'
import type { Except } from 'type-fest'

import {
  makeMutationErrorHandlerWithSnackbar,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import listDataApi from '@fleet-web/modules/mifleet/lite/api/MiFleetApi'
import type { SettingsTypes } from '@fleet-web/modules/mifleet/shared/type'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

export const MIFLEET_ARRAY_FISCAL_CONFIGURAION = 'mifleetFiscalConfiguration'
const ENDPOINT = 'setupGeneral/fiscalConfigurationJSON.php'

export default function useMiFleetFiscalConfigurationQuery() {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_FISCAL_CONFIGURAION],
    queryFn: () => fetchMiFleetFiscalConfiguration(),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetFiscalConfiguration() {
  return listDataApi
    .getListData(`${ENDPOINT}?action=read`)
    .then((response: Array<SettingsTypes.fiscalConfig>) => response)
}

export const useDeleteMiFleetFiscalConfigurationQuery = () =>
  useMutation({
    mutationFn: deleteFiscalConfiguraion,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal configuration deleted',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function deleteFiscalConfiguraion(params: {
  fiscal_configuration_id: string
}): Promise<SettingsTypes.fiscalConfig> {
  return listDataApi
    .postListData(`${ENDPOINT}?action=delete`, params)
    .then((response: { objectList: SettingsTypes.fiscalConfig }) => response.objectList)
}
export const useUpdateMiFleetFiscalConfigurationQuery = () =>
  useMutation({
    mutationFn: updateFiscalConfiguraion,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal configuration updated',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function updateFiscalConfiguraion(params: SettingsTypes.fiscalConfig) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=update`, params)
    .then((response: { objectList: SettingsTypes.fiscalConfig }) => response.objectList)
}

export const useCreateMiFleetFiscalConfigurationQuery = () =>
  useMutation({
    mutationFn: createFiscalConfiguraion,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal configuration created',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function createFiscalConfiguraion(
  params: Except<SettingsTypes.fiscalConfig, 'fiscal_configuration_id'>,
) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=create`, params)
    .then((response: { objectList: SettingsTypes.fiscalConfig }) => response.objectList)
}
