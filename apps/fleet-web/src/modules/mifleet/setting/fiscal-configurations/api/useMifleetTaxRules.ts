import { useMutation, useQuery } from '@tanstack/react-query'

import {
  makeMutationErrorHandlerWithSnackbar,
  makeQueryErrorHandlerWithToast,
} from '@fleet-web/api/helpers'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import listDataApi from '@fleet-web/modules/mifleet/lite/api/MiFleetApi'
import type { SettingsTypes } from '@fleet-web/modules/mifleet/shared/type'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const ENDPOINT = 'setupGeneral/fiscalConfigurationVATRuleJSON.php'
const ENDPOINT_FUEL = 'setupVehicle/vehicleFuelTypeJSON.php'
const NewRowTemporaryId = 'new_row'

export const MIFLEET_ARRAY_VAT_CONFIGURAION = 'mifleetVatRulesConfiguration'
export const MIFLEET_ARRAY_FUEL_TYPES = 'mifleetFuelTypesArray'
export const MIFLEET_ARRAY_VEHICLE_TYPES = 'mifleetVehicleTypesArray'
export const ENDPOINT_VEHICLE_TYPE = 'setupVehicle/vehicleTypeJSON.php'
type params = { fiscalConfigurationId: string }
export default function useMiFleetVatRulesQuery({ fiscalConfigurationId }: params) {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_VAT_CONFIGURAION, fiscalConfigurationId],
    queryFn: () =>
      fetchMiFleetVatRules({
        fiscalConfigurationId: fiscalConfigurationId,
      }),
    enabled: !!(fiscalConfigurationId && fiscalConfigurationId !== NewRowTemporaryId),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetVatRules(params: params) {
  return listDataApi
    .getListData(
      `${ENDPOINT}?action=read&fiscalConfigurationId=${params.fiscalConfigurationId}`,
    )
    .then((response: Array<SettingsTypes.vatRules>) => response)
}

export const useCreateMiFleetVatRulesQuery = () =>
  useMutation({
    mutationFn: createVatRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal TAX rule created',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function createVatRules(params: SettingsTypes.vatRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=create`, params)
    .then((response: { objectList: SettingsTypes.vatRules }) => response.objectList)
}
export const useUpdateMiFleetVatRulesQuery = () =>
  useMutation({
    mutationFn: updateVatRules,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal TAX rule updated',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function updateVatRules(params: SettingsTypes.vatRules) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=update`, params)
    .then((response: { objectList: SettingsTypes.vatRules }) => response.objectList)
}

export const useDeleteMiFleetVatRulesQuery = () =>
  useMutation({
    mutationFn: deleteVatRuels,
    onSuccess(data) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Fiscal TAX rule deleted',
        }),
        { variant: 'success' },
      )
      return data
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id: 'Something went wrong, please try again',
          }),
          {
            variant: 'error',
          },
        )
      },
    }),
  })

async function deleteVatRuels(params: {
  fiscal_configuration_vat_rule_id: string
  fiscal_configuration_id: string
}) {
  return listDataApi
    .postListData(`${ENDPOINT}?action=delete`, params)
    .then((response: { objectList: SettingsTypes.vatRules }) => response.objectList)
}

export function useMiFleetFuelTypesQuery() {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_FUEL_TYPES],
    queryFn: () => fetchMiFleetFuelTypes(),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetFuelTypes() {
  return listDataApi
    .getListData(`${ENDPOINT_FUEL}?action=read`)
    .then((response: Array<SettingsTypes.fuelType>) => response)
}
export function useMiFleetVehicleTypesQuery() {
  return useQuery({
    queryKey: [MIFLEET_ARRAY_VEHICLE_TYPES],
    queryFn: () => fetchMiFleetVehicleTypes(),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchMiFleetVehicleTypes() {
  return listDataApi
    .getListData(`${ENDPOINT_VEHICLE_TYPE}?action=read`)
    .then((response: Array<SettingsTypes.vehicleType>) => response)
}
