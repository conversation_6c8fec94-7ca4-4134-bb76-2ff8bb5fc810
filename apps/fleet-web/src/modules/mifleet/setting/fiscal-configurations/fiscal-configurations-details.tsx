import { useState } from 'react'
import { Box, CircularProgress, Stack, styled, Tab, Tabs } from '@karoo-ui/core'
import type { RouteComponentProps } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import type { SettingsTypes } from '../../shared/type'
import {
  useMiFleetFuelTypesQuery,
  useMiFleetVehicleTypesQuery,
} from './api/useMifleetTaxRules'
import DepreciationRules from './fiscal-dep-rules'
import PublicHolidays from './fiscal-ph-rules'
import FiscalVatRules from './fiscal-vat-rules'

const tabs = ['TAX Rules', 'Depreciation Rules', 'Public Holidays'] as const

type fiscalConfigDetailType = {
  fiscalConfigurationId: string
  match?: RouteComponentProps
}
type Props = fiscalConfigDetailType

const FiscalConfigurationsDetails = ({ fiscalConfigurationId }: Props) => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const fetchFuelTypesQuery = useMiFleetFuelTypesQuery()
  const fetchVehicleTypesQuery = useMiFleetVehicleTypesQuery()

  const handleClickTab = (_: React.SyntheticEvent, tab: number) => setActiveTab(tab)

  const getActiveTabContent = (
    e: number,
    mifleetFuelTypes: Array<SettingsTypes.fuelType>,
    mifleetVehicleTypes: Array<SettingsTypes.vehicleType>,
  ) => {
    const sharedProperties = {
      fiscalConfigurationId: fiscalConfigurationId,
      mifleetFuelTypes: mifleetFuelTypes,
      mifleetVehicleTypes: mifleetVehicleTypes,
    }
    switch (e) {
      case 0: {
        return <FiscalVatRules {...sharedProperties} />
      }
      case 1: {
        return <DepreciationRules {...sharedProperties} />
      }
      case 2: {
        return <PublicHolidays fiscalConfigurationId={fiscalConfigurationId} />
      }
      default: {
        return null
      }
    }
  }
  return (
    <StyleDetailHolder>
      {match([fetchFuelTypesQuery, fetchVehicleTypesQuery])
        .with(
          P.when(
            ([fetchFuelTypesQuery, fetchVehicleTypesQuery]) =>
              fetchFuelTypesQuery.status === 'pending' ||
              fetchVehicleTypesQuery.status === 'pending',
          ),
          () => (
            <Stack
              sx={{
                height: '400px',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <CircularProgress />
            </Stack>
          ),
        )
        .with(
          [{ status: 'success' }, { status: 'success' }],
          ([{ data: mifleetFuelTypes }, { data: mifleetVehicleTypes }]) => (
            <>
              <Box
                sx={{
                  borderBottom: 1,
                  borderColor: 'divider',
                  m: '0 16px',
                }}
              >
                <Tabs
                  value={activeTab}
                  onChange={handleClickTab}
                  aria-label="basic tabs example"
                >
                  {tabs.map((item) => (
                    <Tab
                      label={ctIntl.formatMessage({ id: item })}
                      key={item}
                    />
                  ))}
                </Tabs>
              </Box>
              {getActiveTabContent(activeTab, mifleetFuelTypes, mifleetVehicleTypes)}
            </>
          ),
        )
        .otherwise(() => null)}
    </StyleDetailHolder>
  )
}

export default FiscalConfigurationsDetails

const StyleDetailHolder = styled(Box)(({ theme }) => ({
  margin: `${spacing[4]}`,
  boxShadow: theme.shadows[1],
  minHeight: '400px',
}))
