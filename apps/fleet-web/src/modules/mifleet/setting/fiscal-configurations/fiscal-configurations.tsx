import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { isNil, toNumber } from 'lodash'
import {
  Box,
  Button,
  CircularProgress,
  DataGrid,
  DatePicker,
  GridActionsCellItem,
  GridRowEditStopReasons,
  GridRowModes,
  LinearProgress,
  Stack,
  useCallback<PERSON><PERSON>ed,
  useDataGridColumnHelper,
  useSearchTextField,
  type DateRange,
  type GridColDef,
  type GridEventListener,
  type GridRowHeightParams,
  type GridRowId,
  type GridRowModesModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { Controller, useForm, useWatch } from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { messages } from '@fleet-web/shared/forms/messages'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from '@fleet-web/util-functions/search-utils'
import { createZodObjPathGetter } from '@fleet-web/util-functions/zod-utils'

import DeleteSettingsDialog from '../../shared/DeleteSettingsDialog'
import { CustomPagination } from '../../shared/footer-dataGrid'
import MifleetContainer from '../../shared/mifleetContainer'
import type { SettingsTypes } from '../../shared/type'
import useMiFleetFiscalConfigurationQuery, {
  useCreateMiFleetFiscalConfigurationQuery,
  useDeleteMiFleetFiscalConfigurationQuery,
  useUpdateMiFleetFiscalConfigurationQuery,
} from './api/useMifleetFiscalConfiguration'
import FiscalConfigurationsDetails from './fiscal-configurations-details'

const NewRowTemporaryId = 'new_row'

const generateFormikSchema = z
  .object({
    begin_inforce_date: z.date({
      error: messages.required,
    }),
    end_inforce_date: z.date({
      error: messages.required,
    }),
    default_vat_deduction_percentage: z
      .string()
      .min(1, { message: messages.required })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
      .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
    default_max_depreciation_value: z
      .string()
      .min(1, { message: messages.required })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
      .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
    fiscal_configuration_id: z.string(),
    company_id: z.string(),
  })
  .superRefine((data, ctx) => {
    const { createPath } = createZodObjPathGetter(data)
    if (
      !!data.end_inforce_date &&
      !!data.begin_inforce_date &&
      data.begin_inforce_date > data.end_inforce_date
    ) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['begin_inforce_date']),
        message: ctIntl.formatMessage(
          { id: 'mifleet.capture.data.lower.dependent.date' },
          {
            values: {
              param1: ctIntl.formatMessage({ id: 'Start Date' }),
              param2: ctIntl.formatMessage({ id: 'End Date' }),
            },
          },
        ),
      })
      ctx.addIssue({
        code: 'custom',
        path: createPath(['end_inforce_date']),
        message: ctIntl.formatMessage(
          { id: 'mifleet.capture.data.higher.dependent.date' },
          {
            values: {
              param1: ctIntl.formatMessage({ id: 'End Date' }),
              param2: ctIntl.formatMessage({ id: 'Start Date' }),
            },
          },
        ),
      })
    }
  })

type ValidSchema = z.infer<typeof generateFormikSchema>

const initialValues: ValidSchema = {
  begin_inforce_date: new Date(),
  end_inforce_date: new Date(),
  default_vat_deduction_percentage: '',
  default_max_depreciation_value: '',
  fiscal_configuration_id: NewRowTemporaryId,
  company_id: '',
}
type Props = {
  fiscalConfigurations: Array<SettingsTypes.fiscalConfig>
  refetchFiscalConfigurations: () => void
}
const FiscalConfigurations = () => {
  const mifleetFiscalConfigurationQuery = useMiFleetFiscalConfigurationQuery()
  return match(mifleetFiscalConfigurationQuery)
    .with({ status: 'pending' }, () => (
      <Stack
        sx={{
          height: '100%',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data }) => (
      <FiscalConfigurationsComponent
        fiscalConfigurations={data}
        refetchFiscalConfigurations={mifleetFiscalConfigurationQuery.refetch}
      />
    ))
    .with({ status: 'error' }, () => null)
    .exhaustive()
}

const FiscalConfigurationsComponent = ({
  fiscalConfigurations = [],
  refetchFiscalConfigurations,
}: Props) => {
  const columnHelper = useDataGridColumnHelper<SettingsTypes.fiscalConfig>({
    filterMode: 'client',
  })
  const deleteFiscalConfiguration = useDeleteMiFleetFiscalConfigurationQuery()
  const updateFiscalConfiguration = useUpdateMiFleetFiscalConfigurationQuery()
  const createFiscalConfiguration = useCreateMiFleetFiscalConfigurationQuery()

  const searchProps = useSearchTextField('')
  const [expandedRow, setExpandedRow] = useState<Set<GridRowId>>(new Set())
  const [dateFilter, setDateFilter] = useState<DateRange<DateTime>>([null, null])
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})
  const [deleteItemModalOpen, setDeleteItemModalOpen] = useState<boolean>(false)

  const [fiscalConfigurationsList, setFiscalConfigurationsList] = useState<
    Array<SettingsTypes.fiscalConfig>
  >([])
  const [rowToBeDeleted, setRowToBeDeleted] = useState<
    SettingsTypes.fiscalConfig | undefined
  >(undefined)

  useEffect(() => {
    setFiscalConfigurationsList(fiscalConfigurations)
  }, [fiscalConfigurations])

  const {
    control,
    formState: { isValid, errors },
    reset,
    setValue: setFormValue,
    getValues,
    trigger,
  } = useForm<ValidSchema>({
    resolver: zodResolverV4(generateFormikSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialValues,
  })
  const watchedFiscalId = useWatch({ name: 'fiscal_configuration_id', control })
  const watchBeginDate = useWatch({ name: 'begin_inforce_date', control })
  const watchEndDate = useWatch({ name: 'end_inforce_date', control })

  const handleCreateItem = () => {
    // Check if it is already creating one line
    const createChecker = fiscalConfigurationsList.filter((o) =>
      o.fiscal_configuration_id.includes(NewRowTemporaryId),
    )
    if (createChecker.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'You should save the line first',
        }),
        { variant: 'error' },
      )
      return
    }
    setFiscalConfigurationsList((oldRows) => [
      {
        ...initialValues,
        is_deleted: 'f',
      },
      ...oldRows,
    ])
    setRowModesModel({
      [NewRowTemporaryId]: { mode: GridRowModes.Edit, fieldToFocus: 'name' },
    })
    reset(initialValues)
  }

  const handleResetItem = useCallback(
    (row: SettingsTypes.fiscalConfig) => {
      setFiscalConfigurationsList(
        fiscalConfigurationsList.filter(
          (items) => items.fiscal_configuration_id !== NewRowTemporaryId,
        ),
      )
      setRowModesModel((prevRowModesModel) => ({
        ...prevRowModesModel,
        [row.fiscal_configuration_id]: {
          mode: GridRowModes.View,
          ignoreModifications: true,
        },
      }))
    },
    [fiscalConfigurationsList],
  )

  const handleEditItem = useCallback(
    (row: SettingsTypes.fiscalConfig) => {
      setRowModesModel((oldModel) => ({
        ...R.mapValues(oldModel, (value, key) => {
          if (key === row.fiscal_configuration_id) {
            return value
          }
          return { mode: GridRowModes.View, ignoreModifications: true }
        }),
        [row.fiscal_configuration_id]: { mode: GridRowModes.Edit },
      }))
      reset({
        ...row,
        begin_inforce_date: new Date(row.begin_inforce_date),
        end_inforce_date: new Date(row.end_inforce_date),
      })
    },
    [reset],
  )
  const handleCancelItemModal = () => {
    setDeleteItemModalOpen(false)
    setRowToBeDeleted(undefined)
  }
  const handleDeleteItem = () => {
    if (rowToBeDeleted) {
      const { fiscal_configuration_id } = rowToBeDeleted
      deleteFiscalConfiguration.mutate(
        { fiscal_configuration_id },
        {
          onSuccess() {
            refetchFiscalConfigurations()
          },
        },
      )
      setDeleteItemModalOpen(false)
      setRowToBeDeleted(undefined)
    }
  }
  const onSubmit = useCallback(() => {
    const data = {
      ...getValues(),
      is_deleted: 'f',
    }
    if (watchedFiscalId.includes(NewRowTemporaryId)) {
      const { fiscal_configuration_id, ...restItems } = data
      createFiscalConfiguration.mutate(restItems, {
        onSuccess() {
          refetchFiscalConfigurations()
        },
      })
    } else {
      updateFiscalConfiguration.mutate(data, {
        onSuccess() {
          refetchFiscalConfigurations()
        },
      })
    }
    setRowModesModel({
      ...rowModesModel,
      [watchedFiscalId]: { mode: GridRowModes.View },
    })
  }, [
    createFiscalConfiguration,
    getValues,
    refetchFiscalConfigurations,
    rowModesModel,
    updateFiscalConfiguration,
    watchedFiscalId,
  ])

  const handleSubmitForm = useCallback(() => {
    const startDate = DateTime.fromJSDate(watchBeginDate).startOf('day')
    const endDate = DateTime.fromJSDate(watchEndDate).startOf('day')
    const fiscalItem = fiscalConfigurationsList.find(
      (item: SettingsTypes.fiscalConfig) => {
        const itemStartDate = DateTime.fromSQL(
          item.begin_inforce_date as string,
        ).startOf('day')
        const itemEndDate = DateTime.fromSQL(item.end_inforce_date as string).startOf(
          'day',
        )
        //(StartA <= EndB) and (EndA >= StartB)
        // Check date range overlap and editing mode
        const isOverlap = itemStartDate <= endDate && itemEndDate >= startDate
        const isNotInEditMode =
          rowModesModel[item.fiscal_configuration_id]?.mode !== GridRowModes.Edit
        return isOverlap && isNotInEditMode
      },
    )
    if (fiscalItem) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'Duplicated entry. Change your date.',
        }),
        { variant: 'error' },
      )
    } else {
      onSubmit()
    }
  }, [fiscalConfigurationsList, onSubmit, rowModesModel, watchBeginDate, watchEndDate])

  const renderFiscalCellDate = useCallback(
    (key: 'begin_inforce_date' | 'end_inforce_date') => {
      const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
        e.preventDefault()
        e.stopPropagation()
      }
      return (
        <Controller
          control={control}
          name={key}
          render={({ field, fieldState }) => (
            <DatePicker
              value={
                isNil(field.value) ? field.value : DateTime.fromJSDate(field.value)
              }
              onChange={(newValue) => {
                setFormValue(key, newValue ? newValue.toJSDate() : new Date(), {
                  shouldValidate: true,
                })
                if (key.includes('begin_inforce_date')) {
                  trigger(['end_inforce_date'], {
                    shouldFocus: true,
                  })
                } else {
                  trigger(['begin_inforce_date'], {
                    shouldFocus: true,
                  })
                }
              }}
              slotProps={{
                textField: {
                  error: !!fieldState.error,
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  required: true,
                  onKeyDownCapture: onKeyDown,
                  sx: {
                    '.MuiInputBase-root': {
                      backgroundColor: '#fff',
                    },
                  },
                },
              }}
            />
          )}
        />
      )
    },
    [control, setFormValue, trigger],
  )

  const renderFiscalCellNumber = useCallback(
    (key: 'default_max_depreciation_value' | 'default_vat_deduction_percentage') => (
      <TextFieldControlled
        ControllerProps={{
          name: key,
          control,
        }}
        sx={{ '.MuiOutlinedInput-input': { backgroundColor: '#fff' } }}
        onChange={(e) => {
          const newValue = e.target.value
          setFormValue(key, newValue, {
            shouldValidate: true,
          })
        }}
        onKeyDown={(event) => {
          // https://stackoverflow.com/questions/71055614/why-is-the-space-key-being-filtered-out-by-muis-text-field-component
          event.stopPropagation()
        }}
        required
      />
    ),
    [control, setFormValue],
  )

  const handleToggleItemModal = useCallback((row: SettingsTypes.fiscalConfig) => {
    setRowToBeDeleted(row)
    setDeleteItemModalOpen(true)
  }, [])

  const columns = useMemo(
    (): Array<GridColDef<SettingsTypes.fiscalConfig>> => [
      columnHelper.date({
        field: 'begin_inforce_date',
        headerName: ctIntl.formatMessage({ id: 'Start Date' }),
        minWidth: 140,
        flex: 1,
        editable: true,
        valueGetter: (_, row) =>
          row.begin_inforce_date ? new Date(row.begin_inforce_date) : null,
        renderEditCell: () => renderFiscalCellDate('begin_inforce_date'),
      }),
      columnHelper.date({
        headerName: ctIntl.formatMessage({ id: 'End Date' }),
        field: 'end_inforce_date',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueGetter: (_, row) =>
          row.end_inforce_date ? new Date(row.end_inforce_date) : null,
        renderEditCell: () => renderFiscalCellDate('end_inforce_date'),
      }),
      columnHelper.number((_, row) => Number(row.default_vat_deduction_percentage), {
        headerName: `${ctIntl.formatMessage({ id: 'Default VAT Deduction' })} (%)`,
        field: 'default_vat_deduction_percentage',
        minWidth: 140,
        flex: 1,
        editable: true,
        headerAlign: 'left',
        align: 'left',
        renderCell: ({ row }) => row.default_vat_deduction_percentage,
        renderEditCell: () =>
          renderFiscalCellNumber('default_vat_deduction_percentage'),
      }),
      columnHelper.number((_, row) => Number(row.default_max_depreciation_value), {
        headerName: `${ctIntl.formatMessage({
          id: 'Default Max Depreciation',
        })} (${ctIntl.formatMessage({ id: 'Years' })})`,
        field: 'default_max_depreciation_value',
        minWidth: 140,
        flex: 1,
        editable: true,
        headerAlign: 'left',
        align: 'left',
        renderCell: ({ row }) => row.default_max_depreciation_value,
        renderEditCell: () => renderFiscalCellNumber('default_max_depreciation_value'),
      }),
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const isInEditMode =
            rowModesModel[row.fiscal_configuration_id]?.mode === GridRowModes.Edit
          return isInEditMode
            ? [
                <GridActionsCellItem
                  key="saveEdit"
                  icon={<CheckOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Update' })}
                  disabled={!isValid}
                  onClick={() => handleSubmitForm()}
                  material={{ color: 'success' }}
                />,
                <GridActionsCellItem
                  key="cancelEdit"
                  icon={<CloseOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Close' })}
                  onClick={() => handleResetItem(row)}
                  material={{ color: 'error' }}
                />,
              ]
            : [
                <GridActionsCellItem
                  key="edit"
                  icon={<ModeEditOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => handleEditItem(row)}
                />,
                <GridActionsCellItem
                  key="delete"
                  icon={<DeleteOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={() => handleToggleItemModal(row)}
                />,
              ]
        },
      },
    ],
    [
      columnHelper,
      handleEditItem,
      handleResetItem,
      handleSubmitForm,
      handleToggleItemModal,
      isValid,
      renderFiscalCellDate,
      renderFiscalCellNumber,
      rowModesModel,
    ],
  )

  const tableData = useMemo(() => {
    const searchFilters: Filters<SettingsTypes.fiscalConfig> = {
      search: [
        (u) => u.default_vat_deduction_percentage?.toString(),
        (u) => u.default_max_depreciation_value?.toString(),
      ],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    const result = fiscalConfigurationsList.filter(
      (category: SettingsTypes.fiscalConfig) =>
        itemMatchesWithTextAndFilters(category, searchFilters),
    )
    const startDate = dateFilter[0]?.startOf('day')
    const endDate = dateFilter[1]?.endOf('day')

    return startDate && endDate
      ? result.filter(
          (i: SettingsTypes.fiscalConfig) =>
            startDate <= DateTime.fromJSDate(new Date(i.begin_inforce_date)) &&
            endDate >= DateTime.fromJSDate(new Date(i.end_inforce_date)),
        )
      : result
  }, [fiscalConfigurationsList, searchProps.value, dateFilter])

  const handleDetailPanelExpandedRowIdsChange = useCallback(
    (newIds: Set<GridRowId>) => {
      if (newIds.size > 1) {
        setExpandedRow(new Set([Array.from(newIds)[newIds.size - 1]]))
      } else {
        setExpandedRow(newIds)
      }
    },
    [],
  )
  const handleRowEditStop: GridEventListener<'rowEditStop'> = (params, event) => {
    if (params.reason === GridRowEditStopReasons.rowFocusOut) {
      // eslint-disable-next-line no-param-reassign
      event.defaultMuiPrevented = true
    }
  }

  const rowSelectionModel = useMemo(() => {
    const editedRow = Object.entries(rowModesModel).find(
      ([_, value]) => value.mode === 'edit',
    )

    return new Set(editedRow ? [editedRow[0]] : [])
  }, [rowModesModel])

  return (
    <MifleetContainer
      title="Fiscal Configurations"
      isSubComponent={true}
    >
      <Box sx={{ height: '100%', pt: '12px' }}>
        <UserDataGridWithSavedSettingsOnIDB
          sx={(theme) => ({
            '.MuiDataGrid-row.Mui-selected': {
              backgroundColor: rgba(
                theme.palette.primary.main,
                theme.palette.action.selectedOpacity,
              ),
              '.MuiDataGrid-cell': {
                alignItems: () =>
                  Object.keys(errors).length > 0 ? 'flex-start !important' : 'inherit',
                padding: '8px 10px',
                backgroundColor: 'transparent',
              },
            },
          })}
          getRowHeight={useCallbackBranded(
            ({ id }: GridRowHeightParams<SettingsTypes.fiscalConfig>) => {
              if (
                Object.keys(errors).length > 0 &&
                rowModesModel[id]?.mode === GridRowModes.Edit
              ) {
                return 75
              }
              return null
            },
            [errors, rowModesModel],
          )}
          Component={DataGrid}
          dataGridId="fiscalConfiguration"
          pagination
          getDetailPanelContent={({ row }) => (
            <FiscalConfigurationsDetails
              fiscalConfigurationId={row.fiscal_configuration_id}
            />
          )}
          onDetailPanelExpandedRowIdsChange={handleDetailPanelExpandedRowIdsChange}
          detailPanelExpandedRowIds={expandedRow}
          getDetailPanelHeight={() => 'auto'}
          rows={tableData}
          getRowId={useCallbackBranded(
            (row: SettingsTypes.fiscalConfig) => row.fiscal_configuration_id,
            [],
          )}
          rowSelectionModel={rowSelectionModel}
          columns={columns}
          editMode="row"
          rowModesModel={rowModesModel}
          onRowEditStop={handleRowEditStop}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            pagination: CustomPagination,
          }}
          slotProps={{
            filterPanel: {
              columnsSort: 'asc',
            },
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                filterButton: { show: true },
                settingsButton: { show: true },
                dateRangePicker: {
                  show: true,
                  props: {
                    value: dateFilter,
                    onChange: setDateFilter,
                  },
                },
              },
              extraContent: {
                right: (
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={handleCreateItem}
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Configuration',
                    })}
                  </Button>
                ),
              },
            }),
          }}
        />

        {deleteItemModalOpen && (
          <DeleteSettingsDialog
            onClose={handleCancelItemModal}
            onDelete={handleDeleteItem}
            labels={{
              titleLabel: 'Fiscal Configurations',
            }}
          />
        )}
      </Box>
    </MifleetContainer>
  )
}

export default FiscalConfigurations
