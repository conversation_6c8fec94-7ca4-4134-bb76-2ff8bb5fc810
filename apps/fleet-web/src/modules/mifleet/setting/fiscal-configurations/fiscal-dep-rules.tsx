// eslint-disable-next-line
/* eslint-disable unicorn/no-array-for-each */
import { useCallback, useEffect, useMemo, useState } from 'react'
import { toNumber } from 'lodash'
import {
  Autocomplete,
  Button,
  CircularProgress,
  DataGrid,
  GridActionsCellItem,
  GridRowEditStopReasons,
  GridRowModes,
  LinearProgress,
  Stack,
  TextField,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridEventListener,
  type GridRowHeightParams,
  type GridRowId,
  type GridRowModesModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined'
import { rgba } from 'polished'
import { Controller, useForm, useWatch } from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { getSettings } from '@fleet-web/duxs/user'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { messages } from '@fleet-web/shared/forms/messages'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DeleteSettingsDialog from '../../shared/DeleteSettingsDialog'
import type { SettingsTypes } from '../../shared/type'
import useMiFleetDepRulesQuery, {
  useCreateMiFleetDepRulesQuery,
  useDeleteMiFleetDepRulesQuery,
  useUpdateMiFleetDepRulesQuery,
} from './api/useMifleetDepreciationRules'

const NewRowTemporaryId = 'new_row'

const initialFormValues = {
  vehicle_fuel_type_id: '',
  vehicle_type_id: '',
  max_depreciation_value: '',
  fiscal_configuration_depreciation_rule_id: '',
}
const formikSchema = z.object({
  fiscal_configuration_depreciation_rule_id: z.string(),
  vehicle_fuel_type_id: z.string().min(1, { message: messages.required }),
  vehicle_type_id: z.string().min(1, { message: messages.required }),
  max_depreciation_value: z
    .string()
    .min(1, { message: messages.required })
    .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
    .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
})
type ValidSchema = z.infer<typeof formikSchema>

type Props = {
  fiscalConfigurationId: string
  mifleetFuelTypes: Array<SettingsTypes.fuelType>
  mifleetVehicleTypes: Array<SettingsTypes.vehicleType>
}
type FiscalDepRulesProps = {
  fiscalConfigurationId: string
  depRules: Array<SettingsTypes.depRules>
  vehicleTypes: Array<SettingsTypes.vehicleType>
  fuelTypes: Array<SettingsTypes.fuelType>
  refetchDepRulesQuery: () => void
}
const FiscalDepreciationRules = ({
  fiscalConfigurationId,
  mifleetFuelTypes,
  mifleetVehicleTypes,
}: Props) => {
  const fetchDepRulesQuery = useMiFleetDepRulesQuery({
    fiscalConfigurationId: fiscalConfigurationId,
  })

  return match(fetchDepRulesQuery)
    .with({ status: 'pending', fetchStatus: 'fetching' }, () => (
      <Stack
        sx={{
          height: '400px',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data }) => (
      <FiscalDepreciationRulesComponent
        depRules={data}
        refetchDepRulesQuery={fetchDepRulesQuery.refetch}
        fiscalConfigurationId={fiscalConfigurationId}
        fuelTypes={mifleetFuelTypes}
        vehicleTypes={mifleetVehicleTypes}
      />
    ))
    .with({ status: 'error' }, () => null)
    .otherwise(() => null)
}
const FiscalDepreciationRulesComponent = ({
  fiscalConfigurationId,
  depRules = [],
  vehicleTypes = [],
  fuelTypes = [],
  refetchDepRulesQuery,
}: FiscalDepRulesProps) => {
  const [depRulesList, setDepRulesList] = useState<Array<SettingsTypes.depRules>>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeItemToDelete, setActiveItemToDelete] = useState<
    SettingsTypes.depRules | undefined
  >(undefined)
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})
  const deleteFiscalDepRuleQuery = useDeleteMiFleetDepRulesQuery()
  const createFiscalDepRuleQuery = useCreateMiFleetDepRulesQuery()
  const updateFiscalDepRuleQuery = useUpdateMiFleetDepRulesQuery()
  const { currencySymbol } = useTypedSelector(getSettings)

  const columnHelper = useDataGridColumnHelper<SettingsTypes.depRules>({
    filterMode: 'client',
  })
  useEffect(() => {
    setDepRulesList(depRules)
  }, [depRules])

  const {
    control,
    formState: { errors, isValid },
    reset,
    setValue: setFormValue,
    getValues,
  } = useForm<ValidSchema>({
    resolver: zodResolverV4(formikSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialFormValues,
  })
  const watchFuelType = useWatch({ name: 'vehicle_fuel_type_id', control })
  const watchVehicleType = useWatch({ name: 'vehicle_type_id', control })
  const watchDepId = useWatch({
    name: 'fiscal_configuration_depreciation_rule_id',
    control,
  })
  const errorsLength = Object.keys(errors).length

  const getVehicleTypeOptions = useMemo(
    () =>
      vehicleTypes
        .map((row: SettingsTypes.vehicleType) => ({
          label: row.vehicle_type,
          value: row.vehicle_type_id,
        }))
        .sort((a: { label: string }, b: { label: string }) =>
          a.label.localeCompare(b.label),
        ),
    [vehicleTypes],
  )
  const getFuelTypeOptions = useMemo(
    () =>
      fuelTypes
        .map((row: SettingsTypes.fuelType) => ({
          label: row.fuel_type,
          value: row.vehicle_fuel_type_id,
        }))
        .sort((a: { label: string }, b: { label: string }) =>
          a.label.localeCompare(b.label),
        ),
    [fuelTypes],
  )
  const handleCreateItem = () => {
    // Check if it is already creating one line
    const createChecker = depRulesList.filter((o) =>
      o.fiscal_configuration_depreciation_rule_id.includes(NewRowTemporaryId),
    )
    if (createChecker.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'You should save the line first!' }),
        { variant: 'error' },
      )
      return
    }

    const defaultObj = {
      ...initialFormValues,
      fiscal_configuration_depreciation_rule_id: NewRowTemporaryId,
      fiscal_configuration_id: fiscalConfigurationId,
      vehicle_fuel_type: '',
      vehicle_type: '',
    }
    setDepRulesList((oldRows) => [
      {
        ...defaultObj,
      },
      ...oldRows,
    ])
    setRowModesModel({
      [NewRowTemporaryId]: { mode: GridRowModes.Edit, fieldToFocus: 'name' },
    })
    reset({
      ...initialFormValues,
      fiscal_configuration_depreciation_rule_id: NewRowTemporaryId,
    })
  }

  const handleResetItem = useCallback(
    (row: SettingsTypes.depRules) => {
      setDepRulesList(
        depRulesList.filter(
          (item) =>
            item.fiscal_configuration_depreciation_rule_id !== NewRowTemporaryId,
        ),
      )
      setRowModesModel((prevRowModesModel) => ({
        ...prevRowModesModel,
        [row.fiscal_configuration_depreciation_rule_id]: {
          mode: GridRowModes.View,
          ignoreModifications: true,
        },
      }))
      reset(initialFormValues)
    },
    [depRulesList, reset],
  )

  const handleEditItem = useCallback(
    (row: SettingsTypes.depRules) => {
      if (watchDepId.includes(NewRowTemporaryId)) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'mifleet.settings.save.form.first' }),
          { variant: 'error' },
        )
        return
      }
      setRowModesModel((oldModel) => ({
        ...R.mapValues(oldModel, (value, key) => {
          if (key === row.fiscal_configuration_depreciation_rule_id) {
            return value
          }
          return { mode: GridRowModes.View, ignoreModifications: true }
        }),
        [row.fiscal_configuration_depreciation_rule_id]: { mode: GridRowModes.Edit },
      }))
      reset(row)
    },
    [reset, watchDepId],
  )

  const handleSaveItem = useCallback(() => {
    const isdepRulesExit = depRulesList.filter(
      (item: SettingsTypes.depRules) =>
        item.vehicle_fuel_type_id === watchFuelType &&
        item.vehicle_type_id === watchVehicleType &&
        rowModesModel[item.fiscal_configuration_depreciation_rule_id]?.mode !==
          GridRowModes.Edit,
    )
    if (isdepRulesExit.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Duplicated entry. Change your data.' }),
        { variant: 'error' },
      )
      return
    }
    if (watchDepId.includes(NewRowTemporaryId)) {
      const data = {
        ...getValues(),
        fiscal_configuration_id: fiscalConfigurationId,
        vehicle_fuel_type: '',
        vehicle_type: '',
      }
      createFiscalDepRuleQuery.mutate(data, {
        onSuccess() {
          refetchDepRulesQuery()
        },
      })
    } else {
      updateFiscalDepRuleQuery.mutate(getValues() as SettingsTypes.depRules, {
        onSuccess() {
          refetchDepRulesQuery()
        },
      })
    }

    setRowModesModel({
      ...rowModesModel,
      [watchDepId]: { mode: GridRowModes.View },
    })
    reset(initialFormValues)
  }, [
    createFiscalDepRuleQuery,
    depRulesList,
    fiscalConfigurationId,
    getValues,
    refetchDepRulesQuery,
    reset,
    rowModesModel,
    updateFiscalDepRuleQuery,
    watchDepId,
    watchFuelType,
    watchVehicleType,
  ])
  const openDeletionModal = (row: SettingsTypes.depRules) => {
    setActiveItemToDelete(row)
    setIsModalOpen(true)
  }

  const handleDeleteItem = () => {
    if (activeItemToDelete) {
      const { fiscal_configuration_depreciation_rule_id, fiscal_configuration_id } =
        activeItemToDelete
      deleteFiscalDepRuleQuery.mutate(
        {
          fiscal_configuration_depreciation_rule_id,
          fiscal_configuration_id,
        },
        {
          onSuccess() {
            refetchDepRulesQuery()
          },
        },
      )
      setActiveItemToDelete(undefined)
      setIsModalOpen(false)
    }
  }

  const renderFiscalCellNumber = useCallback(
    (key: 'max_depreciation_value') => (
      <TextFieldControlled
        fullWidth
        ControllerProps={{
          name: key,
          control,
        }}
        sx={{
          input: {
            backgroundColor: '#fff',
          },
        }}
        required
        onKeyDown={(event) => {
          // https://stackoverflow.com/questions/71055614/why-is-the-space-key-being-filtered-out-by-muis-text-field-component
          event.stopPropagation()
        }}
      />
    ),
    [control],
  )

  const renderDropdownCell = useCallback(
    (key: 'vehicle_fuel_type_id' | 'vehicle_type_id') => (
      <Controller
        control={control}
        name={key}
        render={({ field, fieldState }) => (
          <Autocomplete
            fullWidth
            options={
              key === 'vehicle_fuel_type_id'
                ? getFuelTypeOptions || []
                : getVehicleTypeOptions || []
            }
            onChange={(_, newValue) => {
              setFormValue(field.name, newValue?.value || '', {
                shouldValidate: true,
              })
            }}
            value={
              (key === 'vehicle_fuel_type_id'
                ? getFuelTypeOptions.find(
                    (type: { value: string }) => type.value === field.value,
                  )
                : getVehicleTypeOptions.find(
                    (type: { value: string }) => type.value === field.value,
                  )) || null
            }
            renderInput={(params) => (
              <TextField
                sx={{
                  '.MuiOutlinedInput-root': {
                    backgroundColor: '#fff',
                  },
                }}
                {...params}
                required
                helperText={ctIntl.formatMessage({
                  id: fieldState.error?.message ?? '',
                })}
                error={!!fieldState.error}
              />
            )}
          />
        )}
      />
    ),
    [control, getFuelTypeOptions, getVehicleTypeOptions, setFormValue],
  )
  const columns = useMemo(
    (): Array<GridColDef<SettingsTypes.depRules>> => [
      columnHelper.singleSelect((_, row) => row.vehicle_fuel_type_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Fuel Type' }),
        field: 'vehicle_fuel_type_id',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueOptions: getFuelTypeOptions,
        renderEditCell: () => renderDropdownCell('vehicle_fuel_type_id'),
      }),
      columnHelper.singleSelect((_, row) => row.vehicle_type_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Type' }),
        field: 'vehicle_type_id',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueOptions: getVehicleTypeOptions,
        renderEditCell: () => renderDropdownCell('vehicle_type_id'),
      }),

      {
        headerName: `${ctIntl.formatMessage({
          id: 'MAX VAT Deduction',
        })} (${currencySymbol})`,
        field: 'max_depreciation_value',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueGetter: (_, row) => Number(row.max_depreciation_value),
        renderEditCell: () => renderFiscalCellNumber('max_depreciation_value'),
      },
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const isInEditMode =
            rowModesModel[row.fiscal_configuration_depreciation_rule_id]?.mode ===
            GridRowModes.Edit
          return isInEditMode
            ? [
                <GridActionsCellItem
                  key="saveEdit"
                  icon={<CheckOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Update' })}
                  onClick={handleSaveItem}
                  material={{ color: 'success' }}
                  disabled={!isValid}
                />,
                <GridActionsCellItem
                  key="cancelEdit"
                  icon={<CloseOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Close' })}
                  onClick={() => handleResetItem(row)}
                  material={{ color: 'error' }}
                />,
              ]
            : [
                <GridActionsCellItem
                  key="edit"
                  icon={<ModeEditOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => handleEditItem(row)}
                />,
                <GridActionsCellItem
                  key="delete"
                  icon={<DeleteOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={() => openDeletionModal(row)}
                />,
              ]
        },
      },
    ],
    [
      columnHelper,
      currencySymbol,
      getFuelTypeOptions,
      getVehicleTypeOptions,
      handleEditItem,
      handleResetItem,
      handleSaveItem,
      isValid,
      renderDropdownCell,
      renderFiscalCellNumber,
      rowModesModel,
    ],
  )
  const handleRowEditStop: GridEventListener<'rowEditStop'> = (params, event) => {
    if (params.reason === GridRowEditStopReasons.rowFocusOut) {
      // eslint-disable-next-line no-param-reassign
      event.defaultMuiPrevented = true
    }
  }

  const rowSelectionModel = useMemo((): ReadonlySet<GridRowId> => {
    const editedRow = Object.entries(rowModesModel).find(
      ([_, value]) => value.mode === 'edit',
    )
    return editedRow ? new Set([editedRow[0]]) : new Set()
  }, [rowModesModel])

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        sx={(theme) => ({
          '.MuiDataGrid-virtualScroller': {
            minHeight: '400px',
            height: '100%',
          },
          '.MuiDataGrid-row.Mui-selected': {
            backgroundColor: rgba(
              theme.palette.primary.main,
              theme.palette.action.selectedOpacity,
            ),
            '.MuiDataGrid-cell': {
              alignItems: () =>
                errorsLength > 0 ? 'flex-start !important' : 'inherit',
              padding: '8px 10px',
            },
          },
        })}
        getRowHeight={useCallbackBranded(
          ({ id }: GridRowHeightParams<SettingsTypes.depRules>) => {
            if (errorsLength > 0 && id === watchDepId) {
              return 75
            }
            return null
          },
          [errorsLength, watchDepId],
        )}
        Component={DataGrid}
        dataGridId="fiscalDepRules"
        disableRowSelectionOnClick
        pagination
        rows={depRulesList}
        getRowId={useCallbackBranded(
          (row: SettingsTypes.depRules) =>
            row.fiscal_configuration_depreciation_rule_id,
          [],
        )}
        columns={columns}
        autoPageSize
        rowSelectionModel={rowSelectionModel}
        editMode="row"
        rowModesModel={rowModesModel}
        onRowEditStop={handleRowEditStop}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          filterPanel: {
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <Button
                  color="primary"
                  variant="outlined"
                  startIcon={<AddIcon />}
                  size="small"
                  onClick={handleCreateItem}
                >
                  {ctIntl.formatMessage({
                    id: 'Add New',
                  })}
                </Button>
              ),
            },
          }),
        }}
      />

      {isModalOpen && (
        <DeleteSettingsDialog
          onClose={() => {
            setActiveItemToDelete(undefined)
            setIsModalOpen(false)
          }}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Depreciation Rules',
          }}
        />
      )}
    </>
  )
}

export default FiscalDepreciationRules
