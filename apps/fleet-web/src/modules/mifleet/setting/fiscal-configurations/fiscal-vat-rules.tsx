// eslint-disable-next-line
/* eslint-disable unicorn/no-array-for-each */
import { useCallback, useEffect, useMemo, useState } from 'react'
import { toNumber } from 'lodash'
import {
  Autocomplete,
  Button,
  CircularProgress,
  DataGrid,
  GridActionsCellItem,
  GridRowEditStopReasons,
  GridRowModes,
  LinearProgress,
  Stack,
  TextField,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridEventListener,
  type GridRowHeightParams,
  type GridRowId,
  type GridRowModesModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined'
import { rgba } from 'polished'
import { Controller, useForm, useWatch } from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { messages } from '@fleet-web/shared/forms/messages'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DeleteSettingsDialog from '../../shared/DeleteSettingsDialog'
import type { SettingsTypes } from '../../shared/type'
import useMiFleetVatRulesQuery, {
  useCreateMiFleetVatRulesQuery,
  useDeleteMiFleetVatRulesQuery,
  useUpdateMiFleetVatRulesQuery,
} from './api/useMifleetTaxRules'

const NewRowTemporaryId = 'new_row'

const initialFormValues = {
  vehicle_fuel_type_id: '',
  vehicle_type_id: '',
  vat_deduction_percentage: '',
  fiscal_configuration_vat_rule_id: '',
}
const schema = z.object({
  fiscal_configuration_vat_rule_id: z.string(),
  vehicle_fuel_type_id: z.string().min(1, { message: messages.required }),
  vehicle_type_id: z.string().min(1, { message: messages.required }),
  vat_deduction_percentage: z
    .string()
    .min(1, { message: messages.required })
    .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
    .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
})

type ValidSchema = z.infer<typeof schema>
type Props = {
  fiscalConfigurationId: string
  mifleetFuelTypes: Array<SettingsTypes.fuelType>
  mifleetVehicleTypes: Array<SettingsTypes.vehicleType>
}
type FiscalVatRulesProps = {
  refetchVatRulesQuery: () => void
  fiscalConfigurationId: string
  vatRules: Array<SettingsTypes.vatRules>
  vehicleTypes: Array<SettingsTypes.vehicleType>
  fuelTypes: Array<SettingsTypes.fuelType>
}
const FiscalVatRules = ({
  fiscalConfigurationId,
  mifleetFuelTypes,
  mifleetVehicleTypes,
}: Props) => {
  const fetchVatRulesQuery = useMiFleetVatRulesQuery({
    fiscalConfigurationId: fiscalConfigurationId,
  })
  return match(fetchVatRulesQuery)
    .with({ status: 'pending', fetchStatus: 'fetching' }, () => (
      <Stack
        sx={{
          height: '400px',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data }) => (
      <FiscalVatRulesComponent
        vatRules={data}
        refetchVatRulesQuery={fetchVatRulesQuery.refetch}
        fiscalConfigurationId={fiscalConfigurationId}
        fuelTypes={mifleetFuelTypes}
        vehicleTypes={mifleetVehicleTypes}
      />
    ))
    .with({ status: 'error' }, () => null)
    .otherwise(() => null)
}

const FiscalVatRulesComponent = ({
  refetchVatRulesQuery,
  fiscalConfigurationId,
  vatRules = [],
  vehicleTypes = [],
  fuelTypes = [],
}: FiscalVatRulesProps) => {
  const [vatRulesList, setVatRulesList] = useState<Array<SettingsTypes.vatRules>>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeItemToDelete, setActiveItemToDelete] = useState<
    SettingsTypes.vatRules | undefined
  >(undefined)
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})
  const deleteFiscalVatRuleQuery = useDeleteMiFleetVatRulesQuery()
  const createFiscalVatRuleQuery = useCreateMiFleetVatRulesQuery()
  const updateFiscalVatRuleQuery = useUpdateMiFleetVatRulesQuery()

  const columnHelper = useDataGridColumnHelper<SettingsTypes.vatRules>({
    filterMode: 'client',
  })
  const {
    control,
    formState: { errors, isValid },
    reset,
    setValue: setFormValue,
    getValues,
  } = useForm<ValidSchema>({
    resolver: zodResolverV4(schema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialFormValues,
  })

  const watchFuelType = useWatch({ name: 'vehicle_fuel_type_id', control })
  const watchVehicleType = useWatch({ name: 'vehicle_type_id', control })
  const watchVatId = useWatch({ name: 'fiscal_configuration_vat_rule_id', control })
  const errorsLength = Object.keys(errors).length

  useEffect(() => {
    setVatRulesList(vatRules)
  }, [vatRules])

  const getVehicleTypeOptions = useMemo(
    () =>
      vehicleTypes
        .map((row: SettingsTypes.vehicleType) => ({
          label: row.vehicle_type,
          value: row.vehicle_type_id,
        }))
        .sort((a: { label: string }, b: { label: string }) =>
          a.label.localeCompare(b.label),
        ),
    [vehicleTypes],
  )
  const getFuelTypeOptions = useMemo(
    () =>
      fuelTypes
        .map((row: SettingsTypes.fuelType) => ({
          label: row.fuel_type,
          value: row.vehicle_fuel_type_id,
        }))
        .sort((a: { label: string }, b: { label: string }) =>
          a.label.localeCompare(b.label),
        ),
    [fuelTypes],
  )
  const handleCreateItem = () => {
    // Check if it is already creating one line
    const createChecker = vatRulesList.filter((o) =>
      o.fiscal_configuration_vat_rule_id.includes(NewRowTemporaryId),
    )
    if (createChecker.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'You should save the line first!' }),
        { variant: 'error' },
      )
      return
    }

    const defaultObj = {
      ...initialFormValues,
      fiscal_configuration_vat_rule_id: NewRowTemporaryId,
      fiscal_configuration_id: fiscalConfigurationId,
      vehicle_fuel_type: '',
      vehicle_type: '',
    }
    setVatRulesList((oldRows) => [
      {
        ...defaultObj,
      },
      ...oldRows,
    ])
    setRowModesModel({
      [NewRowTemporaryId]: { mode: GridRowModes.Edit, fieldToFocus: 'name' },
    })
    reset({ ...initialFormValues, fiscal_configuration_vat_rule_id: NewRowTemporaryId })
  }

  const handleResetItem = useCallback(
    (row: SettingsTypes.vatRules) => {
      setVatRulesList(
        vatRulesList.filter(
          (item) => item.fiscal_configuration_vat_rule_id !== NewRowTemporaryId,
        ),
      )
      setRowModesModel((prevRowModesModel) => ({
        ...prevRowModesModel,
        [row.fiscal_configuration_vat_rule_id]: {
          mode: GridRowModes.View,
          ignoreModifications: true,
        },
      }))
      reset(initialFormValues)
    },
    [reset, vatRulesList],
  )
  const handleEditItem = useCallback(
    (row: SettingsTypes.vatRules) => {
      if (watchVatId.includes(NewRowTemporaryId)) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'mifleet.settings.save.form.first' }),
          { variant: 'error' },
        )
        return
      }
      setRowModesModel((oldModel) => ({
        ...R.mapValues(oldModel, (value, key) => {
          if (key === row.fiscal_configuration_vat_rule_id) {
            return value
          }
          return { mode: GridRowModes.View, ignoreModifications: true }
        }),
        [row.fiscal_configuration_vat_rule_id]: { mode: GridRowModes.Edit },
      }))
      reset(row)
    },
    [reset, watchVatId],
  )
  const handleSaveItem = useCallback(() => {
    const isVatRulesExist = vatRulesList.filter(
      (item: SettingsTypes.vatRules) =>
        item.vehicle_fuel_type_id === watchFuelType &&
        item.vehicle_type_id === watchVehicleType &&
        rowModesModel[item.fiscal_configuration_vat_rule_id]?.mode !==
          GridRowModes.Edit,
    )
    if (isVatRulesExist.length > 0) {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Duplicated entry. Change your data.' }),
        { variant: 'error' },
      )
      return
    }

    if (watchVatId.includes(NewRowTemporaryId)) {
      const data = {
        ...getValues(),
        fiscal_configuration_id: fiscalConfigurationId,
        vehicle_fuel_type: '',
        vehicle_type: '',
      }
      createFiscalVatRuleQuery.mutate(data, {
        onSuccess() {
          refetchVatRulesQuery()
        },
      })
    } else {
      updateFiscalVatRuleQuery.mutate(getValues() as SettingsTypes.vatRules, {
        onSuccess() {
          refetchVatRulesQuery()
        },
      })
    }

    setRowModesModel({
      ...rowModesModel,
      [watchVatId]: { mode: GridRowModes.View },
    })
    reset(initialFormValues)
  }, [
    createFiscalVatRuleQuery,
    fiscalConfigurationId,
    getValues,
    refetchVatRulesQuery,
    reset,
    rowModesModel,
    updateFiscalVatRuleQuery,
    vatRulesList,
    watchFuelType,
    watchVatId,
    watchVehicleType,
  ])
  const handleDeleteModal = (row: SettingsTypes.vatRules) => {
    setActiveItemToDelete(row)
    setIsModalOpen(true)
  }

  const handleDeleteItem = () => {
    const { fiscal_configuration_vat_rule_id, fiscal_configuration_id } =
      activeItemToDelete as SettingsTypes.vatRules
    deleteFiscalVatRuleQuery.mutate(
      {
        fiscal_configuration_vat_rule_id,
        fiscal_configuration_id,
      },
      {
        onSuccess() {
          refetchVatRulesQuery()
        },
      },
    )
    setActiveItemToDelete(undefined)
    setIsModalOpen(false)
  }

  const renderFiscalCellNumber = useCallback(
    (key: 'vat_deduction_percentage') => (
      <TextFieldControlled
        fullWidth
        ControllerProps={{
          name: key,
          control,
        }}
        sx={{
          input: {
            backgroundColor: '#fff',
          },
        }}
        onKeyDown={(event) => {
          // https://stackoverflow.com/questions/71055614/why-is-the-space-key-being-filtered-out-by-muis-text-field-component
          event.stopPropagation()
        }}
        required
      />
    ),
    [control],
  )

  const renderDropdownCell = useCallback(
    (key: 'vehicle_fuel_type_id' | 'vehicle_type_id') => (
      <Controller
        control={control}
        name={key}
        render={({ field, fieldState }) => (
          <Autocomplete
            fullWidth
            options={
              key === 'vehicle_fuel_type_id'
                ? getFuelTypeOptions || []
                : getVehicleTypeOptions || []
            }
            onChange={(_, newValue) => {
              setFormValue(field.name, newValue?.value || '', {
                shouldValidate: true,
              })
            }}
            value={
              (key === 'vehicle_fuel_type_id'
                ? getFuelTypeOptions.find(
                    (type: { value: string }) => type.value === field.value,
                  )
                : getVehicleTypeOptions.find(
                    (type: { value: string }) => type.value === field.value,
                  )) || null
            }
            renderInput={(params) => (
              <TextField
                sx={{
                  '.MuiOutlinedInput-root': {
                    backgroundColor: '#fff',
                  },
                }}
                {...params}
                required
                helperText={ctIntl.formatMessage({
                  id: fieldState.error?.message ?? '',
                })}
                error={!!fieldState.error}
              />
            )}
          />
        )}
      />
    ),
    [control, getFuelTypeOptions, getVehicleTypeOptions, setFormValue],
  )

  const columns = useMemo(
    (): Array<GridColDef<SettingsTypes.vatRules>> => [
      columnHelper.singleSelect((_, row) => row.vehicle_fuel_type_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Fuel Type' }),
        field: 'vehicle_fuel_type_id',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueOptions: getFuelTypeOptions,
        renderEditCell: () => renderDropdownCell('vehicle_fuel_type_id'),
      }),
      columnHelper.singleSelect((_, row) => row.vehicle_type_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Type' }),
        field: 'vehicle_type_id',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueOptions: getVehicleTypeOptions,
        renderEditCell: () => renderDropdownCell('vehicle_type_id'),
      }),

      {
        headerName: `${ctIntl.formatMessage({ id: 'TAX Deduction' })} (%)`,
        field: 'vat_deduction_percentage',
        minWidth: 140,
        flex: 1,
        editable: true,
        valueGetter: (_, row) => Number(row.vat_deduction_percentage),
        renderEditCell: () => renderFiscalCellNumber('vat_deduction_percentage'),
      },
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const isInEditMode =
            rowModesModel[row.fiscal_configuration_vat_rule_id]?.mode ===
            GridRowModes.Edit
          return isInEditMode
            ? [
                <GridActionsCellItem
                  key="saveEdit"
                  icon={<CheckOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Update' })}
                  onClick={handleSaveItem}
                  disabled={!isValid}
                  material={{ color: 'success' }}
                />,
                <GridActionsCellItem
                  key="cancelEdit"
                  icon={<CloseOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Close' })}
                  onClick={() => handleResetItem(row)}
                  material={{ color: 'error' }}
                />,
              ]
            : [
                <GridActionsCellItem
                  key="edit"
                  icon={<ModeEditOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={() => handleEditItem(row)}
                />,
                <GridActionsCellItem
                  key="delete"
                  icon={<DeleteOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={() => handleDeleteModal(row)}
                />,
              ]
        },
      },
    ],

    [
      columnHelper,
      getFuelTypeOptions,
      getVehicleTypeOptions,
      handleEditItem,
      handleResetItem,
      handleSaveItem,
      isValid,
      renderDropdownCell,
      renderFiscalCellNumber,
      rowModesModel,
    ],
  )
  const handleRowEditStop: GridEventListener<'rowEditStop'> = (params, event) => {
    if (params.reason === GridRowEditStopReasons.rowFocusOut) {
      // eslint-disable-next-line no-param-reassign
      event.defaultMuiPrevented = true
    }
  }

  const rowSelectionModel = useMemo((): ReadonlySet<GridRowId> => {
    const editedRow = Object.entries(rowModesModel).find(
      ([_, value]) => value.mode === 'edit',
    )

    return editedRow ? new Set([editedRow[0]]) : new Set()
  }, [rowModesModel])

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        sx={(theme) => ({
          '.MuiDataGrid-virtualScroller': {
            minHeight: '400px',
            height: '100%',
          },
          '.MuiDataGrid-row.Mui-selected': {
            backgroundColor: rgba(
              theme.palette.primary.main,
              theme.palette.action.selectedOpacity,
            ),
            '.MuiDataGrid-cell': {
              alignItems: () =>
                errorsLength > 0 ? 'flex-start !important' : 'inherit',
              padding: '8px 10px',
            },
          },
        })}
        getRowHeight={useCallbackBranded(
          ({ id }: GridRowHeightParams<SettingsTypes.vatRules>) => {
            if (errorsLength > 0 && watchVatId === id) {
              return 75
            }
            return null
          },
          [errorsLength, watchVatId],
        )}
        Component={DataGrid}
        dataGridId="fiscalVatRules"
        disableRowSelectionOnClick
        pagination
        rows={vatRulesList}
        getRowId={useCallbackBranded(
          (row: SettingsTypes.vatRules) => row.fiscal_configuration_vat_rule_id,
          [],
        )}
        columns={columns}
        autoPageSize
        rowSelectionModel={rowSelectionModel}
        editMode="row"
        rowModesModel={rowModesModel}
        onRowEditStop={handleRowEditStop}
        slots={{ toolbar: KarooToolbar, loadingOverlay: LinearProgress }}
        slotProps={{
          filterPanel: {
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              settingsButton: { show: true },
            },
            extraContent: {
              right: (
                <Button
                  color="primary"
                  size="small"
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleCreateItem}
                >
                  {ctIntl.formatMessage({
                    id: 'Add New',
                  })}
                </Button>
              ),
            },
          }),
        }}
      />

      {isModalOpen && (
        <DeleteSettingsDialog
          onClose={() => {
            setActiveItemToDelete(undefined)
            setIsModalOpen(false)
          }}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'TAX Rules',
          }}
        />
      )}
    </>
  )
}

export default FiscalVatRules
