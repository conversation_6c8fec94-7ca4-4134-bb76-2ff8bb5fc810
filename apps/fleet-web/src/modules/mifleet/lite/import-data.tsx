import { useEffect, useState } from 'react'
import { uniqueId } from 'lodash'
import AccountTree from '@mui/icons-material/AccountTree'
import FileCopy from '@mui/icons-material/FileCopy'
import type { History } from 'history'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'
import type { RouteComponentProps } from 'react-router-dom'
import styled from 'styled-components'

import Importer from '@fleet-web/modules/importer/importer'
import mifleetCreateLine from '@fleet-web/modules/mifleet/components/documents/line-create-form'
import {
  fetchDocumentArrayTypes,
  resetItem,
  setItem,
} from '@fleet-web/modules/mifleet/DocumentsEdit/slice'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import type { FixMeAny } from '@fleet-web/types'
import { Checkbox } from '@fleet-web/util-components'

import MenuLite, { type Option } from '../shared/shared-menu'

const DocumentLineCreate: FixMeAny = mifleetCreateLine

type NavigationType = 'import' | 'capture'

type Props = {
  history: History<{
    forceNavigation?: NavigationType
    forceMenu?: string
    activeMenu?: string
  }>
} & RouteComponentProps &
  typeof actionCreators

const LiteImportData = ({ history, setItem, fetchDocumentArrayTypes }: Props) => {
  const [activeSection, setActiveSection] = useState<NavigationType>(
    (history?.location?.state?.forceNavigation as NavigationType) || 'import',
  )

  const [tabsActiveMenu, setTabsActiveMenu] = useState<string>(
    history?.location?.state?.forceMenu || 'fuel',
  )

  const [isSelectedMenu, setIsSelectedMenu] = useState(false) //MIFLEET STATE

  const defaultDoc = {
    cancelled_reason: null,
    documentLines: [],
    document_number: uniqueId(),
    document_status: 'DOCUMENT_STATUS_PENDING',
    document_status_id: '1',
    document_type: 'DOCUMENT_TYPE_INVOICE',
    document_type_id: '2',
  }

  useEffect(() => {
    resetItem()
  }, [])

  useEffect(() => {
    setIsSelectedMenu(false)
  }, [isSelectedMenu])

  const getSelectedConceptBySelection = () => {
    switch (tabsActiveMenu) {
      case 'fuel': {
        return '5'
      }
      case 'tolls': {
        return '13'
      }
      case 'fines': {
        return '4'
      }
      case 'tyres': {
        return '14'
      }
      case 'maintenance': {
        return '8'
      }
      case 'accidents': {
        return '6'
      }
      default: {
        return undefined
      }
    }
  }

  const extraTabs: Array<Option> = [
    {
      name: activeSection === 'import' ? 'All Imports' : 'MISC',
      icon: <FileCopy />,
      id: activeSection === 'import' ? 'miscImport' : 'miscConcept',
    },
  ]

  if (activeSection === 'import') {
    extraTabs.unshift({
      name: 'Multi Cost',
      icon: <AccountTree />,
      id: 'multiImport',
    })
  }

  return (
    <PageWrapper>
      <CheckMenuWrapper>
        <StyledCheckBox
          checked={activeSection === 'import'}
          value={activeSection === 'import'}
          label={'Import File'}
          onChange={() => {
            setActiveSection('import')
          }}
        />
        <StyledCheckBox
          checked={activeSection === 'capture'}
          value={activeSection === 'capture'}
          label={'Capture Data'}
          onChange={() => {
            fetchDocumentArrayTypes()
            setItem(defaultDoc)
            setActiveSection('capture')
          }}
        />
      </CheckMenuWrapper>
      <MenuLite
        tabIdsToHide={
          activeSection === 'import' ? ['dashboard', 'tyres'] : ['dashboard']
        }
        onMenuClick={(menu) => {
          setTabsActiveMenu(menu.id)
          setIsSelectedMenu(true)
        }}
        activeMenuId={tabsActiveMenu}
        extraTabs={extraTabs}
      />
      {activeSection === 'import' && (
        <Importer
          mifleetLiteDefaultImport={tabsActiveMenu}
          isSelectedMenu={isSelectedMenu}
        />
      )}
      {activeSection === 'capture' && (
        <DocumentLineCreate
          preSelectedConceptTypeId={getSelectedConceptBySelection()}
        />
      )}
    </PageWrapper>
  )
}

const actionCreators = {
  setItem,
  fetchDocumentArrayTypes,
}

export default withRouter(connect(null, actionCreators)(LiteImportData))

const PageWrapper = styled.div`
  padding: ${spacing[2]} ${spacing[4]} 0;
`

const CheckMenuWrapper = styled.div`
  display: flex;
`

const StyledCheckBox = styled(Checkbox)<{ checked: boolean }>`
  margin: 20px 20px 20px 0;
  align-items: center;

  label {
    background: ${(props) =>
      props.checked
        ? props.theme.colors.styleActiveButtonsColour
        : 'transparent'} !important;
    background-color: ${(props) =>
      props.checked
        ? props.theme.colors.styleActiveButtonsColour
        : 'transparent'} !important;
    width: 26px;
    height: 26px;
    border-radius: 13px;
    top: 0;

    &:after {
      content: none !important;
    }
  }
`
