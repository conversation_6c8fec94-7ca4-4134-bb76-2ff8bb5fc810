import type { VehicleId } from '@fleet-web/api/types'
import type { ContractStatus } from '@fleet-web/modules/mifleet/components/contracts/shared/types'
import type { FixMeAny } from '@fleet-web/types'

import type { DocumentStatus } from '../api/costInput/shared/types'

export type VehicleDetailsCostTable = {
  list: { [key: string]: VehicleDetailsCostTable }
} & Record<string, any>

export type VehicleDetailsMonthlyCosts = {
  table: { [key: string]: VehicleDetailsCostTable }
  total: { [key: string]: VehicleDetailsCostTable }
}

export type VehicleDetailsOil = OperationalVehicle & {
  oil_id: number
  vehicle_oil_type_id: number
  vehicle_oil_type: string
  fill_in_date: string | null
  validation_date: string | null
}

export type VehicleDetailsCostsPerKilometer = {
  table: { [key: string]: VehicleDetailsCostTable }
  total: { [key: string]: VehicleDetailsCostTable }
}

export type VehicleDetailsAccessory = {
  accessory_id: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  plate: string
  total_value: string
  vehicle_accessory_type: string
  vehicle_accessory_type_id: string
  vehicle_id: string
}

export type VehicleDetailsAccessoryType = {
  vehicle_accessory_type_id: string
  accessory_type: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsAssignedList = {
  assigned_id: number | null
  driver_id: string
  driver_name: string
  from_date: string
  to_date: string | null
}

export type VehicleDetailsBreakdown = {
  breakdown_id: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  driver_id: string
  driver_name: string
  plate: string
  total_value: string
  towing_delivery_date: string
  towing_description: string
  towing_start_date: string
  vehicle_breakdown_type: string
  vehicle_breakdown_type_id: string
  vehicle_id: string
}

export type VehicleDetailsBreakdownType = {
  vehicle_breakdown_type_id: string
  company_id: string
  breakdown_type: string
  is_deleted: string
}

export type VehicleDetailsCleaning = {
  cleaning_id: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  plate: string
  total_value: string
  vehicle_cleaning_type: string
  vehicle_cleaning_type_id: string
  vehicle_id: string
}

type OperationalVehicle = {
  document_line_id: number
  document_id: number
  company_id: number
  document_date: string
  vehicle_id: number
  plate: string
  description: number
  total_value: number
}

export type VehicleDetailsConsumable = OperationalVehicle & {
  consumable_id: number
  vehicle_consumable_type_id: number
  vehicle_consumable_type: string
  fill_in_date: string
  validation_date: string
}

export type VehicleDetailsCleaningType = {
  vehicle_cleaning_type_id: string
  company_id: string
  cleaning_type: string
  is_deleted: string
}

export type VehicleConsumableType = {
  vehicle_consumable_type_id: number
  company_id: number
  consumable_type: string
  is_deleted: boolean
}

export type VehicleDetailsContract = {
  contract_id: string
  contract_date: string
  supplier_id: number
  supplier: string
  contract_start_date: string
  contract_end_date: string
  contract_type: string
  contract_status: string
  contract_status_id: number
  contract_sub_type: string
}

export type VehicleDetailsContractTypes = {
  contract_type_id: number
  contract_type: string
  is_deleted: boolean
}

export type VehicleDetailsFinancing = {
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  financing_id: string
  plate: string
  total_value: string
  vehicle_financing_type: string
  vehicle_financing_type_id: string
  vehicle_id: string
}

export type VehicleDetailsFine = {
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  driver_id: string
  driver_name: string
  fine_id: string
  infringement: string
  infringement_location: string
  infringement_date: string
  payment_due_date: string
  infringement_number: string
  plate: string
  register_date: string
  total_value: string
  vehicle_fine_type: string
  vehicle_fine_type_id: string
  vehicle_id: string
}

export type VehicleDetailsFineType = {
  vehicle_fine_type_id: string
  company_id: string
  fine_type: string
  is_deleted: string
}

export type VehicleDetailsFuelling = {
  company_id: string
  contract_fuel_card_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  driver_id: string
  driver_name: string
  fuel_card: string
  fuel_transaction_type_id: string
  fuelling_id: string
  fuelling_order: string
  fuelling_station: string
  is_tank_full: string
  plate: string
  total_value: string
  transaction_type: string
  vehicle_id: string
}

export type VehicleDetailsFuelCard = {
  contract_id: number
  contract_fuel_card_id: number
  company_id: number
  vehicle_id: number
  plate: string
  driver_id: FixMeAny
  driver_name: FixMeAny
  supplier_id: number
  supplier: string
  contract_date: string
  contract_start_date: string
  contract_end_date: string
  contract_status_id: number
  contract_status: string
  payment_method_id: FixMeAny
  payment_method: FixMeAny
  payment_term_id: FixMeAny
  payment_term: FixMeAny
  notes: FixMeAny
  net_value: number
  tax_type_id: number
  tax_non_deductable_value: number
  tax_deductable_value: number
  total_value: number
  accounting_chart_number: string | null
  fleet_odometer: number | null
  create_timestamp: string
  create_user_id: number
  create_user_name: string
  update_timestamp: string
  update_user_id: number
  update_user_name: string
  card_number: number
  target_object: string
}

export type VehicleDetailsFuelTransactionType = {
  fuel_transaction_type_id: string
  tag: string
  is_deleted: string
}

export type VehicleDetailsIncident = {
  claim_number: string
  company_fanchise: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  document_franchise: string
  driver_id: string
  driver_name: string
  has_recoveries: string
  incident_date: string
  incident_id: string
  incident_loss_value: string
  place: string
  plate: string
  process_number: string
  total_value: string
  vehicle_id: string
  vehicle_incident_type: string
  vehicle_incident_type_id: string
  document_status: DocumentStatus
  document_status_id: MifleetGeneralId
  source_id: string
  create_timestamp: string
}

export type VehicleDetailsIncidentType = {
  vehicle_incident_type_id: string
  incident_type: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsLeasingCostType = {
  vehicle_leasing_cost_type_id: number
  company_id: number
  leasing_cost_type: string
  is_deleted: boolean
}

type VehicleDetailsInsurance = {
  document_line_id: string
  document_id: string
  company_id: string
  document_date: string
  vehicle_id: string
  plate: string
  contract_insurance_id: string | null
  insurance_type_id: string | null
  insurance_type: string | null
  policy_number: number | null
  total_value: string
}

export type VehicleDetailsLeasing = OperationalVehicle & {
  leasing_id: number
  vehicle_leasing_cost_type_id: number
  vehicle_leasing_cost_type: string
}

export type VehicleDetailsMaintenance = {
  budget: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  maintenance_id: string
  plate: string
  total_value: string
  vehicle_id: string
  vehicle_maintenance_type: string
  vehicle_maintenance_type_id: string
}

export type VehicleDetailsMaintenanceType = {
  vehicle_maintenance_type_id: string
  maintenance_type: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsOilType = {
  vehicle_oil_type_id: number
  company_id: number
  oil_type: string
  is_deleted: boolean
}

export type VehicleDetailsPermit = {
  begin_date: string
  begin_odometer: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  end_odometer: string
  expiration_date: string
  permit_id: string
  plate: string
  total_value: string
  vehicle_id: string
  vehicle_permit_type: string
  vehicle_permit_type_id: string
}

export type VehicleDetailsAccessories = {
  accessory_id: string
  vehicle_accessory_type_id: string
  description: string
  document_line_id: string
  document_id: string
  company_id: string
  document_date: string
  vehicle_id: string
  plate: string
  vehicle_accessory_type: string
  total_value: string
}

export type VehicleDetailsPermitType = {
  vehicle_permit_type_id: string
  permit_type: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsRentalCostType = {
  vehicle_rental_cost_type_id: number
  company_id: number
  rental_cost_type: string
  is_deleted: boolean
}

export type VehicleDetailsDriverPermitType = {
  driver_permit_type_id: string
  permit_type: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsDriverCostType = {
  driver_cost_type_id: string
  company_id: string
  cost_type: string
  is_deleted: string
}

export type VehicleDetailsPurchase = {
  accessories_price: string
  administration_tax: string
  company_id: string
  depreciation_tax: string
  description: string
  discount: string
  document_date: string
  document_id: string
  document_line_id: string
  estimated_lifetime: string
  mileage_purchase: string
  plate: string
  purchase_id: string
  registration_date: string
  registration_tax: string
  residual_value: string
  retail_price: string
  total_value: string
  vehicle_id: string
}

export type VehicleDetailsRental = OperationalVehicle & {
  rental_id: number
  vehicle_rental_cost_type_id: number
  vehicle_rental_cost_type: string
}

export type VehicleDetailsTax = {
  begin_date: string
  company_id: string
  description: string
  document_date: string
  document_id: string
  document_line_id: string
  expiration_date: string
  plate: string
  tax_id: string
  tax_type: string
  tax_type_id: string
  total_value: string
  vehicle_id: string
}

export type VehicleDetailsTaxTypes = {
  tax_type_id: string
  company_id: string
  tax_name: string
  description: string
  tax_value: number
  is_vat_tax: boolean
  is_deleted: boolean
}

export type VehicleDetailsTire = {
  company_id: number
  description: string
  create_timestamp: string
  document_date: string
  document_id: number
  document_line_id: number
  plate: string
  total_value: number
  tyre_id: number
  vehicle_id: number
  vehicle_tyre_operation: string
  vehicle_tyre_operation_id: number
  document_status: DocumentStatus
  document_status_id: MifleetGeneralId
  source_id: string
}

export type VehicleDetailsTireLocation = {
  vehicle_tyre_location_id: string
  tyre_location: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsTireOperation = {
  vehicle_tyre_operation_id: string
  tyre_operation: string
  company_id: string
  is_deleted: string
}

export type VehicleDetailsToll = {
  company_id: number
  description: string
  document_date: string
  document_id: number
  document_line_id: number
  passage_name: string
  plate: string
  toll_entry: string
  toll_exit: string
  toll_id: number
  total_value: number
  vehicle_id: number
}

export type VehicleDetailsVehicle = {
  vehicle_id: number
  company_id: number
  manufacturer: string
  model: string
  vehicle_type_id: number
  vehicletype: string
  vehicle_gearbox_type_id: null
  vehicle_fuel_type_id: null
  cost_centre_id: null
  costcentre: null
  plate: string
  engine_number: string
  chassis_number: string
  engine_capacity: null
  fuel_tank_capacity: null
  manufacture_year: number
  is_new: boolean
  working_time_units: number
  colour: string
  vehicle_work_counter_type_id: null
  work_counter_shortname: null
  is_monitored: boolean
  is_deleted: boolean
  pool_active: null
  is_mifleet: boolean
  is_pool: boolean
}

export type VehicleDetails = {
  accessories: Array<VehicleDetailsAccessory>
  assignedList: Array<VehicleDetailsAssignedList>
  assignedListHistory: Array<VehicleDetailsAssignedList>
  breakdowns: Array<VehicleDetailsBreakdown>
  cleanings: Array<VehicleDetailsCleaning>
  consumables: Array<VehicleDetailsConsumable>
  contracts: Array<VehicleDetailsContract>
  contractsHistory: Array<VehicleDetailsContract>
  contractTypes: Array<VehicleDetailsContractTypes>
  financing: Array<VehicleDetailsFinancing>
  fines: Array<VehicleDetailsFine>
  fuellings: Array<VehicleDetailsFuelling>
  incidents: Array<VehicleDetailsIncident>
  insurances: Array<VehicleDetailsInsurance>
  leasings: Array<VehicleDetailsLeasing>
  maintenances: Array<VehicleDetailsMaintenance>
  oils: Array<VehicleDetailsOil>
  permits: Array<VehicleDetailsPermit>
  purchase: Array<VehicleDetailsPurchase>
  rentals: Array<VehicleDetailsRental>
  taxes: Array<VehicleDetailsTax>
  tires: Array<VehicleDetailsTire>
  tolls: Array<VehicleDetailsToll>
  vehicle: VehicleDetailsVehicle
  accessoryTypes: Array<VehicleDetailsAccessoryType>
  breakdownTypes: Array<VehicleDetailsBreakdownType>
  cleaningTypes: Array<VehicleDetailsCleaningType>
  consumableTypes: Array<VehicleConsumableType>
  fineTypes: Array<VehicleDetailsFineType>
  fuelCards: Array<VehicleDetailsFuelCard>
  fuelTransactionTypes: Array<VehicleDetailsFuelTransactionType>
  incidentTypes: Array<VehicleDetailsIncidentType>
  leasingCostTypes: Array<VehicleDetailsLeasingCostType>
  maintenanceTypes: Array<VehicleDetailsMaintenanceType>
  oilTypes: Array<VehicleDetailsOilType>
  permitTypes: Array<VehicleDetailsPermitType>
  rentalCostTypes: Array<VehicleDetailsRentalCostType>
  tireLocations: Array<VehicleDetailsTireLocation>
  tireOperations: Array<VehicleDetailsTireOperation>
  driverPermitTypes: Array<VehicleDetailsDriverPermitType>
  driverCostTypes: Array<VehicleDetailsDriverCostType>
  taxTypes: Array<VehicleDetailsTaxTypes>
}

export type MiFleetDriver = {
  driver_id: string
  details_id: string | null
  company_id: string
  driver_status_id: string
  short_name: string
  first_name: string
  surname: string | null
  gender: string
  identification_number: string | null
  social_security_number: string | null
  employee_number: string | null
  address_line1: string | null
  address_line2: string | null
  address_line3: string | null
  postal_code: string | null
  phone_number: string | null
  email: string | null
  hire_date: string | null
  leave_date: string | null
  labor_rate: string | null
  billing_rate: string | null
  monthly_wage_cost: string | null
}

export type MifleetDocumentConceptsType = {
  document_concept_id: string
  document_concept: string
  vehicle_type: string
  driver_type: string
  other_type: string
  is_deleted: string
}

export type MifleetDocumentStatusType = {
  document_status: DocumentStatus
  document_status_id: MifleetGeneralId
  is_deleted: string
}

export type MifleetDocumentTypes = {
  document_type: DocumentType
  document_type_id: MifleetGeneralId
}

export type MifleetDriversTypes = {
  driver_id: string
  is_allowed: boolean
  short_name: string
  user_role_driver_id: string | null
}

export type MifleetPaymentMethodsTypes = {
  company_id: string
  is_deleted: string
  payment_method: string
  payment_method_id: string
}

export type MifleetPaymentTermsTypes = {
  company_id: string
  is_deleted: string
  due_days: string
  payment_term: string
  payment_term_id: string
}

export type MifleetVehiclesSettingTypes = {
  driver_id: string | null
  is_deleted: string
  plate: string
  vehicle_id: number
}

export type MileetFuelCardSettingTypes = {
  card_number: string
  contract_fuel_card_id: string
  contract_status: ContractStatus
  supplier: string
  vehicle_id: VehicleId
}
