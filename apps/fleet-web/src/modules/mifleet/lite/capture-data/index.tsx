import { useCallback, useEffect, useMemo, useState } from 'react'
import { isNil, isNumber, toNumber, toString } from 'lodash'
import {
  Autocomplete,
  Box,
  Button,
  DateTimePicker,
  Paper,
  Stack,
  TextField,
  Tooltip,
  Typography,
  type PaperProps,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import KeyboardArrowDownOutlinedIcon from '@mui/icons-material/KeyboardArrowDownOutlined'
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined'
import BigNumber from 'bignumber.js'
import { DateTime } from 'luxon'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import { getAutocompleteVirtualizedProps } from '@fleet-web/components/_dropdowns/AutocompleteVirtualized'
import { getLocale, getStandardizedDefaultCountrySetting } from '@fleet-web/duxs/user'
import { handlePDFReportFont } from '@fleet-web/modules/delivery/report/component-to-pdf/helper'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { messages } from '@fleet-web/shared/forms/messages'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import intlNumbers from '@fleet-web/util-functions/intl-numbers'
import {
  calcNetValue,
  calcTaxedValue,
} from '@fleet-web/util-functions/mifleet/taxes-utils'
import { isTrue } from '@fleet-web/util-functions/validation'
import { createZodObjPathGetter } from '@fleet-web/util-functions/zod-utils'

import {
  DOCUMENT_STATUS_OPTIONS,
  DOCUMENT_TYPE_OPTIONS,
} from '../../api/costInput/shared/types'
import {
  DOCUMENT_CONCEPT_ACCESSORY,
  getConceptIdentifierBy,
} from '../../components/documents/concept-types'
import { SupplierFormModal } from '../../components/suppliers/supplier-form/supplier-form-modal-custom'
import type { FetchMiFleetArrayTypes } from '../api/useMiFleetArrayTypes'
import {
  useCreateMiFleetCostMutation,
  useUpdateMiFleetCostMutation,
  type ReadMiFleetCostQueryData,
  type UpdateDocumentLineType,
} from '../api/useMiFleetCost'
import useFetchMiFleetTaxDeductionValues from '../api/useMiFleetTaxCalculations'
import ConceptFormsRoot, { type ValidSchema as PaymentSchema } from './concept-forms'
import { AccordionDetailsStyle, AccordionStyle, AccordionSummaryStyle } from './style'
import { conceptsWhereDateIsHidden, conceptsWhereDriverIsMandatory } from './utils'

const schema = z
  .object({
    document_line_id: z.string().nullable(),
    document_type_id: z.string(),
    document_status_id: z.string(),
    document_concept_id: z.string(),
    vehicle_id: z.string().nullable(),
    driver_id: z.string().nullable(),
    isDriverConcept: z.boolean(),
    supplier_id: z.string().min(1, { message: messages.required }),
    tax_type_id: z.string().min(1, { message: messages.required }),
    description: z
      .string()
      .refine((val) => val.length > 0, { message: messages.required }),
    accounting_chart_number: z.string().nullable(),
    quantity: z
      .string()
      .min(1, { message: messages.required })
      .max(16, { message: messages.numberInputMaxLength })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
      .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
    price: z
      .string()
      .min(1, { message: messages.required })
      .max(16, { message: messages.numberInputMaxLength })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
      .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
    net_value: z
      .string()
      .min(1, { message: messages.required })
      .max(16, { message: messages.numberInputMaxLength })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber),
    discount: z
      .string()
      .max(16, { message: messages.numberInputMaxLength })
      .nullable()
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber)
      .refine((val) => toNumber(val) >= 0, messages.validPositiveNumber),
    total_value: z
      .string()
      .min(1, { message: messages.required })
      .max(16, { message: messages.numberInputMaxLength })
      .refine((val) => toNumber(val) || toNumber(val) === 0, messages.validNumber),
    document_number: z.string().nullable(),
    tax_deductable_value: z.string().nullable(),
    tax_non_deductable_value: z.string().nullable(),
    document_date: z.date({ error: () => messages.required }),
  })
  .superRefine((data, ctx) => {
    const { createPath } = createZodObjPathGetter(data)

    if (
      Boolean(toNumber(data.net_value)) &&
      data.document_type_id !== '2' &&
      toNumber(data.net_value) <= 0
    ) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['net_value']),
        message: messages.validPositiveNumber,
      })
    }

    if (
      Boolean(toNumber(data.total_value)) &&
      data.document_type_id !== '2' &&
      toNumber(data.total_value) <= 0
    ) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['total_value']),
        message: messages.validPositiveNumber,
      })
    }

    if (Number(data.total_value) < 0)
      ctx.addIssue({
        code: 'custom',
        path: createPath(['discount']),
        message: 'input.error.discount.total',
      })
    if (!data.isDriverConcept && !data.vehicle_id) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['vehicle_id']),
        message: messages.required,
      })
    } else if (data.isDriverConcept && !data.driver_id) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['driver_id']),
        message: messages.required,
      })
    }
  })

type ValidSchema = z.infer<typeof schema>

export type FormPossibleValues = {
  document_line_id: ValidSchema['document_line_id']
  document_type_id: ValidSchema['document_type_id']
  document_status_id: ValidSchema['document_status_id']
  document_concept_id: ValidSchema['document_concept_id']
  vehicle_id: ValidSchema['vehicle_id']
  driver_id: ValidSchema['driver_id']
  isDriverConcept: ValidSchema['isDriverConcept']
  supplier_id: ValidSchema['supplier_id']
  tax_type_id: ValidSchema['tax_type_id']
  description: ValidSchema['description']
  accounting_chart_number: ValidSchema['accounting_chart_number']
  quantity: ValidSchema['quantity']
  price: ValidSchema['price']
  net_value: ValidSchema['net_value']
  discount: ValidSchema['discount']
  total_value: ValidSchema['total_value']
  document_number: ValidSchema['document_number'] | null
  tax_deductable_value: ValidSchema['tax_deductable_value'] | null
  tax_non_deductable_value: ValidSchema['tax_deductable_value'] | null
  document_date: ValidSchema['document_date']
}

type Props = {
  defaultConcept: { name: string; id: string }
  activeCost?: string
  onClose: () => void
  isSuccessCreation?: () => void
  isSuccessUpdating?: () => void
  arrayTypes?: FetchMiFleetArrayTypes.ParsedReturn
  refetchArrayType: () => void
  readCostQueryData?: ReadMiFleetCostQueryData
}

const CaptureData = ({
  defaultConcept,
  activeCost,
  onClose,
  isSuccessCreation,
  isSuccessUpdating,
  arrayTypes,
  refetchArrayType,
  readCostQueryData,
}: Props) => {
  const locale = useTypedSelector(getLocale)
  const defaultCountry = useTypedSelector(getStandardizedDefaultCountrySetting)

  handlePDFReportFont(defaultCountry, locale)

  const [isAdvancedOptionsOpen, setIsAdvancedOptionsOpen] = useState(false)
  const [supplierModalIsOpen, setSupplierModalIsOpen] = useState<boolean>(false)

  const getTaxDeductionValues = useFetchMiFleetTaxDeductionValues()
  const createCostMutation = useCreateMiFleetCostMutation()
  const updateCostMutation = useUpdateMiFleetCostMutation()
  const updateDocumentNumberOnConceptChange = (concept: string) =>
    `${ctIntl.formatMessage({ id: getConceptIdentifierBy(concept, 'id') })}-${Date.now()
      .toString()
      .slice(-6)}`

  const SupplierComponentCustom = useCallback((options: PaperProps) => {
    const { children } = options
    return (
      <Paper {...options}>
        {children}
        <Button
          fullWidth
          color="primary"
          startIcon={<AddIcon />}
          onMouseDown={() => setSupplierModalIsOpen(true)}
          size="small"
          sx={{
            p: '16px',
            borderTop: '1px solid rgba(0, 0, 0, 0.12)',
            display: 'flex',
            justifyContent: 'flex-start',
          }}
        >
          {ctIntl.formatMessage({
            id: 'Add New Supplier',
          })}
        </Button>
      </Paper>
    )
  }, [])

  const initialValues: FormPossibleValues =
    readCostQueryData && activeCost
      ? (() => {
          const {
            document_line_id,
            document_type_id,
            document_status_id,
            document_concept_id,
            vehicle_id,
            driver_id,
            supplier_id,
            tax_type_id,
            description,
            accounting_chart_number,
            quantity,
            price,
            net_value,
            discount,
            total_value,
            document_number,
            tax_deductable_value,
            tax_non_deductable_value,
            document_date,
          } = readCostQueryData

          return {
            document_line_id,
            document_type_id,
            document_status_id,
            document_concept_id,
            vehicle_id: vehicle_id || '',
            driver_id: driver_id || '',
            isDriverConcept:
              conceptsWhereDriverIsMandatory.includes(document_concept_id),
            supplier_id: supplier_id || '',
            tax_type_id: tax_type_id || '',
            description: description || '',
            accounting_chart_number: accounting_chart_number || '',
            quantity: quantity || '',
            price: price || '',
            net_value: net_value || '',
            discount: discount || '',
            total_value: total_value || '',
            document_number: document_number || '',
            tax_deductable_value: tax_deductable_value || '',
            tax_non_deductable_value: tax_non_deductable_value || '',
            document_date: new Date(document_date),
          }
        })()
      : {
          document_line_id: null,
          document_type_id: DOCUMENT_TYPE_OPTIONS[0].document_type_id,
          document_status_id: DOCUMENT_STATUS_OPTIONS[0].document_status_id,
          document_concept_id:
            defaultConcept.id !== 'multicost'
              ? defaultConcept.id
              : DOCUMENT_CONCEPT_ACCESSORY,
          vehicle_id: '',
          driver_id: '',
          isDriverConcept: false,
          supplier_id: '',
          tax_type_id: '',
          description: '',
          accounting_chart_number: '',
          quantity: '',
          price: '',
          net_value: '0',
          discount: '',
          total_value: '',
          document_number: updateDocumentNumberOnConceptChange(
            defaultConcept.id !== 'multicost'
              ? defaultConcept.id
              : DOCUMENT_CONCEPT_ACCESSORY,
          ),
          tax_deductable_value: '0',
          tax_non_deductable_value: '0',
          document_date: new Date(),
        }

  const {
    control,
    formState: { isValid },
    setValue: setFormValue,
    getValues,
    trigger,
  } = useForm<FormPossibleValues>({
    resolver: zodResolverV4(schema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const watchedPrice = useWatch({ name: 'price', control })
  const watchedQuantity = useWatch({ name: 'quantity', control })
  const watchedDiscount = useWatch({ name: 'discount', control })
  const watchedTaxTypeId = useWatch({ name: 'tax_type_id', control })
  const watchedConceptId = useWatch({ name: 'document_concept_id', control })
  const watchedNetValue = useWatch({ name: 'net_value', control })
  const watchedVehicleId = useWatch({ name: 'vehicle_id', control })
  const watchedDriverId = useWatch({ name: 'driver_id', control })
  const watchIsDriverConcept = useWatch({ name: 'isDriverConcept', control })

  const renderCostDateField = () => {
    if (conceptsWhereDateIsHidden.includes(watchedConceptId)) {
      return null
    }

    return (
      <Controller
        control={control}
        name="document_date"
        render={({ field, fieldState }) => (
          <DateTimePicker
            sx={{ width: 360 }}
            disableFuture
            maxDate={DateTime.fromJSDate(new Date())}
            label={ctIntl.formatMessage({ id: 'mifleet.imports.manual.cost.date' })}
            value={DateTime.fromJSDate(field.value)}
            onChange={(newValue) =>
              setFormValue(field.name, newValue ? newValue.toJSDate() : new Date(), {
                shouldValidate: false,
              })
            }
            slotProps={{
              textField: {
                required: true,
                error: !!fieldState.error,
                helperText: ctIntl.formatMessage({
                  id: fieldState.error?.message ?? '',
                }),
              },
            }}
          />
        )}
      />
    )
  }

  useEffect(() => {
    if (defaultConcept.id !== 'multicost' && !activeCost) {
      setFormValue('document_concept_id', defaultConcept.id, { shouldValidate: false })
      setFormValue(
        'document_number',
        updateDocumentNumberOnConceptChange(defaultConcept.id),
        { shouldValidate: false },
      )
    }
  }, [activeCost, defaultConcept.id, setFormValue])

  useEffect(() => {
    // Use this useEffect _ONLY_ to calculate net_value and total_value
    let bigPrice = new BigNumber(0)
    let bigQuantity = new BigNumber(0)
    let bigDiscount = new BigNumber(0)

    if (isNumber(toNumber(watchedPrice))) {
      bigPrice = new BigNumber(toNumber(watchedPrice))
    }

    if (isNumber(toNumber(watchedQuantity))) {
      bigQuantity = new BigNumber(toNumber(watchedQuantity))
    }

    if (isNumber(toNumber(watchedDiscount))) {
      bigDiscount = new BigNumber(toNumber(watchedDiscount))
    }

    const netValue = intlNumbers.formatBigNumberWithNormalNotation(
      calcNetValue(bigPrice, bigQuantity, bigDiscount),
    )

    const selectedTax = arrayTypes?.taxTypes?.find(
      (tax) => tax.tax_type_id === watchedTaxTypeId,
    )

    const totalValue = new BigNumber(netValue).plus(
      calcTaxedValue(netValue, toNumber(selectedTax?.tax_value) || 0),
    )

    setFormValue('net_value', toString(Number(netValue).toFixed(2)), {
      shouldValidate: true,
    })
    setFormValue('total_value', toString(Number(totalValue).toFixed(2)), {
      shouldValidate: true,
    })
  }, [
    arrayTypes?.taxTypes,
    setFormValue,
    watchedDiscount,
    watchedPrice,
    watchedQuantity,
    watchedTaxTypeId,
  ])

  const getDeductionUpdatedValues = () => {
    getTaxDeductionValues.mutate(
      {
        vehicle_id: watchIsDriverConcept ? null : toString(watchedVehicleId),
        document_date: DateTime.fromJSDate(new Date()).toISODate(),
        tax_type_id: watchedTaxTypeId || '',
        net_value: watchedNetValue || '',
      },
      {
        onSuccess(data) {
          setFormValue('tax_deductable_value', toString(data.tax_deductable_value), {
            shouldValidate: false,
          })
          setFormValue(
            'tax_non_deductable_value',
            toString(data.tax_non_deductable_value),
            { shouldValidate: false },
          )
        },
      },
    )
  }

  const canFetchDeductionValues = () =>
    !isNil(watchedTaxTypeId) &&
    !isNil(watchedNetValue) &&
    (watchIsDriverConcept ? !isNil(watchedDriverId) : !isNil(watchedVehicleId)) &&
    isValid

  const setConceptFormState = (
    isConceptFormValid: boolean,
    conceptValues?:
      | UpdateDocumentLineType.FineSpecificType
      | UpdateDocumentLineType.TollsSpecificType
      | UpdateDocumentLineType.FuelSpecificType
      | UpdateDocumentLineType.TiresSpecificType
      | UpdateDocumentLineType.AccidentsSpecificType
      | UpdateDocumentLineType.MaintenanceSpecificType
      | UpdateDocumentLineType.RentalCostSpecificType
      | UpdateDocumentLineType.CleaningSpecificType
      | UpdateDocumentLineType.OilSpecificType
      | UpdateDocumentLineType.LeasingCostSpecificType
      | UpdateDocumentLineType.ConsumableSpecificType
      | UpdateDocumentLineType.DriverCostSpecificType
      | UpdateDocumentLineType.DriverPermitSpecificType
      | UpdateDocumentLineType.TaxSpecificType
      | UpdateDocumentLineType.PurchaseSpecificType
      | UpdateDocumentLineType.BreakdownSpecificType
      | UpdateDocumentLineType.VehicleLicenseSpecificType
      | UpdateDocumentLineType.AccessorySpecificType,
    paymentValues?: PaymentSchema,
  ) => {
    trigger()
    if (isValid && isConceptFormValid) {
      // both forms valid
      const params = JSON.parse(
        JSON.stringify({
          ...getValues(),
          ...paymentValues,
          specificData: [{ ...conceptValues }],
        }),
      )

      if (!params.document_line_id) {
        createCostMutation.mutate(params, {
          onSuccess(data) {
            setFormValue('document_line_id', data.document_line_id, {
              shouldValidate: false,
            })
          },
        })
      } else {
        updateCostMutation.mutate(params)
      }
    }
  }

  useEffect(() => {
    if (isSuccessCreation && createCostMutation.isSuccess) {
      isSuccessCreation()
    }
  }, [createCostMutation.isSuccess, isSuccessCreation])

  useEffect(() => {
    if (isSuccessUpdating && updateCostMutation.isSuccess) {
      isSuccessUpdating()
    }
  }, [isSuccessUpdating, updateCostMutation.isSuccess])

  const TRANSLATED_DOCUMENT_TYPE_OPTIONS = DOCUMENT_TYPE_OPTIONS.map((option) => ({
    ...option,
    label: ctIntl.formatMessage({ id: option.label }),
  }))
  const TRANSLATED_DOCUMENT_STATUS_OPTIONS = DOCUMENT_STATUS_OPTIONS.map((option) => ({
    ...option,
    label: ctIntl.formatMessage({ id: option.label }),
  }))
  const getActiveTaxType = useMemo(
    () =>
      arrayTypes?.taxTypes?.filter(
        (type) => !isTrue(type.is_deleted) && isTrue(type.is_vat_tax),
      ),
    [arrayTypes?.taxTypes],
  )
  return (
    <Box>
      <form id={'accounting_details_concept_form'}>
        <AccordionStyle
          disableGutters
          defaultExpanded={true}
        >
          <AccordionSummaryStyle expandIcon={<KeyboardArrowDownOutlinedIcon />}>
            <Typography variant="subtitle2">
              {ctIntl.formatMessage({
                id: 'mifleet.imports.manual.document.info',
              })}
            </Typography>
          </AccordionSummaryStyle>
          <AccordionDetailsStyle>
            <Stack
              direction="row"
              justifyContent={'space-between'}
              marginBottom={2}
            >
              <Controller
                control={control}
                name="document_type_id"
                render={({ field }) => (
                  <Autocomplete
                    disableClearable
                    sx={{ width: 360 }}
                    {...getAutocompleteVirtualizedProps({
                      options: TRANSLATED_DOCUMENT_TYPE_OPTIONS,
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue.document_type_id, {
                        shouldValidate: false,
                      })
                    }}
                    value={TRANSLATED_DOCUMENT_TYPE_OPTIONS.find(
                      (type) => type.document_type_id === field.value,
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Document Type' })}
                        required
                      />
                    )}
                  />
                )}
              />
              <Controller
                control={control}
                name="document_status_id"
                render={({ field }) => (
                  <Autocomplete
                    disableClearable
                    sx={{ width: 360 }}
                    {...getAutocompleteVirtualizedProps({
                      options: TRANSLATED_DOCUMENT_STATUS_OPTIONS,
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue.document_status_id, {
                        shouldValidate: false,
                      })
                    }}
                    value={TRANSLATED_DOCUMENT_STATUS_OPTIONS.find(
                      (status) => status.document_status_id === field.value,
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Document Status' })}
                        required
                      />
                    )}
                  />
                )}
              />
            </Stack>
          </AccordionDetailsStyle>
        </AccordionStyle>

        <AccordionStyle
          disableGutters
          defaultExpanded={true}
        >
          <AccordionSummaryStyle expandIcon={<KeyboardArrowDownOutlinedIcon />}>
            <Typography variant="subtitle2">
              {ctIntl.formatMessage({
                id: 'Accounting Details',
              })}
            </Typography>
          </AccordionSummaryStyle>
          <AccordionDetailsStyle>
            <Stack
              direction="row"
              flexWrap={'wrap'}
              spacing={2}
              useFlexGap
              justifyContent={'space-between'}
              marginBottom={2}
            >
              {defaultConcept.id === 'multicost' && (
                <Controller
                  control={control}
                  name="document_concept_id"
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      disableClearable
                      disabled={Boolean(activeCost)}
                      sx={{ width: 360 }}
                      {...getAutocompleteVirtualizedProps({
                        options: arrayTypes?.documentConcepts || [],
                      })}
                      onChange={(_, newValue) => {
                        const forwardValue = newValue
                          ? newValue.document_concept_id
                          : DOCUMENT_CONCEPT_ACCESSORY
                        setFormValue(
                          'document_number',
                          updateDocumentNumberOnConceptChange(forwardValue),
                          { shouldValidate: false },
                        )
                        setFormValue(
                          'isDriverConcept',
                          conceptsWhereDriverIsMandatory.includes(forwardValue),
                          { shouldValidate: false },
                        )
                        setFormValue(field.name, forwardValue, {
                          shouldValidate: true,
                        })
                      }}
                      value={
                        field.value
                          ? arrayTypes?.documentConcepts.find(
                              (vehicle) => vehicle.document_concept_id === field.value,
                            )
                          : undefined
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label={ctIntl.formatMessage({ id: 'Concept' })}
                          helperText={ctIntl.formatMessage({
                            id: fieldState.error?.message ?? '',
                          })}
                          error={!!fieldState.error}
                          required
                        />
                      )}
                    />
                  )}
                />
              )}
              <Controller
                control={control}
                name="supplier_id"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    sx={{ width: 360 }}
                    {...getAutocompleteVirtualizedProps({
                      options: arrayTypes?.suppliers || [],
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue ? newValue.supplier_id : '', {
                        shouldValidate: true,
                      })
                    }}
                    value={
                      field.value
                        ? arrayTypes?.suppliers?.find(
                            (sup) => sup.supplier_id === field.value,
                          )
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Supplier' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                        required
                      />
                    )}
                    PaperComponent={SupplierComponentCustom}
                  />
                )}
              />
              {!conceptsWhereDriverIsMandatory.includes(watchedConceptId) && (
                <Controller
                  control={control}
                  name="vehicle_id"
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      sx={{ width: 360 }}
                      {...getAutocompleteVirtualizedProps({
                        options: arrayTypes?.activeVehicles || [],
                      })}
                      onChange={(_, newValue) => {
                        setFormValue(
                          field.name,
                          newValue ? toString(newValue.vehicle_id) : '',
                          {
                            shouldValidate: true,
                          },
                        )
                      }}
                      value={
                        field.value
                          ? arrayTypes?.activeVehicles?.find(
                              (vehicle) => toString(vehicle.vehicle_id) === field.value,
                            )
                          : null
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label={ctIntl.formatMessage({ id: 'Vehicle' })}
                          helperText={ctIntl.formatMessage({
                            id: fieldState.error?.message ?? '',
                          })}
                          error={!!fieldState.error}
                          required
                        />
                      )}
                    />
                  )}
                />
              )}
              {conceptsWhereDriverIsMandatory.includes(watchedConceptId) && (
                <Controller
                  control={control}
                  name="driver_id"
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      sx={{ width: 360 }}
                      {...getAutocompleteVirtualizedProps({
                        options: arrayTypes?.allowedDrivers || [],
                      })}
                      onChange={(_, newValue) => {
                        setFormValue(field.name, newValue ? newValue.driver_id : '', {
                          shouldValidate: true,
                        })
                      }}
                      value={
                        field.value
                          ? arrayTypes?.allowedDrivers.find(
                              (vehicle) => vehicle.driver_id === field.value,
                            )
                          : null
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label={ctIntl.formatMessage({ id: 'Driver' })}
                          helperText={ctIntl.formatMessage({
                            id: fieldState.error?.message ?? '',
                          })}
                          error={!!fieldState.error}
                          required
                        />
                      )}
                    />
                  )}
                />
              )}
              {renderCostDateField()}
              <TextFieldControlled
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'description',
                  control,
                }}
                label={ctIntl.formatMessage({
                  id: 'Description',
                })}
                required
              />
              <TextFieldControlled
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'accounting_chart_number',
                  control,
                }}
                label={ctIntl.formatMessage({ id: 'Accounting Chart Nr' })}
              />
              <TextFieldControlled
                placeholder="18"
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'quantity',
                  control,
                }}
                label={ctIntl.formatMessage({
                  id: 'Quantity',
                })}
                required
                onChange={(_newValue) => {
                  setFormValue('quantity', _newValue.target.value, {
                    shouldValidate: true,
                  })
                  trigger(['total_value', 'net_value', 'discount'], {
                    shouldFocus: true,
                  })
                }}
              />
              <TextFieldControlled
                placeholder="1.54"
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'price',
                  control,
                }}
                label={ctIntl.formatMessage({
                  id: 'Price',
                })}
                required
                onChange={(_newValue) => {
                  setFormValue('price', _newValue.target.value, {
                    shouldValidate: true,
                  })
                  trigger(['total_value', 'net_value', 'discount'], {
                    shouldFocus: true,
                  })
                }}
              />
              <TextFieldControlled
                placeholder="6"
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'discount',
                  control,
                }}
                label={ctIntl.formatMessage({ id: 'Discount Value' })}
                onChange={(_newValue) => {
                  setFormValue('discount', _newValue.target.value, {
                    shouldValidate: true,
                  })
                  trigger(['total_value', 'net_value'])
                }}
              />
              <TextFieldControlled
                sx={{ width: 360 }}
                disabled
                ControllerProps={{
                  name: 'net_value',
                  control,
                }}
                label={ctIntl.formatMessage({
                  id: 'Net Value',
                })}
                required
              />
              <Controller
                control={control}
                name="tax_type_id"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    sx={{ width: 360 }}
                    {...getAutocompleteVirtualizedProps({
                      options: getActiveTaxType || [],
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue ? newValue.tax_type_id : '', {
                        shouldValidate: true,
                      })
                    }}
                    value={
                      field.value
                        ? arrayTypes?.taxTypes?.find(
                            (vehicle) => vehicle.tax_type_id === field.value,
                          )
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Tax Type' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                        required
                      />
                    )}
                  />
                )}
              />
              <TextFieldControlled
                placeholder="323"
                sx={{ width: 360 }}
                ControllerProps={{
                  name: 'total_value',
                  control,
                }}
                label={ctIntl.formatMessage({
                  id: 'Total Value',
                })}
                required
              />
            </Stack>
          </AccordionDetailsStyle>
        </AccordionStyle>

        <AccordionStyle
          disableGutters
          sx={{
            '.MuiAccordionSummary-root': {
              width: 'fit-content',
            },
            '.Mui-disabled': {
              color: 'unset',
            },
          }}
        >
          <Tooltip
            placement="top-start"
            title={
              !canFetchDeductionValues()
                ? ctIntl.formatMessage({
                    id: 'mifleet.document.line.advanced.options.warning',
                  })
                : null
            }
            PopperProps={{
              popperOptions: {
                modifiers: [
                  {
                    name: 'arrow',
                    options: {
                      padding: 150,
                    },
                  },
                ],
              },
            }}
          >
            <span>
              <AccordionSummaryStyle
                disabled={!canFetchDeductionValues()}
                expandIcon={
                  <KeyboardArrowDownOutlinedIcon
                    color={canFetchDeductionValues() ? 'primary' : 'inherit'}
                  />
                }
                sx={{
                  color: 'primary.main',
                  alignItems: 'flex-start',
                }}
                onClick={() => {
                  if (!isAdvancedOptionsOpen) {
                    getDeductionUpdatedValues()
                  }
                  setIsAdvancedOptionsOpen((prev) => !prev)
                }}
              >
                {ctIntl.formatMessage({
                  id:
                    isAdvancedOptionsOpen && canFetchDeductionValues()
                      ? 'mifleet.document.lines.hide.advanced'
                      : 'mifleet.document.lines.show.advanced',
                })}
              </AccordionSummaryStyle>
            </span>
          </Tooltip>
          <AccordionDetailsStyle>
            {isAdvancedOptionsOpen && canFetchDeductionValues() && (
              <>
                <Stack
                  direction="row"
                  justifyContent={'space-between'}
                  marginBottom={2}
                >
                  <TextFieldControlled
                    placeholder="Invoice #335"
                    sx={{ width: 360 }}
                    ControllerProps={{
                      name: 'document_number',
                      control,
                    }}
                    label={ctIntl.formatMessage({
                      id: 'Document Number',
                    })}
                    required
                  />
                  <TextFieldControlled
                    disabled
                    sx={{ width: 360 }}
                    ControllerProps={{
                      name: 'tax_deductable_value',
                      control,
                    }}
                    label={ctIntl.formatMessage({
                      id: 'mifleet.imports.mapping.match.field.tax_deductable_value',
                    })}
                    required
                  />
                </Stack>
                <Stack
                  direction="row"
                  spacing={4}
                  marginBottom={2}
                >
                  <TextFieldControlled
                    disabled
                    sx={{ width: 360 }}
                    ControllerProps={{
                      name: 'tax_non_deductable_value',
                      control,
                    }}
                    label={ctIntl.formatMessage({
                      id: 'NDED Tax',
                    })}
                    required
                  />
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: 'mifleet.document.lines.update.deduction.values',
                    })}
                    placement="top"
                    arrow
                  >
                    <Button
                      size="medium"
                      color="secondary"
                      variant="outlined"
                      onClick={() => getDeductionUpdatedValues()}
                      startIcon={<RefreshOutlinedIcon />}
                    >
                      {ctIntl.formatMessage({
                        id: 'mifleet.document.lines.update.deduction.values',
                      })}
                    </Button>
                  </Tooltip>
                </Stack>
              </>
            )}
          </AccordionDetailsStyle>
        </AccordionStyle>
      </form>
      <ConceptFormsRoot
        setConceptFormState={setConceptFormState}
        activeConcept={watchedConceptId}
        activeNameConcept={defaultConcept.name}
        fullDocumentLine={readCostQueryData}
        accountingDetail={getValues()}
        onClose={onClose}
        isPending={
          activeCost ? updateCostMutation.isPending : createCostMutation.isPending
        }
        arrayTypes={arrayTypes}
        onSubmit={() => {
          trigger() // trigger all fields to make sure error messages are shown
        }}
      />
      <SupplierFormModal
        isOpen={supplierModalIsOpen}
        onClose={() => setSupplierModalIsOpen(false)}
        onSuccess={() => {
          refetchArrayType()
          setSupplierModalIsOpen(false)
        }}
      />
    </Box>
  )
}

export default CaptureData
