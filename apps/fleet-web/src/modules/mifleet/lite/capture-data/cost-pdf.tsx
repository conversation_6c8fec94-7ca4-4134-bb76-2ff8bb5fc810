import { Document, Image, Page, pdf, StyleSheet, Text, View } from '@react-pdf/renderer'
import saveAs from 'file-saver'
import { DateTime } from 'luxon'

import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { isTrue } from '@fleet-web/util-functions/validation'

import MFLogo from 'assets/icons/mf_logo.png'

import { getConceptIdentifierBy } from '../../components/documents/concept-types'
import type { FetchMiFleetArrayTypes } from '../api/useMiFleetArrayTypes'
import type { DocumentLineTypes, ReadMiFleetCostQueryData } from '../api/useMiFleetCost'
import { conceptsWhereDateIsHidden } from './utils'

const pdfStyles = (isThirdParties?: boolean) =>
  StyleSheet.create({
    page: {
      padding: 10,
      display: 'flex',
      flexDirection: 'column',
      fontFamily: 'DeliveryFont',
    },
    mfLogo: {
      width: 150,
      height: 40,
    },
    documentDate: {
      fontSize: 10,
      marginLeft: 10,
    },
    documentInfoSection: {
      margin: 20,
    },
    documentInfoSectionParagraph: {
      marginBottom: 5,
      fontSize: 10,
    },
    dividerView: {
      marginBottom: 10,
      height: '30px',
      backgroundColor: '#f47735',
      border: '1px solid gray',
      justifyContent: 'center',
      paddingLeft: 10,
      fontSize: 12,
    },
    stackView: {
      display: 'flex',
      flexDirection: 'row',
      padding: '0 5%',
      flexWrap: 'wrap',
    },
    valueView: {
      display: 'flex',
      flexDirection: 'column',
      width: isThirdParties ? '25%' : '30%',
      marginBottom: 10,
      justifyContent: 'center',
    },
    valueViewTitle: {
      fontSize: 8,
      marginBottom: 5,
      color: 'gray',
    },
    valueViewValue: {
      fontSize: 10,
      paddingLeft: 5,
    },
  })

const na = 'N/A'

const valueView = (title: string, value: string, isThirdParties?: boolean) => (
  <View style={pdfStyles(isThirdParties).valueView}>
    <Text style={pdfStyles().valueViewTitle}>
      {ctIntl.formatMessage({
        id: title,
      })}
    </Text>
    <Text style={pdfStyles().valueViewValue}>{value}</Text>
  </View>
)

const dividerView = (title: string) => (
  <View style={pdfStyles().dividerView}>
    <Text>
      {ctIntl.formatMessage({
        id: title,
      })}
    </Text>
  </View>
)

const CostPDF = ({
  data,
  arrayTypes,
}: {
  data: ReadMiFleetCostQueryData
  arrayTypes: FetchMiFleetArrayTypes.ParsedReturn
}) => {
  const {
    documentTypes,
    documentStatus: documentStatuses,
    suppliers,
    vehicles,
    taxTypes,
  } = arrayTypes

  const documentType =
    documentTypes.find((type) => type.document_type_id === data.document_type_id)
      ?.label || na

  const documentStatus =
    documentStatuses.find((type) => type.document_status_id === data.document_status_id)
      ?.label || na

  const supplier =
    suppliers.find((sup) => sup.supplier_id === data.supplier_id)?.label || na

  const vehicle =
    vehicles.find((vehicle) => String(vehicle.vehicle_id) === data.vehicle_id)?.label ||
    na

  const taxTypeLabel =
    taxTypes.find((tax) => tax.tax_type_id === data.tax_type_id)?.label || na

  const taxTypeValue =
    taxTypes.find((tax) => tax.tax_type_id === data.tax_type_id)?.tax_value || na

  const getDocumentDate = () => {
    if (conceptsWhereDateIsHidden.includes(data.document_concept_id)) {
      if (data.document_concept_id === '5') {
        return (data.specificData[0] as DocumentLineTypes.FuelSpecificType)
          .fuelling_date
      }

      if (data.document_concept_id === '13') {
        return (data.specificData[0] as DocumentLineTypes.TollsSpecificType).toll_entry
      }

      if (data.document_concept_id === '4') {
        return (data.specificData[0] as DocumentLineTypes.FineSpecificType)
          .infringement_date
      }

      if (data.document_concept_id === '6') {
        return (data.specificData[0] as DocumentLineTypes.IncidentsSpecificType)
          .incident_date
      }
    }

    return data.document_date
  }

  const documentDate = getDocumentDate()

  return (
    <Document>
      <Page
        size="A4"
        style={pdfStyles().page}
      >
        <View>
          <Image
            style={pdfStyles().mfLogo}
            src={MFLogo}
          />
          <Text style={pdfStyles().documentDate}>{`${ctIntl.formatMessage({
            id: 'Created on',
          })}: ${DateTime.fromJSDate(new Date()).toFormat('D t')}`}</Text>
        </View>
        <View style={pdfStyles().documentInfoSection}>
          <Text style={pdfStyles().documentInfoSectionParagraph}>
            {`${ctIntl.formatMessage({
              id: 'Document Type',
            })}: ${ctIntl.formatMessage({ id: documentType })}`}
          </Text>
          <Text style={pdfStyles().documentInfoSectionParagraph}>
            {`${ctIntl.formatMessage({
              id: 'Document Status',
            })}: ${ctIntl.formatMessage({ id: documentStatus })}`}
          </Text>
        </View>
        {dividerView('Accounting Details')}
        <View style={pdfStyles().stackView}>
          {valueView('Document Number', data.document_number || na)}
          {valueView(
            'mifleet.imports.mapping.match.field.tax_deductable_value',
            data.tax_deductable_value || na,
          )}
          {valueView('NDED Tax', data.tax_non_deductable_value || na)}
        </View>
        <View style={pdfStyles().stackView}>
          {valueView(
            'mifleet.imports.manual.cost.date',
            documentDate
              ? DateTime.fromJSDate(new Date(documentDate)).toFormat('D t')
              : na,
          )}
          {valueView('Supplier', supplier)}
          {valueView('Vehicle', vehicle)}
        </View>
        <View style={pdfStyles().stackView}>
          {valueView('Description', data.description || na)}
          {valueView('Accounting Chart Nr', data.accounting_chart_number || na)}
        </View>
        <View style={pdfStyles().stackView}>
          {valueView('Quantity', data.quantity || na)}
          {valueView('Price', data.price || na)}
          {valueView('Discount Value', data.discount || na)}
        </View>
        <View style={pdfStyles().stackView}>
          {valueView('Net Value', data.net_value || na)}
          {valueView('Tax Type', `${taxTypeLabel} - (${taxTypeValue})`)}
          {valueView('Total Value', data.total_value || na)}
        </View>
        {data.document_concept_id !== '3' &&
          data.document_concept_id !== '7' &&
          dividerView('Concept Management Details')}
        {getConceptDataForm(data.document_concept_id, data.specificData[0], arrayTypes)}
      </Page>
    </Document>
  )
}

const getConceptDataForm = (
  conceptId: string,
  concept: FixMeAny,
  arrayTypes: FetchMiFleetArrayTypes.ParsedReturn,
) => {
  if (!concept) {
    return null
  }

  const {
    activeFuelCards,
    allowedDrivers,
    fuelTransactionTypes,
    fineTypes,
    tireOperations,
    tireLocations,
    maintenanceTypes,
    incidentTypes,
    accessoryTypes,
    breakdownTypes,
    permitTypes,
    cleaningTypes,
    taxTypes,
    driverPermitTypes,
    driverCostTypes,
    oilTypes,
    consumableTypes,
    rentalCostTypes,
    leasingCostTypes,
  } = arrayTypes

  switch (conceptId) {
    case '1': {
      // ACCESSORY
      const accessoryType =
        accessoryTypes.find(
          (accessory) =>
            accessory.vehicle_accessory_type_id === concept.vehicle_accessory_type_id,
        )?.label || na

      return (
        <View style={pdfStyles().stackView}>
          {valueView('Accessory Type', accessoryType)}
          {valueView('Description', concept.description || na)}
        </View>
      )
    }
    case '2': {
      // BREAKDOWN

      const breakdownType =
        breakdownTypes.find(
          (breakdown) =>
            breakdown.vehicle_breakdown_type_id === concept.vehicle_breakdown_type_id,
        )?.label || na

      const driver =
        allowedDrivers.find((driver) => driver.driver_id === concept.driver_id)
          ?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Breakdown Type', breakdownType)}
            {valueView('Driver', driver)}
            {valueView('Aditional Notes', concept.description || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView(
              'Towing Date Start',
              concept.towing_start_date
                ? DateTime.fromJSDate(new Date(concept.towing_start_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
            {valueView(
              'Towing Date Deliver',
              concept.towing_delivery_date
                ? DateTime.fromJSDate(new Date(concept.towing_delivery_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
            {valueView('Towing Description', concept.towing_description || na)}
          </View>
        </>
      )
    }
    case '3': {
      // FINANCING
      return null
    }
    case '4': {
      // FINES

      const fineType =
        fineTypes.find(
          (fine) => fine.vehicle_fine_type_id === concept.vehicle_fine_type_id,
        )?.label || na

      const driver =
        allowedDrivers.find((driver) => driver.driver_id === concept.driver_id)
          ?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Fine Type', fineType)}
            {valueView('Driver', driver)}
            {valueView(
              'Infringement Date',
              concept.infringement_date
                ? DateTime.fromJSDate(new Date(concept.infringement_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Infringement Number', concept.infringement_number || na)}
            {valueView('Infringement Location', concept.infringement_location || na)}
            {valueView('Infringement Description', concept.infringement || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView(
              'Payment Due Date',
              concept.payment_due_date
                ? DateTime.fromJSDate(new Date(concept.payment_due_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '5': {
      //FUEL
      const fuelCard =
        activeFuelCards.find(
          (card) => card.contract_fuel_card_id === concept.contract_fuel_card_id,
        )?.label || na

      const driver =
        allowedDrivers.find((driver) => driver.driver_id === concept.driver_id)
          ?.label || na

      const transactionType =
        fuelTransactionTypes.find(
          (transaction) =>
            transaction.fuel_transaction_type_id === concept.fuel_transaction_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Fuel Card', fuelCard)}
            {valueView('Driver', driver)}
            {valueView('Transaction Type', transactionType)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Fuel Station', concept.fuelling_station || na)}
            {valueView(
              'Fuel Transaction Date',
              concept.fuelling_date
                ? DateTime.fromJSDate(new Date(concept.fuelling_date)).toFormat('D t')
                : na,
            )}
            {valueView('Fuelling Order', concept.fuelling_order || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Odometer Reading', concept.fuelling_date_mileage || na)}
            {valueView('Additional Notes', concept.description || na)}
            {valueView(
              'Tank Full?',
              ctIntl.formatMessage({ id: isTrue(concept.is_tank_full) ? 'Yes' : 'No' }),
            )}
          </View>
        </>
      )
    }
    case '6': {
      // ACCIDENTS

      const accidentType =
        incidentTypes.find(
          (incident) =>
            incident.vehicle_incident_type_id === concept.vehicle_incident_type_id,
        )?.label || na

      const driver =
        allowedDrivers.find((driver) => driver.driver_id === concept.driver_id)
          ?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Accident Type', accidentType)}
            {valueView('Driver', driver)}
            {valueView('Location of Accident', concept.place || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView(
              'Accident Date',
              concept.incident_date
                ? DateTime.fromJSDate(new Date(concept.incident_date)).toFormat('D t')
                : na,
            )}
            {valueView('Accident Loss Value', concept.incident_loss_value || na)}
            {valueView(
              'Has Recoveries?',
              ctIntl.formatMessage({
                id: isTrue(concept.has_recoveries) ? 'Yes' : 'No',
              }),
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Case/Process Number', concept.process_number || na)}
            {valueView('Insurance Claim Number', concept.claim_number || na)}
            {valueView(
              'Approved Claim',
              ctIntl.formatMessage({
                id: isTrue(concept.approved_claim) ? 'Yes' : 'No',
              }),
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Detailed Accident Description', concept.description || na)}
          </View>
          {concept.thirdParties.length > 0 && dividerView('Third Parties Involved')}
          {concept.thirdParties.length > 0 && (
            <View style={pdfStyles().stackView}>
              {concept.thirdParties.map((tp: FixMeAny) => [
                valueView('Name', tp.third_party_name || na, true),
                valueView('Identification', tp.identification || na, true),
                valueView('Contact', tp.contact || na, true),
                valueView('Notes', tp.notes || na, true),
              ])}
            </View>
          )}
        </>
      )
    }
    case '7': {
      // INSURANCE
      return null
    }
    case '8': {
      // MAINTENANCE

      const maintenanceType =
        maintenanceTypes.find(
          (maintenance) =>
            maintenance.vehicle_maintenance_type_id ===
            concept.vehicle_maintenance_type_id,
        )?.label || na

      const driver =
        allowedDrivers.find((driver) => driver.driver_id === concept.driver_id)
          ?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Maintenance Type', maintenanceType)}
            {valueView(
              'Maintenance Date',
              concept.maintenance_date
                ? DateTime.fromJSDate(new Date(concept.maintenance_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
            {valueView('Maintenance Budget', concept.budget || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Vehicle MMV (Make/Model/Variant)', concept.vehicle_mmv || na)}
            {valueView('Job Card Reference', concept.job_card_reference || na)}
            {valueView('Driver', driver)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Fleet Controller', concept.fleet_controller || na)}
            {valueView('Odometer Reading', concept.mileage || na)}
          </View>
        </>
      )
    }
    case '9': {
      // PERMIT

      const permitType =
        permitTypes.find(
          (permit) => permit.vehicle_permit_type_id === concept.vehicle_permit_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Vehicle Licenses', permitType)}
            {valueView(
              'Begin Date',
              concept.begin_date
                ? DateTime.fromJSDate(new Date(concept.begin_date)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Expiration Date',
              concept.expiration_date
                ? DateTime.fromJSDate(new Date(concept.expiration_date)).toFormat('D t')
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Begin Odometer', concept.begin_odometer || na)}
            {valueView('End Odometer', concept.end_odometer || na)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '10': {
      // PURCHASE

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView(
              'Registration Date',
              concept.registration_date
                ? DateTime.fromJSDate(new Date(concept.registration_date)).toFormat(
                    'D t',
                  )
                : na,
            )}
            {valueView('Mileage at Purchase', concept.mileage_purchase || na)}
            {valueView('Retail Price', concept.retail_price || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Accessories Price', concept.accessories_price || na)}
            {valueView('Discount', concept.discount || na)}
            {valueView('Administration Tax', concept.administration_tax || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Registration Tax', concept.registration_tax || na)}
            {valueView('Depreciation Tax', concept.depreciation_tax || na)}
            {valueView('Estimated Lifetime', concept.estimated_lifetime || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Residual Value', concept.residual_value || na)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '11': {
      // CLEANING

      const cleaningType =
        cleaningTypes.find(
          (cleaning) =>
            cleaning.vehicle_cleaning_type_id === concept.vehicle_cleaning_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Cleaning Type', cleaningType)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '12': {
      // TAX

      const taxType =
        taxTypes.find((tax) => tax.tax_type_id === concept.tax_type_id)?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Tax Type', taxType)}
            {valueView(
              'Begin Date',
              concept.begin_date
                ? DateTime.fromJSDate(new Date(concept.begin_date)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Expiration Date',
              concept.expiration_date
                ? DateTime.fromJSDate(new Date(concept.expiration_date)).toFormat('D t')
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '13': {
      // TOLLS

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Passage Name', concept.passage_name || na)}
            {valueView('Description', concept.description || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView(
              'Toll Entry',
              concept.toll_entry
                ? DateTime.fromJSDate(new Date(concept.toll_entry)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Toll Exit',
              concept.toll_exit
                ? DateTime.fromJSDate(new Date(concept.toll_exit)).toFormat('D t')
                : na,
            )}
          </View>
        </>
      )
    }

    case '14': {
      // TYRES

      const tyreOperation =
        tireOperations.find(
          (tire) =>
            tire.vehicle_tyre_operation_id === concept.vehicle_tyre_operation_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Tire Operation', tyreOperation)}
            {valueView(
              'mifleet.imports.manual.concept.tyres.operation.date',
              concept.tyre_date
                ? DateTime.fromJSDate(new Date(concept.tyre_date)).toFormat('D t')
                : na,
            )}
            {valueView('Description', concept.description || na)}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Odometer Reading', concept.mileage || na)}
          </View>
          {concept.tires.length > 0 &&
            dividerView('mifleet.delete.listdata.item.error.tyre')}
          {concept.tires.length > 0 && (
            <View style={pdfStyles().stackView}>
              {concept.tires.map((tireLocal: FixMeAny) => {
                const tyreLocation =
                  tireLocations.find(
                    (tire) =>
                      tire.vehicle_tyre_location_id ===
                      tireLocal.vehicle_tyre_location_id,
                  )?.label || na

                return [
                  valueView('Tire Location', tyreLocation, true),
                  valueView('Brand', tireLocal.brand || na, true),
                  // eslint-disable-next-line
                  // eslint-disable-next-line unicorn/explicit-length-check
                  valueView('Size', tireLocal.size || na, true),
                  valueView('Code', tireLocal.code || na, true),
                ]
              })}
            </View>
          )}
        </>
      )
    }
    case '15': {
      // DRIVER PERMIT

      const licenseType =
        driverPermitTypes.find(
          (permit) => permit.driver_permit_type_id === concept.driver_permit_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('License Type', licenseType)}
            {valueView(
              'Begin Date',
              concept.begin_date
                ? DateTime.fromJSDate(new Date(concept.begin_date)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Expiration Date',
              concept.expiration_date
                ? DateTime.fromJSDate(new Date(concept.expiration_date)).toFormat('D t')
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('License Number', concept.permit_number || na)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '16': {
      // DRIVER COST

      const driverCostType =
        driverCostTypes.find(
          (cost) => cost.driver_cost_type_id === concept.driver_cost_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Cost Type', driverCostType)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '17': {
      // OIL

      const oilType =
        oilTypes.find((oil) => oil.vehicle_oil_type_id === concept.vehicle_oil_type_id)
          ?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Oil Type', oilType)}
            {valueView(
              'Fill in Date',
              concept.fill_in_date
                ? DateTime.fromJSDate(new Date(concept.fill_in_date)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Validation Date',
              concept.validation_date
                ? DateTime.fromJSDate(new Date(concept.validation_date)).toFormat('D t')
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '18': {
      // CONSUMABLE

      const consumableType =
        consumableTypes.find(
          (consumable) =>
            consumable.vehicle_consumable_type_id ===
            concept.vehicle_consumable_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Consumable Type', consumableType)}
            {valueView(
              'Fill in Date',
              concept.fill_in_date
                ? DateTime.fromJSDate(new Date(concept.fill_in_date)).toFormat('D t')
                : na,
            )}
            {valueView(
              'Validation Date',
              concept.validation_date
                ? DateTime.fromJSDate(new Date(concept.validation_date)).toFormat('D t')
                : na,
            )}
          </View>
          <View style={pdfStyles().stackView}>
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '19': {
      // RENTAL COST

      const rentalCostType =
        rentalCostTypes.find(
          (cost) =>
            cost.vehicle_rental_cost_type_id === concept.vehicle_rental_cost_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Rental Cost Types', rentalCostType)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    case '20': {
      // LEASING COST

      const leasingCostType =
        leasingCostTypes.find(
          (cost) =>
            cost.vehicle_leasing_cost_type_id === concept.vehicle_leasing_cost_type_id,
        )?.label || na

      return (
        <>
          <View style={pdfStyles().stackView}>
            {valueView('Leasing Cost Type', leasingCostType)}
            {valueView('Description', concept.description || na)}
          </View>
        </>
      )
    }
    default: {
      return null
    }
  }
}

const createPDFFromCost = async (
  data: ReadMiFleetCostQueryData,
  arrayTypes: FetchMiFleetArrayTypes.ParsedReturn,
) => {
  const blob = await pdf(
    <CostPDF
      data={data}
      arrayTypes={arrayTypes}
    />,
  ).toBlob()

  saveAs(
    blob,
    `${ctIntl.formatMessage({
      id: getConceptIdentifierBy(data.document_concept_id, 'id'),
    })}_${DateTime.fromJSDate(new Date()).toFormat('D_hhmm')}`,
  )
}

export default createPDFFromCost
