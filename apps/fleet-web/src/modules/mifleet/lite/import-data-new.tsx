import { useState } from 'react'
import { Box, Drawer, Skeleton, Tab, Tabs } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { NewTitleBlock } from '../components/fraudValidation/styled'
import useMiFleetArrayTypes from './api/useMiFleetArrayTypes'
import CaptureData from './capture-data'
import ImportsDash from './imports'

type TabPanelProps = {
  children?: React.ReactNode
  index: number
  value: number
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ height: '100%', overflowY: 'scroll' }}
      {...other}
    >
      <Box sx={{ height: '100%' }}>{children}</Box>
    </div>
  )
}

type Props = {
  activeTab: 'add' | 'import'
  onClose: () => void
  forceMenu: Array<{ name: string; id: string }>
  isSuccessCreation: () => void
}

const a11yProps = (index: number) => ({
  id: `simple-tab-${index}`,
  'aria-controls': `simple-tabpanel-${index}`,
})

const MiFleetImportData = ({
  activeTab,
  onClose,
  forceMenu,
  isSuccessCreation,
}: Props) => {
  const [tabsValue, setTabsValue] = useState(() =>
    activeTab.includes('import') ? 1 : 0,
  )

  const {
    isPending,
    data: arrayTypes,
    refetch: refetchArrayType,
  } = useMiFleetArrayTypes()

  const handleChange = (_event: React.SyntheticEvent, newValue: number) =>
    setTabsValue(newValue)

  return (
    <Drawer
      open
      onClose={onClose}
      anchor="right"
      sx={{ '.MuiDrawer-paper': { width: '800px', padding: '24px 0 85px' } }}
    >
      <NewTitleBlock
        onClose={onClose}
        title={ctIntl.formatMessage(
          { id: 'mifleet.costs.add.header' },
          {
            values: {
              method: ctIntl.formatMessage({
                id: tabsValue === 1 ? 'Import' : 'Add',
              }),
              concept: ctIntl.formatMessage({
                id: forceMenu[0].name.includes('Multi Cost')
                  ? 'Multi'
                  : forceMenu[0].name,
              }),
            },
          },
        )}
      />
      <Box
        sx={{ borderBottom: 1, borderColor: 'divider' }}
        m={3}
        mt={0}
      >
        <Tabs
          value={tabsValue}
          onChange={handleChange}
          aria-label="import tabs"
        >
          <Tab
            label={ctIntl.formatMessage({ id: 'add single transaction' })}
            {...a11yProps(0)}
          />
          <Tab
            label={ctIntl.formatMessage({ id: 'bulk import' })}
            {...a11yProps(1)}
          />
        </Tabs>
      </Box>

      <TabPanel
        value={tabsValue}
        index={0}
      >
        {isPending ? (
          <Box sx={{ mr: 2, ml: 2 }}>
            <Skeleton
              variant="text"
              animation="wave"
              sx={{ fontSize: '1rem', mb: 6 }}
            />
            <Skeleton
              sx={{ mb: 4 }}
              variant="rounded"
              animation="wave"
              height={60}
            />
            <Skeleton
              sx={{ mb: 4 }}
              variant="rounded"
              animation="wave"
              height={60}
            />
            <Skeleton
              sx={{ mb: 4 }}
              variant="rounded"
              animation="wave"
              height={60}
            />
          </Box>
        ) : (
          <CaptureData
            defaultConcept={forceMenu[0]}
            onClose={onClose}
            isSuccessCreation={isSuccessCreation}
            arrayTypes={arrayTypes}
            refetchArrayType={refetchArrayType}
          />
        )}
      </TabPanel>
      <TabPanel
        value={tabsValue}
        index={1}
      >
        <ImportsDash
          defaultConcept={forceMenu}
          onClose={onClose}
        />
      </TabPanel>
    </Drawer>
  )
}

export default MiFleetImportData
