import { useMemo, useState } from 'react'
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  DataGrid,
  GridPagination,
  LinearProgress,
  Modal,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  use<PERSON><PERSON>backB<PERSON>ed,
  useDataGridColumnHelper,
  type <PERSON><PERSON><PERSON><PERSON>,
  type GridColDef,
  type GridPaginationModel,
  type GridRowParams,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import HistoryIcon from '@mui/icons-material/History'
import styled from 'styled-components'
import { match, P } from 'ts-pattern'

import ConfirmationModal from '@fleet-web/components/_modals/Confirmation'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user-sensitive-selectors'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import translateMiFleetImportError from '@fleet-web/modules/importer/utils/translate-mifleet-import-error'
import calculateTableRowCount from '@fleet-web/modules/mifleet/components/utils/calculate-table-row-count'
import { CustomPagination } from '@fleet-web/modules/mifleet/shared/footer-dataGrid'
import MifleetContainer from '@fleet-web/modules/mifleet/shared/mifleetContainer'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { isTrue } from '@fleet-web/util-functions/validation'

import AddNewCost from '../../../lite/import-data-new'
import {
  useDeleteMiFleetImport,
  useDownloadImportErrors,
  useListAllImportStatus,
  useReadSingleMiFleetImport,
  type AllStatusReturnItemType,
  type ImportType,
} from '../../api/useMiFleetImports'

type TabType = 'active-imports' | 'deleted-imports'

const defaultParamsTable = {
  page: 0,
  pageSize: calculateTableRowCount([], 150) || 10,
}

const ImportsHistory = () => {
  const { isAdmin } = useTypedSelector(getSettings_UNSAFE)
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [activeDetailImport, setActiveDetailImport] = useState<string | undefined>(
    undefined,
  )
  const [importHistoryTab, setImportHistoryTab] = useState<TabType>('active-imports')

  const [isAddCostModalOpen, setIsAddCostModalOpen] = useState(false)
  const [dateFilter, setDateFilter] = useState<DateRange<FixMeAny>>([null, null])
  const [importIdToDelete, setImportIdToDelete] = useState<string | undefined>(
    undefined,
  )

  const { data: importsListData, isPending } = useListAllImportStatus()
  const columnHelper = useDataGridColumnHelper<AllStatusReturnItemType>({
    filterMode: 'client',
  })

  const deleteImportFile = useDeleteMiFleetImport()

  const handleRowClick = (rowInfo: GridRowParams<AllStatusReturnItemType>) =>
    Number(rowInfo.row.total_of_failed_import) > 0 &&
    setActiveDetailImport(String(rowInfo.id))

  const columnsGetters = useMemo(
    () => ({
      importId: (i: AllStatusReturnItemType) => i.import_id,
      filename: (i: AllStatusReturnItemType) => i.filename,
      dateImported: (i: AllStatusReturnItemType) =>
        i.start_time ? new Date(i.start_time) : null,
      dateFinished: (i: AllStatusReturnItemType) =>
        i.end_time ? new Date(i.end_time) : null,
      percentage: (i: AllStatusReturnItemType) => Number(i.import_percentage),
      fileStatus: (i: AllStatusReturnItemType) =>
        match([isTrue(i.status), i.error_message])
          .with([true, P._], () => ({ status: 'Completed', color: 'success' as const }))
          .with([false, P.union(P.nullish, '')], () => ({
            status: 'Pending',
            color: 'warning' as const,
          }))
          .with([false, P.nonNullable], () => ({
            status: 'Failed',
            color: 'error' as const,
          }))
          .exhaustive(),
      totalRecords: (i: AllStatusReturnItemType) =>
        Number(i.total_of_failed_import) + Number(i.total_sucessfully_imported),
      failedRecords: (i: AllStatusReturnItemType) => Number(i.total_of_failed_import),
      successRecords: (i: AllStatusReturnItemType) =>
        Number(i.total_sucessfully_imported),
    }),
    [],
  )

  const columns = useMemo((): Array<GridColDef<AllStatusReturnItemType>> => {
    const adminSpecificColumns = [
      columnHelper.string((_, row) => columnsGetters.importId(row), {
        field: 'import_id',
        headerName: ctIntl.formatMessage({
          id: 'Revert',
        }),
        flex: 1,
        align: 'center',
        headerAlign: 'left',
        renderCell: ({ row }) => {
          const statusObj = columnsGetters.fileStatus(row)

          if (statusObj.status === 'Pending' || statusObj.status === 'Failed') {
            return (
              <Tooltip
                title={
                  statusObj.status === 'Pending'
                    ? 'The import is not finished yet'
                    : 'The import failed'
                }
                PopperProps={{ disablePortal: true }}
              >
                <div>
                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<HistoryIcon />}
                    disabled
                    onClick={() => {}}
                    size="small"
                  >
                    {ctIntl.formatMessage({ id: 'Revert' })}
                  </Button>
                </div>
              </Tooltip>
            )
          }

          return (
            <Button
              variant="outlined"
              color="warning"
              startIcon={<HistoryIcon />}
              onClick={(e) => {
                e.stopPropagation()
                setImportIdToDelete(row.import_id)
              }}
              size="small"
            >
              {ctIntl.formatMessage({ id: 'Revert' })}
            </Button>
          )
        },
      }),
    ]

    const columnsArray = [
      columnHelper.string((_, row) => columnsGetters.filename(row) ?? '', {
        field: 'filename',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.filename',
        }),
        flex: 1,
      }),
      columnHelper.dateTime({
        field: 'Date Imported',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.date.imported',
        }),
        valueGetter: (_, row) => columnsGetters.dateImported(row),
        flex: 1,
      }),
      columnHelper.dateTime({
        field: 'Date Completed',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.date.completed',
        }),
        valueGetter: (_, row) => columnsGetters.dateFinished(row),
        flex: 1,
      }),
      columnHelper.number((_, row) => columnsGetters.percentage(row), {
        field: 'percentage',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.percentage',
        }),
        valueFormatter: (_, row) => columnsGetters.percentage(row) + '%',
        flex: 1,
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.singleSelect((_, row) => columnsGetters.fileStatus(row).status, {
        field: 'status',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.file.upload',
        }),
        align: 'center',
        valueOptions: [
          {
            label: ctIntl.formatMessage({ id: 'Completed' }),
            value: 'Completed',
          },
          {
            label: ctIntl.formatMessage({ id: 'Pending' }),
            value: 'Pending',
          },
          {
            label: ctIntl.formatMessage({ id: 'Failed' }),
            value: 'Failed',
          },
        ],
        renderCell: ({ row }) => {
          const statusObj = columnsGetters.fileStatus(row)

          const tooltipMessage = () => {
            switch (statusObj.status) {
              case 'Pending':
                return 'The import is not finished yet'
              case 'Failed':
                return row.error_message
              case 'Completed':
              default:
                return 'Completed'
            }
          }

          return (
            <Stack>
              <Tooltip
                title={tooltipMessage()}
                PopperProps={{ disablePortal: true }}
              >
                <Chip
                  label={statusObj.status}
                  color={statusObj.color}
                  size="small"
                  variant="filled"
                />
              </Tooltip>
              {statusObj.status === 'Failed' && row.error_message && (
                <Typography
                  variant="caption"
                  sx={(theme) => ({
                    color: theme.palette.text.secondary,
                  })}
                >
                  {ctIntl.formatMessage({ id: row.error_message })}
                </Typography>
              )}
            </Stack>
          )
        },
        flex: 1,
      }),
      columnHelper.number((_, row) => columnsGetters.successRecords(row), {
        field: 'total_sucessfully_imported',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.records.success',
        }),
        flex: 1,
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.number((_, row) => columnsGetters.failedRecords(row), {
        field: 'total_of_failed_import',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.records.error',
        }),
        flex: 1,
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.number((_, row) => columnsGetters.totalRecords(row), {
        field: 'totals',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.columns.records.total',
        }),
        flex: 1,
        align: 'right',
        headerAlign: 'left',
      }),
      ...(isAdmin && importHistoryTab === 'active-imports' ? adminSpecificColumns : []),
    ]

    return columnsArray
  }, [columnHelper, columnsGetters, importHistoryTab, isAdmin])

  const memoHistoryData =
    useMemo(() => {
      const items = importsListData?.imports
      const startDate = new Date(dateFilter[0]).getTime()
      const endDate = new Date(dateFilter[1])

      return items
        ?.filter((item) => {
          const currentDate = new Date(item.start_time).getTime()
          return (
            currentDate >= startDate &&
            //second date has to be till end of date
            (endDate.getTime()
              ? currentDate <= endDate.setHours(23, 59, 59, 999)
              : true)
          )
        })
        .filter((item) =>
          importHistoryTab === 'deleted-imports'
            ? isTrue(item.is_deleted)
            : !isTrue(item.is_deleted),
        )
    }, [dateFilter, importHistoryTab, importsListData?.imports]) || []

  const handleChangeTab = (_: React.SyntheticEvent, newValue: TabType) => {
    setImportHistoryTab(newValue)
  }

  return (
    <MifleetContainer title="mifleet.imports.history.title">
      {importIdToDelete && (
        <ConfirmationModal
          title="mifleet.imports.history.confirmation.title"
          open
          onClose={() => setImportIdToDelete(undefined)}
          onConfirm={() => {
            deleteImportFile.mutate({ import_id: importIdToDelete })
            setImportIdToDelete(undefined)
          }}
          isLoading={deleteImportFile.isPending}
        >
          {ctIntl.formatMessage({ id: 'imports.modal.deleteImport.message' })}
        </ConfirmationModal>
      )}
      <InnerRowComponent
        importId={activeDetailImport}
        onClose={() => setActiveDetailImport(undefined)}
      />
      <Tabs
        value={importHistoryTab}
        onChange={handleChangeTab}
      >
        <Tab
          value={'active-imports'}
          label={ctIntl.formatMessage({ id: 'Active Imports' })}
          id="active-imports-tab"
        />
        <Tab
          value={'deleted-imports'}
          label={ctIntl.formatMessage({ id: 'Reverted Imports' })}
          id="deleted-imports-tab"
        />
      </Tabs>
      <TableWrapper mainContainer={true}>
        <UserDataGridWithSavedSettingsOnIDB
          Component={DataGrid}
          dataGridId="importHistoryList"
          pagination
          loading={isPending}
          rows={memoHistoryData}
          columns={columns}
          getRowId={useCallbackBranded(
            (row: (typeof memoHistoryData)[number]) => row.import_id,
            [],
          )}
          onRowClick={handleRowClick}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            pagination: CustomPagination,
          }}
          slotProps={{
            filterPanel: { columnsSort: 'asc' },
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
                filterButton: { show: true },
                dateRangePicker: {
                  show: true,
                  props: {
                    value: dateFilter,
                    onChange: setDateFilter,
                  },
                },
              },
              extraContent: {
                right: (
                  <>
                    <Button
                      variant="outlined"
                      color="secondary"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        setNewCostActiveTab('add')
                        setIsAddCostModalOpen(true)
                      }}
                      sx={{ mr: 1 }}
                      size="small"
                    >
                      {ctIntl.formatMessage({ id: 'Add Cost' })}
                    </Button>
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<FileUploadOutlinedIcon />}
                      onClick={() => {
                        setNewCostActiveTab('import')
                        setIsAddCostModalOpen(true)
                      }}
                      size="small"
                    >
                      {ctIntl.formatMessage({ id: 'mifleet.imports.new.import' })}
                    </Button>
                  </>
                ),
              },
            }),
          }}
        />
      </TableWrapper>
      {/*Importing */}

      {isAddCostModalOpen && (
        <AddNewCost
          activeTab={newCostActiveTab}
          isSuccessCreation={() => setIsAddCostModalOpen(false)}
          onClose={() => setIsAddCostModalOpen(false)}
          forceMenu={[{ name: '', id: 'multicost' }]}
        />
      )}
    </MifleetContainer>
  )
}

const InnerRowComponent = ({
  importId,
  onClose,
}: {
  importId: string | undefined
  onClose: () => void
}) => {
  const [tableDetailParams, setTableDetailParams] = useState<{
    page: number
    pageSize: number
  }>(defaultParamsTable)

  const { data: importDetailedData, isPending } = useReadSingleMiFleetImport({
    import_id: importId,
    start: tableDetailParams.page,
    limit: tableDetailParams.pageSize,
  })

  const downloadErrors = useDownloadImportErrors()

  const columnsGetters = useMemo(
    () => ({
      row: (i: ImportType.ImportHistoryDetailReturn['result_json'][number]) =>
        i.row_number,
      error: (i: ImportType.ImportHistoryDetailReturn['result_json'][number]) =>
        i.error_message,
    }),
    [],
  )

  const columns = useMemo(
    (): Array<
      GridColDef<ImportType.ImportHistoryDetailReturn['result_json'][number]>
    > => [
      {
        field: 'row_number',
        headerName: ctIntl.formatMessage({
          id: 'mifleet.imports.history.detail.error.row',
        }),
        valueGetter: (_, row) => columnsGetters.row(row),
        renderCell: (params) => (
          <Typography>{columnsGetters.row(params.row)}</Typography>
        ),
        flex: 0.15,
      },
      {
        field: 'error_message',
        headerName: ctIntl.formatMessage({ id: 'Error' }),
        valueGetter: (_, row) => columnsGetters.error(row),
        renderCell: (params) => (
          <Typography>
            {translateMiFleetImportError(columnsGetters.error(params.row))}
          </Typography>
        ),
        flex: 1,
      },
    ],
    [columnsGetters],
  )

  const handlePaginationDetail = (model: GridPaginationModel) => {
    const { page, pageSize } = model
    setTableDetailParams({
      page: page,
      pageSize: pageSize,
    })
  }

  const rows = importDetailedData?.result_json || []
  const getRowId = useCallbackBranded(
    (row: (typeof rows)[number]) => row.row_number,
    [],
  )

  return (
    <Modal
      open={Boolean(importId)}
      onClose={() => {
        onClose()
        setTableDetailParams({ ...tableDetailParams, page: defaultParamsTable.page })
      }}
    >
      <InnerRowComponentWrapper>
        {isPending ? (
          <CircularProgress />
        ) : (
          <ErrorsTableContainer>
            <Typography>{`${ctIntl.formatMessage({
              id: 'File Uploaded',
            })}: ${importDetailedData?.filename}`}</Typography>
            <TableWrapper>
              <UserDataGridWithSavedSettingsOnIDB
                disableRowSelectionOnClick
                Component={DataGrid}
                dataGridId="importHistoryDetail"
                pagination
                rows={rows}
                columns={columns}
                autoPageSize
                getRowId={getRowId}
                paginationMode="server"
                onPaginationModelChange={handlePaginationDetail}
                rowCount={importDetailedData?.total_of_failed_import || 0}
                initialState={{ pagination: { paginationModel: tableDetailParams } }}
                slots={{
                  footer: () => (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        pl: 2,
                        pr: 2,
                      }}
                    >
                      <Button
                        variant="outlined"
                        color="info"
                        startIcon={<FileDownloadOutlinedIcon />}
                        onClick={() =>
                          downloadErrors.mutate({
                            import_id: Number(importId),
                            filename:
                              importDetailedData?.filename || 'MiFleetImportErrors',
                          })
                        }
                      >
                        {ctIntl.formatMessage({ id: 'Download All' })}
                      </Button>
                      <GridPagination />
                    </Box>
                  ),
                }}
              />
            </TableWrapper>
          </ErrorsTableContainer>
        )}
      </InnerRowComponentWrapper>
    </Modal>
  )
}

export default ImportsHistory

const TableWrapper = styled.div<{ mainContainer?: boolean }>`
  width: 100%;
  height: calc(100% - 40px);
`

const InnerRowComponentWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 70%;
  background-color: white;
  border-radius: 10px;
  padding: 10px;
`
const ErrorsTableContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`
