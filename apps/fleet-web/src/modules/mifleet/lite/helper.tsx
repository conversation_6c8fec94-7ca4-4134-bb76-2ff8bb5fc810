import { useMemo } from 'react'
import { DateTime } from 'luxon'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { DOCUMENT_STATUS_OPTIONS } from '../api/costInput/shared/types'
import {
  DOCUMENT_CONCEPT_FINE,
  DOCUMENT_CONCEPT_FUELLING,
  DOCUMENT_CONCEPT_INCIDENT,
  DOCUMENT_CONCEPT_MAINTENANCE,
  DOCUMENT_CONCEPT_TIRE,
  DOCUMENT_CONCEPT_TOLL,
} from '../components/documents/concept-types'

export const costCategory = [
  {
    name: 'Fuel',
    label: 'Fuel',
    id: DOCUMENT_CONCEPT_FUELLING,
  },
  {
    name: 'Tolls',
    label: 'Tolls',
    id: DOCUMENT_CONCEPT_TOLL,
  },
  {
    name: 'Fines',
    label: 'Fines',
    id: DOCUMENT_CONCEPT_FINE,
  },
  {
    name: '<PERSON><PERSON>',
    label: 'Ty<PERSON>',
    id: DOCUMENT_CONCEPT_TIRE,
  },
  {
    name: 'Maintenances',
    label: 'Maintenances',
    id: DOCUMENT_CONCEPT_MAINTENANCE,
  },
  {
    name: 'Accidents',
    label: 'Accidents',
    id: DOCUMENT_CONCEPT_INCIDENT,
  },
  {
    name: 'Multi Cost',
    label: 'Multi Cost',
    id: 'multicost',
  },
  {
    name: 'Contracts',
    label: 'Contracts',
    id: 'contracts',
  },
]

// We generate this in hook instead of a normal const export to make sure luxon has been initialized with the correct timezone and locale already
export const useDefaultRangeFilterInNumber = (): [number, number] =>
  useMemo(
    () => [
      DateTime.local().minus({ days: 60 }).toSeconds(),
      DateTime.local().toSeconds(),
    ],
    [],
  )

export const DocumentStatusOptions = () =>
  DOCUMENT_STATUS_OPTIONS.map((option) => ({
    value: option.document_status_id,
    label: ctIntl.formatMessage({ id: option.label }),
  }))
