import { useState } from 'react'
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined'
import WorkspacesOutlinedIcon from '@mui/icons-material/WorkspacesOutlined'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import type { MifleetReportReferredName } from '@fleet-web/api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from '@fleet-web/api/utils'
import { REPORTS } from '@fleet-web/modules/app/components/routes/reports'
import { exportReportSearchParamsSchema } from '@fleet-web/modules/reports/ExportReport'

import FuelFraudByCost from '../components/fraudValidation/fuel-fraud-by-cost/fuel-fraud-by-cost-lite'
import GroupedDocuments from '../operational/grouped-documents'
import Fines from '../operational/operational-fines'
import Accidents from '../operational/operational-incidents'
import Maintenance from '../operational/operational-maintenance'
import Misc from '../operational/operational-misc'
import Tires from '../operational/operational-tires'
import Toll from '../operational/operational-toll'
import MifleetContainer from '../shared/mifleetContainer'
import MenuLite from '../shared/shared-menu'
import OverviewCosts from './overview-costs'

const DashboardLite = () => {
  const history = useHistory()
  const [activeMenu, setActiveMenu] = useState<string>('dashboard')

  const viewReportClick = (key: MifleetReportReferredName) => {
    history.push(
      `${
        REPORTS.subMenusRoutes.ALL_REPORTS.path
      }/export?${buildRouteQueryStringKeepingExistingSearchParams({
        location: history.location,
        schema: exportReportSearchParamsSchema,
        searchParams: { type: 'add', id: key },
      })}`,
    )
  }

  const sharedProps = {
    viewReportClick,
  }

  return (
    <MifleetContainer title="Costs">
      <MenuLite
        onMenuClick={(menu) => setActiveMenu(menu.id)}
        activeMenuId={activeMenu}
        extraTabs={[
          {
            name: 'MISC',
            icon: <InsertDriveFileOutlinedIcon />,
            id: 'miscConcept',
          },
          {
            name: 'mifleet.costs.grouped.costs',
            icon: <WorkspacesOutlinedIcon />,
            id: 'groupedDocuments',
          },
        ]}
      />
      {match(activeMenu)
        .with('fuel', () => <FuelFraudByCost {...sharedProps} />)
        .with('tolls', () => <Toll {...sharedProps} />)
        .with('fines', () => <Fines {...sharedProps} />)
        .with('tyres', () => <Tires {...sharedProps} />)
        .with('maintenance', () => <Maintenance {...sharedProps} />)
        .with('accidents', () => <Accidents {...sharedProps} />)
        .with('miscConcept', () => <Misc />)
        .with('groupedDocuments', () => <GroupedDocuments />)
        .otherwise(() => (
          <OverviewCosts />
        ))}
    </MifleetContainer>
  )
}

export default DashboardLite
