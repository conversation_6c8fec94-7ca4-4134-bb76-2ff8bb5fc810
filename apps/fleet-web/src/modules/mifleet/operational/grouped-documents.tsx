import { useCallback, useEffect, useMemo, useState } from 'react'
import { groupBy } from 'lodash'
import {
  Box,
  DataGrid,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useCallbackBranded,
  useDataGridColumnHelper,
  type DateRange,
  type GridColDef,
  type GridRowId,
  type GridRowParams,
} from '@karoo-ui/core'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'

import {
  actions,
  miFleetOperationalSelectors,
  type OperationalGrouped,
} from '@fleet-web/duxs/mifleet/operational'
import { useDateRangeShortcutItems } from '@fleet-web/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { fetchDocumentArrayTypes } from '@fleet-web/modules/mifleet/DocumentsEdit/slice'
import { useDeleteDocumentMutation } from '@fleet-web/modules/mifleet/lite/api/useMiFleetCost'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { useMifleetFormattedNumber } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import {
  DOCUMENT_CONCEPT_FINE,
  DOCUMENT_CONCEPT_FUELLING,
  DOCUMENT_CONCEPT_INCIDENT,
  DOCUMENT_CONCEPT_MAINTENANCE,
  DOCUMENT_CONCEPT_TIRE,
  DOCUMENT_CONCEPT_TOLL,
} from '../components/documents/concept-types'
import { useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'

const { getOperationalLoading, allGroupedCosts } = miFleetOperationalSelectors

const { fetchOperationalGrouped } = actions

const DOCUMENT_CANCELLED_STATUS_ID = 5

type OperationDocument = OperationalGrouped['document_lines'][number]

const menu = (id: MifleetGeneralId) => {
  switch (id) {
    case DOCUMENT_CONCEPT_FUELLING: {
      return {
        name: 'Fuel',
        id: DOCUMENT_CONCEPT_FUELLING,
      }
    }
    case DOCUMENT_CONCEPT_TOLL: {
      return {
        name: 'Tolls',
        id: DOCUMENT_CONCEPT_TOLL,
      }
    }
    case DOCUMENT_CONCEPT_FINE: {
      return {
        name: 'Fines',
        id: DOCUMENT_CONCEPT_FINE,
      }
    }

    case DOCUMENT_CONCEPT_MAINTENANCE: {
      return {
        name: 'Maintenances',
        id: DOCUMENT_CONCEPT_MAINTENANCE,
      }
    }
    case DOCUMENT_CONCEPT_INCIDENT: {
      return {
        name: 'Accidents',
        id: DOCUMENT_CONCEPT_INCIDENT,
      }
    }
    case DOCUMENT_CONCEPT_TIRE: {
      return {
        name: 'Tyres',
        id: DOCUMENT_CONCEPT_TIRE,
      }
    }
    default: {
      return {
        name: 'Multi Cost',
        id: 'multicost',
      }
    }
  }
}

const GroupedCosts = () => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsGroupedFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <GroupedCostsContent
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const GroupedCostsContent = ({
  setCostsFilters,
  costsFilters,
}: {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const dispatch = useDispatch()
  const isLoading = useTypedSelector(getOperationalLoading)
  const operationalGrouped = useTypedSelector(allGroupedCosts)
  const columnHelper = useDataGridColumnHelper<OperationalGrouped>({
    filterMode: 'client',
  })

  const detailColumnHelper = useDataGridColumnHelper<OperationDocument>({
    filterMode: 'client',
  })

  const [selectedConcept, setSelectedConcept] = useState<{ name: string; id: string }>({
    name: 'Fuel',
    id: DOCUMENT_CONCEPT_FUELLING,
  })
  const [isEditCostModalOpen, setIsEditCostModalOpen] = useState<boolean>(false)
  const [selectedRow, setSelectedRow] = useState<OperationDocument | undefined>(
    undefined,
  )
  const [itemToDelete, setItemToDelete] = useState<OperationDocument | undefined>(
    undefined,
  )

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const [detailPanelExpandedRowIds, setDetailPanelExpandedRowIds] = useState<
    ReadonlyArray<GridRowId>
  >([])
  const detailPanelExpandedRowIdsSet = useMemo(
    () => new Set(detailPanelExpandedRowIds),
    [detailPanelExpandedRowIds],
  )

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null
    dispatch(fetchOperationalGrouped({ payload: dateObject }))
  }, [dateRangeFilter, dispatch])

  useEffect(() => {
    dispatch(fetchDocumentArrayTypes())
  }, [dispatch])

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null

    dispatch(fetchOperationalGrouped({ payload: dateObject }))
  }, [dateRangeFilter, dispatch])

  const columns = useMemo(
    (): Array<GridColDef<OperationalGrouped>> => [
      columnHelper.date({
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        field: 'date',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => DateTime.fromSQL(row.date).toJSDate(),
      }),
      columnHelper.string((_, row) => row.document_number, {
        headerName: ctIntl.formatMessage({ id: 'Document Number' }),
        field: 'document_number',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.number((_, row) => Number((row.document_lines || []).length) || 0, {
        headerName: ctIntl.formatMessage({ id: 'Costs' }),
        field: 'document_lines',
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          row.document_lines
            .map((line) => ctIntl.formatMessage({ id: line.document_status }))
            .join(', '),
        {
          headerName: ctIntl.formatMessage({ id: 'Status' }),
          field: 'document_lines_length',
          flex: 1,
          renderCell: ({ row }) => {
            const result = groupBy(row.document_lines, 'document_status_id')
            return Object.keys(result).map((key, index) => (
              <StatusValidation
                // eslint-disable-next-line react/no-array-index-key
                key={key + index}
                statusId={Number(key)}
                statusName={result[key][0].document_status}
                source="groupedCosts"
                size={result[key].length}
              />
            ))
          },
        },
      ),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        renderCell: ({ row }) => formatNumber(row.total_value),
        width: 150,
        align: 'right',
        headerAlign: 'left',
      }),
    ],
    [columnHelper, formatNumber],
  )

  const detailColumns = useMemo(
    (): Array<GridColDef<OperationDocument>> => [
      detailColumnHelper.string((_, row) => row.plate || row.driver, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle / Driver' }),
        field: 'plate',
        minWidth: 140,
        flex: 1,
      }),
      detailColumnHelper.string(
        (_, row) => ctIntl.formatMessage({ id: row.document_concept }),
        {
          headerName: ctIntl.formatMessage({ id: 'Concept' }),
          field: 'document_concept',
          flex: 1,
          minWidth: 140,
        },
      ),
      detailColumnHelper.string((_, row) => row.supplier, {
        headerName: ctIntl.formatMessage({ id: 'Supplier' }),
        field: 'supplier',
        flex: 1,
        minWidth: 140,
      }),
      detailColumnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        minWidth: 150,
        flex: 1,
      }),
      detailColumnHelper.string(
        (_, row) =>
          row.source_id && SourceFromSourceId[row.source_id]
            ? ctIntl.formatMessage({ id: SourceFromSourceId[row.source_id] })
            : '',
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          width: 150,
        },
      ),
      detailColumnHelper.string((_, row) => row.document_status, {
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        field: 'document_status',
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="subGroup"
          />
        ),
        width: 140,
      }),
      detailColumnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Total Value' }),
        field: 'total_value',
        renderCell: ({ row }) => formatNumber(row.total_value),
        width: 150,
        align: 'right',
        headerAlign: 'left',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View Transaction' })}
              arrow
            >
              <IconButton
                color="secondary"
                size="small"
                onClick={(e) => {
                  setSelectedConcept(menu(row.document_concept_id))
                  e.stopPropagation()
                  setSelectedRow(row)
                  setIsEditCostModalOpen(true)
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({ id: 'Delete Cost' })}
              arrow
            >
              <IconButton
                disabled={
                  Number(row.document_status_id) === DOCUMENT_CANCELLED_STATUS_ID
                }
                onClick={() => setItemToDelete(row)}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ],
    [detailColumnHelper, formatNumber],
  )

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const handleDetailPanelExpandedRowIdsChange = useCallback(
    (newIdsSet: ReadonlySet<GridRowId>) => {
      const newIds = Array.from(newIdsSet)
      if (newIds.length > 1) {
        setDetailPanelExpandedRowIds([newIds[newIds.length - 1]])
      } else {
        setDetailPanelExpandedRowIds(newIds)
      }
    },
    [],
  )

  const documentLineGetRowId = useCallbackBranded(
    (row: OperationalGrouped['document_lines'][number]) => row.document_line_id,
    [],
  )

  const rowSelectionModel = useMemo(
    (): Set<GridRowId> =>
      selectedRow && selectedRow.document_line_id
        ? new Set([selectedRow.document_line_id])
        : new Set(),
    [selectedRow],
  )

  const getDetailPanelContent = useCallback(
    ({ row }: GridRowParams<OperationalGrouped>) => (
      <Box
        p={2}
        sx={{
          backgroundColor: ' #F9F9F9 ',
          '.MuiPaper-root': {
            boxShadow: `0px 2px 1px -1px rgba(0, 0, 0, 0.20), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)`,
          },
        }}
      >
        <UserDataGridWithSavedSettingsOnIDB
          Component={DataGrid}
          dataGridId="operationalDetailGroupedDocuments"
          disableRowSelectionOnClick
          onRowClick={({ row }) => {
            setSelectedConcept(menu(row.document_concept_id))
            setSelectedRow(row)
            setIsEditCostModalOpen(true)
          }}
          loading={!row.document_lines}
          rows={row.document_lines}
          getRowId={documentLineGetRowId}
          columns={detailColumns}
          hideFooter={true}
          rowSelectionModel={rowSelectionModel}
        />
      </Box>
    ),
    [documentLineGetRowId, detailColumns, rowSelectionModel],
  )

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: String(itemToDelete.document_id) },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        sx={{
          '.MuiDataGrid-row .MuiDataGrid-cell': {
            span: {
              overflow: 'hidden !important',
              textOverflow: 'ellipsis !important',
            },
          },
        }}
        Component={DataGrid}
        dataGridId="operationalGroupedDocuments"
        disableRowSelectionOnClick
        loading={isLoading}
        pagination
        rows={operationalGrouped}
        getRowId={useCallbackBranded(
          (row: OperationalGrouped) => `${row.date}-${row.document_number}`,
          [],
        )}
        columns={columns}
        detailPanelExpandedRowIds={detailPanelExpandedRowIdsSet}
        onRowClick={(row) =>
          handleDetailPanelExpandedRowIdsChange(
            new Set([...detailPanelExpandedRowIds, row.id]),
          )
        }
        onDetailPanelExpandedRowIdsChange={handleDetailPanelExpandedRowIdsChange}
        getDetailPanelContent={getDetailPanelContent}
        getDetailPanelHeight={() => 'auto'}
        filterModel={filterModel}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: `${menu(itemToDelete.document_concept_id).name}`,
          }}
        />
      )}
      {/* Edit */}
      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            setIsEditCostModalOpen(false)
          }}
          selectedRow={selectedRow}
          forceMenu={selectedConcept}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            setIsEditCostModalOpen(false)
            getCostsList()
          }}
          groupedPage
        />
      )}
    </DataGridHolder>
  )
}

export default GroupedCosts

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
