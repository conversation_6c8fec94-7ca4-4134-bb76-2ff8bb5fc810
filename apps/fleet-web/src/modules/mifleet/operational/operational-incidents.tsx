import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useC<PERSON>backBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
  type GridRowId,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import type { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { with<PERSON>outer, type RouteComponentProps } from 'react-router'
import { useParams } from 'react-router-dom'

import type { MifleetReportReferredName, VehicleId } from '@fleet-web/api/types'
import {
  actions,
  miFleetOperationalSelectors,
} from '@fleet-web/duxs/mifleet/operational'
import { useModal } from '@fleet-web/hooks'
import { useDateRangeShortcutItems } from '@fleet-web/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  fetchDocumentArrayTypes,
  getArrayTypes,
} from '@fleet-web/modules/mifleet/DocumentsEdit/slice'
import { useDeleteDocumentMutation } from '@fleet-web/modules/mifleet/lite/api/useMiFleetCost'
import type { AppState } from '@fleet-web/root-reducer'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { useMifleetFormattedNumber } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_INCIDENT } from '../components/documents/concept-types'
import type { FetchMiFleetArrayTypes } from '../lite/api/useMiFleetArrayTypes'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import type { OperationalIncidentListType } from './shared/type'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const {
  isOperationallIncidentsLoading,
  allOperationalIncidents,
  allOperationalIncidentsTypes,
} = miFleetOperationalSelectors

const { fetchOperationalIncidents } = actions

type IncidentsProps = {
  isLoading: boolean
  automationId?: string
  operationalIncidents: Array<OperationalIncidentListType>
  operationalIncidentsTypes: FetchMiFleetArrayTypes.ParsedReturn['incidentTypes']
  incidentTypesDropDownOptions: Array<{ label: string; value: string }>
  viewReportClick: (key: MifleetReportReferredName) => void
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Incidents = (props: IncidentsProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsIncidentsFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <IncidentsContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const IncidentsContent = ({
  isLoading,
  automationId,
  operationalIncidents,
  operationalIncidentsTypes,
  incidentTypesDropDownOptions,
  viewReportClick,
  dispatch,
  arrayTypesAllDrivers,
  costsFilters,
  setCostsFilters,
}: IncidentsProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper = useDataGridColumnHelper<OperationalIncidentListType>({
    filterMode: 'client',
  })

  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal(false)
  const [isEditCostModalOpen, editCostModal] = useModal(false)
  const [selectedRow, setSelectedRow] = useState<
    OperationalIncidentListType | undefined
  >(undefined)
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const { vehicleId } = useParams<{ vehicleId: VehicleId }>()

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    if (!arrayTypesAllDrivers || arrayTypesAllDrivers.length === 0) {
      dispatch(fetchDocumentArrayTypes())
    }
  }, [arrayTypesAllDrivers, dispatch])

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalIncidents({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalIncidents({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const columns = useMemo(
    (): Array<GridColDef<OperationalIncidentListType>> => [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Created Date' }),
        field: 'create_timestamp',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.create_timestamp),
      }),
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Transaction Date' }),
        field: 'incident_date',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.incident_date),
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id],
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.singleSelect(
        (_, row) => {
          const type = operationalIncidentsTypes.find(
            (o) => o.value === row.vehicle_incident_type_id,
          )
          return type ? type.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Accident Type' }),
          field: 'vehicle_incident_type_id',
          flex: 1,
          minWidth: 150,
          valueOptions: incidentTypesDropDownOptions,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driver = arrayTypesAllDrivers.find((o) => o.driver_id === row.driver_id)

          return driver ? driver.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver Name' }),
          field: 'driver_id',
          flex: 1,
          minWidth: 100,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
        minWidth: 150,
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        width: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value),
        align: 'right',
        headerAlign: 'left',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View Transaction' })}
              arrow
            >
              <IconButton
                id={automationId ? `view-doc-${automationId}-btn` : undefined}
                color="secondary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedRow(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize={'small'} />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ],
    [
      columnHelper,
      incidentTypesDropDownOptions,
      operationalIncidentsTypes,
      arrayTypesAllDrivers,
      formatNumber,
      automationId,
      editCostModal,
    ],
  )

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        create_timestamp,
        incident_date,
        plate,
        document_status,
        source_id,
        vehicle_incident_type_id,
        driver_id,
        description,
        total_value,
      } = d

      const type =
        operationalIncidentsTypes.find((o) => o.value === vehicle_incident_type_id)
          ?.name || ''

      const driver =
        arrayTypesAllDrivers.find((o) => o.driver_id === driver_id)?.name || ''

      return {
        create_timestamp: create_timestamp ? create_timestamp : '',
        incident_date: incident_date ? incident_date : '',
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        type,
        driver,
        description,
        total_value,
      }
    })

    const header = [
      'Created Date',
      'Transaction Date',
      'Vehicle',
      'Document Status',
      'Source',
      'Accident Type',
      'Driver Name',
      'Description',
      'Gross Total',
    ]

    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('Accidents', dateRangeFilter),
      'Accidents',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  const rowSelectionModel = useMemo(
    (): Set<GridRowId> =>
      selectedRow && selectedRow.document_line_id
        ? new Set([selectedRow.document_line_id])
        : new Set(),
    [selectedRow],
  )

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        Component={DataGrid}
        dataGridId="operationalAccidents"
        disableRowSelectionOnClick
        loading={!operationalIncidents || isLoading}
        pagination
        rows={operationalIncidents}
        getRowId={useCallbackBranded(
          (row: (typeof operationalIncidents)[number]) => row.document_line_id,
          [],
        )}
        rowSelectionModel={rowSelectionModel}
        columns={columns}
        filterModel={filterModel}
        onRowClick={({ row }) => {
          setSelectedRow(row)
          editCostModal.open()
        }}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      viewReportClick('REPORT_ACCIDENTS' as MifleetReportReferredName)
                    }
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Report',
                    })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Accidents Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Accidents',
          }}
        />
      )}
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Accidents',
              id: DOCUMENT_CONCEPT_INCIDENT,
            },
          ]}
        />
      )}
      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            editCostModal.close()
          }}
          selectedRow={selectedRow}
          forceMenu={{
            name: 'Accidents',
            id: DOCUMENT_CONCEPT_INCIDENT,
          }}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            editCostModal.close()
            getCostsList()
          }}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  arrayTypesAllDrivers: getArrayTypes(state).allDrivers,
  isLoading: isOperationallIncidentsLoading(state),
  operationalIncidents: allOperationalIncidents(state),
  operationalIncidentsTypes: allOperationalIncidentsTypes(state),
  incidentTypesDropDownOptions: [
    ...allOperationalIncidentsTypes(state)
      .map((f) => ({
        label: f.incident_type,
        value: f.incident_type,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)),
  ],
})

export default withRouter(connect(mapStateToProps)(Incidents))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
