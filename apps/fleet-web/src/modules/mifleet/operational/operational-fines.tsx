import { useCallback, useEffect, useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Box,
  Button,
  DataGrid,
  Drawer,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import { DateTime } from 'luxon'
import { Controller } from 'react-hook-form'
import { connect, type DispatchProp } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router'
import { useParams } from 'react-router-dom'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import type { MifleetReportReferredName, VehicleId } from '@fleet-web/api/types'
import {
  actions as overviewActions,
  selectors as overviewSelectors,
} from '@fleet-web/duxs/mifleet/overview/overview'
import { useModal } from '@fleet-web/hooks'
import { useDateRangeShortcutItems } from '@fleet-web/hooks/useDateRangeShortcutItems'
import PhoneNumberInput from '@fleet-web/modules/app/authentication/v2/components/PhoneNumberInput'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  FRAUD_STATUS_FILTER_OPTIONS,
  FRAUD_STATUS_PENDING_VALUE,
  getValidationStatusLabel,
} from '@fleet-web/modules/mifleet/components/fraudValidation/shared/helpers'
import {
  fetchDocumentArrayTypes,
  getArrayTypes,
} from '@fleet-web/modules/mifleet/DocumentsEdit/slice'
import { useDeleteDocumentMutation } from '@fleet-web/modules/mifleet/lite/api/useMiFleetCost'
import {
  useCreateIOXLink,
  useReadMiFleetIOXData,
} from '@fleet-web/modules/mifleet/lite/api/useMiFleetIOX'
import type { AppState } from '@fleet-web/root-reducer'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { useMifleetFormattedNumber } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { createZodObjPathGetter } from '@fleet-web/util-functions/zod-utils'

import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_FINE } from '../components/documents/concept-types'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import type { OperationalFineListType } from './shared/type'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const { fetchFineList, fetchFineDetails, updateFineFraud } = overviewActions

const schemaIox = z
  .object({
    user_id: z.string(),
    user_name: z.string(),
    contact_number: z.string(),
    first_name: z.string(),
    last_name: z.string(),
    initials: z.string(),
    email: z.string().email(),
    company_name: z.string(),
    company_number: z.string(),
    brn_number: z.string(),
    iox_jwt: z.string().nullable(),
    id_number: z.string().nullable(),
    passport_number: z.string().nullable(),
  })
  .superRefine((data, ctx) => {
    const { createPath } = createZodObjPathGetter(data)

    if (!data.id_number && !data.passport_number) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['passport_number']),
        message: 'Fill Number ID or Passport Number',
      })
    }
  })

type ValidationSchema = z.infer<typeof schemaIox>

type FinesProps = {
  isLoading: boolean
  automationId?: string
  fineList: Array<OperationalFineListType>
  detailsFineFraud?: FineFraudDetail
  viewReportClick: (key: MifleetReportReferredName) => void
  isDetailsLoading: boolean
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Fines = (props: FinesProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsFineFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <FinesContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const FinesContent = ({
  isLoading,
  automationId = undefined,
  viewReportClick,
  dispatch,
  fineList,
  detailsFineFraud,
  arrayTypesAllDrivers,
  costsFilters,
  setCostsFilters,
  isDetailsLoading,
}: FinesProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper = useDataGridColumnHelper<OperationalFineListType>({
    filterMode: 'client',
  })
  const [ioxModalOpen, setIoxModalOpen] = useState(false)
  const { vehicleId } = useParams() as { vehicleId: VehicleId }

  const [selectedRow, setSelectedRow] = useState<OperationalFineListType | undefined>(
    undefined,
  )
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal(false)
  const [isEditCostModalOpen, editCostModal] = useModal(false)
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const {
    data: ioxData,
    refetch: refetchIoxData,
    isFetching: isIOXDataFetching,
  } = useReadMiFleetIOXData()
  const createIOXAccount = useCreateIOXLink()

  const ioxInitialValues = {
    user_id: '',
    user_name: '',
    first_name: '',
    last_name: '',
    initials: '',
    company_name: '',
    brn_number: '',
    id_number: '',
    passport_number: '',
    iox_jwt: '',
    ...(ioxData
      ? {
          email: ioxData.email.split(';')[0] ?? '',
          contact_number: ioxData.contact_number ?? '',
          company_number: `${ioxData.company_number}1`,
        }
      : {
          email: '',
          contact_number: '',
          company_number: '',
        }),
  }

  const { handleSubmit, control } = useControlledForm<ValidationSchema>({
    resolver: zodResolverV4(schemaIox),
    defaultValues: ioxInitialValues,
    mode: 'onChange',
  })

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchFineList(dateObject))
  }, [dateRangeFilter, dispatch, vehicleId])

  useEffect(() => {
    if (!arrayTypesAllDrivers || arrayTypesAllDrivers.length === 0) {
      dispatch(fetchDocumentArrayTypes())
    }
  }, [arrayTypesAllDrivers, dispatch])

  const FINE_FRAUD_STATUS_FILTER_OPTIONS = useMemo(
    () =>
      FRAUD_STATUS_FILTER_OPTIONS.map((c) => ({
        ...c,
        label: ctIntl.formatMessage({ id: c.label }),
      })).sort((a, b) => a.label.localeCompare(b.label)),
    [],
  )

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchFineList(dateObject))
  }, [dateRangeFilter, dispatch, vehicleId])

  const columns = useMemo((): Array<GridColDef<OperationalFineListType>> => {
    const customMaximumColumnWidth = (
      header: string,
      accessor: 'fine_validation_status_id',
    ): number => {
      const maxWidth = 600
      const magicSpacing = 12
      const headerText = ctIntl.formatMessage({ id: header })
      const cellLength = Math.max(
        ...fineList.map((row) =>
          row[accessor]
            ? ctIntl.formatMessage({
                id: getValidationStatusLabel(row[accessor]) || '',
              }).length
            : 0,
        ),
        headerText.length,
      )
      return Math.min(maxWidth, cellLength * magicSpacing)
    }
    return [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Created Date' }),
        field: 'create_timestamp',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.create_timestamp),
      }),
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Transaction Date' }),
        field: 'infringement_date',
        minWidth: 170,
        filterable: false,
        valueGetter: (_, row) =>
          row.infringement_date ? new Date(row.infringement_date) : null,
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        minWidth: 140,
        flex: 1,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id],
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driver = arrayTypesAllDrivers.find((o) => o.driver_id === row.driver_id)

          return driver ? driver.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driver_id',
          flex: 1,
          minWidth: 100,
        },
      ),
      columnHelper.string((_, row) => row.infringement_location, {
        headerName: ctIntl.formatMessage({ id: 'Infringement Location' }),
        field: 'infringement_location',
        flex: 1,
        minWidth: 100,
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        width: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value),
        cellClassName: 'util-tableEnd',
        headerAlign: 'left',
      }),
      columnHelper.singleSelect(
        (_, row) =>
          row.fine_validation_status_id
            ? row.fine_validation_status_id.toString()
            : FRAUD_STATUS_PENDING_VALUE,
        {
          headerName: ctIntl.formatMessage({ id: 'Fraud Status' }),
          field: 'fine_validation_status_id',
          valueOptions: FINE_FRAUD_STATUS_FILTER_OPTIONS,
          width: customMaximumColumnWidth('Status', 'fine_validation_status_id'),
          renderCell: ({ row }) => (
            <StatusValidation
              statusId={Number(
                row.fine_validation_status_id
                  ? row.fine_validation_status_id.toString()
                  : FRAUD_STATUS_PENDING_VALUE,
              )}
              source="operational"
              isIOXFine={ioxData && !isEmpty(ioxData?.login_url)}
            />
          ),
        },
      ),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={`${ctIntl.formatMessage({
                id: 'Validate Transaction',
              })}, ${ctIntl.formatMessage({ id: 'View Transaction' })}`}
              arrow
            >
              <IconButton
                id={automationId ? `view-doc-${automationId}-btn` : undefined}
                color="secondary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  dispatch(fetchFineDetails(row.document_line_id))
                  setSelectedRow(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize={'small'} />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ]
  }, [
    columnHelper,
    FINE_FRAUD_STATUS_FILTER_OPTIONS,
    fineList,
    arrayTypesAllDrivers,
    formatNumber,
    ioxData,
    automationId,
    dispatch,
    editCostModal,
  ])

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        create_timestamp,
        infringement_date,
        plate,
        document_status,
        source_id,
        driver_id,
        infringement_location,
        fine_validation_status_id,
        total_value,
      } = d

      const driverName =
        arrayTypesAllDrivers.find((driver) => driver.driver_id === driver_id)?.name ||
        ''
      const status = ctIntl.formatMessage({
        id: getValidationStatusLabel(fine_validation_status_id || '') || '',
      })
      return {
        create_timestamp: create_timestamp ? create_timestamp : '',
        infringement_date: infringement_date
          ? DateTime.fromSQL(infringement_date).toFormat('D t')
          : '',
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        driverName,
        infringement_location,
        total_value,
        status,
      }
    })

    const header = [
      'Created Date',
      'Transaction Date',
      'Vehicle',
      'Document Status',
      'Source',
      'Driver',
      'Infringement Location',
      'Gross Total',
      'Status',
    ]

    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('Fines', dateRangeFilter),
      'Fines',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  const getIOXButton = () => {
    if (ioxData) {
      if (ioxData.login_url) {
        return (
          <Button
            color="inherit"
            variant="outlined"
            loading={isIOXDataFetching}
            onClick={() => {
              refetchIoxData().then(
                () => !isIOXDataFetching && window.open(ioxData.login_url, '_blank'),
              )
            }}
            size="small"
          >
            {ctIntl.formatMessage({
              id: 'Pay/Dispute Fines',
            })}
          </Button>
        )
      }

      if (!ioxData.iox_jwt) {
        return (
          <Button
            color="inherit"
            variant="outlined"
            onClick={() => setIoxModalOpen(true)}
            size="small"
          >
            {ctIntl.formatMessage({
              id: 'Register For Fine Management',
            })}
          </Button>
        )
      }
      return null
    }

    return null
  }

  const rowSelectionModel = useMemo(
    (): ReadonlySet<number> =>
      selectedRow ? new Set([selectedRow.document_line_id]) : new Set(),
    [selectedRow],
  )

  return (
    <DataGridHolder>
      {ioxModalOpen && (
        <Drawer
          anchor="right"
          open
          onClose={() => setIoxModalOpen(false)}
          PaperProps={{
            sx: {
              borderTopLeftRadius: 4,
              borderBottomLeftRadius: 4,
            },
          }}
        >
          <Stack
            sx={{
              p: 2,
              width: '500px',
              gap: 2,
            }}
          >
            <Typography variant="h5">
              {ctIntl.formatMessage({ id: 'Opt into IOX' })}
            </Typography>
            <TextFieldControlled
              ControllerProps={{ control, name: 'user_id' }}
              disabled
              label={ctIntl.formatMessage({ id: 'User ID' })}
              data-testid="ioxOptFormUserId"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'user_name' }}
              disabled
              label={ctIntl.formatMessage({ id: 'Username' })}
              data-testid="ioxOptFormName"
            />
            <Controller
              name="contact_number"
              control={control}
              render={({ field }) => (
                <PhoneNumberInput
                  onChange={(phoneNumber) => {
                    field.onChange(phoneNumber)
                  }}
                />
              )}
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'first_name' }}
              label={ctIntl.formatMessage({ id: 'First Name' })}
              data-testid="ioxOptFormFirstName"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'last_name' }}
              label={ctIntl.formatMessage({ id: 'Last Name' })}
              data-testid="ioxOptFormLastName"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'initials' }}
              label={ctIntl.formatMessage({ id: 'Initials' })}
              data-testid="ioxOptFormInitials"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'id_number' }}
              label={ctIntl.formatMessage({ id: 'ID Number' })}
              data-testid="ioxOptFormCompanyIDNumber"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'passport_number' }}
              label={ctIntl.formatMessage({ id: 'Passport Number' })}
              data-testid="ioxOptFormCompanyPassport"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'email' }}
              label={ctIntl.formatMessage({ id: 'Email' })}
              data-testid="ioxOptFormEmail"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'company_name' }}
              disabled
              label={ctIntl.formatMessage({ id: 'Company Name' })}
              data-testid="ioxOptFormCompanyName"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'company_number' }}
              disabled
              label={ctIntl.formatMessage({ id: 'Company Number' })}
              data-testid="ioxOptFormCompanyNumber"
            />
            <TextFieldControlled
              ControllerProps={{ control, name: 'brn_number' }}
              label={ctIntl.formatMessage({
                id: 'Traffic Business Registration Number',
              })}
              data-testid="ioxOptFormCompanyBrnNumber"
            />
            <Button
              loading={createIOXAccount.isPending}
              variant="contained"
              size="small"
              onClick={handleSubmit((values) => {
                createIOXAccount.mutate(values, {
                  onSuccess() {
                    refetchIoxData()
                    setIoxModalOpen(false)
                  },
                })
              })}
            >
              {ctIntl.formatMessage({
                id: 'Create IOX account',
              })}
            </Button>
          </Stack>
        </Drawer>
      )}

      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        Component={DataGrid}
        dataGridId="operationalFines"
        disableRowSelectionOnClick
        loading={!fineList || isLoading}
        pagination
        rowSelectionModel={rowSelectionModel}
        rows={fineList}
        getRowId={useCallbackBranded(
          (row: (typeof fineList)[number]) => row.document_line_id,
          [],
        )}
        columns={columns}
        onRowClick={({ row }) => {
          dispatch(fetchFineDetails(row.document_line_id))
          setSelectedRow(row)
          editCostModal.open()
        }}
        filterModel={filterModel}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  {getIOXButton()}
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      viewReportClick('REPORT_FINES' as MifleetReportReferredName)
                    }
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Report',
                    })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Fines Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Fine',
          }}
        />
      )}
      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            editCostModal.close()
          }}
          selectedRow={selectedRow}
          forceMenu={{
            name: 'Fine',
            id: DOCUMENT_CONCEPT_FINE,
          }}
          detailedFraud={detailsFineFraud}
          updateFraud={(validated, validation_reason) => {
            dispatch(
              updateFineFraud({
                document_line_id: selectedRow.document_line_id,
                fleet_check: selectedRow.fleet_check,
                validated,
                validation_reason,
              }),
            )
            getCostsList()
          }}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            editCostModal.close()
            getCostsList()
          }}
          fraudIsLoading={isDetailsLoading}
          isIOXFines={ioxData && !isEmpty(ioxData?.login_url)}
        />
      )}

      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Fines',
              id: DOCUMENT_CONCEPT_FINE,
            },
          ]}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  arrayTypesAllDrivers: getArrayTypes(state).allDrivers,
  isLoading: overviewSelectors.isFineListLoading(state),
  fineList: state.overview.fineList,
  detailsFineFraud: state.overview.detailsFineFraud,
  isDetailsLoading: overviewSelectors.isDetailsFineLoading(state),
})

export default withRouter(connect(mapStateToProps)(Fines))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
