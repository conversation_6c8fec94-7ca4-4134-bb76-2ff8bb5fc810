import { useState } from 'react'
import { <PERSON>, Drawer, Skeleton, Tab, Tabs, type DateRange } from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'

import type { CostMisc, OperationalGrouped } from '@fleet-web/duxs/mifleet/operational'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import {
  DOCUMENT_CONCEPT_FINE,
  DOCUMENT_CONCEPT_FUELLING,
  DOCUMENT_CONCEPT_TOLL,
} from '../components/documents/concept-types'
import FineFraud from '../components/fraudValidation/fine-fraud/fine-fraud-details'
import FuelFraud from '../components/fraudValidation/fuel-fraud-by-cost/fraud-details'
import { FRAUD_STATUS_PENDING_VALUE } from '../components/fraudValidation/shared/helpers'
import { NewTitleBlock } from '../components/fraudValidation/styled'
import useMiFleetArrayTypesQuery from '../lite/api/useMiFleetArrayTypes'
import { useReadMiFleetCostQuery } from '../lite/api/useMiFleetCost'
import CaptureData from '../lite/capture-data'
import type {
  OperationalFineListType,
  OperationalIncidentListType,
  OperationalMaintenanceType,
  OperationalTireListType,
  OperationalTollListTypes,
} from './shared/type'
import FraudDetails from './toll-fraud-details'

type Props = {
  forceMenu: { name: string; id: string }
  onClose: () => void
  updateFraud?: (validated: boolean, validation_reason?: string) => void
  detailedFraud?: FraudDetail | FineFraudDetail
  selectedRow:
    | OperationalTollListTypes
    | OperationalFineListType
    | Status
    | OperationalMaintenanceType.MaintenanceList
    | OperationalTireListType
    | OperationalIncidentListType
    | CostMisc
    | OperationalGrouped['document_lines'][number]
  isSuccessUpdating: () => void
  fraudIsLoading?: boolean
  dateFilters?: DateRange<DateTime>
  groupedPage?: boolean
  isIOXFines?: boolean
}
type TabPanelProps = {
  children?: React.ReactNode
  index: number
  value: number
}

const conceptsWithFraudValidation = new Set([
  DOCUMENT_CONCEPT_FINE,
  DOCUMENT_CONCEPT_TOLL,
  DOCUMENT_CONCEPT_FUELLING,
])
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ height: '100%', overflowY: 'scroll' }}
      {...other}
    >
      <Box sx={{ height: '100%' }}>{children}</Box>
    </div>
  )
}

export function FloatingPanelCostDetailDrawer(props: Props) {
  const {
    onClose,
    detailedFraud,
    selectedRow,
    selectedRow: { document_line_id: costId },
    updateFraud,
    forceMenu,
    isSuccessUpdating,
    fraudIsLoading,
    dateFilters,
    groupedPage,
    isIOXFines,
  } = props

  const [tabsValue, setTabsValue] = useState(1)
  const mifleetArrayTypesQuery = useMiFleetArrayTypesQuery()
  const mifleetReadCostQuery = useReadMiFleetCostQuery({
    documentLineId: String(costId),
  })
  const shouldShowLoadingFraud = () =>
    conceptsWithFraudValidation.has(forceMenu.id) && !groupedPage
      ? fraudIsLoading
      : false

  const handleChange = (_event: React.SyntheticEvent, newValue: number) =>
    setTabsValue(newValue)

  const a11yProps = (index: number) => ({
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  })

  const isIOXFine = forceMenu.id === DOCUMENT_CONCEPT_FINE && isIOXFines

  const FraudComponent = () => {
    switch (forceMenu.id) {
      case DOCUMENT_CONCEPT_FUELLING: {
        const status = (
          (detailedFraud as FraudDetail).fuel_validation_status_id === null
            ? FRAUD_STATUS_PENDING_VALUE
            : (detailedFraud as FraudDetail).fuel_validation_status_id
        ) as string
        return {
          fraud: (
            <FuelFraud
              vehicle={selectedRow as Status}
              takeFraudAction={
                updateFraud as (validated: boolean, validation_reason?: string) => void
              }
              detailedFraud={detailedFraud as FraudDetail}
              dateFilters={dateFilters}
              fraudStatus={status}
            />
          ),
          date: (selectedRow as Status).fuelling_date,
          status,
        }
      }
      case DOCUMENT_CONCEPT_FINE: {
        const status = (
          (detailedFraud as FineFraudDetail).fine_validation_status_id === null
            ? FRAUD_STATUS_PENDING_VALUE
            : (detailedFraud as FineFraudDetail).fine_validation_status_id
        ) as string

        return {
          fraud: (
            <FineFraud
              vehicle={selectedRow as OperationalFineListType}
              takeFraudAction={
                updateFraud as (validated: boolean, validation_reason?: string) => void
              }
              detailedFraud={detailedFraud as FineFraudDetail}
              isIOXFine={isIOXFine}
              fraudStatus={status}
            />
          ),
          date: (selectedRow as OperationalFineListType).infringement_date,
          status,
        }
      }
      case DOCUMENT_CONCEPT_TOLL: {
        const status = (
          (detailedFraud as FraudDetail)?.toll_validation_status_id === null
            ? FRAUD_STATUS_PENDING_VALUE
            : (detailedFraud as FraudDetail)?.toll_validation_status_id
        ) as string
        return {
          fraud: (
            <FraudDetails
              activeFraudDetails={selectedRow as OperationalTollListTypes}
              updateTollFraud={
                updateFraud as (validated: boolean, validation_reason?: string) => void
              }
              detailedFraud={detailedFraud as FraudDetail}
              fraudStatus={status}
            />
          ),
          date: (selectedRow as OperationalTollListTypes).toll_entry,
          status,
        }
      }
      default: {
        return
      }
    }
  }

  return (
    <Drawer
      open
      onClose={onClose}
      anchor={'right'}
      sx={{ '.MuiDrawer-paper': { width: '800px', padding: '24px 0 85px' } }}
    >
      {shouldShowLoadingFraud() ? (
        <Box sx={{ minHeight: '124px' }}>
          <NewTitleBlock
            onClose={props.onClose}
            title=""
          />
          <Skeleton
            variant="text"
            animation="wave"
            sx={{ fontSize: '1rem', mb: 6, ml: 2, mr: 2 }}
          />
        </Box>
      ) : (
        <>
          <NewTitleBlock
            vehicle={`${detailedFraud?.vehicle_details?.manufacturer} / ${detailedFraud?.vehicle_details?.model}`}
            onClose={onClose}
            title={detailedFraud?.vehicle_details?.plate}
            date={FraudComponent()?.date}
            status={FraudComponent()?.status}
            isAdBlue={(detailedFraud as FraudDetail)?.is_adblue || false}
            fraudIsActive={
              conceptsWithFraudValidation.has(forceMenu.id) && !groupedPage
            }
            isIOXFine={isIOXFine}
          />
        </>
      )}
      {match([mifleetArrayTypesQuery, mifleetReadCostQuery])
        .with(
          P.when(
            ([mifleetArrayTypesQuery, mifleetReadCostQuery]) =>
              mifleetArrayTypesQuery.status === 'pending' ||
              mifleetReadCostQuery.isLoading,
          ),
          () => (
            <>
              <Skeleton
                variant="text"
                animation="wave"
                sx={{ fontSize: '1rem', mb: 6, ml: 2, mr: 2 }}
              />
              <Skeleton
                sx={{ mb: 4, ml: 2, mr: 2 }}
                variant="rounded"
                animation="wave"
                height={60}
              />
            </>
          ),
        )
        .with(
          [{ status: 'success' }, { status: 'success', fetchStatus: 'idle' }],
          ([
            { data: mifleetArrayTypesQueryData, refetch },
            { data: mifleetReadCostQueryData },
          ]) => (
            <>
              {conceptsWithFraudValidation.has(forceMenu.id) && !groupedPage && (
                <Box
                  sx={{ borderBottom: 1, borderColor: 'divider' }}
                  m={3}
                  mt={0}
                >
                  <Tabs
                    value={tabsValue}
                    onChange={handleChange}
                    aria-label="import tabs"
                  >
                    <Tab
                      label={ctIntl.formatMessage({ id: 'Validate Transaction' })}
                      {...a11yProps(0)}
                      disabled={
                        'fleet_check' in selectedRow && selectedRow.fleet_check == null
                      }
                    />
                    <Tab
                      label={ctIntl.formatMessage({ id: 'View Transaction' })}
                      {...a11yProps(1)}
                    />
                  </Tabs>
                </Box>
              )}
              <TabPanel
                value={tabsValue}
                index={0}
              >
                {!groupedPage && !shouldShowLoadingFraud() && FraudComponent()?.fraud}
              </TabPanel>
              <TabPanel
                value={tabsValue}
                index={1}
              >
                <CaptureData
                  defaultConcept={forceMenu}
                  activeCost={String(costId)}
                  onClose={onClose}
                  isSuccessUpdating={isSuccessUpdating}
                  arrayTypes={mifleetArrayTypesQueryData || []}
                  refetchArrayType={refetch}
                  readCostQueryData={mifleetReadCostQueryData}
                />
              </TabPanel>
            </>
          ),
        )
        .otherwise(() => null)}
    </Drawer>
  )
}
