import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useC<PERSON>backBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
  type GridRowId,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import type { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { with<PERSON>outer, type RouteComponentProps } from 'react-router'
import { useParams } from 'react-router-dom'

import type { MifleetReportReferredName, VehicleId } from '@fleet-web/api/types'
import {
  miFleetOperationalSelectors,
  actions as operationalActions,
} from '@fleet-web/duxs/mifleet/operational'
import { useModal } from '@fleet-web/hooks'
import { useDateRangeShortcutItems } from '@fleet-web/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from '@fleet-web/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useDeleteDocumentMutation } from '@fleet-web/modules/mifleet/lite/api/useMiFleetCost'
import type { AppState } from '@fleet-web/root-reducer'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import KarooToolbar from '@fleet-web/shared/data-grid/KarooToolbar'
import { useMifleetFormattedNumber } from '@fleet-web/util-components'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_MAINTENANCE } from '../components/documents/concept-types'
import type { FetchMiFleetArrayTypes } from '../lite/api/useMiFleetArrayTypes'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import type { OperationalMaintenanceType } from './shared/type'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const { fetchOperationalMaintenance } = operationalActions

const {
  allOperationalMaintenance,
  allOperationalMaintenanceTypes,
  isOperationallMaintenanceLoading,
} = miFleetOperationalSelectors

type MaintenanceProps = {
  isLoading: boolean
  automationId?: string
  operationalMaintenance: Array<OperationalMaintenanceType.MaintenanceList>
  operationalMaintenanceTypes: FetchMiFleetArrayTypes.ParsedReturn['maintenanceTypes']
  maintenanceTypesDropDownOptions: Array<{ label: string; value: string }>
  viewReportClick: (key: MifleetReportReferredName) => void
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Maintenance = (props: MaintenanceProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsMaintenanceFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <MaintenanceContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const MaintenanceContent = ({
  isLoading,
  automationId = undefined,
  operationalMaintenance,
  operationalMaintenanceTypes,
  maintenanceTypesDropDownOptions,
  dispatch,
  viewReportClick,
  costsFilters,
  setCostsFilters,
}: MaintenanceProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper =
    useDataGridColumnHelper<OperationalMaintenanceType.MaintenanceList>({
      filterMode: 'client',
    })
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal(false)
  const [isEditCostModalOpen, editCostModal] = useModal(false)
  const [selectedRow, setSelectedRow] = useState<
    OperationalMaintenanceType.MaintenanceList | undefined
  >(undefined)
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const { vehicleId } = useParams<{ vehicleId: VehicleId }>()

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalMaintenance({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalMaintenance({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const columns = useMemo(
    (): Array<GridColDef<OperationalMaintenanceType.MaintenanceList>> => [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Created Date' }),
        field: 'create_timestamp',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.create_timestamp),
      }),
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Transaction Date' }),
        field: 'document_date',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.document_date),
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        minWidth: 140,
        flex: 1,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id] || '',
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.string((_, row) => row.accounting_chart_number, {
        headerName: ctIntl.formatMessage({ id: 'General Ledger (GL) Code' }),
        field: 'accounting_chart_number',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect(
        (_, row) => {
          const type = operationalMaintenanceTypes.find(
            (o) => o.value === row.vehicle_maintenance_type_id,
          )
          return type ? type.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Maintenance Type' }),
          field: 'vehicle_maintenance_type_id',
          flex: 1,
          minWidth: 150,
          valueOptions: maintenanceTypesDropDownOptions,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
        minWidth: 150,
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        width: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value),
        align: 'right',
        headerAlign: 'left',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View Transaction' })}
              arrow
            >
              <IconButton
                id={automationId ? `view-doc-${automationId}-btn` : undefined}
                color="secondary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedRow(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ],
    [
      automationId,
      columnHelper,
      editCostModal,
      formatNumber,
      maintenanceTypesDropDownOptions,
      operationalMaintenanceTypes,
    ],
  )

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        create_timestamp,
        document_date,
        plate,
        document_status,
        source_id,
        accounting_chart_number,
        vehicle_maintenance_type_id,
        description,
        total_value,
      } = d

      const type =
        operationalMaintenanceTypes.find((o) => o.value === vehicle_maintenance_type_id)
          ?.name || ''

      return {
        create_timestamp,
        document_date,
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        accounting_chart_number,
        type,
        description,
        total_value,
      }
    })

    const header = [
      'Created Date',
      'Transaction Date',
      'Vehicle',
      'Document Status',
      'Source',
      'General Ledger (GL) Code',
      'Maintenance Type',
      'Description',
      'Gross Total',
    ]

    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('Maintenance', dateRangeFilter),
      'Maintenance',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }

  const rowSelectionModel = useMemo(
    (): Set<GridRowId> =>
      selectedRow && selectedRow.document_line_id
        ? new Set([selectedRow.document_line_id])
        : new Set(),
    [selectedRow],
  )

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        Component={DataGrid}
        dataGridId="operationalMaintenance"
        disableRowSelectionOnClick
        loading={!operationalMaintenance || isLoading}
        rowSelectionModel={rowSelectionModel}
        pagination
        rows={operationalMaintenance}
        getRowId={useCallbackBranded(
          (row: (typeof operationalMaintenance)[number]) => row.document_line_id,
          [],
        )}
        columns={columns}
        filterModel={filterModel}
        onRowClick={({ row }) => {
          setSelectedRow(row)
          editCostModal.open()
        }}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        initialState={{
          columns: {
            columnVisibilityModel: {
              accounting_chart_number: false,
            },
          },
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      viewReportClick(
                        'REPORT_MAINTENANCES' as MifleetReportReferredName,
                      )
                    }
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Report',
                    })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Maintenance Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Maintenances',
          }}
        />
      )}
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Maintenances',
              id: DOCUMENT_CONCEPT_MAINTENANCE,
            },
          ]}
        />
      )}
      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            editCostModal.close()
          }}
          selectedRow={selectedRow}
          forceMenu={{
            name: 'Maintenances',
            id: DOCUMENT_CONCEPT_MAINTENANCE,
          }}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            editCostModal.close()
            getCostsList()
          }}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  isLoading: isOperationallMaintenanceLoading(state),
  operationalMaintenance: allOperationalMaintenance(state),
  operationalMaintenanceTypes: allOperationalMaintenanceTypes(state),
  maintenanceTypesDropDownOptions: [
    ...allOperationalMaintenanceTypes(state)
      .map((f) => ({
        label: f.maintenance_type,
        value: f.maintenance_type,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)),
  ],
})

export default withRouter(connect(mapStateToProps)(Maintenance))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
