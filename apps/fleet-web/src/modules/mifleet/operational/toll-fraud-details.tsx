/* eslint-disable sonarjs/concise-regex */
import { useState } from 'react'
import { isEmpty } from 'lodash'
import {
  <PERSON><PERSON>,
  <PERSON>ertTitle,
  Box,
  Button,
  Stack,
  styled,
  TextField,
  Typography,
} from '@karoo-ui/core'
import RouteIcon from '@mui/icons-material/Route'
import { DateTime } from 'luxon'
import { connect } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import type { VehicleId } from '@fleet-web/api/types'
import { actions as reducerActions } from '@fleet-web/duxs/mifleet/operational'
import { actions } from '@fleet-web/duxs/mifleet/overview/overview'
import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import { LIST } from '@fleet-web/modules/app/components/routes/list'
import {
  createMapFleetSearchParams,
  type ParseFleetSearchParams,
} from '@fleet-web/modules/map-view/MapFleetProvider'
import type { AppState } from '@fleet-web/root-reducer'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import DetailsMap from '../components/fraudValidation/fuel-fraud-by-cost/details-map'
import { TollFraudStatus } from '../components/fraudValidation/fuel-fraud-by-cost/fraud-utils'
import warningMessage from '../components/fraudValidation/fuel-fraud-by-cost/warning-message'
import CheckCustomIcon, {
  BlocksFeedWrapper,
  ClearCustomIcon,
  ErrorCustomIcon,
  FraudBlock,
  InnerBlock,
  LastBlock,
} from '../components/fraudValidation/styledMui'
import { ButtonsHolder } from '../lite/capture-data/style'
import type { OperationalTollListTypes } from './shared/type'

type Props = {
  updateTollFraud: (validated: boolean, validation_reason?: string) => void
  activeFraudDetails: OperationalTollListTypes
  detailedFraud: FraudDetail
  fraudStatus: string
} & ReturnType<typeof mapStateToProps> &
  typeof actionCreators

function TollFraud(props: Props) {
  const history = useHistory()
  const {
    updateTollFraud,
    detailedFraud,
    defaultLocation,
    addStation,
    activeFraudDetails,
    fraudStatus,
  } = props

  const [isAddingStation, setIsAddingStation] = useState<null | {
    stationName: string
  }>(null)

  const [isAddingReason, setIsAddingReason] = useState<null | {
    reason: string
    validated: boolean
  }>(null)

  const takeFraudAction = (validated: boolean) => {
    setIsAddingReason({ reason: '', validated })
  }

  const isValidationReasonValid = !isEmpty(isAddingReason?.reason?.trim())

  const getLocationErrorMessage = () => {
    if (isEmpty(detailedFraud.vehicle_coords)) {
      return 'mifleet.fraud.no.device'
    }
    if (detailedFraud?.vehicle_coords && !detailedFraud.possible_toll_stations) {
      return 'no tolls found nearby at the time of record'
    }
    return 'mifleet.tolls.fraud.no.data'
  }

  const addReasonFormSection = () => {
    if (!isAddingReason) {
      return null
    }

    return (
      <AddStationWrapper>
        <TextField
          sx={{ marginBottom: '10px' }}
          required
          label={ctIntl.formatMessage({ id: 'Reason' })}
          value={isAddingReason.reason}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setIsAddingReason({
              reason: e.target.value,
              validated: isAddingReason.validated,
            })
          }
        />
        <StationActionWrapper>
          <Button
            variant="outlined"
            onClick={() => setIsAddingReason(null)}
            color="inherit"
            size={'small'}
            sx={{ marginRight: '5px' }}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
        </StationActionWrapper>
      </AddStationWrapper>
    )
  }

  const addStationFormSection = () => {
    if (
      !isAddingStation ||
      detailedFraud.fleet_check_result_id !== TollFraudStatus.FAIL_MISSING_TOLL_STATION
    ) {
      return null
    }

    return (
      <AddStationWrapper>
        <TextField
          sx={{ marginBottom: '10px' }}
          required
          label={ctIntl.formatMessage({ id: 'Toll Station Name' })}
          value={isAddingStation.stationName}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setIsAddingStation({
              stationName: e.target.value,
            })
          }
        />
        <StationActionWrapper>
          <Button
            variant="outlined"
            onClick={() => setIsAddingStation(null)}
            color="inherit"
            size={'small'}
            sx={{ marginRight: '5px' }}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            variant="contained"
            color="success"
            type={'submit'}
            disabled={isEmpty(isAddingStation.stationName?.trim())}
            onClick={() => {
              if (isAddingStation.stationName) {
                addStation({
                  toll_station: isAddingStation.stationName,
                  document_line_id: detailedFraud.document_line_id,
                  latitude: detailedFraud.vehicle_coords[0].fleet_latitude,
                  longitude: detailedFraud.vehicle_coords[0].fleet_longitude,
                })
              }
            }}
            size={'small'}
          >
            {ctIntl.formatMessage({ id: 'Save New Toll Station' })}
          </Button>
        </StationActionWrapper>
      </AddStationWrapper>
    )
  }

  const getLocationStatusIcon = () => {
    if (detailedFraud.distance.distance_status === 'green') {
      return <CheckCustomIcon />
    }

    if (detailedFraud.distance.distance_status === 'orange') {
      return <ErrorCustomIcon />
    }

    return <ClearCustomIcon />
  }

  const getIntervalStatusIcon = () => {
    if (detailedFraud.interval.interval_status === 'green') {
      return <CheckCustomIcon />
    }
    return <ClearCustomIcon />
  }
  const getIntervalValue = (field: 'checkInterval' | 'tollInterval') => {
    const date = activeFraudDetails.toll_date
    const interval =
      field === 'checkInterval'
        ? detailedFraud.interval.check_interval
        : detailedFraud.interval.toll_interval
    const intervalSplited = (interval as string).split(',') ?? ['0', '0', '0']
    return DateTime.fromSQL(date as string)
      .minus({
        days: Number(intervalSplited[0].match(/[0-9]+/)?.[0] ?? 0),
        hours: Number(intervalSplited[1].match(/[0-9]+/)?.[0] ?? 0),
        minutes: Number(intervalSplited[2].match(/[0-9]+/)?.[0] ?? 0),
      })
      .toFormat('D t')
  }
  const onVehicleClick = () => history.push(LIST.subMenusRoutes.VEHICLES.path)

  return (
    <Box sx={{ height: '100%' }}>
      <Stack direction="column">
        <MainContainer>
          {!isEmpty(detailedFraud.validation_reason) && (
            <Alert
              severity={'info'}
              sx={{ mb: 2 }}
            >
              <AlertTitle>
                {ctIntl.formatMessage({
                  id: 'Validation Reason:',
                })}
              </AlertTitle>
              {detailedFraud.validation_reason}
            </Alert>
          )}
          <Typography mb={2}>
            {ctIntl.formatMessage({
              id: 'mifleet.fraud.header.info',
            })}
          </Typography>
          <DetailsMap
            mapDataType="toll"
            fraudEntryDetails={detailedFraud}
            defaultLocation={defaultLocation}
            fraudStatus={fraudStatus}
          />
          <Button
            startIcon={<RouteIcon />}
            size="small"
            variant="outlined"
            color="inherit"
            onClick={() => {
              const startDate = DateTime.fromSQL(
                activeFraudDetails.toll_date,
              ).toISODate()
              const endDate = DateTime.fromSQL(activeFraudDetails.toll_date).toISODate()

              window.open(
                createMapFleetSearchParams({
                  locationPrefix: '/map/fleet',
                  history,
                  newSearchParams: (state): ParseFleetSearchParams => ({
                    ...state,
                    selectedVehicleMeta: {
                      initialVehicleId:
                        detailedFraud.vehicle_details.vehicle_id.toString() as VehicleId,
                      bottomPanel: {
                        initialSelectedTripId:
                          detailedFraud.trip_id?.toString() ?? undefined,
                        sectionType: 'table',
                      },
                      detailsPanel: null,
                    },
                    startDate,
                    endDate,
                  }),
                }),
                '_blank',
              )
            }}
            sx={{
              color: '#333333',
              margin: '16px 0',
              width: 'fit-content',
            }}
          >
            {ctIntl.formatMessage({ id: 'mifleet.fraud.see.map.activity' })}
          </Button>
          {warningMessage(detailedFraud, 'toll', onVehicleClick) ? (
            <BlocksFeedWrapper>
              {warningMessage(detailedFraud, 'toll', onVehicleClick)}
            </BlocksFeedWrapper>
          ) : (
            <BlocksFeedWrapper mainSection={true}>
              {/* Distance */}
              <FraudBlock
                rowNumber="1"
                title={'Location'}
                error={getLocationErrorMessage()}
                showError={!detailedFraud.show_distance_data}
                statusIcon={getLocationStatusIcon()}
                leftBlock={
                  <InnerBlock
                    title="Toll"
                    label={detailedFraud.distance.toll_station_position || ''}
                    labelTip={detailedFraud.distance.toll_station_position || ''}
                  />
                }
                centerBlock={
                  <InnerBlock
                    title={ctIntl.formatMessage({ id: 'Vehicle Location' })}
                    label={detailedFraud.distance.vehicle_position || ''}
                    labelTip={detailedFraud.distance.vehicle_position || ''}
                    position="middle"
                  />
                }
                rightBlock={
                  <LastBlock
                    title="Distance"
                    label={detailedFraud.distance.distance}
                    statusColor={detailedFraud.distance.distance_status}
                  />
                }
              />
              {/* Interval */}
              {detailedFraud.show_interval_data && (
                <FraudBlock
                  rowNumber="2"
                  title={'Interval'}
                  error={''}
                  showError={false}
                  statusIcon={getIntervalStatusIcon()}
                  leftBlock={
                    <InnerBlock
                      title={ctIntl.formatMessage({ id: 'check interval' })}
                      label={getIntervalValue('checkInterval')}
                      labelTip={''}
                    />
                  }
                  centerBlock={
                    <InnerBlock
                      title={ctIntl.formatMessage({ id: 'toll interval' })}
                      label={getIntervalValue('tollInterval')}
                      position="middle"
                    />
                  }
                />
              )}
              {Boolean(isAddingStation) && addStationFormSection()}
            </BlocksFeedWrapper>
          )}
          {Boolean(isAddingReason) && addReasonFormSection()}
        </MainContainer>
        <ButtonsHolder>
          <Stack
            direction="row-reverse"
            sx={{
              justifyContent: 'space-between',
              m: 3,
            }}
          >
            {match(detailedFraud.toll_validation_status_id)
              .with(P.union(1, 3), () => (
                <Button
                  variant="contained"
                  color="error"
                  onClick={() =>
                    isValidationReasonValid
                      ? updateTollFraud(false, isAddingReason?.reason)
                      : takeFraudAction(false)
                  }
                >
                  {ctIntl.formatMessage({
                    id: 'Mark as Risk',
                  })}
                </Button>
              ))
              .with(4, () => (
                <Box>
                  {detailedFraud.fleet_check_result_id ===
                    TollFraudStatus.FAIL_MISSING_TOLL_STATION && (
                    <Button
                      variant="outlined"
                      color="secondary"
                      sx={{ mr: '10px' }}
                      size={'small'}
                      onClick={() => {
                        setIsAddingStation({
                          stationName: '',
                        })
                      }}
                      disabled={Boolean(isAddingStation)}
                    >
                      {ctIntl.formatMessage({
                        id: 'Add Toll Station',
                      })}
                    </Button>
                  )}
                  <Button
                    onClick={() =>
                      isValidationReasonValid
                        ? updateTollFraud(true, isAddingReason?.reason)
                        : takeFraudAction(true)
                    }
                    disabled={
                      Boolean(isAddingStation) ||
                      (Boolean(isAddingReason) && !isValidationReasonValid)
                    }
                    color="success"
                    variant="contained"
                    size={'small'}
                  >
                    {ctIntl.formatMessage({
                      id: 'Approve Transaction',
                    })}
                  </Button>
                </Box>
              ))
              .otherwise(() => (
                <>
                  <Box>
                    {detailedFraud.fleet_check_result_id ===
                      TollFraudStatus.FAIL_MISSING_TOLL_STATION && (
                      <Button
                        variant="outlined"
                        color="secondary"
                        sx={{ mr: '10px' }}
                        size={'small'}
                        onClick={() => {
                          setIsAddingStation({
                            stationName: '',
                          })
                        }}
                        disabled={Boolean(isAddingStation)}
                      >
                        {ctIntl.formatMessage({
                          id: 'Add Toll Station',
                        })}
                      </Button>
                    )}
                    <Button
                      onClick={() =>
                        isValidationReasonValid
                          ? updateTollFraud(true, isAddingReason?.reason)
                          : takeFraudAction(true)
                      }
                      disabled={Boolean(isAddingStation) || Boolean(isAddingReason)}
                      color="success"
                      variant="contained"
                      size={'small'}
                    >
                      {ctIntl.formatMessage({
                        id: 'Approve Transaction',
                      })}
                    </Button>
                  </Box>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() =>
                      isValidationReasonValid
                        ? updateTollFraud(false, isAddingReason?.reason)
                        : takeFraudAction(false)
                    }
                    disabled={Boolean(isAddingStation) || Boolean(isAddingReason)}
                    sx={{ margin: 'auto 10px' }}
                    size={'small'}
                  >
                    {ctIntl.formatMessage({
                      id: 'Mark as Risk',
                    })}
                  </Button>
                </>
              ))}
          </Stack>
        </ButtonsHolder>
      </Stack>
    </Box>
  )
}

const mapStateToProps = (state: AppState) => {
  const { defaultMapLat, defaultMapLon } = getSettings_UNSAFE(state)
  const defaultLocation = {
    lat: defaultMapLat || 0,
    lng: defaultMapLon || 0,
  }
  return {
    defaultLocation,
  }
}

const actionCreators = {
  addStation: reducerActions.addNewFraudTollStation,
  recheckTollFraud: actions.recheckTollFraud,
}

export default connect(mapStateToProps, actionCreators)(TollFraud)

const StationActionWrapper = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  '.button': {
    maxWidth: '50%',
  },
})

const AddStationWrapper = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  padding: '10px',
  backgroundColor: '#f9f9f9',
  border: '1px solid transparent',
  borderRadius: '10px',
  marginBottom: '10px',
})

const MainContainer = styled(Box)`
  margin: 0 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 500px;
`
