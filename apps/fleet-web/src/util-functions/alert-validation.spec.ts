import { describe, it } from 'vitest'
import { vExpect } from '@fleet-web/vitest/utils'

import { validTriggers } from './alert-validation'

describe('validTriggers function', () => {
  it('returns true when all triggers are valid numbers', () => {
    const triggers = [1, 2, 3, 4]
    const result = validTriggers(triggers)
    vExpect(result).toBe(true)
  })

  it('returns false when triggers array is empty', () => {
    const triggers = [] as Array<number>
    const result = validTriggers(triggers)
    vExpect(result).toBe(false)
  })

  it('returns false when at least one trigger is not finite', () => {
    const triggers = [1, 2, Number.NaN, 4] // NaN is not finite
    const result = validTriggers(triggers)
    vExpect(result).toBe(false)
  })

  it('returns false when triggers array is null', () => {
    const triggers = null
    const result = validTriggers(triggers)
    vExpect(result).toBe(false)
  })

  it('returns false when triggers array is undefined', () => {
    const triggers = undefined
    const result = validTriggers(triggers)
    vExpect(result).toBe(false)
  })
})
