import { describe, it } from 'vitest'
import { vExpect } from '@fleet-web/vitest/utils'

import dropDownSort from './dropdown-sort-util'

describe('dropDownSort function', () => {
  const items = [
    { name: 'Apple', value: 'A' },
    { name: '<PERSON><PERSON>', value: 'B' },
    { name: '<PERSON>', value: 'C' },
  ]

  const sortByProp = 'name'

  const selected = { name: 'Banana', value: 'B' }

  it('sorts and drops the selected item to the top of the list', () => {
    const result = dropDownSort(items, sortByProp, selected)
    vExpect(result[0]).toBe(selected)
    vExpect(result[1].name).toBe('Apple')
    vExpect(result[2].name).toBe('Cherry')
  })

  it('resolves missing properties with an empty string', () => {
    const result = dropDownSort(items, sortByProp, selected)
    for (const item of result) {
      vExpect(item[sortByProp]).toBeDefined()
    }
  })

  it('handles items with missing property', () => {
    const modifiedItems = [
      { name: '<PERSON>' },
      { name: '<PERSON><PERSON>', value: 'B' },
      { name: '<PERSON>', value: 'C' },
    ]
    const result = dropDownSort(modifiedItems, sortByProp, selected)
    vExpect(result[0]).toBe(selected)
    vExpect(result[1].name).toBe('Apple')
    vExpect(result[2].name).toBe('Cherry')
  })

  it('handles items with duplicate names', () => {
    const duplicateItems = [
      { name: 'Apple', value: 'X' },
      { name: 'Banana', value: 'B' },
      { name: 'Cherry', value: 'C' },
      { name: 'Apple', value: 'Y' },
    ]
    const result = dropDownSort(duplicateItems, sortByProp, selected)
    vExpect(result[0]).toBe(selected)
    vExpect(result[1].name).toBe('Apple')
    vExpect(result[2].name).toBe('Cherry')
  })
})
