import { describe, it } from 'vitest'
import { vExpect } from '@fleet-web/vitest/utils'

import { toNumber } from './form-normalization'

describe('form-normalization', () => {
  describe('toNumber', () => {
    it('should return the value', () => {
      vExpect(toNumber(1)).toEqual(1)
      vExpect(toNumber('')).toEqual(null)
      vExpect(toNumber('-')).toEqual('-')
      vExpect(toNumber('+')).toEqual(undefined)
      vExpect(toNumber('+', 4)).toEqual(4)
      vExpect(toNumber('3')).toEqual(3)
      vExpect(toNumber('-3')).toEqual(-3)
      vExpect(toNumber('3.14')).toEqual(3.14)
      vExpect(toNumber('-3.14')).toEqual(-3.14)
      vExpect(toNumber('3.14.15', 3.14)).toEqual(3.14)
    })
  })
})
