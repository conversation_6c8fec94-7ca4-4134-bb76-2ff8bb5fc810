import html2canvas from '@trainiac/html2canvas'
import { jsPDF } from 'jspdf'
import XLSX from 'xlsx'

import { enqueueSnackbarWithCloseAction } from '@fleet-web/components/Snackbar/Notistack/utils'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

export type InputEncoding = 'base64' | 'binary' | 'text'

export const getFileFromEvent = (event: React.ChangeEvent<HTMLInputElement>) => {
  const files = event.currentTarget.files

  if (files !== null) {
    return files[0]
  }
  return undefined
}

export const getFileExtension = (file: File) => file.name.split('.').pop()

export const readFileData = (file: File, encoding: InputEncoding = 'base64') => {
  if (encoding === 'binary') {
    return file.arrayBuffer()
  } else {
    const fr = new FileReader()

    return new Promise<string | ArrayBuffer | null>((resolve, reject) => {
      if (encoding === 'base64') {
        fr.readAsDataURL(file)
      } else if (encoding === 'text') {
        // eslint-disable-next-line
        // eslint-disable-next-line unicorn/prefer-blob-reading-methods
        fr.readAsText(file)
      }

      fr.addEventListener('load', () => resolve(fr.result))
      fr.addEventListener('error', (err) => reject(err))
    })
  }
}

export const getXLSFileFromData = (data: FixMeAny, options = {}) => {
  const workbook = XLSX.read(data, {
    type: 'array',
    cellDates: true,
    ...options,
  })

  return workbook.Sheets[workbook.SheetNames[0]]
}

export const exportJSONToXLSFile = (
  headers = {},
  items: Array<Record<string, unknown>> = [],
  fileTitle = 'import_results',
) => {
  const jsonData = [headers, ...items]
  const fileName = `${fileTitle}.xls`

  const sheetName = fileTitle.length <= 31 ? fileTitle : 'import_results'

  const wb = XLSX.utils.book_new()
  const data = XLSX.utils.json_to_sheet(jsonData, { skipHeader: true })
  XLSX.utils.book_append_sheet(wb, data, sheetName)

  XLSX.writeFile(wb, fileName)
}

const PIXEL_TO_MM = 0.264583

export const downloadHtmlElementByIdAsPdf = ({
  elementId,
  fileName,
  title,
  displaySvg = true,
}: {
  elementId: string
  fileName: string
  title?: string
  displaySvg?: boolean
}) => {
  const container = document.getElementById(elementId) as HTMLElement
  const { offsetWidth, scrollHeight } = container
  const NewHeaderHeight = 100
  html2canvas(container, {
    width: offsetWidth,
    height: scrollHeight + NewHeaderHeight,
    onclone: (document) => {
      const clonedContainer = document.getElementById(elementId) as HTMLElement
      const newDiv = document.createElement('div')

      if (title) {
        const newContent = document.createTextNode(title)
        newDiv.appendChild(newContent)
        newDiv.style.fontSize = '1.25rem'
        newDiv.style.fontWeight = '500'
        clonedContainer.insertBefore(newDiv, clonedContainer.firstChild)
      }

      clonedContainer.style.padding = '16px'
      clonedContainer.style.width = `${offsetWidth}px`
      clonedContainer.style.height = `${scrollHeight + (title ? NewHeaderHeight : 0)}px`

      if (displaySvg) {
        // for displaying svg icons
        const svgElements = Array.from(
          clonedContainer.querySelectorAll('svg'),
        ) as Array<SVGSVGElement>
        for (const item of svgElements) {
          item.setAttribute('width', item.getBoundingClientRect().width.toString())
          item.setAttribute('height', item.getBoundingClientRect().height.toString())
        }
      }
    },
    useCORS: true,
  }).then((canvas: HTMLCanvasElement) => {
    const width = canvas.width
    const height = canvas.height
    const orientation = width > height ? 'landscape' : 'portrait'

    const imgData = canvas.toDataURL('image/jpeg')
    const doc = new jsPDF({
      unit: 'mm',
      format: [canvas.width, canvas.height],
    })
    doc.deletePage(1)
    doc.addPage(
      [Math.floor(width * PIXEL_TO_MM), Math.floor(height * PIXEL_TO_MM)],
      orientation,
    )
    doc.addImage(imgData, 'JPEG', 0, 0, 0, 0)
    doc.save(fileName)

    enqueueSnackbarWithCloseAction(
      ctIntl.formatMessage({ id: 'Downloaded successfully!' }),
      { variant: 'success' },
    )
  })
}

export const showSupportedFileFormats = (formats: Array<string>) =>
  formats.map((format) => `.${format}`).join(', ')
