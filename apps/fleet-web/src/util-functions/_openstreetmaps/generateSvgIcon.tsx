import { groupBy } from 'lodash'
import type L from 'leaflet'

import type { FixMeAny } from '@fleet-web/types'

export function generateSvgIcon(
  cluster: L.MarkerCluster,
  statusColors: Record<string, string>,
) {
  const defaultOffset = 5
  const totalClustered = cluster.getChildCount()

  const items = cluster.getAllChildMarkers().map((item) => ({
    status: (item.options as FixMeAny).status as string,
  }))

  const groupedVehiclesStatus = groupBy(items, 'status')
  const list = Object.keys(groupedVehiclesStatus)
    .map((item) => {
      const fill = statusColors

      const count = groupedVehiclesStatus[item].length
      const strokePercentage = (count / totalClustered) * 100

      const color =
        Object.keys(statusColors).find((color) => item.includes(color)) || 'no-signal'

      return {
        name: item,
        fill: fill[color],
        percentage: strokePercentage,
        strokeDasharray: `${strokePercentage} ${100 - strokePercentage}`,
      }
    })
    .reduce(
      (acc, b, i) => {
        const item = {
          ...b,
          offset: (() => {
            if (i === 0) {
              return defaultOffset
            }

            const reduced = acc
              .map((item) => item.percentage)
              .reduce((x, y) => x + y, 0)
            return 100 - reduced + defaultOffset
          })(),
        }

        acc.push(item)
        return acc
      },
      [] as Array<{
        name: string
        fill: string
        percentage: number
        strokeDasharray: string
        offset: number
      }>,
    )

  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 42 42"
      className="OpenLayerPieCluster-donut"
    >
      <circle
        className="donut-hole"
        cx="21"
        cy="21"
        r="15.91549430918954"
        fill="#fff"
      />
      <circle
        className="donut-ring"
        cx="21"
        cy="21"
        r="15.91549430918954"
        fill="transparent"
        stroke="#fff"
        strokeWidth="5"
      />

      {list.map(({ fill, strokeDasharray, offset, name: key }) => (
        <circle
          className="donut-segment"
          cx="21"
          cy="21"
          r="15.91549430918954"
          fill="transparent"
          stroke={fill}
          strokeWidth={5}
          strokeDasharray={strokeDasharray}
          strokeDashoffset={offset}
          key={key}
        />
      ))}

      <g className="OpenLayerPieCluster-donut-chart-text">
        <text
          x="50%"
          y="50%"
          className="OpenLayerPieCluster-donut-chart-number"
        >
          {totalClustered}
        </text>
      </g>
    </svg>
  )
}
