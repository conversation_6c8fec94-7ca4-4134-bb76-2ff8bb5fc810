import type { FixMeAny } from '@fleet-web/types'

/**
 * Allows multiple refs to the same element to be acquired so that none is overwritten by another
 */
export const combineRefs =
  <E>(
    ...refs: Array<
      ((instance: E | null) => void) | React.RefObject<E> | null | undefined
    >
  ) =>
  (instance: E) => {
    for (const ref of refs) {
      if (typeof ref === 'function') {
        ref(instance)
      } else if (ref) {
        const refAny = ref as FixMeAny

        refAny.current = instance
      }
    }
  }
