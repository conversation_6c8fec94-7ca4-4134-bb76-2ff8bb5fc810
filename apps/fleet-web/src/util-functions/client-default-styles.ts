import StyleCartrackMenuIconDefault from '@fleet-web/svg-inline/cartrack-main-logo-white.svg'
import StyleCartrackIconPathDefault from '@fleet-web/svg-inline/cartrack-menu-icon-white.svg'

export const defaultStyles = {
  styleAppName: 'Fleet Platform',
  styleLoginBackgroundColour: '#F9F9F9',
  styleLoginTextColour: '#3E5762',

  styleCustomerMainLogo: '',
  styleCustomerMenuLogo: '',

  // Sidebar Colors TODO: Redesign - BE update/add new colors
  styleNavbarColour: '#FFFFFF',
  styleSidebarMenuHeaderIconPath: StyleCartrackIconPathDefault,
  styleSidebarMenuFooterLogoPath: StyleCartrackMenuIconDefault,
  styleSidebarMenuFooterIconPath: StyleCartrackIconPathDefault,
  styleSidebarMenuActiveFontColour: '#FFFFFF',
  styleSidebarMenuInactiveFontColour: '#E0E0E0',
  styleSidebarMenuHoverFontColour: '#FFFFFF',
  styleSidebarMenuHoverColour: '#424242',
  styleSidebarMenuActiveColour: '#616161',
  styleSidebarSubMenuInactiveFontColour: '#BDBDBD',

  styleMenuActiveFontColour: '#FFFFFF',
  styleMenuInactiveFontColour: '#E0E0E0',

  // Used outside of the sidebar
  styleSubmenuActiveColour: '#F47735',
  styleMenuActiveIconColour: '#6AD04E',
  styleMenuInactiveIconColour: '#FFFFFF',
  // END

  styleActiveButtonsColour: '#F47735',
  styleTableColumnFontColourActive: '#F47735',
  styleInputfieldColourActive: '#F47735',
  styleInputfieldColourSelection: '#F47735',
  styleInputfieldColourHover: '#EEEEEE',
  styleInputfieldIconColourStandard: '#467BA0',
  styleInputfieldIconColourActive: '#F47735',
  styleLoadingBarColour: '#F47735',
  styleLoadingSpinnerColour: '#F47735',
  styleIconColour: '#F47735',
  styleSuperscriptCounterColour: '#F47735',

  styleButtonDefaultColourActive: '#F47735',
  styleButtonDefaultTextColourActive: '#FFFFFF',
  styleButtonDefaultColourStandard: '#F9F9F9',
  styleButtonDefaultTextColourStandard: '#666666',
  styleButtonDefaultBorderColourStandard: '#CCCCCC',
  styleButtonDefaultColourHover: '#F9F9F9',
  styleButtonDefaultTextColourHover: '#F47735',

  styleButtonActionColourActive: '#23567C',
  styleButtonActionTextColourActive: '#FFFFFF',
  styleButtonActionColourStandard: '#5390BC',
  styleButtonActionTextColourStandard: '#FFFFFF',
  styleButtonActionColourHover: '#2E72A4',
  styleButtonActionTextColourHover: '#FFFFFF',
  styleButtonActionColourDisabled: '#5390BC',
  styleButtonActionTextColourDisabled: '#F9F9F9',

  styleButtonLinkColourActive: 'transparent',
  styleButtonLinkTextColourActive: '#23567C',
  styleButtonLinkColourStandard: 'transparent',
  styleButtonLinkTextColourStandard: '#5390BC',
  styleButtonLinkColourHover: 'transparent',
  styleButtonLinkTextColourHover: '#2E72A4',
  styleButtonLinkColourDisabled: 'transparent',

  styleButtonRedColourActive: '#963825',
  styleButtonRedTextColourActive: '#FFFFFF',
  styleButtonRedColourStandard: '#F9F9F9',
  styleButtonRedTextColourStandard: '#CE5239',
  styleButtonRedColourHover: '#CE5239',
  styleButtonRedTextColourHover: '#FFFFFF',
  styleButtonRedColourDisabled: '#FFFFFF',

  styleButtonGreenColourActive: '#3F7F42',
  styleButtonGreenTextColourActive: '#FFFFFF',
  styleButtonGreenColourStandard: '#F9F9F9',
  styleButtonGreenTextColourStandard: '#5CAE60',
  styleButtonGreenColourHover: '#5CAE60',
  styleButtonGreenTextColourHover: '#FFFFFF',

  styleButtonRaisedColourActive: '#F47735',
  styleButtonRaisedTextColourActive: '#FFFFFF',
  styleButtonRaisedColourStandard: '#F9F9F9',
  styleButtonRaisedTextColourStandard: '#666666',
  styleButtonRaisedColourHover: '#F9F9F9',
  styleButtonRaisedTextColourHover: '#F47735',

  styleButtonWhiteColourActive: '#FFFFFF',
  styleButtonWhiteTextColourActive: '#727272',
  styleButtonWhiteColourStandard: '#333333',
  styleButtonWhiteTextColourStandard: '#FFFFFF',
  styleButtonWhiteColourHover: '#8f8f8f',
  styleButtonWhiteTextColourHover: '#FFFFFF',
  styleMainLogoPoweredBy: false,
  styleMenuLogoPoweredBy: false,
  styleLogoPoweredByType: 1,
  styleLogoPoweredByType3Label: null,
  styleFavicon: 'favicon.png',
  styleLoginLanguage: true,
  styleLoginFooterHeight: 50,
  styleLoginForgotUsername: true,
  styleLoginStayLoggedIn: true,
  styleLoginSignUp: true,
  styleLoginLogoInsideBox: false,
  styleLoginFooterLogo: false,
  styleLoginModalFooter: false,
  styleLoginFooterBackgroundColor: '#FFFFFF',
  styleLoginFooterContrastTextColor: '#000000',
  styleLoginMainTitleText: null,
}
