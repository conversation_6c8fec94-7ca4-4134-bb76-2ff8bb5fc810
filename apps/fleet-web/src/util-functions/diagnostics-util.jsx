import { isEmpty } from 'lodash'
import styled from 'styled-components'

import ArrowedTooltip from '@fleet-web/components/_popups/Tooltip/Arrowed'

const StyledArrowedTooltip = styled(ArrowedTooltip)`
  max-height: 400px;
  max-width: 200px;
  text-align: left;
`
const StyledSpanWithHover = styled.span`
  &:hover {
    text-decoration: underline;
  }
`

export const renderVehicleGroupsColumn = (headerName, columnWidth) => {
  const renderCell = (rowValue) => {
    const noValue = '-'
    const [, , ...rest] = rowValue
    const showValue = rowValue.length > 2 && rowValue.slice(0, 2)

    if (isEmpty(rowValue)) {
      return noValue
    }

    if (rowValue.length > 2) {
      return (
        <span>
          {showValue.join(', ')},
          <StyledArrowedTooltip
            label={rest.join(', ')}
            placement="bottom"
          >
            <span>
              {' '}
              <StyledSpanWithHover>{`+${rest.length}...`}</StyledSpanWithHover>
            </span>
          </StyledArrowedTooltip>
        </span>
      )
    }

    return rowValue.join(', ')
  }

  return {
    Header: headerName,
    accessor: 'vehicleGroup',
    className: 'table-cell EngineAlerts-table-cell-left',
    width: columnWidth,
    Cell: (row) => renderCell(row.value),
  }
}
