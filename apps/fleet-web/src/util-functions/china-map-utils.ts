/**
 *
 * BASED ON https://github.com/googollee/eviltransform/blob/master/javascript/transform.js
 */

/**
 * NOTE: According to https://en.wikipedia.org/wiki/Restrictions_on_geographic_data_in_China
 * google maps has worked with autonavi to applied gcj02 to the street map, while still wgs84 to satellite.
 * So the consequence is we need to conver from wgs84 to gcj02 in:
 * 1. Macau with autonavi
 * 2. Hongkong with autonavi
 * 3. satellite (hybrid) with Google map in China
 */

import { MapApiProvider } from '@fleet-web/api/user/types'

import { CHINA_WITHOUT_TAIWAN_POLYGON } from './predefined-geo-boundaries/china-without-taiwan'
import { hongKongBoundaries } from './predefined-geo-boundaries/hong-kong'
import { MACAU_POLYGON, MACAU_POLYGON_2 } from './predefined-geo-boundaries/macau'
import {
  isPointInPolygon,
  // isPointWithinBoundaries,
} from './predefined-geo-boundaries/utils'

type LatLng = {
  lat: number
  lng: number
}

const earthR = 6378137

const isCoordinateInHongKong = (point: LatLng) =>
  // isPointWithinBoundaries({ lat, lng }, hongKongBoundaries)
  isPointInPolygon(point, hongKongBoundaries)

const isCoordinateInMacau = (point: LatLng) =>
  // isPointWithinBoundaries({ lat, lng }, MACAU_BOUNDARIES)
  isPointInPolygon(point, MACAU_POLYGON) || isPointInPolygon(point, MACAU_POLYGON_2)

const isCoordinateInChinaWithoutTaiwan = (point: LatLng) =>
  isPointInPolygon(point, CHINA_WITHOUT_TAIWAN_POLYGON)

function transform(x: number, y: number) {
  const xy = x * y
  const absX = Math.sqrt(Math.abs(x))
  const xPi = x * Math.PI
  const yPi = y * Math.PI
  const d = 20 * Math.sin(6 * xPi) + 20 * Math.sin(2 * xPi)

  let lat = d
  let lng = d

  lat += 20 * Math.sin(yPi) + 40 * Math.sin(yPi / 3)
  lng += 20 * Math.sin(xPi) + 40 * Math.sin(xPi / 3)

  lat += 160 * Math.sin(yPi / 12) + 320 * Math.sin(yPi / 30)
  lng += 150 * Math.sin(xPi / 12) + 300 * Math.sin(xPi / 30)

  lat *= 2 / 3
  lng *= 2 / 3

  lat += -100 + 2 * x + 3 * y + 0.2 * y * y + 0.1 * xy + 0.2 * absX
  lng += 300 + x + 2 * y + 0.1 * x * x + 0.1 * xy + 0.1 * absX

  return { lat: lat, lng: lng }
}

function delta({ lat, lng }: LatLng) {
  // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
  const ee = 0.00669342162296594323
  const d = transform(lng - 105, lat - 35)
  const radLat = (lat / 180) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  d.lat = (d.lat * 180) / (((earthR * (1 - ee)) / (magic * sqrtMagic)) * Math.PI)
  d.lng = (d.lng * 180) / ((earthR / sqrtMagic) * Math.cos(radLat) * Math.PI)
  return d
}

function wgs2gcj(point: LatLng): LatLng {
  const { lng, lat } = point

  if (lat === 360 && lng === 360) {
    return point
  }

  const d = delta(point)
  return { lat: lat + d.lat, lng: lng + d.lng }
}

const isInHongKongOrMacau = ({ lng, lat }: LatLng) =>
  isCoordinateInHongKong({ lat, lng }) || isCoordinateInMacau({ lat, lng })

const wgs2gcjIfInHongKongOrMacau = (point: LatLng): LatLng =>
  isInHongKongOrMacau(point) ? wgs2gcj(point) : point

const gcj2wgs = ({ gcjLat, gcjLng }: { gcjLat: number; gcjLng: number }) => {
  if (gcjLat === 360 && gcjLng === 360) {
    return { lng: 360, lat: 360 }
  }

  const d = delta({ lat: gcjLat, lng: gcjLng })
  return { lat: gcjLat - d.lat, lng: gcjLng - d.lng }
}

function gcj2wgsIfInHongKongOrMacau(gcjPoint: { gcjLat: number; gcjLng: number }) {
  const point = { lat: gcjPoint.gcjLat, lng: gcjPoint.gcjLng }
  const wgsPoint = gcj2wgs(gcjPoint)
  return isInHongKongOrMacau(wgsPoint) ? wgsPoint : point
}

const wgs2gcjIfInChinaMainland = (point: LatLng): LatLng =>
  isCoordinateInChinaWithoutTaiwan(point) && !isInHongKongOrMacau(point)
    ? wgs2gcj(point)
    : point

// Knowledge:
// 1. Wgs -> Gcj, the coordinate would move to right down
// 2. isPointInPolygon use wgs to check the point and polygon, so we have to use wgs coord
// so when we have a coordinate in the border of mainland and HK, it's IMPOSSIBLE to check if it's wgs or gcj
// Currently, we convert it to wgs to check if it's in HK/MC, so it may have some frustration for the cross border
const gcj2wgsIfInChinaMainland = (gcjPoint: { gcjLat: number; gcjLng: number }) => {
  const point = { lat: gcjPoint.gcjLat, lng: gcjPoint.gcjLng }
  const wgsPoint = gcj2wgs(gcjPoint)

  return isCoordinateInChinaWithoutTaiwan(point) && !isInHongKongOrMacau(wgsPoint)
    ? wgsPoint
    : point
}

const wgs2gcjIfInChinaWithoutTaiwan = (point: LatLng): LatLng =>
  isCoordinateInChinaWithoutTaiwan(point) ? wgs2gcj(point) : point

const gcj2wgsIfInChinaWithoutTaiwan = (gcjPoint: {
  gcjLat: number
  gcjLng: number
}): LatLng => {
  const point = { lat: gcjPoint.gcjLat, lng: gcjPoint.gcjLng }
  const wgsPoint = gcj2wgs(gcjPoint)
  return isCoordinateInChinaWithoutTaiwan(wgsPoint) ? wgsPoint : point
}

/**
 * Gets the wgs84 value from the rendered coords (which can be either wgs84 or gcj02) according to the map provider and map type id
 * @returns
 */
const getWgs84ValueFromRenderedCoordsOnMap = ({
  renderedCoordsInWgsOrGcj,
  mapApiProviderId,
  mapTypeId,
}: {
  renderedCoordsInWgsOrGcj: LatLng
  mapApiProviderId: MapApiProvider
  mapTypeId: google.maps.MapTypeId
}) => {
  // NOTE: if it's from autonavi map, the position would be gcj in China and HK/MC
  if (mapApiProviderId === MapApiProvider.AUTO_NAVI_CHINESE) {
    return gcj2wgsIfInChinaWithoutTaiwan({
      gcjLat: renderedCoordsInWgsOrGcj.lat,
      gcjLng: renderedCoordsInWgsOrGcj.lng,
    })
    // NOTE: if it's from google map, the position would only be gcj in China mainland
  } else if (mapApiProviderId === MapApiProvider.GOOGLE && mapTypeId === 'roadmap') {
    return gcj2wgsIfInChinaMainland({
      gcjLat: renderedCoordsInWgsOrGcj.lat,
      gcjLng: renderedCoordsInWgsOrGcj.lng,
    })
  }

  return renderedCoordsInWgsOrGcj
}

export {
  wgs2gcjIfInHongKongOrMacau,
  wgs2gcjIfInChinaMainland,
  wgs2gcjIfInChinaWithoutTaiwan,
  gcj2wgsIfInHongKongOrMacau,
  gcj2wgsIfInChinaMainland,
  gcj2wgsIfInChinaWithoutTaiwan,
  getWgs84ValueFromRenderedCoordsOnMap,
}
