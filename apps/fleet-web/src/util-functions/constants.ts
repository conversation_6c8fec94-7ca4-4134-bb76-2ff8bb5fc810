import { invert } from 'lodash'

import type { ComputedEventStatusForUI } from '@fleet-web/modules/map-view/map/types'

export const GOOGLE_MAPS_VERSION = '3.60.10'
/**
 * In meters
 */
export const GOOGLE_AUTO_COMPLETE_RADIUS_USED_FOR_PREDICTION_BIASING = 35000
export const MAP_DEFAULT_ZOOM = 12
export const MAP_MAX_ZOOM = 19
export const HERE_MAPS_MAX_ZOOM = 18
/** Value to be used for incrementing and decrementing zoom levels */
export const MAP_ZOOM_CALCULATION_VALUE = 1
export const MAP_MIN_ZOOM = 3
export const MAP_EVENT_SAMPLE_THRESHOLD = 60000

export const MAP_CLUSTER_MIN_ZOOM = 0
export const MAP_CLUSTER_MAX_ZOOM = MAP_MAX_ZOOM - 2
export const MAP_CLUSTER_RADIUS = 42

export const MAP_PLACE_ZOOM = 15

export const VIEW_MODE = {
  portrait: 0,
  landscape: 1,
  fullscreen: 2,
} as const

export const EVENT_TYPE_TO_ID = {
  'harsh-accel': 32,
  'harsh-braking': 30,
  'harsh-turning': 35,
  idling: 46,
  'excessive-idling': 29,
  'fare-start': 159,
  'fare-end': 160,
  'excessive-rpm': 47,
}

export const EVENT_ID_TO_TYPE = invert(EVENT_TYPE_TO_ID)

export const STATUS_TO_MESSAGE: Record<ComputedEventStatusForUI, string> = {
  stationary: 'Stationary',
  driving: 'Driving',
  'driving-manual': 'Driving',
  'harsh-accel': 'Harsh Acceleration',
  'harsh-acceleration': 'Harsh Acceleration',
  'harsh-braking': 'Harsh Braking',
  'harsh-turning': 'Harsh Turning',
  idling: 'Idling',
  'ignition-on': 'Ignition On',
  'ignition-off': 'Ignition Off',
  'max-speed': 'Max Speed',
  'no-signal ns-with-time': 'Last Seen',
  'no-signal': 'Not Connected',
  'off-duty': 'Off Duty',
  'on-duty': 'On Duty',
  'sleeper-berth': 'Sleeper Berth',
  speeding: 'Speeding',
  pc: 'PC',
  ym: 'YM',
  'excessive-idling': 'Excessive Idling',
  maintenance: 'Maintenance',
  'excessive-rpm': 'Excessive RPM',
  'moving-ignition-off': 'Moving - Ignition Off',
  vision: 'Vision',
  generic_sensor: 'Sensor',
}

export const MINITRACK_ICON_TO_TYPE = {
  0: 'Default',
  1: 'Trailer',
  2: 'Container',
  3: 'Cage',
  4: 'Compactor',
}
