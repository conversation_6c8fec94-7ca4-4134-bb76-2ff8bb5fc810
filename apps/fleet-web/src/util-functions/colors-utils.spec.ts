import { describe, it } from 'vitest'
import { vExpect } from '@fleet-web/vitest/utils'

import { colorToRgb } from './colors-utils'

describe('colorToRgb function', () => {
  it('returns the same color string if it starts with "rgb"', () => {
    const color = 'rgb(255, 0, 0)'
    const result = colorToRgb(color)
    vExpect(result).toBe(color)
  })

  it('converts hex color to RGB format', () => {
    const color = '#00ff00'
    const result = colorToRgb(color)
    vExpect(result).toBe('rgb(0, 255, 0)')
  })

  it('returns the same color string if it is neither "rgb" nor hex format', () => {
    const color = 'blue'
    const result = colorToRgb(color)
    vExpect(result).toBe(color)
  })

  it('handles uppercase hex color format', () => {
    const color = '#FF00AA'
    const result = colorToRgb(color)
    vExpect(result).toBe('rgb(255, 0, 170)')
  })

  it('handles short hex color format', () => {
    const color = '#123'
    const result = colorToRgb(color)
    vExpect(result).toBe('rgb(17, 34, 51)')
  })
})
