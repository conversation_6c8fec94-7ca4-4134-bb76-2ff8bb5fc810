/* eslint-disable no-nested-ternary */
import { memo, useMemo } from 'react'
import { connect } from 'react-redux'
import type { Except } from 'type-fest'

import { getSettings_UNSAFE } from '@fleet-web/duxs/user'
import type { AppState } from '@fleet-web/root-reducer'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const CONVERSION_RATE = 1.60934

const formatKmToMiles = (distance: number) => distance / CONVERSION_RATE

type Props = {
  value: number
  units?: boolean
  round?: boolean
  isMeters?: boolean
  perTime?: boolean
  distanceInMiles: boolean
}

type Options = {
  isMiles: boolean
} & Except<Props, 'distanceInMiles' | 'value'>

/**
 * @deprecated Prefer using `useUserFormatLengthInKmOrMiles` instead
 */
export function formatDistance(
  value: number,
  { units, perTime, round, isMeters, isMiles }: Options,
) {
  const perTimeUnit = ctIntl.formatMessage({
    id: isMiles ? 'units.distancePerHour.mile' : 'units.distancePerHour',
  })
  const shortUnit = ctIntl.formatMessage({
    id: isMeters
      ? 'units.distance.smallShort'
      : isMiles
        ? 'units.distance.mile.short'
        : 'units.distance.short',
  })

  const parsedUnits = perTime ? perTimeUnit : units ? shortUnit : ''
  let parsedValue = isMiles ? formatKmToMiles(value).toFixed(2) : value
  if (round) {
    parsedValue = Math.round(Number(parsedValue))
  }

  return `${parsedValue} ${parsedUnits}`
}

/**
 * @deprecated Prefer using `<UserFormattedLengthInKmOrMiles />` instead
 */
function FormattedDistance({
  value,
  units,
  round,
  perTime,
  isMeters,
  distanceInMiles,
}: Props) {
  const formattedValue = useMemo(() => {
    const options = {
      units: units,
      round: round,
      perTime: perTime,
      isMeters: isMeters,
      isMiles: distanceInMiles,
    }

    return formatDistance(value, options)
  }, [distanceInMiles, isMeters, perTime, round, units, value])

  return <span>{formattedValue}</span>
}

function mapStateToProps(state: AppState) {
  const { distanceInMiles } = getSettings_UNSAFE(state)
  return {
    distanceInMiles,
  }
}

export default memo(connect(mapStateToProps)(FormattedDistance))
