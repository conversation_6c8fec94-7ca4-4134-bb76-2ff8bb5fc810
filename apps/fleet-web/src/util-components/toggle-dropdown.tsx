import { PureComponent } from 'react'
import { startCase } from 'lodash'
import { FormattedMessage } from 'react-intl'
import type { ReadonlyDeep } from 'type-fest'

import Icon from '@fleet-web/components/Icon'
import type { FixMeAny } from '@fleet-web/types'
import {
  hasNoWhiteSpacesAndAtLeastADot,
  startCaseNoDestructive,
} from '@fleet-web/util-functions/string-utils'

import Checkbox from './checkbox'

type ItemCheckboxProps = {
  checked: boolean
  child: boolean
  disabled?: boolean
  label: string
  name: string
  onChange: (checked: boolean, name: string) => void
  showCaret: boolean
  noDestructiveStartCase: boolean
  isExpanded?: boolean
  handleToggleExpandded?: (key: string) => void
}

const ItemCheckbox = ({
  checked,
  child,
  disabled = false,
  label,
  name,
  onChange,
  showCaret,
  noDestructiveStartCase = false,
  isExpanded = false,
  handleToggleExpandded,
}: ItemCheckboxProps) => (
  <div className={`ToggleDropdown-item ${child ? 'ToggleDropdown-item--child' : ''}`}>
    <Checkbox
      extraClassNames={{ labelClassNames: 'label' }}
      label={
        // eslint-disable-next-line no-nested-ternary
        hasNoWhiteSpacesAndAtLeastADot(label)
          ? label
          : noDestructiveStartCase
            ? startCaseNoDestructive(label)
            : startCase(label)
      }
      name={name}
      value={checked}
      small={child}
      onChange={onChange}
      disabled={disabled}
    />
    {showCaret && !!handleToggleExpandded && (
      <Icon
        icon={`caret-${isExpanded ? 'up' : 'down'}`}
        className="ToggleDropdown-arrow"
        onClick={(e: React.MouseEvent<HTMLOrSVGElement, MouseEvent>) => {
          e.stopPropagation()
          handleToggleExpandded(name)
        }}
      />
    )}
  </div>
)

/** Allows for legacy code to do something like booleanVariable.children without changing the code for now */
type BooleanWithChildrenPlaceholder = boolean & {
  children?: undefined
  label?: undefined
}

type Options = {
  [key: string]:
    | {
        label?: string
        children?: Array<{ key: string; disabled?: boolean; label?: string }>
      }
    | BooleanWithChildrenPlaceholder
}

type Props = typeof ToggleDropdown.defaultProps & {
  optionsButtonId?: string
  options: Options | ReadonlyDeep<Options>
  submenuId?: string
  optionsLineId?: string
  values: Record<string, FixMeAny>
  className?: string
  hasOtherProps?: boolean
  innerClassName?: string
  onChange: (optionChanged: { [name: string]: boolean }) => void
  label: string
  component?: React.ComponentType<FixMeAny>
  containerHeight?: number
  styleProperties?: Record<string, FixMeAny>
  noDestructiveStartCase?: boolean
}

type State = { isOpen: boolean; expanded: Record<string, boolean> }

class ToggleDropdown extends PureComponent<Props, State> {
  static defaultProps = {
    optionsButtonId: '',
    className: '',
    submenuId: '',
    optionsLineId: '',
    hasOtherProps: false,
    innerClassName: '',
    containerHeight: 0,
    styleProperties: {},
    noDestructiveStartCase: false,
  }

  constructor(props: Props) {
    super(props)
    this.state = {
      isOpen: false,
      expanded: {},
    }
  }

  handleCheckboxClick = (value: boolean, label: string) => {
    const update = {
      [label]: value,
    }
    this.props.onChange(update)
    // Show the child options if tick to parent item
    if (value) {
      const newExpanded = { ...this.state.expanded }
      newExpanded[label] = true
      this.setState({ expanded: newExpanded })
    }
  }

  handleToggleOpen = () => {
    this.setState({ isOpen: !this.state.isOpen })
  }

  getToggleStyle = () => {
    const { containerHeight } = this.props
    const style =
      containerHeight === 0
        ? {}
        : {
            maxHeight: containerHeight - 45,
          }

    return style
  }

  handleMouseLeave = () => {
    if (this.state.isOpen) {
      this.setState({ isOpen: false })
    }
  }

  handleToggleExpandded = (key: string) => {
    const newExpanded = { ...this.state.expanded }
    newExpanded[key] = !newExpanded[key]
    this.setState({ expanded: newExpanded })
  }

  render() {
    const {
      options,
      values,
      className,
      hasOtherProps,
      innerClassName,
      label,
      component: Component,
      submenuId,
      optionsLineId,
      noDestructiveStartCase,
    } = this.props

    return (
      <div
        id={submenuId}
        className={`ToggleDropdown ${className} ${this.state.isOpen ? 'is-open' : ''}`}
      >
        <div
          id={optionsLineId}
          className={`ToggleDropdown-inner
               ${innerClassName} ${this.state.isOpen ? 'is-open' : ''}`}
          style={this.getToggleStyle()}
          onMouseLeave={this.handleMouseLeave}
        >
          {Component && !this.state.isOpen ? (
            <Component
              id={this.props.optionsButtonId}
              onClick={this.handleToggleOpen}
              styleProperties={this.props.styleProperties}
            />
          ) : (
            <div
              className="ToggleDropdown-header"
              onClick={this.handleToggleOpen}
            >
              <FormattedMessage
                id={label}
                defaultMessage={label}
              />
              <Icon
                icon="caret-down"
                className={`
                    ToggleDropdown-header-toggle
                    ${this.state.isOpen ? 'is-open' : ''}`}
              />
            </div>
          )}
          {this.state.isOpen ? (
            <div>
              {Object.keys(this.props.options).map((key) => {
                const item = options[key]
                if (['Selected', 'Edit', 'Delete'].includes(key)) return null

                return (
                  <div key={key}>
                    <ItemCheckbox
                      key={key}
                      name={key}
                      label={item.label ?? key}
                      checked={values[key]}
                      onChange={this.handleCheckboxClick}
                      child={false}
                      showCaret={(item.children || []).length > 0}
                      noDestructiveStartCase={noDestructiveStartCase}
                      isExpanded={!!this.state.expanded[key]}
                      handleToggleExpandded={this.handleToggleExpandded}
                    />
                    {!!this.state.expanded[key] && item.children
                      ? item.children.map((child) => {
                          const item = hasOtherProps
                            ? child.key
                            : (child as unknown as string)
                          const disable = hasOtherProps ? child.disabled : false
                          return (
                            <ItemCheckbox
                              key={item}
                              name={item}
                              label={child.label ?? item}
                              checked={values[item]}
                              onChange={this.handleCheckboxClick}
                              child
                              disabled={disable}
                              showCaret={false}
                              noDestructiveStartCase={noDestructiveStartCase}
                            />
                          )
                        })
                      : null}
                  </div>
                )
              })}
            </div>
          ) : null}
        </div>
      </div>
    )
  }
}

export default ToggleDropdown
