import type * as React from 'react'
import { isEmpty } from 'lodash'

import Icon from '@fleet-web/components/Icon'

import Button from './button'
import FileInput from './file-input'

const buttonClassNames = {
  containerClassNames: 'PersonInfo-updateImage',
  labelClassNames: 'Button--small',
}

type Props = {
  image?: string
  name: string
  onImageChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClearImage?: () => any
  showImageUpload?: boolean
  className?: string
}

const PersonInfo = ({
  image = '',
  name,
  onImageChange = () => null,
  showImageUpload = false,
  onClearImage = () => null,
  className,
}: Props) => {
  const hasImage = isEmpty(image) === false
  return (
    <figure className={`PersonInfo ${className}`}>
      {hasImage ? (
        <img
          src={image}
          className="PersonInfo-image"
        />
      ) : (
        <Icon
          icon="user-circle"
          className="PersonInfo-placeholder"
        />
      )}
      <div>
        <div className="PersonInfo-name">{name}</div>
        {showImageUpload && (
          <div className="PersonInfo-buttonGroup">
            <FileInput
              onChange={onImageChange}
              label={hasImage ? 'Update Image' : 'Upload Image'}
              icon="arrow-circle-up"
              accept="image/*"
              extraClassNames={buttonClassNames}
            />
            {image && (
              <div
                className={`PersonInfo-btnContainer ${buttonClassNames.containerClassNames}`}
              >
                <Button
                  className={`PersonInfo-label ${buttonClassNames.labelClassNames}`}
                  label="Clear image"
                  icon="trash-alt"
                  white
                  onClick={onClearImage}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </figure>
  )
}

export default PersonInfo
