/* These components aim to replace "src/util-components/edit-button" since it has become a mess */
import type * as React from 'react'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import Button from './button'

type ButtonProps = React.ComponentProps<typeof Button>

/* Note:
   I'm giving priority to "small" size due to the fact that the design is shifting towards a more "Editable Section" approach.
   This means we are not having as much "global" Edit buttons for the entire form and as such, "small" size is more suited
*/
const StatusButton = ({
  icon = 'archive',
  isCurrentActive,
  label,
  labelSuffix = '',
  ...rest
}: {
  icon?: 'archive' | 'trash-alt'
  isCurrentActive: boolean
  labelSuffix?: string
} & ButtonProps) => (
  <Button
    icon={icon}
    label={
      label ? label : `${isCurrentActive ? 'Deactivate' : 'Activate'} ${labelSuffix}`
    }
    red={isCurrentActive}
    green={!isCurrentActive}
    grouped
    {...rest}
  />
)

const CancelButton = ({ label = 'Cancel', ...rest }: ButtonProps) => (
  <Button
    label={label}
    grouped
    {...rest}
  />
)

const SaveButton = ({ label = 'Save', ...rest }: ButtonProps) => (
  <Button
    label={label}
    action
    grouped
    {...rest}
  />
)

const EditButton = ({
  label = 'Edit',
  labelSuffix = '',
  ...rest
}: { label?: string; labelSuffix?: string } & ButtonProps) => (
  <Button
    icon="pencil-alt"
    label={
      ctIntl.formatMessage({
        id: `${label}`,
      }) +
      ' ' +
      ctIntl.formatMessage({
        id: `${labelSuffix}`,
      })
    }
    grouped
    {...rest}
  />
)

export const EditActionsGroup = {
  StatusButton,
  CancelButton,
  SaveButton,
  EditButton,
}
