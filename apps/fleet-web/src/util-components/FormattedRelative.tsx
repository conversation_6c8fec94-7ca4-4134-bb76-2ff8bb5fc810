import { useMemo } from 'react'
import type { Except } from 'type-fest'

import { selectUnit } from '@fleet-web/util-functions/backwards-compatible-intl-utils'

import FormattedRelativeTime, {
  type FormattedRelativeTimeProps,
} from './FormattedRelativeTime'

type DateSource = Date | string | number

export type FormattedRelativeProps = {
  value: DateSource
} & Except<FormattedRelativeTimeProps, 'value' | 'unit'>

/**
 * Backwards compatible way to use the old `FormattedRelative` component available in v2.
 *
 * @deprecated Use `FormattedRelativeTime` instead.
 */
export function FormattedRelative({
  value: valueProp,
  ...props
}: FormattedRelativeProps) {
  // Implementation based on https://formatjs.io/docs/react-intl/upgrade-guide-3x#formattedrelativetime
  const { value, unit } = useMemo(
    () => selectUnit(typeof valueProp === 'string' ? new Date(valueProp) : valueProp),
    [valueProp],
  )
  return (
    <FormattedRelativeTime
      value={value}
      unit={unit}
      {...props}
    />
  )
}
