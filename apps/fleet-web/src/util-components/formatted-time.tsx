import { isNil } from 'lodash'
import { FormattedTime } from 'react-intl'
import { connect } from 'react-redux'

import { getFormattedTimeWithTZ, getIsTime24 } from '@fleet-web/duxs/user'
import { getUserTimeZone } from '@fleet-web/duxs/user-sensitive-selectors'
import type { AppState } from '@fleet-web/root-reducer'
import type { DateTimeParam } from '@fleet-web/types'

type FormattedUserTimeProps = {
  timeZone?: string
  extraClassName?: string
  ignoreTZ?: boolean
  value: DateTimeParam
  tzOffset?: number
  includeSeconds?: boolean
}

/**
 * Returns time displayed correctly according to timezone and 12/24 hour format
 * @param props component props
 * @param props.userTimeZone - timezone from user settings
 * @param props.isTime24 - determines if time should be displayed in 12 or 24 hour format
 * @param props.extraClassName - classNames to style the span containing the time
 * @param props.ignoreTZ - determines if timezone is handled in backend or frontend. In the future we want FormattedTime to be removed so that timezones are handled exclusively in the backend.
 * @param props.value - dateTime value in milliseconds, valid dateTime string or JS Date object
 * @returns formatted user time
 */
const FormattedUserTime = ({
  userTimeZone,
  timeZone,
  isTime24 = true,
  extraClassName = '',
  ignoreTZ = false,
  value,
  doGetFormattedTimeWithTZ,
  tzOffset,
  includeSeconds = false,
  ...rest
}: Props) => (
  <span className={'util-uppercase ' + extraClassName}>
    {ignoreTZ ? (
      doGetFormattedTimeWithTZ(value, tzOffset, includeSeconds)
    ) : (
      <FormattedTime
        timeZone={isNil(timeZone) ? userTimeZone.ianaName : timeZone}
        hour12={!isTime24}
        value={value}
        {...rest}
      />
    )}
  </span>
)

const mapStateToProps = (state: AppState) => ({
  userTimeZone: getUserTimeZone(state),
  isTime24: getIsTime24(state),
  doGetFormattedTimeWithTZ: (
    dateTime: DateTimeParam,
    tzOffset: number | undefined,
    includeSeconds: boolean,
  ) => getFormattedTimeWithTZ(state, dateTime, tzOffset, includeSeconds),
})

type Props = ReturnType<typeof mapStateToProps> & FormattedUserTimeProps

export default connect(mapStateToProps)(FormattedUserTime)
