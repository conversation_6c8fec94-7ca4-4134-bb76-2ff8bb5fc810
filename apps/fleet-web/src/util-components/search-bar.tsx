import type * as React from 'react'
import styled from 'styled-components'

import { variables } from '@fleet-web/shared/components/styled/global-styles'
import type { FixMeAny } from '@fleet-web/types'

import TextInput from './text-input'

export type Props = {
  id?: string
  className?: string
  focus?: boolean
  placeholder?: string
  forceOriginalValue?: boolean
  onBlur?: (...args: Array<FixMeAny>) => FixMeAny
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClick?: (...args: Array<FixMeAny>) => FixMeAny
  onClear?: () => void
  value: string
  clearable?: boolean
}

const SearchBar = ({
  id = '',
  className = '',
  focus = false,
  placeholder = 'Search',
  forceOriginalValue = false,
  onBlur,
  onChange,
  onClick,
  onClear,
  value,
  clearable = false,
}: Props) => (
  <StyledTextInput
    id={id}
    onBlur={onBlur}
    onClick={onClick}
    focus={focus}
    input={{
      onChange: onChange,
      value: value,
    }}
    placeholder={placeholder}
    iconName={value && clearable ? 'times' : 'search'}
    onIconClick={() => {
      if (clearable) {
        onClear?.()
      }
    }}
    forceOriginalValue={forceOriginalValue}
    extraClassNames={{
      containerClassNames: `SearchBar ${className}`,
    }}
  />
)

export default SearchBar

const StyledTextInput = styled(TextInput)`
  max-width: ${variables.searchBarMaxWidth};
`
