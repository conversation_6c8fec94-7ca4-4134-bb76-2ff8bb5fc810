import { Box, styled } from '@karoo-ui/core'
import AccessTimeFilled from '@mui/icons-material/AccessTimeFilled'
import CheckCircle from '@mui/icons-material/CheckCircle'
import ConstructionIcon from '@mui/icons-material/Construction'
import Loop from '@mui/icons-material/Loop'
import RemoveCircle from '@mui/icons-material/RemoveCircle'
import { match, P } from 'ts-pattern'

import type { FetchVehicles } from '@fleet-web/api/vehicles/types'
import { BookingStatusMapping } from '@fleet-web/modules/carpool/utils/constants'

type Props = {
  statusClassName: FetchVehicles.Vehicle['statusClassName']
  carpoolStatus?: FetchVehicles.Vehicle['carpool_status']
}

const ReVehicleCarpoolStatusIcon = ({ statusClassName, carpoolStatus }: Props) => {
  if (statusClassName === 'maintenance') {
    return (
      <StatusIconBase>
        <ConstructionIcon sx={{ fontSize: 16, color: '#f44336' }} />
      </StatusIconBase>
    )
  }

  return match(carpoolStatus)
    .with(null, () => null)
    .with(undefined, () => null)
    .with(P.union(...BookingStatusMapping.free), () => (
      <StatusIconBase>
        <RemoveCircle sx={{ fontSize: 15, color: '#BDBDBD' }} />
      </StatusIconBase>
    ))
    .with(P.union(...BookingStatusMapping.requested), () => (
      <StatusIconBase>
        <AccessTimeFilled sx={{ fontSize: 15, color: '#F57C00' }} />
      </StatusIconBase>
    ))
    .with(P.union(...BookingStatusMapping.active), () => (
      <StatusIconBase>
        <Loop
          sx={{
            fontSize: 15,
            color: '#fff',
            backgroundColor: '#2196F3',
            borderRadius: '50%',
          }}
        />
      </StatusIconBase>
    ))
    .with(P.union(...BookingStatusMapping.issued), () => (
      <StatusIconBase>
        <CheckCircle sx={{ fontSize: 15, color: '#43A047' }} />
      </StatusIconBase>
    ))
    .otherwise(() => null)
}

export default ReVehicleCarpoolStatusIcon

const StatusIconBase = styled(Box)({
  position: 'absolute',
  bottom: '0',
  right: '0',
  background: '#fff',
  border: '1px solid #fff',
  borderRadius: '50%',
  width: '16px',
  height: '16px',
})
