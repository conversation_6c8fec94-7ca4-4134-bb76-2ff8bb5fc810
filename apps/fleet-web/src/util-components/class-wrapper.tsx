import { PureComponent } from 'react'
import * as React from 'react'
import { forEach } from 'lodash'

import type { FixMeAny } from '@fleet-web/types'

type Props = {
  getRef?: (el: HTMLElement) => void
  eventListeners?: Record<string, (...args: Array<FixMeAny>) => FixMeAny>
  children: React.ReactElement<any, string | React.JSXElementConstructor<any>>
}

class ClassWrapper extends PureComponent<Props> {
  render() {
    return React.cloneElement(this.props.children, {
      ref: (el: HTMLElement) => {
        if (el && this.props.eventListeners && this.props.getRef) {
          forEach(this.props.eventListeners, (listener, event) => {
            // eslint-disable-next-line no-param-reassign
            ;(el as FixMeAny)[event] = listener
          })
          this.props.getRef(el)
        }
      },
    })
  }
}

export default ClassWrapper
