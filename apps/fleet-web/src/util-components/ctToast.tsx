import { isEmpty, isNil } from 'lodash'
import { Slide, toast as toastify, type ToastOptions } from 'react-toastify'
import type { Except } from 'type-fest'

import {
  ctIntl,
  type CtIntlFormatMessageOptions,
} from '@fleet-web/util-components/ctIntl'

type CTToastOptions = Except<ToastOptions, 'autoClose'> & {
  duration: number
}
export type ToastType = 'success' | 'error' | 'info' | 'warn'
export type MessageValuesType = NonNullable<CtIntlFormatMessageOptions['values']>
export type MessageType = { id: string; values?: MessageValuesType }

const defaultMessagesByType = {
  success: 'Successful',
  error: 'There has been an error',
  info: 'Message is required',
  warn: 'Message is required',
}

export const ctToast = Object.freeze({
  fire: (
    type: ToastType,
    message: MessageType['id'],
    options?: {
      formatMessageOptions?: CtIntlFormatMessageOptions
      toastOptions?: CTToastOptions
    },
  ) => {
    let forwardMessage = message
    const forwardType = type || 'info'

    if (isNil(message) || isEmpty(message)) {
      if (ENV.NODE_ENV === 'development') {
        console.warn(
          `[Cartrack] - An empty or null/undefined message was provided to ctToast.`,
        )
      }
      // This helps if for some reason JSX files or API sends a null/undefined/""
      forwardMessage = defaultMessagesByType[forwardType]
    }

    return toastify[forwardType](
      ctIntl.formatMessage({ id: forwardMessage }, options?.formatMessageOptions),
      {
        theme: 'colored',
        toastId: forwardMessage,
        position: 'top-center',
        autoClose: options?.toastOptions?.duration ?? 3000,
        closeButton: false,
        transition: Slide,
        ...options?.toastOptions,
      },
    )
  },
  torretFire: (
    success: boolean,
    messages: {
      successMessage: MessageType['id']
      errorMessage: MessageType['id']
    },
    options?: {
      formatMessageOptions?: { values: MessageValuesType }
      toastOptions?: CTToastOptions
    },
  ) => {
    const forwardMessage = success ? messages.successMessage : messages.errorMessage
    const forwardType = success ? 'success' : 'error'

    return ctToast.fire(forwardType, forwardMessage, options)
  },
})
