import { memo, type CSSProperties } from 'react'

import Icon from '@fleet-web/components/Icon'

type Props = {
  rating: number
  maxRating?: number
  className?: string
  onlyStars?: boolean
  color?: string | null
  onHover?: {
    onMouseEnter?: (startNumber: number) => void
    onMouseLeave?: (
      event: React.MouseEvent<HTMLSpanElement, MouseEvent>,
      startNumber: number,
    ) => void
  }
  onStarClick?: (starNumber: number) => void
}

const StarRating = ({
  rating,
  maxRating = 3,
  className = '',
  onlyStars = false,
  color,
  onHover,
  onStarClick,
}: Props) => {
  const stars = []
  let style: CSSProperties = {}
  if (color) {
    style = {
      color,
    }
  }

  for (let i = 1; i <= maxRating; i++) {
    const isDisabled = rating < i
    if (onlyStars && isDisabled) continue
    stars.push(
      <span
        key={`star_rating_${i}`}
        onMouseEnter={() => onHover?.onMouseEnter?.(i)}
        onMouseLeave={(e) => onHover?.onMouseLeave?.(e, i)}
        onClick={() => onStarClick?.(i)}
      >
        <Icon
          key={i}
          icon="star"
          className={`StarRating-faStar ${isDisabled ? 'is-disabled' : ''}`}
          style={style}
        />
      </span>,
    )
  }

  return <span className={`${className} StarRating`}>{stars}</span>
}

export default memo(StarRating)
