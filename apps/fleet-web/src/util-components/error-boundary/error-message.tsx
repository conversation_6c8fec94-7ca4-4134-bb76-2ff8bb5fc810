import { styled } from '@karoo-ui/core'
import { FormattedMessage } from 'react-intl'
import type { RouteComponentProps } from 'react-router-dom'

import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import carSvg from '../../../assets/svg/error.svg'
import Button from '../button'

const StandardError = ({ history }: { history: RouteComponentProps['history'] }) => (
  <StandardErrorContainer>
    <h1 className="ErrorBoundary-header">
      <FormattedMessage id="Uh-oh! Something went wrong." />
    </h1>
    <h2 className="util-h2">
      <FormattedMessage id="We alerted our team of nerds, and they are already working on a fix." />
    </h2>
    <h2 className="util-h2">
      <FormattedMessage id="You can try refreshing the page or" />
      <Button
        className="ErrorBoundary-back"
        label="Go Back"
        onClick={history.goBack}
        small
        action
        style={{ marginLeft: 8 }}
      />
    </h2>
    <svg
      {...makeSanitizedInnerHtmlProp({ dirtyHtml: carSvg })}
      viewBox="0 0 10 5"
      className="ErrorBoundary-image"
    />
  </StandardErrorContainer>
)

export default StandardError

const StandardErrorContainer = styled('div')`
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
`
