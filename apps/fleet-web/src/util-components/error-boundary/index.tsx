import { Component, type ErrorInfo } from 'react'
import * as Sentry from '@sentry/browser'
import { connect } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router-dom'

import { getUser } from '@fleet-web/duxs/user-sensitive-selectors'
import type { AppState } from '@fleet-web/root-reducer'
import { logException } from '@fleet-web/shared/posthog-integration'
import type { FixMeAny } from '@fleet-web/types'

import StandardError from './error-message'

type Props = {
  children?: React.ReactNode
} & RouteComponentProps &
  ReturnType<typeof mapStateToProps>

type State = {
  didError: boolean
}

class ErrorBoundary extends Component<Props, State> {
  static getDerivedStateFromError(): Partial<State> {
    return { didError: true }
  }

  state = {
    didError: false,
  }

  componentDidUpdate(prevProps: Props) {
    if (this.props.location !== prevProps.location) {
      this.setStateValue()
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Set Sentry user scope (useful to know how many users are having the same error)
    if (ENV.NODE_ENV === 'production') {
      Sentry.withScope((scope) => {
        scope.setExtras(errorInfo as FixMeAny)
        // Group error by error name
        scope.setFingerprint(['javascript-error', error.name])
        Sentry.captureException(error)
      })
      logException(error, { additionalProps: errorInfo, monitorTool: 'posthog' })
    }
  }

  setStateValue = () => this.setState({ didError: false })

  render() {
    return this.state.didError ? (
      <div className="ErrorBoundary">
        <StandardError history={this.props.history} />
      </div>
    ) : (
      this.props.children
    )
  }
}

function mapStateToProps(state: AppState) {
  return {
    user: getUser(state),
  }
}

export default withRouter(connect(mapStateToProps)(ErrorBoundary))
