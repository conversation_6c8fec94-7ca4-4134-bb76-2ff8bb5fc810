import { Button, ButtonGroup, styled, type ButtonGroupOwnProps } from '@karoo-ui/core'
import { rgba } from 'polished'
import type { ReadonlyDeep } from 'type-fest'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

type OptionBaseType = {
  name: string
  value: string
  icon?: React.ReactNode
}

type Props<Option extends OptionBaseType> = {
  options: Array<Option> | ReadonlyDeep<Array<Option>>
  handleClick: (value: Option['value']) => void
  value: Option['value']
  className?: string
  sx?: ButtonGroupOwnProps['sx']
}

const ReTabGroup = <Option extends OptionBaseType>({
  options,
  handleClick,
  value,
  className = '',
  sx = {},
}: Props<Option>) => (
  <ButtonGroup
    variant="outlined"
    className={className}
    sx={{ height: 'min-content', ...sx }}
  >
    {options.map((option) => (
      <StyledButton
        key={option.value}
        onClick={() => handleClick(option.value)}
        btnSelected={option.value === value}
      >
        {option.icon}
        {ctIntl.formatMessage({ id: option.name })}
      </StyledButton>
    ))}
  </ButtonGroup>
)

export default ReTabGroup

const StyledButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== 'btnSelected',
})<{ btnSelected: boolean }>(({ btnSelected, theme: { palette } }) => ({
  fontSize: '13px',
  backgroundColor: btnSelected
    ? rgba(palette.primary.main, palette.action.selectedOpacity)
    : 'transparent',
}))
