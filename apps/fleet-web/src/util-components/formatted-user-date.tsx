import { FormattedDate, injectIntl, IntlProvider, type IntlShape } from 'react-intl'
import { connect } from 'react-redux'

import { getFormattedDateWithTZ } from '@fleet-web/duxs/user'
import {
  getDefaultDateTimeLocale,
  getUserTimeZoneIANA,
} from '@fleet-web/duxs/user-sensitive-selectors'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { AppState } from '@fleet-web/root-reducer'

/**
 * @deprecated
 */
const FormattedUserDate = ({
  timeZone,
  value,
  ignoreTZ = false,
  includeDay = false,
  doGetFormattedDateWithTZ,
  intl,
}: {
  value: string | number | Date
  timeZone: string
  ignoreTZ?: boolean
  includeDay?: boolean
  type?: string
  doGetFormattedDateWithTZ: (
    date: string | number | Date,
    includeDay: boolean,
    dateFormat: string,
  ) => string
  intl: IntlShape
}) => {
  const defaultDateTimeLocale = useTypedSelector(getDefaultDateTimeLocale)
  if (ignoreTZ) {
    return doGetFormattedDateWithTZ(
      value,
      includeDay,
      intl.messages['util.dateFormat'] as string,
    )
  }
  // Below code is for backward compatibility and is intended to be phased out

  return (
    <>
      {defaultDateTimeLocale ? (
        <IntlProvider locale={defaultDateTimeLocale}>
          <FormattedDate
            day="2-digit"
            month="2-digit"
            year="numeric"
            value={value}
            timeZone={timeZone}
          />
        </IntlProvider>
      ) : (
        <FormattedDate
          day="2-digit"
          month="2-digit"
          year="numeric"
          value={value}
          timeZone={timeZone}
        />
      )}
    </>
  )
}

const mapStateToProps = (state: AppState, ownProps: { timeZone?: string }) => ({
  timeZone: ownProps.timeZone || getUserTimeZoneIANA(state),
  doGetFormattedDateWithTZ: (
    date: string | number | Date,
    includeDay: boolean,
    dateFormat: string,
  ) => getFormattedDateWithTZ(state, date, includeDay, dateFormat),
})

export default connect(mapStateToProps)(injectIntl(FormattedUserDate))
