import type { IntlShape } from 'react-intl'

import {
  getDateWithTZ,
  getFormattedDateWithTZ,
  getFormattedTimeWithTZ,
} from '@fleet-web/duxs/user'
import { getReduxStore_DANGER } from '@fleet-web/redux-store-sync'
import type { FixMeAny } from '@fleet-web/types'
import {
  kilometersToMiles,
  milesToKilometers,
} from '@fleet-web/util-functions/distance-utils'
import { isTrue } from '@fleet-web/util-functions/validation'

import type { DateSource } from './ctIntl'
import { getINTL } from './intl-global-provider'

/** !!IMPORTANT!! - This version of ctIntl aims to only contain functions that use redux store.
 *  Despite that, beware that we SHOULD MINIMIZE THE USAGE OF THESE FUNCTIONS as:
 *  - They are not pure in any sense
 *  - For the reason above, they have serious side effects:
 *     - They are not guaranteed to re-run if the store is updated.
 *     - Since the store is imported in this file, all the modules associated with it (quite a lot) are imported as well.
 *       This causes unpredictable behavior when trying to add stories/tests to components that use functions from this file.
 *
 *  THE GOAL HERE, is to turn these functions into redux selectors, or even better, __find a way to achieve the same functionality by not using the store__.
 */
export const connectedCtIntl = Object.freeze({
  /**
   * @deprecated Luxon provides better support for date formatting.
   * Formats a date with time (FOLLOWS FormattedUserTime and FormattedUserDate logic and combines both into a function)
   * Also returns the original date and formattedTime/formattedDate separately so they can be used with more flexibility.
   * Examples of outputs:
   * @example
   * 1 - {
   *  date: 2019-11-24 10:05:51+0000,
   *  formatted: 24/11/2019 10:05,
   *  formattedDate: 24/11/2019,
   *  formattedTime: 10:05
   * }
   * 2 - {
   *  date: 2019-11-24 10:05:51+0000,
   *  formatted: 11/24/2019 04:05 PM,
   *  formattedDate: 11/24/2019,
   *  formattedTime: 04:05 PM
   * }
   */
  formatDateWithHourMinute: (
    value: DateSource,
    state?: FixMeAny /* FixMeAny prevent types circular dependency */,
  ) => {
    const storeState = state ?? getReduxStore_DANGER().getState()
    const date = getDateWithTZ(storeState, value as FixMeAny)

    const formattedDate = formatDateHelper(getINTL(), value)

    const formattedTime = getFormattedTimeWithTZ(
      storeState,
      value as FixMeAny,
      undefined as FixMeAny,
      false,
    )

    return {
      date,
      formatted: `${formattedDate} ${formattedTime}`,
      formattedDate,
      formattedTime,
    }
  },
  /**
   * @deprecated Luxon provides better support for date formatting.
   * @example
   * 11/24/2019
   * 24/11/2019
   */
  formatDate(value: DateSource) {
    const state = getReduxStore_DANGER().getState()

    const date = getDateWithTZ(state, value as FixMeAny)

    return {
      date,
      formatted: formatDateHelper(getINTL(), value),
    }
  },
  /**
   * @deprecated
   * Converts the distance to miles or kms according to the app setting: `distanceInMiles`. This is the single source of truth.
   */
  formatDistanceIfNeeded(
    distance: number,
    {
      distanceInMiles,
      unitToFormatToIfNeeded,
    }: { distanceInMiles: boolean; unitToFormatToIfNeeded: 'miles' | 'kms' },
  ) {
    const needsToConvert = isTrue(distanceInMiles)

    // We only need to convert if distanceInMiles setting is true
    if (needsToConvert) {
      return unitToFormatToIfNeeded === 'miles'
        ? kilometersToMiles(distance)
        : milesToKilometers(distance)
    }

    // Otherwise, return value as is
    return distance
  },
})

function formatDateHelper(intl: IntlShape, value: DateSource) {
  const state = getReduxStore_DANGER().getState()

  const formattedDate = getFormattedDateWithTZ(
    state,
    value as FixMeAny,
    false,
    intl?.messages['util.dateFormat'] as string,
  )

  return formattedDate
}

export type HourMinuteDate = ReturnType<typeof connectedCtIntl.formatDateWithHourMinute>

export type FormattedDate = ReturnType<typeof connectedCtIntl.formatDate>
