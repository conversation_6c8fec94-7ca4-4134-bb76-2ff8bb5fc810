import { Stack, Tooltip, Typography, useOverflowableElement } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export const CellTextWithMore = ({ value }: { value: string }) => {
  const [isOverflown, wrapperRef] = useOverflowableElement<HTMLSpanElement>(
    { initialValue: false, orientation: 'all' },
    [value],
  )

  return value.length === 0 ? null : (
    <Stack
      direction="row"
      spacing={0.5}
      sx={{
        width: '100%',
      }}
    >
      <Typography
        ref={wrapperRef}
        variant="inherit"
        noWrap
        sx={{
          WebkitLineClamp: '1', // must be a string, otherwise mui considers it as a number with px
          WebkitBoxOrient: 'vertical',
          // display: '-webkit-box',
        }}
      >
        {value}
      </Typography>

      {isOverflown && (
        <Tooltip
          title={value}
          arrow
          placement="top"
        >
          <Typography
            sx={{
              fontSize: '10px',
              textDecoration: 'underline',
              cursor: 'pointer',
              alignSelf: 'center',
            }}
            color="primary"
          >
            {ctIntl.formatMessage({ id: 'More' })}
          </Typography>
        </Tooltip>
      )}
    </Stack>
  )
}
