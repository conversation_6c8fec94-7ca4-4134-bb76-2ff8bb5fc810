import { capitalize } from 'lodash'
import { Box, styled, Tooltip } from '@karoo-ui/core'
import WifiOffOutlinedIcon from '@mui/icons-material/WifiOffOutlined'
import { match } from 'ts-pattern'

import { colorHash } from '@fleet-web/api/colors'
import type { VehicleType } from '@fleet-web/api/types'
import theme from '@fleet-web/components/_themes/default'
import SVGIcon from '@fleet-web/components/Icon/SVGIcon'
import { getSelectedHardwareType } from '@fleet-web/duxs/map'
import { getCarpoolSetting } from '@fleet-web/duxs/user'
import { getPreferences } from '@fleet-web/duxs/user-sensitive-selectors'
import type { ListVehicle } from '@fleet-web/modules/map-view/FleetMapView/LeftPanel/Vehicle/types'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import ReVehicleCarpoolStatusIcon from './re-vehicle-carpool-status-icon'
import vehicleIcons from './vehicle-icons'

type Props = {
  type: VehicleType
  description: string
  carpoolStatus?: ListVehicle['carpoolStatus'] | null
  statusClassName: ListVehicle['statusClassName']
  iconColor?: ListVehicle['iconColor']
}

const ToolTipCircle = ({
  description,
  children,
  statusClassName,
  carpoolStatus,
  userHasCarpool,
}: {
  description: string
  userHasCarpool: boolean
  children: React.ReactNode
  statusClassName: ListVehicle['statusClassName']
  carpoolStatus?: ListVehicle['carpoolStatus']
}) => (
  <Tooltip
    placement="right"
    title={description}
    arrow
  >
    <Box sx={{ position: 'relative', maxHeight: '44px' }}>
      {children}
      {userHasCarpool && carpoolStatus && (
        <ReVehicleCarpoolStatusIcon
          statusClassName={statusClassName}
          carpoolStatus={carpoolStatus}
        />
      )}
    </Box>
  </Tooltip>
)

const StatusColor = (statusClassName: ListVehicle['statusClassName']) =>
  match(statusClassName)
    .with('no-signal', () => theme.colors.vehicleStatus['NO_SIGNAL'])
    .with('driving', () => theme.colors.vehicleStatus['DRIVING'])
    .with('speeding', () => theme.colors.vehicleStatus['SPEEDING'])
    .with('idling', () => theme.colors.vehicleStatus['IDLING'])
    .with('ignition-off', () => theme.colors.vehicleStatus['IGNITION_OFF'])
    .with('stationary', () => theme.colors.vehicleStatus['STATIONARY'])
    .with('maintenance', () => theme.colors.vehicleStatus['MAINTENANCE'])
    .with(
      'moving-ignition-off',
      () => theme.colors.vehicleStatus['MOVING_IGNITION_OFF'],
    )
    .otherwise(() => '#dddddd')

const getIconColor = (iconColor: string) => {
  const formattedColor = capitalize(iconColor)
  return formattedColor in colorHash
    ? colorHash[formattedColor as keyof typeof colorHash]
    : null
}

const ReVehicleIconCircle = ({
  type,
  description,
  statusClassName,
  carpoolStatus,
  iconColor,
}: Props) => {
  const selectedHardwareType = useTypedSelector(getSelectedHardwareType)
  const userHasCarpool = useTypedSelector(getCarpoolSetting)
  const { useVehicleIconColor } = useTypedSelector(getPreferences)

  const formattedIconColor = iconColor ? getIconColor(iconColor) : null

  const iconBackgroundColor =
    useVehicleIconColor && formattedIconColor
      ? formattedIconColor
      : StatusColor(statusClassName)

  // const isWithDefects = type === 'isWithDefects'
  if (statusClassName.includes('no-signal') && selectedHardwareType !== 'Flash units') {
    return (
      <ToolTipCircle
        description={description}
        statusClassName={statusClassName}
        carpoolStatus={carpoolStatus}
        userHasCarpool={userHasCarpool}
      >
        <CircleIcon backgroundColor={iconBackgroundColor}>
          <WifiOffOutlinedIcon sx={{ color: '#9E9E9E', fontSize: '20px' }} />
        </CircleIcon>
      </ToolTipCircle>
    )
  }
  return (
    <ToolTipCircle
      description={description}
      statusClassName={statusClassName}
      carpoolStatus={carpoolStatus}
      userHasCarpool={userHasCarpool}
    >
      <CircleIcon backgroundColor={iconBackgroundColor}>
        <SVGIcon
          svg={vehicleIcons[type]}
          width={20}
          color="#ffffff"
        />
      </CircleIcon>
    </ToolTipCircle>
  )
}

export default ReVehicleIconCircle

const CircleIcon = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'backgroundColor',
})<{ backgroundColor: string }>(({ backgroundColor }) => ({
  display: 'flex',
  width: '36px',
  minWidth: '36px',
  height: '36px',
  minHeight: '36px',
  borderRadius: '50%',
  backgroundColor: backgroundColor,
  justifyContent: 'center',
  alignItems: 'center',
}))
