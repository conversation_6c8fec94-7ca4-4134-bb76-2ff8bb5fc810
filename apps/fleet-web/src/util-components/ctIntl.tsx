import { isEmpty, isNil, isString } from 'lodash'
import { formatDuration as dateFNSFormatDuration } from 'date-fns'
import moment from 'moment'
import type { FormatDateOptions, FormatRelativeTimeOptions } from 'react-intl'

import type { ExplicitDate } from '@fleet-web/types'
import { selectUnit } from '@fleet-web/util-functions/backwards-compatible-intl-utils'

import { getINTL } from './intl-global-provider'
import type { FacilityTranslationTermsBanned } from './intlBannedTerms'

export type DateSource = Date | string | number

type MessageDescriptor = {
  id: string
  description?: string
  defaultMessage?: string
}
type MessageValue = string | number | boolean | null | undefined

type BannedTermsFromStandardFormatMessage = FacilityTranslationTermsBanned

type NotABannedTerm<T> = T extends BannedTermsFromStandardFormatMessage
  ? 'This is a banned term. Use the explicit term translator function instead'
  : T

type FormatXMLElementFn<T, R = string | T | Array<string | T>> = (
  parts: Array<string | T>,
) => R
export type CtIntlFormatMessageOptions = {
  values?: Record<string, MessageValue | FormatXMLElementFn<string, string>>
  form?: 'singular' | 'plural'
  /**
   * Avoid setting this to __false__.
   */
  strict?: boolean
}

export type CtIntlFormatMessageDescriptor<Id extends string> = {
  id: NotABannedTerm<Id>
  description?: string
  defaultMessage?: string
}

/**
 * !!IMPORTANT!! - DO NOT use redux store in this file. If you still think you need it, use __connectedCtIntl__ instead.
 * These methods can only be used once IntlProvider has made 'intl' available. It's usage, on the other hand, is more broad than using react-intl since it can be used outside of components.
 */
export const ctIntl = Object.freeze({
  /**
   * Formats message
   * @example
   * en.json
   * {
   * ...,
   * "Vehicle": "{count, plural, =0 {Vehicles} one {Vehicle} other {Vehicles}}",
   * "Driver": "Driver",
   * ...
   * }
   * const message1 = formatMessage({id: 'Vehicle'}, {values: {count: 2}})
   * const message2 = formatMessage({id: 'Driver'})
   */
  formatMessage: <Id extends string>(
    { id, defaultMessage = id, description }: CtIntlFormatMessageDescriptor<Id>,
    { values, form, strict = true }: CtIntlFormatMessageOptions = {},
  ): string => {
    const INTL = getINTL()
    if (ENV.NODE_ENV === 'development') {
      // Extra checks for usages in JS files (correct types are not guaranteed)
      showInfoIfNotAString('id', id)
      showInfoIfNotAString('defaultMessage', defaultMessage)
      if (['singular', 'plural', undefined].includes(form) === false) {
        console.error(
          `[Cartrack] - 'form' must be 'singular', 'plural' or undefined. The form you provided is of type ${typeof form}`,
        )
      }

      if (strict && !INTL) {
        throw new Error(
          '[Cartrack] - You called ctIntl before it got initialized. You can use "strict": false as a workaround but be aware that the translation might not work.',
        )
      }
    } else if (isNil(id)) {
      console.error(
        `[Cartrack] - The 'id' you provided must NOT be of type ${typeof id}`,
      )

      return ''
    }

    if (id === '' && defaultMessage === '') {
      return ''
    }

    const rawResourceValue = INTL?.messages[id]
    const translationValue = INTL?.formatMessage(
      { id, defaultMessage, description },
      form === undefined ? values : { count: form === 'singular' ? 1 : 2 },
    )

    /*
      When in DEV react-intl falls back to defaultMessage if the resource by 'id' is not a string.
      In PROD, it does not. This can cause unpredictable behavior that can only be reproduced in PROD.
      To nullify this, a custom check is made so we can see this in DEV as well.
    */
    if (
      ENV.NODE_ENV === 'development' &&
      !isString(rawResourceValue) &&
      rawResourceValue !== undefined // [react-intl] already warns about undefined resources. For this reason, it's not handled by this function
    ) {
      console.error(`[Cartrack] - The value of the resource with id "${id}" is of type '${typeof rawResourceValue}' -> ${JSON.stringify(
        rawResourceValue,
      )}.
    Please provide a resource id that has a value of type 'string'.
    `)
    }

    return translationValue?.toString() ?? ''
  },
  /**
   *
   * Avoid using this method. Prefer __formatMessage__ instead.
   */
  unsafe_formatMessage: (
    props: MessageDescriptor,
    options: {
      values?: { [key: string]: MessageValue }
      form?: 'singular' | 'plural'
    } = {},
  ) => ctIntl.formatMessage(props, { strict: false, ...options }),
  formatTime: (value: DateSource, options?: FormatDateOptions) => {
    const INTL = getINTL()
    if (!INTL) {
      throw new Error('[Cartrack] - You called ctIntl before it got initialized.')
    }

    return INTL.formatTime(value, options) // Can and should be adapted to our needs in the future
  },
  formatTimeRelative: (rawValue: DateSource, options?: FormatRelativeTimeOptions) => {
    const INTL = getINTL()
    if (!INTL) {
      throw new Error('[Cartrack] - You called ctIntl before it got initialized.')
    }
    // Based on migration guide https://formatjs.io/docs/react-intl/upgrade-guide-3x#formattedrelativetime
    const { unit, value } = selectUnit(
      typeof rawValue === 'string' ? new Date(rawValue) : rawValue,
    )
    return INTL.formatRelativeTime(value, unit, options) // Can and should be adapted to our needs in the future
  },

  /** Check "options" parameter in https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat for more info */
  formatShortMonthWithDay: ({ year, month, day }: ExplicitDate) => {
    const INTL = getINTL()
    if (!INTL) {
      throw new Error('[Cartrack] - You called ctIntl before it got initialized.')
    }
    return INTL.formatDate(new Date(year, month - 1, day), {
      month: 'short',
      day: 'numeric',
    })
  },

  /**
   * @example
   * 4h20m
   * 4h20m10s
   * 20m10s
   * 10s
   */
  formatDuration: (seconds: number, options = { includeSeconds: false }) => {
    const duration = moment.duration(seconds, 'seconds')
    const hoursDuration = Math.floor(duration.asHours())
    const minutesDuration = duration.minutes()
    const secondsDuration = duration.seconds()

    if (hoursDuration === 0 && minutesDuration === 0) {
      return `${secondsDuration}s`
    }

    return `${hoursDuration > 0 ? `${hoursDuration}h` : ''}${
      minutesDuration > 0 ? ` ${minutesDuration}m` : ''
    }${options.includeSeconds && secondsDuration !== 0 ? ` ${secondsDuration}s` : ''}`
  },

  /**
   * @example
   * 4:20
   * 04:20
   */
  formatDurationWithHourMinute: (seconds: number, twoDigitHour = false) => {
    const duration = moment.duration(seconds, 'seconds')
    const hours = Math.floor(duration.asHours())
    return `${(twoDigitHour && hours < 10 ? '0' : '') + hours}:${(
      '0' + duration.minutes()
    ).slice(-2)}`
  },

  /**
   * @example
   * {
   *   hours: 54,
   *   formatted: '54:00'
   * }
   *
   * {
   *   hours: 3.4277777777777776,
   *   formatted: '3:25'
   *   humanized: '3 hours 25 minutes'
   * }
   *
   * with twoDigitHour as true:
   * {
   *   hours: 3.4277777777777776,
   *   formatted: '03:25'
   *   humanized: '3 hours 25 minutes'
   * }
   */
  getDurationWithFormattedHourMinute(seconds: number, twoDigitHour = false) {
    const duration = moment.duration(seconds, 'seconds')

    return {
      hours: duration.asHours(),
      formatted: this.formatDurationWithHourMinute(duration.asSeconds(), twoDigitHour),
      humanized: dateFNSFormatDuration(
        {
          hours: Math.floor(duration.asHours()),
          minutes: duration.minutes(),
        },
        { format: ['hours', 'minutes'] },
      ),
    }
  },
  formatTemperature: ({ valueInCelsius }: { valueInCelsius: number }) => {
    const INTL = getINTL()

    const isCelsius = INTL?.messages['units.temperature'] !== 'fahrenheit'
    const unit = isCelsius ? 'C' : 'F'
    const convertedTemperature = Number.parseFloat(
      (isCelsius ? valueInCelsius : valueInCelsius * (9 / 5) + 32).toFixed(1),
    )

    return {
      unit,
      convertedTemperature,
      formattedTemperature: `${convertedTemperature}º ${unit}`,
    }
  },
  /**
   * @deprecated You should not longer need to use this function. Use luxon DateTime from a JS date instead and format it as needed.
   * Uses removeServerDateStringTimezone() and adds format options
   * Examples:
   * @example
   * 1 - Input
   *  dateString: 2019-11-24 10:05:51+0000
   * 2 - Output
   *  Without options: 2019-11-24 10:05:51
   *  With unix as true: converts (^^) this to unix timestamp
   *  You can always pass the format option to get the date without TZ in the format needed
   */
  transformServerDateStringInto(
    dateString: string,
    options: { unix?: boolean; format: string } = {
      unix: false,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  ) {
    if (isEmpty(dateString)) {
      return undefined
    }

    const zonelessDate = removeServerDateStringTimezone(dateString)

    if (isEmpty(zonelessDate)) {
      return undefined
    }

    if (options.unix) {
      return moment(zonelessDate).valueOf()
    }

    return moment(zonelessDate).format(options.format)
  },
  removeServerDateStringTimezone,
})

function showInfoIfNotAString(variableName: string, variable: any) {
  if (!isString(variable)) {
    console.error(
      `[Cartrack] - ${variableName} ${JSON.stringify(
        variable,
      )} is of type '${typeof variable}' instead of type 'string'`,
    )
  }
}

/**
 * @deprecated
 * Removes all the timezone information from a date string (usually sent by the api), so we can REALLY work without the timezone and
 * avoid the duplication of methods like `formatDate` and `formatDateNoTz` that most of the cases don't really work
 * because dates are being transformed again and again across multiple classes
 * The input should be in the following format: 2020-04-03 11:49:32+1000
 * If you don't have the value in this format you can convert it by using `cartrack-moment-utils` formatDate() like so:
 * formatDate(youDate, 'YYYY-MM-DD HH:mm:ssZZ') and then send the result of that function to this one
 *
 * Examples:
 * @example
 * 1 - Input
 *  dateString: 2019-11-24 10:05:51+0000 || 2019-11-24T10:05:51.000Z
 * 2 - Output
 *  Without options: 2019-11-24 10:05:51
 */
function removeServerDateStringTimezone(dateString: string) {
  if (isEmpty(dateString)) {
    return undefined
  }

  if (dateString.includes('T')) {
    let splitDateT = ''

    const splitDate = dateString.split('T')
    const splitTime = splitDate[1].split('.')

    splitDateT = `${splitDate[0]} ${splitTime[0]}`

    return splitDateT
  }

  const splitPlus = dateString.split('+')
  const splitMinus = dateString.split('-')

  let splitDate = ''

  if (splitPlus.length > 1) {
    splitDate = splitPlus[0]
  } else if (splitMinus.length > 1) {
    splitMinus.pop()
    splitDate = splitMinus.join('-')
  }

  if (isEmpty(splitDate)) {
    return undefined
  }

  return splitDate
}
