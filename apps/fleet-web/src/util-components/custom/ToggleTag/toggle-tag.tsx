import { memo } from 'react'
import type * as React from 'react'
import { startCase } from 'lodash'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { StyledCheckBoxLabel, StyledToggleTag } from './styled'

type Props = {
  checked: boolean
  name: string
  id?: string
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

function ToggleTag({ checked, name, id, onChange }: Props) {
  const idValue = (id ?? name).replaceAll(/\s/g, '')
  return (
    <StyledToggleTag checked={checked}>
      <input
        className="util-hide"
        id={idValue}
        name={name}
        onChange={onChange}
        checked={checked}
        type="checkbox"
      />
      <StyledCheckBoxLabel htmlFor={idValue}>
        {ctIntl.formatMessage({ id: startCase(name) })}
      </StyledCheckBoxLabel>
    </StyledToggleTag>
  )
}

export default memo(ToggleTag)
