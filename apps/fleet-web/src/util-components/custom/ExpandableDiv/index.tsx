import type React from 'react'
import { useState } from 'react'
import styled from 'styled-components'

import Icon from '@fleet-web/components/Icon'

type Props = {
  className?: string
  title: string
  expandedByDefault?: boolean
  children: React.ReactNode
}

const ExpandableDiv = ({
  className = '',
  title,
  expandedByDefault = true,
  children,
}: Props) => {
  const [isExpanded, setIsExpanded] = useState(expandedByDefault)

  const handleClickExpand = () => {
    setIsExpanded((prev: boolean) => !prev)
  }

  return (
    <Container className={className}>
      <Header>
        <Title>{title}</Title>
        <StyledIcon
          $isexpanded={isExpanded}
          icon="angle-down"
          onClick={handleClickExpand}
        />
      </Header>
      <Content $isexpanded={isExpanded}>{children}</Content>
    </Container>
  )
}

export default ExpandableDiv

const Container = styled.div`
  width: 100%;
`

const Header = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid rgba(155, 155, 155, 0.4);
  padding-bottom: 5px;
  margin-bottom: 10px;
`

const Title = styled.div`
  font-size: 18px;
`

const StyledIcon = styled(Icon)<{ $isexpanded?: boolean }>`
  width: 14px;
  color: #666;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  transform: ${({ $isexpanded }) => ($isexpanded ? 'none' : 'rotate(180deg)')};
`

const Content = styled.div<{ $isexpanded?: boolean }>`
  width: 100%;
  overflow: ${({ $isexpanded }) => ($isexpanded ? 'visible' : 'hidden')};
  max-height: ${({ $isexpanded }) => ($isexpanded ? '500px' : 0)};
  transition:
    max-height 0.3s ease,
    overflow 0.3s ease 0.3s;

  & > * {
    margin-bottom: 15px;
  }

  & > *:last-child {
    margin-bottom: 0;
  }
`
