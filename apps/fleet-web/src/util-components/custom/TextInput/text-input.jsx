import { Component } from 'react'
import classNames from 'classnames'
import { bool, element, func, number, oneOf, oneOfType, string } from 'prop-types'

import Icon from '@fleet-web/components/Icon'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

class TextInput extends Component {
  state = {
    focus: false,
  }

  onTxtFocus = (status, event, func) => {
    this.setState({
      focus: status,
    })

    func(event)
  }

  render() {
    const {
      type,
      name,
      label,
      placeholder,
      error,
      required,
      message,
      onChange,
      disabled,
      readOnly,
      icon,
      iconPosition,
      onKeyUp,
      onClick,
      value,
      focus,
      containerClassName,
      extraClassNames,
      autoComplete,
      textArea,
    } = this.props

    const showFloatingLabel = value || this.state.focus
    const Input = textArea ? 'textarea' : 'input'

    return (
      <label
        className={classNames('Input', containerClassName, {
          error,
        })}
        htmlFor={name}
      >
        {label ? (
          <span className="label">
            {label}{' '}
            {required ? (
              <Icon
                icon="asterisk"
                className="required"
              />
            ) : null}
          </span>
        ) : null}
        <div
          className={classNames('inputDiv', {
            'with-icon': icon,
            'icon-left': Boolean(iconPosition === 'left'),
          })}
        >
          {required ? <span className="required">* </span> : null}
          {showFloatingLabel ? (
            <span className="floating-label">
              {ctIntl.formatMessage({ id: placeholder })}
            </span>
          ) : null}
          <Input
            id={name}
            type={type}
            name={name}
            placeholder={
              showFloatingLabel ? '' : ctIntl.formatMessage({ id: placeholder })
            }
            required={required}
            onChange={onChange}
            disabled={disabled}
            readOnly={readOnly}
            onFocus={(e) => this.onTxtFocus(true, e, this.props.onFocus)}
            onBlur={(e) => this.onTxtFocus(false, e, this.props.onBlur)}
            onKeyUp={onKeyUp}
            onClick={onClick}
            value={value}
            focus={focus}
            className={classNames(extraClassNames, {
              'with-value': showFloatingLabel,
            })}
            autoComplete={autoComplete ? undefined : 'off'}
          />
          {icon ? (
            <div className="icon-div">
              {typeof icon === 'string' ? <Icon icon={icon} /> : icon}
            </div>
          ) : null}
        </div>
        {error ? (
          <span className="error-message">{ctIntl.formatMessage({ id: message })}</span>
        ) : null}
      </label>
    )
  }
}

TextInput.propTypes = {
  type: oneOf(['text', 'number']),
  name: string,
  placeholder: string,
  error: bool,
  label: string,
  required: bool,
  message: string,
  onChange: func,
  disabled: bool,
  readOnly: bool,
  icon: oneOfType([string, element]),
  iconPosition: oneOf(['left', 'right']).isRequired,
  onKeyUp: func,
  onClick: func,
  value: oneOfType([string, number]),
  focus: string,
  containerClassName: string,
  autoComplete: bool,
  extraClassNames: string,
  onFocus: func,
  onBlur: func,
  textArea: bool,
}

TextInput.defaultProps = {
  type: 'text',
  name: '',
  placeholder: '',
  error: false,
  label: '',
  required: false,
  message: '',
  onChange: () => {},
  disabled: false,
  readOnly: false,
  string: '',
  icon: '',
  iconPosition: 'right',
  onKeyUp: () => {},
  onClick: () => {},
  value: '',
  focus: '',
  containerClassName: '',
  extraClassNames: '',
  autoComplete: false,
  onFocus: () => {},
  onBlur: () => {},
  textArea: false,
}

export default TextInput
