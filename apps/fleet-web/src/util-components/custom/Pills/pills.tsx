import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { StyledPill, StyledPills } from './styled'

type Props<P extends ReadonlyArray<string>> = {
  pills: P
  activePill: string
  onClick: (target: P[number]) => void
}

function Pills<P extends ReadonlyArray<string>>({
  pills,
  activePill,
  onClick,
}: Props<P>) {
  return (
    <StyledPills>
      {pills.map((pill) => (
        <StyledPill
          key={pill}
          onClick={() => onClick(pill)}
          id={pill}
          active={activePill === pill}
        >
          {ctIntl.formatMessage({ id: `${pill}` })}
        </StyledPill>
      ))}
    </StyledPills>
  )
}

export default Pills
