import { createContext, useContext, useMemo, useState } from 'react'
import { map } from 'lodash'
import { arrayOf, bool, func, node, oneOfType, shape, string } from 'prop-types'
import Select, { components } from 'react-select'

import Icon from '@fleet-web/components/Icon'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

const OptionComponentContext = createContext()

const optionsResolver = (options) =>
  map(options, (item) => ({ ...item, value: item.value, label: item.name }))

const DropdownIndicator = (props) => (
  <components.DropdownIndicator {...props}>
    <Icon
      icon="caret-down"
      className={`InputDropdown-custom-arrow`}
    />
  </components.DropdownIndicator>
)

const ValueContainer = ({ children, ...props }) =>
  props.selectProps.required ? (
    <components.ValueContainer {...props}>
      <div className="InputDropdown-required">*</div>
      {children}
    </components.ValueContainer>
  ) : (
    <components.ValueContainer {...props}>{children}</components.ValueContainer>
  )

ValueContainer.propTypes = {
  children: node.isRequired,
  selectProps: shape({}).isRequired,
}

const SelectTag = (props) => {
  const [isFocused, setIsFocused] = useState(false)

  const {
    options,
    multiple,
    grouped,
    isSearchable,
    placeholder,
    defaultValue,
    value,
    styles,
    handleOptionMouseEnter,
    handleOptionMouseLeave,
    isDisabled,
  } = props

  const showMiniPlaceholder = (value && value.length > 0) || isFocused

  // Using memo to prevent Option component re-rendering unnecessarily
  const optionComponentContextValue = useMemo(
    () => ({ handleOptionMouseEnter, handleOptionMouseLeave }),
    [handleOptionMouseEnter, handleOptionMouseLeave],
  )

  return (
    <OptionComponentContext.Provider value={optionComponentContextValue}>
      <Select
        {...props}
        defaultValue={multiple ? defaultValue || [] : []}
        options={grouped ? options : optionsResolver(options)}
        placeholder={
          showMiniPlaceholder ? '' : ctIntl.formatMessage({ id: placeholder })
        }
        isMulti={multiple}
        isSearchable={isSearchable}
        isRtl={false}
        components={{
          DropdownIndicator,
          ValueContainer,
          Option,
        }}
        styles={{
          ...styles,
          valueContainer: (base) => ({
            ...base,
            marginTop: showMiniPlaceholder && '14px',
          }),
        }}
        onBlur={() => setIsFocused(false)}
        onFocus={() => setIsFocused(true)}
        isDisabled={isDisabled}
      />
      {showMiniPlaceholder && (
        <span className="InputDropdown-placeholder">
          {ctIntl.formatMessage({ id: placeholder })}
        </span>
      )}
    </OptionComponentContext.Provider>
  )
}

SelectTag.propTypes = {
  options: arrayOf(
    shape({
      label: string,
      value: string,
    }),
  ).isRequired,
  multiple: bool,
  grouped: bool,
  isSearchable: bool,
  placeholder: string,
  defaultValue: oneOfType([arrayOf(shape({ name: string, value: string })), string]),
  required: bool,
  styles: shape({}),
  className: string,
  value: oneOfType([arrayOf(shape({ name: string, value: string })), string]),
  onChange: func.isRequired,
  onMenuOpen: func,
  onMenuClose: func,
  handleOptionMouseEnter: func,
  handleOptionMouseLeave: func,
  isDisabled: bool,
}

SelectTag.defaultProps = {
  multiple: false,
  grouped: false,
  isSearchable: false,
  placeholder: 'Please select',
  defaultValue: '',
  required: false,
  styles: undefined,
  className: '',
  value: '',
  onMenuOpen: () => {},
  onMenuClose: () => {},
  handleOptionMouseEnter: () => null,
  handleOptionMouseLeave: () => null,
  isDisabled: false,
}

export default SelectTag

const Option = (optionProps) => {
  const { handleOptionMouseEnter, handleOptionMouseLeave } =
    useContext(OptionComponentContext)

  return (
    <div
      onMouseEnter={() => handleOptionMouseEnter(optionProps.value)}
      onMouseLeave={() => handleOptionMouseLeave()}
    >
      <components.Option {...optionProps} />
    </div>
  )
}
