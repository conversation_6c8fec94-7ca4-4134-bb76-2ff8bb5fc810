import { forwardRef } from 'react'
import type * as React from 'react'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import type History from 'history'
import { Link } from 'react-router-dom'

import Icon from '@fleet-web/components/Icon'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

export type ButtonProps = {
  // Colors
  // We could add the prop color in the future
  // color?:
  //   | 'red'
  //   | 'green'
  //   | 'blue'
  //   | 'white'
  //   | 'orange'
  //   | 'solidGreen'
  red?: boolean
  green?: boolean
  brown?: boolean
  //blue?: boolean
  raised?: boolean
  link?: boolean
  //orange?: boolean
  success?: boolean
  solidGreen?: boolean
  solidAction?: boolean
  action?: boolean
  white?: boolean
  disabled?: boolean
  grouped?: boolean
  progress?: boolean
  shadow?: boolean
  small?: boolean
  square?: boolean
  className?: string
  style?: React.CSSProperties
  expandWidth?: boolean
  fullWidth?: boolean
  type?: 'button' | 'submit' | 'reset'
  // Text
  label?: string | React.ReactNode
  // Icons
  icon?: IconProp //IconName
  iconElement?: JSX.Element
  iconTitle?: string
  // Controls
  id?: string
  for?: string
  onClick?: (
    event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
  ) => void
  target?: string
  to?: History.LocationDescriptor
  color?: string
}

/**
 * This component was created to separate the __Button__ from the tooltip wrapper. This way, the __Button__ is not connected to the store (tooltip uses the store) making it possible to be used in stories for storybook.
 *
 * In the future, the tooltip connected to the store should be less and less used in favor of a combination of the __Button__ with the __ArrowedTooltip__ components.
 *
 * __P.S.__ If you are reading this 2 years later, I'm sorry :(
 */
const StorelessButton = forwardRef<any, ButtonProps>(function Button(
  {
    // Colors + button state
    red = false,
    green = false,
    white = false,
    brown = false,
    //blue = false,
    //orange = false,
    link = false,
    raised = false,
    success = false,
    solidGreen = false,
    solidAction = false,
    action = false,
    disabled = false,
    grouped = false,
    progress = false,
    shadow = false,
    small = false,
    square = false,
    className = '',
    style = {},
    expandWidth = true,
    fullWidth = false,
    type = 'button',
    // Text
    label = '',
    // Icons
    icon,
    iconElement,
    iconTitle,
    // Controls
    id,
    for: htmlFor = '',
    onClick = () => undefined,
    target = '',
    to = '',
    ...restProps
  },
  ref,
) {
  // Default is white
  let color = 'Button--default'

  if (red) {
    color = 'Button--red'
  } else if (green) {
    color = 'Button--green'
  } else if (success) {
    color = 'Button--success'
  } else if (solidGreen) {
    color = 'Button--solidGreen'
  } else if (solidAction) {
    color = 'Button--solidAction'
  } else if (action) {
    color = 'Button--action'
  } else if (link) {
    color = 'Button--link'
  } else if (raised) {
    color = 'Button--raised'
  } else if (white) {
    color = 'Button--white'
  } else if (brown) {
    color = 'Button--brown'
  }

  // For buttons used inside react-table, prevent table row click event
  const preventClick = (
    event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
  ) => {
    event.stopPropagation()
  }

  const buttonProps = {
    id,
    type: type,
    className: `Button ${small ? 'Button--small' : ''} ${className} ${color} ${
      square ? 'Button--square' : ''
    } ${progress ? 'Button--progress' : ''} ${grouped ? 'Button--grouped' : ''} ${
      disabled ? 'Button--disabled' : ''
    } ${shadow ? 'Button--shadow' : ''} ${
      expandWidth ? '' : 'Button--maintained-width'
    } ${fullWidth ? 'Button--full-width' : ''}`,
    style,
    onClick: disabled ? preventClick : onClick,
    htmlFor,
    disabled,
  }

  const buttonLabel =
    typeof label === 'string' && label !== ''
      ? ctIntl.formatMessage({ id: label })
      : label

  // eslint-disable-next-line no-nested-ternary
  const buttonIcon = iconElement ? (
    iconElement
  ) : icon ? (
    <Icon
      icon={icon}
      title={iconTitle}
    />
  ) : null

  return to && !disabled ? (
    <Link
      type={buttonProps.type}
      className={buttonProps.className}
      style={buttonProps.style}
      to={to}
      ref={ref}
      onClick={buttonProps.onClick}
      target={target}
      {...restProps}
    >
      {buttonIcon} {buttonLabel}
    </Link>
  ) : (
    <button
      ref={ref}
      {...buttonProps}
      {...restProps}
    >
      {buttonIcon} {buttonLabel}
    </button>
  )
})

export default StorelessButton
