import { Fragment, type ReactNode } from 'react'
import { connect } from 'react-redux'
import { match } from 'ts-pattern'

import { closeRightPanel } from '@fleet-web/duxs/right-panel'

import IconButton from './icon-button'
import Spinner from './spinner'

type RightPanelProps = {
  extraClassName?: string
  isLoading?: boolean
  onClose?: { type: 'full_override' | 'side_effect'; fn: (() => void) | undefined }
  children: ReactNode
} & typeof mapDispatchToProps

const InfoRightPanel = ({
  extraClassName = '',
  isLoading = false,
  closeRightPanel,
  onClose,
  children,
}: RightPanelProps) => (
  <div className={`InfoRightPanel ${extraClassName}`}>
    <IconButton
      className={`InfoRightPanel-close ${extraClassName}`}
      icon="times"
      tooltipMessage="Close"
      onClick={() => {
        match(onClose)
          .with(undefined, () => {
            closeRightPanel()
          })
          .with({ type: 'side_effect' }, ({ fn }) => {
            fn?.()
            closeRightPanel()
          })
          .with({ type: 'full_override' }, ({ fn }) => {
            fn?.()
          })
          .exhaustive()
      }}
    />
    <Fragment>
      {isLoading ? (
        <div className="InfoRightPanel-loading">
          <Spinner absolute />
        </div>
      ) : (
        <Fragment>{children}</Fragment>
      )}
    </Fragment>
  </div>
)

const mapDispatchToProps = {
  closeRightPanel,
}

export default connect(null, mapDispatchToProps)(InfoRightPanel)
