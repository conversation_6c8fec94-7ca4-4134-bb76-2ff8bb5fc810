import type { ReactElement } from 'react'
import { Box, Tooltip } from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

type TooltipWrapProps = {
  tooltipMessage: string
  children: ReactElement
}

const TooltipWrap = ({ tooltipMessage, children }: TooltipWrapProps) => (
  <Tooltip
    arrow
    title={ctIntl.formatMessage({ id: tooltipMessage })}
    placement="top"
  >
    <Box>{children}</Box>
  </Tooltip>
)

export default TooltipWrap
