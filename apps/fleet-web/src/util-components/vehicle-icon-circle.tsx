/* eslint-disable no-nested-ternary */
import { styled } from '@karoo-ui/core'
import { connect } from 'react-redux'

import type { VehicleType } from '@fleet-web/api/types'
import type { FetchVehicles } from '@fleet-web/api/vehicles/types'
import Icon from '@fleet-web/components/Icon'
import { getSelectedHardwareType } from '@fleet-web/duxs/map'
import { getPreferences, getSettings } from '@fleet-web/duxs/user-sensitive-selectors'
import type { AppState } from '@fleet-web/root-reducer'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import VehicleCarpoolStatusIcon from './vehicle-carpool-status-icon'
import vehicleIcons from './vehicle-icons'

const VehicleIconCircle = ({
  type,
  iconColor,
  className,
  useVehicleIconColor = false,
  selectedHardwareType,
  carpoolStatus,
  statusClassName,
  userHasCarpool,
}: {
  type: VehicleType | 'isWithDefects'
  className: string
  useVehicleIconColor: boolean
  userHasCarpool: boolean
  selectedHardwareType?: string
  iconColor?: string
  statusClassName?: string
  carpoolStatus?: FetchVehicles.Vehicle['carpool_status']
}) => {
  const vehicleIconColorClassName = useVehicleIconColor
    ? 'is-withIconColor is-' + (iconColor?.toLowerCase() ?? '')
    : ''

  return (
    <>
      {type === 'isWithDefects' ? (
        <div className={`VehicleIconCircle ${className} ${vehicleIconColorClassName}`}>
          <Icon icon="exclamation-triangle" />
        </div>
      ) : className.includes('no-signal') && selectedHardwareType !== 'Flash units' ? (
        <div className={`VehicleIconCircle ${className} ${vehicleIconColorClassName}`}>
          <Icon icon={['fas', 'signal-slash']} />
        </div>
      ) : (
        <div
          {...makeSanitizedInnerHtmlProp({ dirtyHtml: vehicleIcons[type] })}
          className={`VehicleIconCircle ${className} ${
            useVehicleIconColor
              ? vehicleIconColorClassName
              : selectedHardwareType === 'Flash units'
                ? 'flash-units'
                : ''
          }`}
        />
      )}
      {userHasCarpool && (
        <IconBox className="carpool-status-icon">
          <VehicleCarpoolStatusIcon
            statusClassName={statusClassName}
            carpoolStatus={carpoolStatus}
          />
        </IconBox>
      )}
    </>
  )
}

function mapStateToProps(state: AppState) {
  return {
    useVehicleIconColor: getPreferences(state).useVehicleIconColor,
    selectedHardwareType: getSelectedHardwareType(state),
    userHasCarpool: getSettings(state).carpool as boolean,
  }
}

export default connect(mapStateToProps)(VehicleIconCircle)

const IconBox = styled('div')`
  position: absolute;
  left: 38px;
  top: 44px;
`
