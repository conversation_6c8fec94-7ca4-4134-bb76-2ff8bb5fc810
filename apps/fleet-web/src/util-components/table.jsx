import { Component, createRef } from 'react'
import { camelCase, isEmpty, isEqual, isNil, reduce, sortBy, uniq } from 'lodash'
import { bool, func, number, shape, string } from 'prop-types'
import { FormattedMessage } from 'react-intl'
import { connect } from 'react-redux'
import ReactTable from 'react-table'

import Icon from '@fleet-web/components/Icon'
import { savePreferences } from '@fleet-web/duxs/user'
import { getPreferences, getSettings } from '@fleet-web/duxs/user-sensitive-selectors'
import Button from '@fleet-web/util-components/button'
import Spinner from '@fleet-web/util-components/spinner'
import ToggleDropdown from '@fleet-web/util-components/toggle-dropdown'
import { firstDefined } from '@fleet-web/util-functions/functional-utils'
import { formatSimpleMessage } from '@fleet-web/util-functions/intl-utils'

function getLoadingProps() {
  return { className: 'react-table-spinner' }
}

function getNoDataProps(tableState) {
  return { className: tableState.loading ? 'util-hide' : '' }
}

function intlizeColumnHeaders(columns) {
  return columns
    .filter((c) => !c.debug)
    .map((column) => ({
      ...column,
      Header:
        typeof column.Header === 'string' ? (
          <FormattedMessage
            id={column.Header}
            defaultMessage={column.Header}
          />
        ) : (
          column.Header
        ),
    }))
}

function shouldColumnBeAvailable({ settings, column } = {}) {
  return column.show !== false && (isEmpty(column.flag) || settings[column.flag])
}

function createColumnState(
  columns,
  settings = {},
  columnVisibilityPreferences = {},
  defaults = {},
  useColumnIdForVisibilityOptions,
  sortVisibilityOptions,
) {
  const columnVisibility = reduce(
    columns,
    (acc, val) => {
      // eslint-disable-next-line no-nested-ternary
      const name = useColumnIdForVisibilityOptions
        ? val.id
        : typeof val.Header === 'function'
          ? 'Selected'
          : val.Header
      if (shouldColumnBeAvailable({ settings, column: val })) {
        acc[name] = firstDefined(
          columnVisibilityPreferences[name],
          defaults[name],
          true,
        )
      }

      return acc
    },
    {},
  )

  let columnOptions = { ...columnVisibility }
  columnOptions = Object.keys(columnOptions)
    .filter((item) => item && item !== 'undefined')
    .map((a) => ({ [a]: columnOptions[a] }))
    .reduce((a, b) => Object.assign({}, a, b), {})

  const sortedColumnOptions = {}
  const visibilityOptionsKeys = sortVisibilityOptions
    ? sortBy(Object.keys(columnOptions))
    : Object.keys(columnOptions)

  for (const key of visibilityOptionsKeys) {
    sortedColumnOptions[key] = columnOptions[key]
  }

  return {
    columnOptions: sortedColumnOptions,
    columnVisibility,
    columns: columnVisibilityPreferences
      ? columns.filter(
          (c) =>
            columnVisibility[c.Header] ||
            (useColumnIdForVisibilityOptions
              ? columnVisibility[c.id]
              : typeof c.Header === 'function'),
        )
      : columns,
  }
}

const GearButton = ({ styleProperties = {}, ...props }) => (
  <Button
    className={
      styleProperties?.styleButtonRedColourDisabled === 'transparent'
        ? 'Button--gear'
        : ''
    }
    tooltipMessage="Options"
    withTooltip
    icon="cogs"
    square
    {...props}
  />
)

class Table extends Component {
  constructor(props) {
    super(props)
    this.reactTable = createRef()
    this.parentTable = createRef()

    this.state = {
      cachedProps: {
        columns: props.columns,
        settings: props.settings,
        preferences: props.preferences,
        defaultVisibility: props.defaultVisibility,
      },
      ...createColumnState(
        props.columns,
        props.settings,
        props.preferences.columnVisibility,
        props.defaultVisibility,
        props.useColumnIdForVisibilityOptions,
        props.sortVisibilityOptions,
      ),
      defaultSorted:
        // eslint-disable-next-line no-nested-ternary
        props.preferences &&
        props.preferences._defaultSorted &&
        props.saveSortingPreference
          ? props.preferences._defaultSorted
          : props.defaultSorted
            ? props.defaultSorted
            : null,
      pageSize: props.savePageSizePreference
        ? props.preferences && props.preferences.pageSize
        : props.pageSize,
      parentHeight: 0,
    }
  }

  static getDerivedStateFromProps(props, state) {
    if (
      !isEqual(props.columns, state.cachedProps.columns) ||
      !isEqual(props.preferences, state.cachedProps.preferences) ||
      !isEqual(props.defaultVisibility, state.cachedProps.defaultVisibility)
    ) {
      return {
        cachedProps: {
          columns: props.columns,
          settings: props.settings,
          preferences: props.preferences,
          defaultVisibility: props.defaultVisibility,
        },
        ...createColumnState(
          props.columns,
          props.settings,
          props.preferences.columnVisibility,
          props.defaultVisibility,
          props.useColumnIdForVisibilityOptions,
          props.sortVisibilityOptions,
        ),
        defaultSorted:
          props.preferences && props.preferences._defaultSorted
            ? props.preferences._defaultSorted
            : props.defaultSorted,
        pageSize: props.savePageSizePreference
          ? props.preferences && props.preferences.pageSize
          : props.pageSize,
      }
    }

    return null
  }

  componentDidMount = () => {
    this.savePreferences()

    const container = this.parentTable
    if (container && container.current) {
      const parentHeight = container.current.clientHeight

      this.setParentHeight(this.props.resizeDropdown ? 310 : parentHeight - 45)
    }
  }

  getSnapshotBeforeUpdate(_, prevState) {
    const container = this.parentTable
    const prevHeight = prevState.parentHeight + 45
    if (
      prevState.parentHeight !== 0 &&
      container &&
      container.current &&
      container.current.clientHeight !== prevHeight
    ) {
      const parentTableHeight = container.current.clientHeight

      return parentTableHeight
    }

    return null
  }

  componentDidUpdate(prevProps, _, snapshot) {
    this.savePreferences()

    const container = this.parentTable
    const parentHeight = container.current.clientHeight

    // Component doesn't reflect the pageSize changes immediately
    if (
      prevProps.resizeDropdown !== this.props.resizeDropdown &&
      snapshot &&
      !this.props.resizeDropdown
    ) {
      this.setParentHeight(snapshot - 70)
    }
    if (parentHeight !== this.state.parentHeight && !this.props.resizeDropdown) {
      this.setParentHeight(parentHeight)
    }
  }

  componentWillUnmount() {
    this.setParentHeight(0)
  }

  setParentHeight = (parentHeight) => this.setState({ parentHeight })

  savePreferences() {
    if (!this.props.preferences) {
      this.props.doSavePreferences(this.props.preferencesId, this.getTablePreferences())
    }
  }

  getTablePreferences({ columnVisibility, defaultSorted, pageSize }) {
    return {
      columnVisibility: columnVisibility ?? this.state.columnVisibility,
      pageSize: this.props.savePageSizePreference
        ? (pageSize ?? this.state.pageSize)
        : undefined,
      _defaultSorted:
        this.props.saveSortingPreference && (defaultSorted ?? this.state.defaultSorted),
    }
  }

  handleColumnChange = (update) => {
    let columnVisibility = {
      ...this.state.columnVisibility,
      ...update,
    }

    const visibleCount = Object.keys(columnVisibility).filter(
      (key) =>
        !['_defaultSorted', 'undefined', 'Selected', 'Edit', 'Delete'].includes(key) &&
        columnVisibility[key],
    ).length

    if (visibleCount === 0) columnVisibility = this.state.columnVisibility

    const columns = this.props.columns.filter(
      (c) =>
        columnVisibility[
          this.props.useColumnIdForVisibilityOptions ? c.id : c.Header
        ] !== false,
    )

    this.props.doSavePreferences(
      this.props.preferencesId,
      this.getTablePreferences({ columnVisibility }),
    )
    this.setState({
      columnVisibility,
      columns,
    })

    if (this.props.onColumnChange) {
      this.props.onColumnChange(columnVisibility)
    }
  }

  handleSortedChange = (defaultSorted) => {
    this.props.doSavePreferences(
      this.props.preferencesId,
      this.getTablePreferences({ defaultSorted }),
    )
    this.setState({ defaultSorted })

    if (this.props.onSortChange) {
      const currentData = this.reactTable.current.getResolvedState().sortedData
      this.props.onSortChange(defaultSorted, currentData)
    }
  }

  updateFaultColumnWidth = (page) => {
    const {
      props: { calculateFaultColumnWidth, onPageChange },
      reactTable: { current },
    } = this

    if (onPageChange) {
      onPageChange(page)
    }

    if (calculateFaultColumnWidth && current)
      calculateFaultColumnWidth(
        current.state.page,
        current.state.pageSize,
        '',
        current.getResolvedState().sortedData,
      )
  }

  onPageSizeChange = (pageSize) => {
    this.props.doSavePreferences(
      this.props.preferencesId,
      this.getTablePreferences({ pageSize }),
    )

    this.setState({ pageSize })

    if (this.props.resizeDropdown) {
      this.setParentHeight(this.parentTable.current.clientHeight)
    }

    if (this.props.onPageSizeChange) {
      this.props.onPageSizeChange(pageSize)
    }
  }

  render() {
    const {
      onRowClick,
      style,
      shouldReinitializeWidth,
      currentPage,
      totalPages,
      manual,
      noDestructiveTranslationForOptionDropDown,
    } = this.props
    const { defaultSorted, columnOptions, columnVisibility, pageSize, parentHeight } =
      this.state

    if (shouldReinitializeWidth) {
      this.updateFaultColumnWidth()
    }

    let pageSizeOptions = [5, 10, 20, 25, 50, 100]

    if (!isNil(this.props.defaultPageSize)) {
      pageSizeOptions = uniq([this.props.defaultPageSize, 5, 10, 20, 25, 50, 100]).sort(
        (a, b) => a - b,
      )
    }

    // To check if we pass pagination pages to add this prop to table
    const extraProps = totalPages === null ? {} : { pages: totalPages }

    return (
      <div
        className={`${this.props.unstyled ? '' : 'Table'} ${
          this.props.editing ? 'editing' : ''
        } ${this.props.className}
        `}
        ref={this.parentTable}
      >
        <ReactTable
          ref={this.reactTable}
          page={currentPage}
          {...extraProps}
          pageSizeOptions={pageSizeOptions}
          style={style}
          getTrProps={(unUsedState, rowInfo) => ({
            onClick: (e, handleOriginal) => {
              onRowClick(rowInfo.original)
              if (handleOriginal) handleOriginal()
            },
          })}
          {...this.props}
          pageSize={pageSize}
          innerRef={(tableInstance) => {
            // eslint-disable-next-line
            this.props.innerRef && this.props.innerRef(tableInstance)
            this.props.getTableContainerRef(this.parentTable)
          }}
          minRows={this.props.minRows}
          columns={intlizeColumnHeaders(this.state.columns)}
          className={`${this.props.tableClassName} ${
            this.props.unstyled ? '' : 'react-table -striped -highlight'
          }`}
          LoadingComponent={Spinner}
          {...(defaultSorted && { defaultSorted })}
          onSortedChange={this.handleSortedChange}
          getLoadingProps={getLoadingProps}
          getNoDataProps={getNoDataProps}
          previousText={
            <div className="pagbtn left">
              <Icon icon={['far', 'chevron-left']} />
              <span className="text">{formatSimpleMessage('Previous')}</span>
            </div>
          }
          nextText={
            <div className="pagbtn right">
              <span className="text">{formatSimpleMessage('Next')}</span>
              <Icon icon={['far', 'chevron-right']} />
            </div>
          }
          loadingText={formatSimpleMessage('Loading')}
          noDataText={formatSimpleMessage(this.props.noDataText)}
          onPageSizeChange={this.onPageSizeChange}
          pageText={formatSimpleMessage('Page')}
          rowsText={formatSimpleMessage('rows')}
          ofText={formatSimpleMessage('pages.Of', 'of')}
          resizable
          onPageChange={this.updateFaultColumnWidth}
          getTheadThProps={() => ({
            style: {
              wordBreak: 'keep-all',
            },
          })}
          // if manual is true => informs React Table that you'll be handling sorting and pagination server-side
          manual={manual}
        />
        {this.props.unstyled ||
        this.props.hideOptions ||
        // eslint-disable-next-line
        // eslint-disable-next-line unicorn/explicit-length-check
        (this.props.data && !this.props.data.length > 0) ? null : (
          <ToggleDropdown
            submenuId={this.props.submenuId}
            selectOptionId={this.props.selectOptionId}
            optionsButtonId={this.props.optionsButtonId}
            optionsLineId={this.props.optionsLineId}
            className="Table-options util-hidePrint"
            innerClassName="Table-options-inner"
            label="Select Columns"
            component={GearButton}
            options={columnOptions}
            values={columnVisibility}
            onChange={this.handleColumnChange}
            containerHeight={parentHeight}
            noDestructiveStartCase={noDestructiveTranslationForOptionDropDown}
            styleProperties={this.props.settings.styleProperties}
          />
        )}
      </div>
    )
  }
}

Table.propTypes = {
  optionsButtonId: string,
  optionsLineId: string,
  className: string,
  tableClassName: string,
  hideOptions: bool,
  saveSortingPreference: bool,
  savePageSizePreference: bool,
  noDataText: string,
  preferences: shape({}),
  preferencesId: string.isRequired,
  doSavePreferences: func.isRequired,
  minRows: number,
  unstyled: bool,
  onRowClick: func,
  onPageChange: func,
  onSortChange: func,
  calculateFaultColumnWidth: func,
  resizeDropdown: bool,
  currentPage: number,
  pageSize: number,
  onPageSizeChange: func,
  useColumnIdForVisibilityOptions: bool,
  sortVisibilityOptions: bool,
  manual: bool,
  noDestructiveTranslationForOptionDropDown: bool,
  ...ReactTable.propTypes,
}

Table.defaultProps = {
  optionsButtonId: '',
  optionsLineId: '',
  className: '',
  tableClassName: '',
  hideOptions: false,
  saveSortingPreference: true,
  savePageSizePreference: false,
  noDataText: 'No data available',
  preferences: undefined,
  minRows: 4,
  unstyled: false,
  calculateFaultColumnWidth: null,
  onRowClick: () => {},
  onPageChange: () => {},
  onSortChange: () => {},
  onPageSizeChange: () => {},
  resizeDropdown: false,
  currentPage: undefined,
  pageSize: undefined,
  useColumnIdForVisibilityOptions: false,
  sortVisibilityOptions: true,
  manual: false,
  noDestructiveTranslationForOptionDropDown: false,
}

function mapStateToProps(state, ownProps) {
  const preferencesId =
    ownProps.tableId ||
    camelCase(
      ownProps.columns.reduce(
        (acc, c) => (typeof c.Header === 'string' ? acc + c.Header : acc),
        '',
      ),
    )
  const preferences = getPreferences(state)[preferencesId] ?? {}
  const settings = getSettings(state)

  return {
    settings,
    preferences,
    preferencesId,
  }
}

function mapDispatchToProps(dispatch) {
  return {
    doSavePreferences: (id, preferences) => dispatch(savePreferences(id, preferences)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Table)
