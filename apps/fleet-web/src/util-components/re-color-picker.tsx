import { Avatar, Stack } from '@karoo-ui/core'
import CircleIcon from '@mui/icons-material/Circle'

import { colors } from '@fleet-web/api/colors'

type ReColorPickerProps = {
  onChange: (color: string) => void
  value: string
  isDisabled: boolean
}

export default function ReColorPicker({
  onChange,
  value,
  isDisabled,
}: ReColorPickerProps) {
  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
    >
      {colors.map((color) =>
        value === color.hex ? (
          <Avatar
            sx={{ backgroundColor: `${color.hex}99`, width: '28px', height: '28px' }}
            key={color.hex}
          >
            <CircleIcon
              sx={{ cursor: 'pointer', color: color.hex }}
              onClick={() => onChange(color.hex)}
            />
          </Avatar>
        ) : (
          <CircleIcon
            key={color.hex}
            sx={{
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              color: `${color.hex}99`,
            }}
            onClick={() => !isDisabled && onChange(color.hex)}
          />
        ),
      )}
    </Stack>
  )
}
