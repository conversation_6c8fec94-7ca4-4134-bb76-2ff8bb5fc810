import { compact, range } from 'lodash'
import moment from 'moment'
import { injectIntl, type IntlShape } from 'react-intl'

import { getCurrentDateWithFormat } from '@fleet-web/util-functions/moment-helper'

import { ctIntl } from './ctIntl'
import InputDropdown from './input-dropdown'

type TimeOption = { name: string; value: number }
type TimeOptionArray = Array<TimeOption>

export type PrivacyHourRangePickerProps = {
  input: {
    value: {
      from: number
      to: number
    }
    onChange: ({
      from,
      to,
      realFrom,
      realTo,
    }: {
      from: number
      to: number
      realFrom: string | undefined
      realTo: string | undefined
    }) => void
  }
  disabled?: boolean
  extraClassNames?: {
    containerClassNames: string
    startTimeExtraClassNames: {
      containerClassNames: string
      inputClassNames: string
    }
    endTimeExtraClassNames: {
      containerClassNames: string
      inputClassNames: string
    }
  }
  placeholders?: {
    startTime: string
    endTime: string
  }
  fifteenIntervals?: boolean
  disableFutureHours?: boolean
  startTimeId?: string
  endTimeId?: string
  intl: IntlShape
}

const END_OF_DAY_TIME_VALUE = 23.99

function _getRealName(i: number, options: TimeOptionArray) {
  return options.find((o) => {
    if (i === 24) {
      return o.value === END_OF_DAY_TIME_VALUE
    }
    return o.value === i
  })?.name
}

function _formatTo(value: number, fifteenIntervals: boolean) {
  if (!fifteenIntervals && value === 24) {
    return 25
  }

  if (fifteenIntervals && value === 24.25) {
    return 23.59
  }

  return value
}

function handleStartChange({
  from,
  input,
  options,
  fifteenIntervals,
}: {
  from: string
  input: PrivacyHourRangePickerProps['input']
  options: TimeOptionArray
  fifteenIntervals: boolean
}) {
  let to = Number(from) + 0.5
  if (Number(from) >= 23.5) {
    if (fifteenIntervals) {
      to = END_OF_DAY_TIME_VALUE
    } else {
      to = 25
    }
  }

  if (
    input.value.to &&
    Number(from) < input.value.to &&
    options.some((o) => o.value === input.value.to)
  ) {
    input.onChange({
      from: Number(from),
      to: Number(input.value.to),
      realFrom: _getRealName(Number(from), options),
      realTo: _getRealName(Number(input.value.to) || 25, options),
    })
  } else {
    input.onChange({
      from: Number(from),
      to,
      realFrom: _getRealName(Number(from), options),
      realTo: _getRealName(_formatTo(Number(from) + 0.5, fifteenIntervals), options),
    })
  }
}

function handleEndChange({
  to,
  input,
  options,
  fifteenIntervals,
}: {
  to: string
  input: PrivacyHourRangePickerProps['input']
  options: TimeOptionArray
  fifteenIntervals: boolean
}) {
  const fromNumber = options.slice(0, -1).some((o) => o.value === input.value.from)
    ? Number(input.value.from)
    : 0
  input.onChange({
    from: fromNumber,
    to: Number(to),
    realFrom: _getRealName(fromNumber || 0, options),
    realTo: _getRealName(_formatTo(Number(to), fifteenIntervals), options),
  })
}

function _formatNoFutureValues(options: TimeOptionArray) {
  const refNow = moment().format('YYYY-MM-DD HH:mm')

  const newOptions = options.map((h) => {
    const refItem = moment(getCurrentDateWithFormat('YYYY-MM-DD') + ' ' + h.name)

    if (!refItem.isAfter(refNow, 'minute')) {
      return h
    }
    return undefined
  })

  return compact(newOptions)
}

function fillAllOptionsArray(disableFutureHours: boolean, fifteenIntervals: boolean) {
  let options = []
  for (const v of range(24)) {
    if (fifteenIntervals) {
      // 00:00
      options.push({
        name: v === 0 ? '00:01' : ctIntl.formatTime(v * 3.6e6, { timeZone: 'UTC' }),
        value: v,
      })
    } else {
      // 00:00
      options.push({
        name: ctIntl.formatTime(v * 3.6e6, { timeZone: 'UTC' }),
        value: v,
      })
    }

    if (fifteenIntervals) {
      // 00:15
      options.push({
        name: ctIntl.formatTime((v + 0.25) * 3.6e6, { timeZone: 'UTC' }),
        value: v + 0.25,
      })
    }

    // 00:30
    options.push({
      name: ctIntl.formatTime((v + 0.5) * 3.6e6, { timeZone: 'UTC' }),
      value: v + 0.5,
    })

    if (fifteenIntervals) {
      // 00:45
      options.push({
        name: ctIntl.formatTime((v + 0.75) * 3.6e6, { timeZone: 'UTC' }),
        value: v + 0.75,
      })
    }
  }

  if (fifteenIntervals) {
    options.push({
      name: '23:59',
      value: END_OF_DAY_TIME_VALUE,
    })
  } else {
    // use constant 24:00 to prevent it's 00:00 will be same option with the first option
    options.push({
      name: '24:00',
      value: 25,
    })
  }

  if (disableFutureHours) {
    options = _formatNoFutureValues(options)
  }

  return options
}

function PrivacyHourRangePicker({
  disabled = false,
  extraClassNames = {
    containerClassNames: '',
    startTimeExtraClassNames: {
      containerClassNames: '',
      inputClassNames: '',
    },
    endTimeExtraClassNames: {
      containerClassNames: '',
      inputClassNames: '',
    },
  },
  placeholders = {
    startTime: 'Start Time',
    endTime: 'End Time',
  },
  fifteenIntervals = false,
  disableFutureHours = false,
  startTimeId = '',
  endTimeId = '',
  input,
}: PrivacyHourRangePickerProps) {
  const allOptions: TimeOptionArray = fillAllOptionsArray(
    disableFutureHours,
    fifteenIntervals,
  )

  const { containerClassNames, startTimeExtraClassNames, endTimeExtraClassNames } =
    extraClassNames

  const remainingOptions: TimeOptionArray = allOptions.filter(
    ({ value }) => value > Number(input.value.from),
  )

  return (
    <div className={`HourRangePicker row ${containerClassNames || ''}`}>
      <InputDropdown
        dropdownButtonId={startTimeId}
        placeholder={
          placeholders && placeholders.startTime ? placeholders.startTime : 'Start Time'
        }
        options={allOptions.slice(0, -1)}
        activeOption={input.value.from}
        onChange={(from: string) =>
          handleStartChange({ from, input, options: allOptions, fifteenIntervals })
        }
        disableOptionIntl
        disableInput
        disabled={disabled}
        iconName="clock"
        extraClassNames={{
          containerClassNames: `HourRangePicker-dropdown ${
            startTimeExtraClassNames && startTimeExtraClassNames.containerClassNames
          }`,
          inputClassNames:
            startTimeExtraClassNames && startTimeExtraClassNames.inputClassNames,
        }}
      />
      <InputDropdown
        dropdownButtonId={endTimeId}
        placeholder={
          placeholders && placeholders.endTime ? placeholders.endTime : 'End Time'
        }
        options={remainingOptions}
        activeOption={input.value.to}
        disableOptionIntl
        disableInput
        disabled={disabled}
        onChange={(to: string) =>
          handleEndChange({ to, input, options: allOptions, fifteenIntervals })
        }
        iconName="clock"
        extraClassNames={{
          containerClassNames: `HourRangePicker-dropdown ${
            endTimeExtraClassNames && endTimeExtraClassNames.containerClassNames
          }`,
          inputClassNames:
            endTimeExtraClassNames && endTimeExtraClassNames.inputClassNames,
        }}
      />
    </div>
  )
}

export default injectIntl(PrivacyHourRangePicker)
