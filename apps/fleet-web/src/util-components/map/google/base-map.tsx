import { Fragment, PureComponent } from 'react'
import type * as React from 'react'
import type { ChangeEventValue } from 'google-map-react'
import type { Cluster, Points } from 'points-cluster'
import { isIOS } from 'react-device-detect'
import { connect } from 'react-redux'
import type { RouteComponentProps } from 'react-router-dom'
import screenfull from 'screenfull'
import styled from 'styled-components'
import { match } from 'ts-pattern'

import type { MapApiProvider } from '@fleet-web/api/user/types'
import { fetchMapGeofencesAction, getAllMapGeofences } from '@fleet-web/duxs/geofences'
import { fetchLandmarks } from '@fleet-web/duxs/landmarks'
import {
  getFocusedGeofence,
  getFocusedLandmark,
  getFollowFocusedItem,
  getIsStreetViewVisible,
  getMapLandmarksWithoutFocusedLandmark,
  getZoom,
  updateMapSize,
} from '@fleet-web/duxs/map'
import { addToast } from '@fleet-web/duxs/toast'
import { getSettings_UNSAFE, getShowMapLivePositionButton } from '@fleet-web/duxs/user'
import { UserGoogleBaseMapWithChinaSupport } from '@fleet-web/modules/components/connected/UserGoogleBaseMap'
import {
  clickedMapFullscreenButton,
  setIsStreetViewVisible,
} from '@fleet-web/modules/map-view/actions'
import {
  getPreviousPathname,
  type FullscreenMapNavigationState,
} from '@fleet-web/modules/map-view/fullscreen-map/slice'
import type { MapPlaceResult } from '@fleet-web/modules/map-view/types'
import type { AppState } from '@fleet-web/root-reducer'
import { spacing } from '@fleet-web/shared/components/styled/global-styles/spacing'
import type { FixMeAny, ValueOf } from '@fleet-web/types'
import type { MapsExtended } from '@fleet-web/types/extended/google-maps'
import type { ExcludeStrict } from '@fleet-web/types/utils'
import MapTrayToggles from '@fleet-web/util-components/map/shared/group-buttons/map-tray-toggles'
import LandmarkTooltip from '@fleet-web/util-components/map/shared/landmark-tooltip'
import MapControls from '@fleet-web/util-components/map/shared/map-controls'
import type {
  MapEventsInjectedProps,
  Position,
} from '@fleet-web/util-components/map/shared/map-events'
import {
  createMapOptions,
  generateClusters,
  isElementPartOfMapBackground,
  setupStreetViewHandlers,
  type VIEW_MODE,
} from '@fleet-web/util-functions'

import LayersMenu from '../shared/layers-menu'
import LivePositionToggle from '../shared/live-position-toggle'
import type { MapProviderMetaData } from '../shared/types'
import { FocusedGeofence } from './layers/FocusedGeofence'
import { renderLandmarkMarkers } from './layers/landmark-components'
import { renderLandmarkMarker } from './layers/landmark-marker'
import MapLayer from './layers/map-layer'
import MeasurePathDistance from './layers/measure-path-distance'
import RenderIfInBounds from './layers/render-if-in-bounds'
import { VisibleGeofences } from './layers/VisibleGeofences'

type LandmarkClusterGenerator = ReturnType<typeof generateLandmarksClusterData>[1]
type LandmarkCluster = Cluster<Points & ReadonlyArray<Record<string, FixMeAny>>>

const mapLandmarkCluster = (c: LandmarkCluster) => ({
  id: `${c.numPoints}_${c.points[0].landmark.id}`,
  landmarkPoints: c.points,
  lat: c.wy,
  lng: c.wx,
  numPoints: c.numPoints,
})

const generateLandmarksClusterData = (props: Props) =>
  generateClusters(props, {
    itemName: 'landmark',
    accessor: 'landmarks',
    mapCluster: mapLandmarkCluster,
  })

export type Props = {
  fullscreenPath?: string
  fullscreenState?: FullscreenMapNavigationState
  hasFocusedItem?: boolean
  showDefaultMarkers?: boolean
  showMapOptions?: boolean | 'viewTray'
  showVisionOptions?: boolean
  vehicleActiveEventCoords?: {
    lat: number
    lng: number
  } | null
  viewMode?: ValueOf<typeof VIEW_MODE>
  viewTrayControls?: (renders: { fullscreenButton: JSX.Element }) => React.ReactNode
  places: Array<MapPlaceResult>
  children: React.ReactNode
  onFullscreenClick?: () => void
  onMapChange?: (obj: ChangeEventValue) => void
  onEscFunction?: () => void
  onGoogleApiLoaded?: () => void
  onMapsApiLoaded: MapsExtended.OnGoogleApiLoaded
  onTilesLoaded?: () => void
  onMapCenterChanged?: () => void
  changeViewMode?: (mode: ValueOf<typeof VIEW_MODE>) => void
  mapProviderMetaData: MapProviderMetaData<(typeof MapApiProvider)['GOOGLE']>
  mapTypeId: google.maps.MapTypeId
  onChangeMapTypeId: (mapTypeId: google.maps.MapTypeId) => void
} & Pick<
  MapEventsInjectedProps,
  | 'currentLayerVisibility'
  | 'changeMapCenterZoom'
  | 'setLayerVisibility'
  | 'onMeasureDistance'
  | 'mapState'
  | 'mapsApi'
  | 'mapRef'
  | 'map'
  | 'zoom'
  | 'setMapState'
  | 'onClusterClick'
  | 'onContextMenuClick'
  | 'onMapRightClick'
  | 'center'
  | 'isMeasuring'
  | 'onMapClick'
  | 'setVehicleFocusedLayerVisibility'
  | 'followFocusedItem'
  | 'onFollowFocusedItemClick'
> &
  Pick<RouteComponentProps, 'history' | 'location'> &
  ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps

type State = {
  landmarkClustersFunc: LandmarkClusterGenerator | null
  landmarkClusters: ReadonlyArray<Record<string, FixMeAny>>
  hideViewTray: boolean
  scrollwheel: boolean
}

const defaultProps = {
  hasFocusedItem: false,
  isComparingTrips: false,
  showDefaultMarkers: true,
  showMapOptions: true as ExcludeStrict<Props['showMapOptions'], undefined>,
}

class BaseMap extends PureComponent<Props & typeof defaultProps, State> {
  static defaultProps = defaultProps

  mapObject: MapsExtended.MapObject | null = null

  state: State = {
    landmarkClustersFunc: null,
    landmarkClusters: [],
    hideViewTray: true,
    scrollwheel: true, // Local scrollwheel map option
  }

  componentDidMount() {
    const {
      fullscreenPath,
      location: { pathname },
    } = this.props

    if (fullscreenPath && pathname === fullscreenPath && !isIOS && screenfull) {
      screenfull.on('change', this.handleEscFunction)
    }

    if (this.props.showDefaultMarkers) {
      this.props.fetchMapGeofencesAction()
      this.props.fetchLandmarks()
    }
  }

  componentDidUpdate(prevProps: Props) {
    // Create clusters and fit bounds to landmarks
    if (
      this.props.landmarks !== prevProps.landmarks ||
      this.props.currentLayerVisibility.livePositionClusters !==
        prevProps.currentLayerVisibility.livePositionClusters
    ) {
      this.generateLandmarkClusters(this.props)
    }
    if (
      screenfull &&
      screenfull.isFullscreen &&
      this.props.isFollowing &&
      prevProps.vehicleActiveEventCoords &&
      this.props.vehicleActiveEventCoords &&
      (prevProps.vehicleActiveEventCoords.lat !==
        this.props.vehicleActiveEventCoords.lat ||
        prevProps.vehicleActiveEventCoords.lng !==
          this.props.vehicleActiveEventCoords.lng)
    ) {
      this.props.changeMapCenterZoom(
        this.props.vehicleActiveEventCoords.lat,
        this.props.vehicleActiveEventCoords.lng,
        this.props.currentZoomLevel,
        'handleMapChangeFollow',
      )
    }

    if (
      !prevProps.hasFocusedItem &&
      this.props.hasFocusedItem &&
      this.props.isStreetViewVisible
    ) {
      this.mapObject?.map.getStreetView().setVisible(false)
    }
  }

  componentWillUnmount() {
    this.props.onMeasureDistance()
    this.props.setIsStreetViewVisible(false)
    if (!isIOS && screenfull && screenfull.enabled) {
      screenfull.off('change', this.handleEscFunction)
    }
  }

  handleGoogleApiLoaded: MapsExtended.OnGoogleApiLoaded = (mapObject) => {
    // Save map object
    this.mapObject = mapObject

    const panorama = mapObject.map.getStreetView()
    panorama.setOptions({
      fullscreenControl: false,
      imageDateControl: true,
    })

    setupStreetViewHandlers(
      panorama,
      this.props.mapRef.current as Element,
      (isStreetViewVisible: boolean) => {
        this.props.setIsStreetViewVisible(isStreetViewVisible)
      },
    )

    if (!this.state.landmarkClustersFunc) {
      this.generateLandmarkClusters()
    }

    if (this.props.onGoogleApiLoaded) {
      this.props.onGoogleApiLoaded()
    }

    mapObject.map.addListener('rightclick', (event: google.maps.MapMouseEvent) => {
      const latLng = event.latLng
      if (latLng === null) {
        return
      }

      const pixel = (event as FixMeAny).pixel
      const position = {
        lat: latLng.lat(),
        lng: latLng.lng(),
        x: pixel.x,
        y: pixel.y,
      }

      const isMapTheTarget =
        mapObject.ref !== null
          ? isElementPartOfMapBackground(
              event.domEvent.target as Element,
              mapObject.ref,
            )
          : false

      this.props.onMapRightClick(position, isMapTheTarget)
    })

    mapObject.map.addListener('center_changed', () => {
      if (this.props.onMapCenterChanged) {
        this.props.onMapCenterChanged()
      }
    })

    this.props.onMapsApiLoaded(mapObject)
  }

  handleMapChange = (obj: ChangeEventValue) => {
    const {
      mapState,
      zoom,
      changeMapCenterZoom,
      setMapState,
      updateMapSize,
      onMapChange,
      isFollowing,
      vehicleActiveEventCoords,
      isStreetViewVisible,
    } = this.props

    // No need to run this on street view mode because the code below only affects the main map layer which is behind the street view layer
    if (!isStreetViewVisible) {
      // Sanity check, this is called twice if the map center/zoom is changed from outside the map itself
      if (isFollowing && vehicleActiveEventCoords) {
        const { lat, lng } = vehicleActiveEventCoords
        changeMapCenterZoom(lat, lng, obj.zoom, 'handleMapChangeFollow')
      }

      if (
        (!mapState ||
          obj.center.lat !== mapState.center.lat ||
          obj.center.lng !== mapState.center.lng ||
          obj.zoom !== zoom ||
          obj.bounds.ne !== mapState.bounds.ne) &&
        !isFollowing
      ) {
        const latPos = obj.center.lat
        const lngPos = obj.center.lng

        changeMapCenterZoom(latPos, lngPos, obj.zoom, 'handleMapChange')

        setMapState(obj)
        updateMapSize(obj.size, obj.bounds)
        this.setState({
          landmarkClusters: this.state.landmarkClustersFunc
            ? this.state.landmarkClustersFunc(obj).map((c) => mapLandmarkCluster(c))
            : [],
        })

        if (onMapChange) {
          onMapChange(obj)
        }
      }
    }
  }

  generateLandmarkClusters = (props = this.props) => {
    const [landmarkClusters, landmarkClustersFunc] = generateLandmarksClusterData(props)

    this.setState({ landmarkClusters, landmarkClustersFunc })
  }

  handleEscFunction = () => {
    if (!isIOS && screenfull && !screenfull.isFullscreen) {
      this.props.history.push({
        pathname: this.props.previousPathname,
        search: this.props.location.search,
      })

      if (this.props.onEscFunction) {
        this.props.onEscFunction()
      }
    }
  }

  handleFullscreenClick = () => {
    if (!isIOS) {
      const {
        fullscreenPath,
        fullscreenState,
        history,
        clickedMapFullscreenButton,
        location: { pathname, search },
      } = this.props

      if (screenfull === false) {
        return
      }

      const { isFullscreen } = screenfull

      if (this.props.onFullscreenClick) {
        this.props.onFullscreenClick()
      }

      if (isFullscreen) {
        history.goBack()
        return screenfull.exit()
      }

      screenfull.request()

      clickedMapFullscreenButton({
        fullscreenMapState: fullscreenState,
        prePathname: pathname,
        fullscreenPath,
        history: {
          ...history,
          location: {
            ...history.location,
            search: search,
          },
        },
      })
    }
    return undefined
  }

  handleLandmarkClusterClick = (event: Record<string, FixMeAny>) =>
    this.props.onClusterClick(event, {
      items: this.state.landmarkClusters,
      accessor: 'landmarkPoints',
      origin: 'handleLandmarkClusterClick',
    })

  handleLandmarkClusterItemClick = (event: Record<string, FixMeAny>) =>
    this.props.onClusterClick(event, {
      items: this.props.landmarks,
      maxZoom: 18,
      origin: 'handleLandmarkClusterItemClick',
    })

  handleLandmarkContextMenuClick = (
    id: string,
    position: ExcludeStrict<
      Parameters<Props['onContextMenuClick']>[2],
      null | undefined
    >,
  ) => {
    this.props.onContextMenuClick('landmark', id, position)
  }

  handleGeofenceContextMenuClick = (id: string, position: Position) => {
    this.props.onContextMenuClick('geofence', id, position)
  }

  handleChangeMapScrollWheel = (scrollwheel: boolean) =>
    this.setState((prevState) => ({
      ...prevState,
      scrollwheel,
    }))

  renderMapViewTray = () => (
    <MapTrayToggles
      toggleFullscreen={this.handleFullscreenClick}
      toggleLandscape={(mode) =>
        this.props.changeViewMode && this.props.changeViewMode(mode)
      }
      togglePortrait={(mode) =>
        this.props.changeViewMode && this.props.changeViewMode(mode)
      }
    />
  )

  render() {
    const {
      center,
      children,
      currentLayerVisibility: {
        geofences: menuGeofences,
        geofenceLabels: showGeofenceLabels,
        landmarks,
        pointsOfInterestLabels: showLandmarkLabels,
        maps: showMaps,
        traffic: showTrafficLayer,
        transit: showTransitLayer,
        bicycle: showBicycleLayer,
      },
      geofencesInWgs,
      showMapLivePositionButton,
      hasFocusedItem,
      isComparingTrips,
      isMeasuring,
      map,
      mapsApi,
      mapState,
      onMapClick,
      onMeasureDistance,
      places,
      setVehicleFocusedLayerVisibility,
      setLayerVisibility,
      showDefaultMarkers,
      showMapOptions,
      zoom,
      onTilesLoaded,
      currentZoomLevel,
      isStreetViewVisible,
      mapTypeId,
      focusedLandmark,
      focusedGeofence,
      showVisionOptions = true,
    } = this.props

    return (
      <Fragment>
        <UserGoogleBaseMapWithChinaSupport
          bootstrapURLKeys={{
            libraries: ['places'],
          }}
          mapTypeId={mapTypeId}
          onGoogleApiLoaded={this.handleGoogleApiLoaded}
          yesIWantToUseGoogleMapApiInternals
          center={center}
          zoom={currentZoomLevel}
          options={(maps) => ({
            ...createMapOptions(maps),
            scrollwheel: this.state.scrollwheel,
          })}
          onChange={this.handleMapChange}
          onClick={onMapClick}
          resetBoundsOnResize
          onTilesLoaded={onTilesLoaded}
        >
          {children}

          {/* Geofences */}
          {!focusedGeofence &&
            showDefaultMarkers &&
            menuGeofences &&
            mapsApi &&
            this.mapObject && (
              <VisibleGeofences
                map={this.mapObject.map}
                mapTypeId={mapTypeId}
                geofencesInWgs={geofencesInWgs}
                alwaysShowGeofenceLabels={showGeofenceLabels}
                onGeofenceContextMenuClick={this.handleGeofenceContextMenuClick}
              />
            )}

          {focusedGeofence && mapsApi && this.mapObject && (
            <FocusedGeofence
              map={this.mapObject.map}
              mapTypeId={mapTypeId}
              geofenceInWgs={focusedGeofence}
            />
          )}

          {/* Landmarks Clusters */}
          {showDefaultMarkers &&
            landmarks &&
            renderLandmarkMarkers({
              landmarkClusters: this.state.landmarkClusters,
              showLabels: showLandmarkLabels,
              toggleMapScrollWheel: this.handleChangeMapScrollWheel,
              onLandmarkClick: onMapClick,
              onLandmarkContextMenuClick: this.handleLandmarkContextMenuClick,
              onLandmarkClusterClick: this.handleLandmarkClusterClick,
              onLandmarkListItemClick: this.handleLandmarkClusterItemClick,
              zoom,
            })}

          {focusedLandmark && mapState && (
            <RenderIfInBounds
              key={focusedLandmark.id}
              bounds={mapState.bounds}
              lat={focusedLandmark.lat}
              lng={focusedLandmark.lng}
            >
              {renderLandmarkMarker({
                key: 'forcused-landmark',
                id: focusedLandmark.id,
                latInWgs84: focusedLandmark.lat,
                lngInWgs84: focusedLandmark.lng,
                scale: 3,
                color: focusedLandmark.color,
                drawRadius: {
                  radius: focusedLandmark.radius,
                  realZoom: zoom,
                },
                customTooltip: <LandmarkTooltip landmark={focusedLandmark} />,
              })}
            </RenderIfInBounds>
          )}

          {/* Landmarks Markers */}
          {mapsApi &&
            mapState &&
            places.map((p) => {
              if (p.type === 'google_place_prediction') {
                return null
              }

              return (
                <RenderIfInBounds
                  key={p.id}
                  bounds={mapState.bounds}
                  lat={p.lat}
                  lng={p.lng}
                >
                  {renderLandmarkMarker({
                    key: 'landmark-marker',
                    id: p.id,
                    name: p.name ?? undefined,
                    icon: p.icon,
                    latInWgs84: p.lat,
                    lngInWgs84: p.lng,
                    onLandmarkClick: onMapClick,
                    onLandmarkContextMenuClick: this.handleLandmarkContextMenuClick,
                  })}
                </RenderIfInBounds>
              )
            })}
          {/* Map Layers */}
          {showMaps &&
            mapsApi &&
            mapState && [
              <MapLayer
                key="traffic-layer"
                layer="TrafficLayer"
                map={map}
                mapsApi={mapsApi}
                toggled={showTrafficLayer}
              />,
              <MapLayer
                key="transit-layer"
                layer="TransitLayer"
                map={map}
                mapsApi={mapsApi}
                toggled={showTransitLayer}
              />,
              <MapLayer
                key="bicycle-layer"
                layer="BicyclingLayer"
                map={map}
                mapsApi={mapsApi}
                toggled={showBicycleLayer}
              />,
            ]}
          {isMeasuring && !!mapsApi && !!map && (
            <MeasurePathDistance
              map={map}
              mapsApi={mapsApi}
              onEndMeasure={onMeasureDistance}
            />
          )}
        </UserGoogleBaseMapWithChinaSupport>

        {/* Map Controls and Options */}
        {match(showMapOptions)
          .with(true, () => (
            <MapControlsContainer>
              <MapControlsTopRightContainer>
                {this.renderMapViewTray()}
                {showMapLivePositionButton && <LivePositionToggle />}
                {!isStreetViewVisible && (
                  <LayersMenu
                    showVisionOptions={showVisionOptions}
                    hasFocusedItem={hasFocusedItem}
                    setLayerVisibility={
                      hasFocusedItem || isComparingTrips
                        ? setVehicleFocusedLayerVisibility
                        : setLayerVisibility
                    }
                  />
                )}
              </MapControlsTopRightContainer>
            </MapControlsContainer>
          ))
          .with('viewTray', () => (
            <MapControlsContainer>
              <MapControlsTopRightContainer>
                {this.renderMapViewTray()}
              </MapControlsTopRightContainer>
            </MapControlsContainer>
          ))
          .with(false, () => null)
          .exhaustive()}

        {mapsApi && map && !isStreetViewVisible && (
          <MapControls
            mapTypeId={mapTypeId}
            mapProviderSelectionUI={
              this.props.mapProviderMetaData.selectionUI === 'do_not_show'
                ? 'do_not_show'
                : {
                    currentMapProvider:
                      this.props.mapProviderMetaData.currentMapProvider,
                    onClick: this.props.mapProviderMetaData.selectionUI.onClick,
                  }
            }
            onChangeMapCenterZoom={this.props.changeMapCenterZoom}
            center={this.props.center}
            zoom={this.props.currentZoomLevel}
            hasFocusedItem={hasFocusedItem}
            onChangeMapTypeId={(newId) => {
              this.props.onChangeMapTypeId(newId)
            }}
            followFocusedItem={this.props.followFocusedItem}
            onFollowFocusedItemClick={this.props.onFollowFocusedItemClick}
            fullscreenUI="do_not_show"
          />
        )}
      </Fragment>
    )
  }
}

const mapStateToProps = (state: AppState) => {
  const { geofenceScaleThreshold } = getSettings_UNSAFE(state)

  return {
    // Map Items
    geofencesInWgs: getAllMapGeofences(state),
    geofenceScaleThreshold,
    showMapLivePositionButton: getShowMapLivePositionButton(state),
    landmarks: getMapLandmarksWithoutFocusedLandmark(state),
    focusedLandmark: getFocusedLandmark(state),
    focusedGeofence: getFocusedGeofence(state),

    isFollowing: getFollowFocusedItem(state),
    currentZoomLevel: getZoom(state),
    isStreetViewVisible: getIsStreetViewVisible(state),
    previousPathname: getPreviousPathname(state),
    // placesLeftPanelActiveMenu: getPlacesLeftPanelActiveMenu(state),
  }
}

const mapDispatchToProps = {
  fetchMapGeofencesAction,
  fetchLandmarks,
  updateMapSize,
  setIsStreetViewVisible,
  clickedMapFullscreenButton,
  addToast,
  // updateDirectionRoutes,
  // closePlaceDirectionBreadcrumb,
}

export default connect(mapStateToProps, mapDispatchToProps)(BaseMap)

const MapControlsContainer = styled.div`
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  padding: ${spacing[4]};
  pointer-events: none;
`

const MapControlsTopRightContainer = styled.div`
  display: flex;
  justify-self: end;
  height: min-content;
  pointer-events: auto;
  gap: ${spacing[1]};
`
