import { Children, cloneElement, isValidElement, type ComponentProps } from 'react'
import GoogleMapReact, { type BootstrapURLKeys } from 'google-map-react'
import * as R from 'remeda'
import { lruMemoize } from 'reselect'
import type { Except } from 'type-fest'

import { useBaseGoogleApiLoaderSingleton } from '@fleet-web/hooks/useGoogleApiLoaderSingleton'
import { createSelectorWithStrictMode } from '@fleet-web/redux-utils'
import {
  MapTypeId,
  type GoogleMapsLibraryName,
} from '@fleet-web/types/extended/google-maps'
import type { ExtractStrict } from '@fleet-web/types/utils'
import { wgs2gcjIfInChinaMainland } from '@fleet-web/util-functions/china-map-utils'
import { GOOGLE_MAPS_VERSION } from '@fleet-web/util-functions/constants'

type EmptyMouseCoords = Record<string, never>
type MouseCoords = google.maps.LatLngLiteral
type MaybeEmptyMouseCoords = EmptyMouseCoords | MouseCoords

export type MapReactProps = ComponentProps<typeof GoogleMapReact>

type MapOptions = Except<GoogleMapReact.MapOptions, 'styles'> & {
  styles?: { mapId: string } | Array<GoogleMapReact.MapTypeStyle>
}

const defaultMapId = 'google-maps-id' // Needed for cloud styling and in order to use AdvancedMarkers

export type GoogleBaseMapNoChinaSupportProps = Except<
  MapReactProps,
  | 'bootstrapURLKeys'
  | 'onChildMouseUp'
  | 'onChildMouseMove'
  | 'options'
  | 'googleMapLoader'
> & {
  children: React.ReactNode
  bootstrapURLKeys?: Except<
    ExtractStrict<BootstrapURLKeys, { key: string }>,
    'version' | 'key' | 'id' | 'libraries'
  > & {
    libraries?: Array<GoogleMapsLibraryName>
  }
  apiKey: string
  // Improved types
  onChildMouseUp?(childKey: any, childProps: any, mouse: MaybeEmptyMouseCoords): void
  onChildMouseMove?(childKey: any, childProps: any, mouse: MaybeEmptyMouseCoords): void
  parseLatLng?: (
    latLng: { lat: number; lng: number },
    child: React.ReactElement<any>,
  ) => { lat: number; lng: number }
  options?: MapOptions | ((maps: GoogleMapReact.Maps) => MapOptions)
}

/** Very useful docs - https://github.com/google-map-react/google-map-react/blob/HEAD/DOC.md */
export function GoogleBaseMapNoChinaSupport({
  bootstrapURLKeys,
  options,
  apiKey: apiKeyProp,
  children,
  parseLatLng,
  ...props
}: GoogleBaseMapNoChinaSupportProps) {
  const sharedMapOption = {
    // https://github.com/google-map-react/google-map-react/issues/271#issuecomment-700634819
    restriction: {
      latLngBounds: {
        north: 85,
        south: -85,
        west: -180,
        east: 180,
      },
    },
    cameraControl: false,
  } satisfies google.maps.MapOptions

  const apiKey = ENV.NODE_ENV === 'development' ? ENV.GMAP_API_KEY : apiKeyProp
  const version = GOOGLE_MAPS_VERSION
  const language = bootstrapURLKeys?.language
  const region = bootstrapURLKeys?.region
  // Marker library is tiny and will eventually be included in core. As such, we include it by default.
  const libraries: Array<GoogleMapsLibraryName> = [
    'marker',
    ...(bootstrapURLKeys?.libraries ?? []),
  ]

  const loaderSingleton = useBaseGoogleApiLoaderSingleton({
    ifNotInitializedInitOptions: {
      apiKey,
      language,
      libraries,
      region,
      version,
    },
  })

  return (
    <GoogleMapReact
      {...props}
      // We don't care about this prop, since we are using our own loader
      bootstrapURLKeys={undefined}
      // Always use our loader singleton to avoid multiple instances of the loader
      googleMapLoader={() => {
        const libToLoad: Array<Promise<unknown>> = []
        for (const library of libraries) {
          libToLoad.push(loaderSingleton.importLibrary(library))
        }
        return Promise.all(libToLoad).then(() => google.maps)
      }}
      options={
        // If styles are provided, we must NOT provide a mapId, since mapId overrides styles with cloud styling.
        R.isFunction(options)
          ? (maps) => {
              const { styles, ...rest } = options(maps)
              return {
                ...sharedMapOption,
                ...rest,
                styles: R.isArray(styles) ? styles : undefined,
                mapId: R.isArray(styles) ? undefined : (styles?.mapId ?? defaultMapId),
              }
            }
          : {
              ...sharedMapOption,
              ...options,
              mapId: R.isArray(options?.styles)
                ? undefined
                : (options?.styles?.mapId ?? defaultMapId),
              styles: R.isArray(options?.styles) ? options.styles : undefined,
            }
      }
    >
      {parseLatLng
        ? Children.map(children, (child) => {
            if (!child) {
              return child
            }
            if (!isValidElement(child)) {
              return child
            }
            const lat = child.props.lat
            const lng = child.props.lng

            if (typeof lat !== 'number' || typeof lng !== 'number') {
              return child
            }

            const parsedCoords = parseLatLng({ lat, lng }, child)
            return cloneElement(child, {
              lat: parsedCoords.lat,
              lng: parsedCoords.lng,
            } as any)
          })
        : children}
    </GoogleMapReact>
  )
}

const selectChildMarkerConvertedCoords = createSelectorWithStrictMode.withTypes<{
  latInWgs84: number
  lngInWgs84: number
}>()(
  [(args) => args.latInWgs84, (args) => args.lngInWgs84],
  (latInWgs84, lngInWgs84): { lat: number; lng: number } =>
    wgs2gcjIfInChinaMainland({ lat: latInWgs84, lng: lngInWgs84 }),
  {
    memoize: lruMemoize,
    memoizeOptions: {
      // We are allowing a maximum of unique markers to be rendered at the same time without re-calculating the coordinates
      // This should be more than enough for the max amount of possible markers our users have. It also consumes low memory.
      maxSize: 900_000,
    },
  },
)

type GoogleMapOptionsWithoutMapTypeId = Except<GoogleMapReact.MapOptions, 'mapTypeId'>

export type GoogleBaseMapWithChinaSupportProps = Except<
  GoogleBaseMapNoChinaSupportProps,
  'parseLatLng' | 'options'
> & {
  mapTypeId: google.maps.MapTypeId
  options?:
    | GoogleMapOptionsWithoutMapTypeId
    | ((maps: GoogleMapReact.Maps) => GoogleMapOptionsWithoutMapTypeId)
}

/**
 * Preferred base component to use.
 * IMPORTANT: All coords sent to children components `lat` and `lng` MUST BE in WGS84.
 */
export function GoogleBaseMapWithChinaSupport({
  mapTypeId,
  options,
  ...props
}: GoogleBaseMapWithChinaSupportProps) {
  return (
    <GoogleBaseMapNoChinaSupport
      parseLatLng={
        mapTypeId === MapTypeId.ROADMAP
          ? (latLng) =>
              selectChildMarkerConvertedCoords({
                latInWgs84: latLng.lat,
                lngInWgs84: latLng.lng,
              })
          : undefined
      }
      options={
        R.isFunction(options)
          ? (maps) => ({ ...options(maps), mapTypeId })
          : { ...options, mapTypeId }
      }
      {...props}
    />
  )
}

/**
 * Mouse coords can be empty __if mouse was moved outside of the map__
 */
export const isMouseCoordsEmpty = <Obj extends MaybeEmptyMouseCoords>(
  obj: Obj,
  // This specific situation needs an Exclude instead of ExcludeStrict
  // eslint-disable-next-line @typescript-eslint/no-restricted-types
): obj is Exclude<Obj, MouseCoords> => Object.keys(obj).length === 0
