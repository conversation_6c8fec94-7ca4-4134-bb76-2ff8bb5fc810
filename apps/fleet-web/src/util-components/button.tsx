import { useMemo } from 'react'

import type { FixMeAny } from '@fleet-web/types'
import Button, { type ButtonProps } from '@fleet-web/util-components/button-storeless'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { withTooltip as tooltipHOC, type WithTooltipOptions } from './tooltip'

/**
 * @deprecated
 * This component should be less and less used in favor of a combination of the __Button__ with the __ArrowedTooltip__ components.
 *
 * @example
  <ArrowedTooltip label="Add Driver">
    <Button action label="Add Driver" icon="plus" />
  </ArrowedTooltip>
 *
 */
export type ButtonWrapperProps = {
  tooltipMessage?: string
  withTooltip?: boolean
  tooltipOptions?: WithTooltipOptions
} & ButtonProps

const ButtonWrapper = ({
  withTooltip = false,
  tooltipMessage = '',
  tooltipOptions,
  ...rest
}: ButtonWrapperProps) => {
  const TooltipButton: FixMeAny = useMemo(
    () => tooltipHOC(tooltipMessage || 'label', tooltipOptions)(Button),
    [tooltipMessage, tooltipOptions],
  )

  return withTooltip ? (
    <TooltipButton
      tooltipMessage={ctIntl.formatMessage({
        id: tooltipMessage,
      })}
      {...rest}
    />
  ) : (
    <Button {...rest} />
  )
}

export default ButtonWrapper
