import { useMemo, useState } from 'react'
import { isNil } from 'lodash'
import {
  Box,
  Button,
  DateTimePicker,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  Tooltip,
} from '@karoo-ui/core'
import { useControlledForm } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import OpenInNewIcon from '@mui/icons-material/OpenInNew'
import ShareIcon from '@mui/icons-material/Share'
import { DateTime } from 'luxon'
import { Controller } from 'react-hook-form'
import { useDispatch } from 'react-redux'
import { z } from 'zod'

import { zodResolverV4 } from '@fleet-web/_maintained-lib-forks/zodResolverV4'
import type { VehicleId } from '@fleet-web/api/types'
import {
  getAuthenticatedUser,
  getVehiclesShareLocationSetting,
} from '@fleet-web/duxs/user'
import { getVehicleLinkToken, updateVehicleLink } from '@fleet-web/duxs/vehicles'
import { MAP } from '@fleet-web/modules/app/components/routes/map'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import { ctToast } from './ctToast'

type Props = {
  vehicleId: VehicleId
}

const shareLocationSchema = z.object({
  linkExpiration: z.date(),
})

type ShareLocationSchema = z.infer<typeof shareLocationSchema>

const ShareLocationModal = ({
  onClose,
  vehicleId,
}: {
  onClose: () => void
  vehicleId: VehicleId
}) => {
  const dispatch = useDispatch()

  const token = useTypedSelector((state) => getVehicleLinkToken(state, vehicleId))
  const { id: userId, cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const [shouldShowGeneratedLink, setShouldShowGeneratedLink] = useState(false)
  const [initialValues] = useState({
    linkExpiration: DateTime.now().plus({ hour: 1 }).toJSDate(),
  })

  const { handleSubmit, control } = useControlledForm<ShareLocationSchema>({
    resolver: zodResolverV4(shareLocationSchema),
    values: initialValues,
    mode: 'onChange',
  })

  const handleCreateVehicleLink = handleSubmit(({ linkExpiration }) => {
    GA4.event({
      category: 'Vehicle',
      action: 'Share Location Click',
    })
    setShouldShowGeneratedLink(true)
    dispatch(updateVehicleLink({ vehicleId, expireTs: linkExpiration }))
  })

  const vehicleShareUrl = useMemo(
    () =>
      `${window.location.origin}${
        MAP.SHARED_APP.path
      }?vehicle=${vehicleId}&account=${userId}&token=${token}${
        !isNil(clientUserId) ? `&client=${clientUserId}` : ''
      }`,
    [clientUserId, token, userId, vehicleId],
  )

  return (
    <Dialog
      open
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          width: '400px',
        },
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        pt={1}
      >
        <DialogTitle
          sx={{
            wordBreak: 'break-all',
            flex: 1,
          }}
        >
          {ctIntl.formatMessage({
            id: 'Share Location  ',
          })}
        </DialogTitle>

        <IconButton
          onClick={onClose}
          color="secondary"
          size="small"
          sx={{ mr: 2 }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </Stack>
      <DialogContent>
        <Controller
          name="linkExpiration"
          control={control}
          render={({ field }) => (
            <DateTimePicker
              sx={{
                '&.MuiTextField-root': {
                  width: '100%',
                },
              }}
              disablePast
              label={ctIntl.formatMessage({ id: 'Link Expiration' })}
              value={field.value ? DateTime.fromJSDate(field.value) : null}
              minDateTime={DateTime.fromJSDate(initialValues.linkExpiration)}
              onChange={(value) => value && field.onChange(value.toJSDate())}
            />
          )}
        />
      </DialogContent>
      <DialogActions
        sx={{ justifyContent: 'space-between', borderTop: '1px solid #EEE' }}
      >
        {token && shouldShowGeneratedLink ? (
          <>
            <a
              className="util-link"
              href={vehicleShareUrl}
            >
              <Button endIcon={<OpenInNewIcon />}>
                {ctIntl.formatMessage({ id: 'Link URL' })}
              </Button>
            </a>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                // eslint-disable-next-line no-restricted-globals
                navigator.clipboard
                  .writeText(vehicleShareUrl)
                  .then(() =>
                    ctToast.fire(
                      'success',
                      ctIntl.formatMessage({ id: 'Link copied to clipboard' }),
                    ),
                  )
                  .catch(() =>
                    ctToast.fire(
                      'error',
                      ctIntl.formatMessage({ id: 'Error copying to clipboard' }),
                    ),
                  )
              }}
            >
              {ctIntl.formatMessage({ id: 'Copy url to clipboard' })}
            </Button>
          </>
        ) : (
          <>
            <Button
              variant="outlined"
              color="secondary"
              onClick={onClose}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              variant="contained"
              onClick={handleCreateVehicleLink}
              loading={shouldShowGeneratedLink}
            >
              {ctIntl.formatMessage({ id: 'Generate Link' })}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

const ReShareLocationButton = ({ vehicleId }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const vehiclesShareLocationSetting = useTypedSelector(getVehiclesShareLocationSetting)

  return (
    <>
      <Tooltip
        title={
          <Box sx={{ textAlign: 'center' }}>
            {ctIntl.formatMessage({
              id: 'map.detailsPanel.tab.viewDaily.shareLocation',
            })}
          </Box>
        }
      >
        <Button
          onClick={() => setIsModalOpen((prev) => !prev)}
          disabled={!vehiclesShareLocationSetting}
          variant="outlined"
          color="secondary"
          size="small"
          startIcon={<ShareIcon />}
          fullWidth
        >
          {ctIntl.formatMessage({ id: 'Share Location' })}
        </Button>
      </Tooltip>
      {isModalOpen && (
        <ShareLocationModal
          onClose={() => setIsModalOpen(false)}
          vehicleId={vehicleId}
        />
      )}
    </>
  )
}

export default ReShareLocationButton
