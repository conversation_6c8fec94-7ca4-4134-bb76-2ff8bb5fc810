import type { ChangeEvent } from 'react'
import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from '@karoo-ui/core'
import {
  useController,
  type Control,
  type FieldPath,
  type FieldPathValue,
  type FieldValues,
} from 'react-hook-form'

import {
  ctIntl,
  type CtIntlFormatMessageOptions,
} from '@fleet-web/util-components/ctIntl'

export type FormListItemType<Value = string | number> = {
  label:
    | string
    | {
        id: string
        option?: CtIntlFormatMessageOptions
      }
  value: Value
}

type FormNamePathError =
  'You either wrote an inexistent field path OR the type of this field value does not match any of the values in the list.'

export type FormSelectProps<
  TValues extends FieldValues,
  TNamePath extends FieldPath<TValues>,
  ListValue extends string | number,
> = {
  formProps: {
    name: FieldPathValue<TValues, TNamePath> extends ListValue | null | undefined
      ? TNamePath
      : FormNamePathError
    control: Control<TValues>
  }
  labelId: string
  label: string
  list: ReadonlyArray<FormListItemType<ListValue>>
  helpText?: string
  disabled?: boolean
  dataTestId?: string
  onChange?: (value: ListValue) => void
}

type BaseFormValue = string | number
export default function FormSelect<
  TValues extends FieldValues,
  TNamePath extends FieldPath<TValues>,
  ListValue extends BaseFormValue,
>({
  formProps,
  disabled,
  list,
  labelId,
  label,
  helpText,
  dataTestId,
  onChange,
}: FormSelectProps<TValues, TNamePath, ListValue>) {
  const {
    field: { value: value_, onChange: onChange_, ...field },
    fieldState,
  } = useController({
    name: formProps.name as any, // fine to use any here
    control: formProps.control,
  })

  const finalHelpText = fieldState.error ? fieldState.error.message : helpText
  const fieldValue = value_ as BaseFormValue
  const fieldOnChange = onChange_ as (event: ChangeEvent | BaseFormValue) => void
  return (
    <FormControl
      data-testid={dataTestId ?? ''}
      size="small"
      fullWidth
    >
      <InputLabel id={labelId}>{label}</InputLabel>
      <Select
        {...field}
        data-testid={`${dataTestId ?? ''}-Selection`}
        value={fieldValue ?? null} // null is needed so that the component is always controlled. Undefined could make it an uncontrolled component.
        labelId={labelId}
        label={label}
        disabled={disabled}
        error={!!fieldState.error}
        onChange={(e) => {
          const event = e as unknown as ChangeEvent
          fieldOnChange(event)
          onChange?.(e.target.value as ListValue)
        }}
      >
        {list.map((entry) => (
          <MenuItem
            data-testid={`${dataTestId ?? ''}-${entry.value}`}
            value={entry.value}
            key={entry.value}
          >
            {typeof entry.label === 'string'
              ? entry.label
              : ctIntl.formatMessage({ id: entry.label.id }, entry.label.option)}
          </MenuItem>
        ))}
      </Select>
      {finalHelpText && (
        <FormHelperText error={!!fieldState.error}>
          {ctIntl.formatMessage({ id: finalHelpText })}
        </FormHelperText>
      )}
    </FormControl>
  )
}
