import { number, oneOf, string } from 'prop-types'

import { FormattedRelative } from '@fleet-web/util-components/FormattedRelative'
import { formatSimpleMessage } from '@fleet-web/util-functions/intl-utils'
import { leftPad } from '@fleet-web/util-functions/string-utils'

function msToTimeSince(ms) {
  let n = Date.now() - ms

  n /= 1000
  if (n < 60) return `${Math.round(n)} ${formatSimpleMessage('sec')}`

  n /= 60
  if (n < 60) return `${Math.round(n)} ${formatSimpleMessage('min')}`

  n /= 60
  if (n < 24) return `${Math.round(n)} ${formatSimpleMessage('hrs')}`

  n /= 24
  return `${Math.round(n)} ${formatSimpleMessage('days')}`
}

function msToTimeSinceHoursMins(ms, colon = false) {
  const mins = Math.abs(ms / 1000 / 60)
  let sMins = Math.ceil(mins % 60)
  let sHours = (mins / 60) | 0 // Truncate to 0
  const neg = ms < 0 ? '-' : ''

  if (sMins === 60) {
    sHours += 1
    sMins = 0
  }

  return colon
    ? `${neg}${sHours}:${leftPad(sMins, 2)}`
    : `${neg}${sHours}hrs ${sMins}min`
}

function strip3Chars(str) {
  return <span>{str.length > 3 ? str.slice(3) : str}</span>
}

const FormattedDuration = (props) => {
  const type = props.type.split('Colon')
  switch (type[0]) {
    case 'hourMin': {
      return (
        <span className={props.className}>
          {msToTimeSinceHoursMins(props.value, type[1] === '')}
        </span>
      )
    }
    case 'minimal': {
      return (
        <span className={props.className}>
          {props.value === 0
            ? formatSimpleMessage('Unknown')
            : msToTimeSince(props.value)}
        </span>
      )
    }
    default: {
      return (
        <FormattedRelative
          value={props.value}
          updateIntervalInSeconds={0}
        >
          {strip3Chars}
        </FormattedRelative>
      )
    }
  }
}

FormattedDuration.propTypes = {
  value: number.isRequired,
  type: oneOf(['hourMin', 'hourMinColon', 'minimal', '']),
  className: string,
}

FormattedDuration.defaultProps = {
  type: '',
  className: '',
}

export default FormattedDuration
