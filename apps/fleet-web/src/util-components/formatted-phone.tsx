import { connect } from 'react-redux'

import { getSettings } from '@fleet-web/duxs/user'
import type { AppState } from '@fleet-web/root-reducer'

function mapStateToProps(state: AppState) {
  const { phonePrefix } = getSettings(state)
  return {
    phonePrefix: phonePrefix as string,
  }
}

type Props = ReturnType<typeof mapStateToProps> & {
  value: string | null | undefined
}

const FormattedPhone = ({ phonePrefix, value }: Props) => {
  if (!value) return null
  if (value.slice(0, phonePrefix.length) === phonePrefix) return '+' + value
  return value
}

export default connect(mapStateToProps)(FormattedPhone)
