import type { ComponentProps } from 'react'
import type * as React from 'react'

import Icon from '@fleet-web/components/Icon'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'
import { capitalize } from '@fleet-web/util-functions/string-utils'

import AlertsBadge from './alerts-badge'
import { withTooltip, type WithTooltipProps } from './tooltip'

type Props = {
  className?: string
  id?: React.HTMLAttributes<HTMLDivElement>['id']
  active?: boolean
  alertsCount?: number
  disabled?: boolean
  icon?: ComponentProps<typeof Icon>['icon']
  iconSvg?: string
  onClick?: React.HTMLAttributes<HTMLDivElement>['onClick']
  iconHtml?: React.ReactNode
  color?: ComponentProps<typeof Icon>['color']
} & WithTooltipProps

const IconButton = ({
  className = '',
  id,
  active = false,
  onClick,
  alertsCount = 0,
  disabled,
  icon,
  iconSvg,
  iconHtml = null,
  color,
}: Props) => (
  <div
    id={id}
    className={`${className} IconButton ${active ? 'is-active' : ''} ${
      disabled ? 'is-disabled' : ''
    }`}
    onClick={disabled ? undefined : onClick}
  >
    {iconSvg && <svg {...makeSanitizedInnerHtmlProp({ dirtyHtml: iconSvg })} />}
    {icon && (
      <Icon
        icon={icon}
        color={color}
      />
    )}
    {iconHtml}
    <AlertsBadge count={alertsCount} />
  </div>
)

export default withTooltip('icon', { messageTransform: capitalize })(
  IconButton,
) as typeof IconButton
