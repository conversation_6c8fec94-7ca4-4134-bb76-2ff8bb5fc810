import { Component, useEffect } from 'react'
import type * as React from 'react'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import { FormattedMessage } from 'react-intl'
import ReactModal, { type ReactModalProps } from 'react-modal'

import Icon from '@fleet-web/components/Icon'
import type { FixMeAny } from '@fleet-web/types'
import Button, { type ButtonProps } from '@fleet-web/util-components/button-storeless'

const modalStyle = {
  overlay: {
    zIndex: 1000,
  },

  content: {
    borderRadius: 0,
    boxShadow: '0 5px 15px 0 rgba(0, 0, 0, 0.5)',
    padding: 0,
  },
}

type ModalIconProps = {
  type?: 'circle' | 'shield'
  icon: IconProp
  iconClassName?: string
}

const ModalIcon = ({ type = 'circle', icon, iconClassName = '' }: ModalIconProps) => (
  <div className="ModalIcon">
    {type === 'shield' && (
      <Icon
        icon="shield-alt"
        className="ModalIcon-shield"
      />
    )}
    <Icon
      icon={icon}
      className={`${
        type === 'shield' ? 'ModalIcon-shield-indicator' : 'ModalIcon-circle'
      } ${iconClassName}`}
    />
  </div>
)

export type ModalProps = {
  hideHeader?: boolean
  hideCloseIcon?: boolean
  children?: React.ReactNode
  modalIcon?: ModalIconProps
  onClose?: (event: FixMeAny) => any
  title?: ReactModalProps['contentLabel']
  extraClassNames?: {
    contentClassName?: string
    overlayClassName?: string
    modalClassName?: string
    headerClassName?: string
    bodyClassName?: string
  }
} & ReactModalProps

/**
 * @deprecated Use Dialog or Modal from `@karoo-ui/core` instead
 */
const Modal = ({
  hideHeader = false,
  modalIcon,
  onClose = () => {},
  title,
  style,
  extraClassNames = {},
  ...props
}: ModalProps) => {
  useEffect(() => {
    // https://github.com/reactjs/react-modal/issues/133#issuecomment-194034344
    ReactModal.setAppElement('#app')
  }, [])

  return (
    <ReactModal
      {...props}
      contentLabel={title}
      style={{ ...modalStyle, ...style }}
      onRequestClose={onClose}
      className={extraClassNames.contentClassName}
      overlayClassName={extraClassNames.overlayClassName}
      shouldCloseOnOverlayClick
    >
      <div className={`Modal ${extraClassNames.modalClassName || ''}`}>
        {hideHeader ? null : (
          <div className={`Modal-header ${extraClassNames.headerClassName || ''} `}>
            <div className="Modal-title">
              {title && <FormattedMessage id={title} />}
            </div>
            {props.hideCloseIcon ? null : (
              <div
                className="Modal-closeWrapper"
                onClick={onClose}
              >
                <Icon icon="times" />
              </div>
            )}
          </div>
        )}
        <div className={`Modal-body ${extraClassNames.bodyClassName || ''} `}>
          {modalIcon && <ModalIcon {...modalIcon} />}
          {props.children}
        </div>
      </div>
    </ReactModal>
  )
}

export default Modal

const decisionStyle = {
  content: {
    left: '50%',
    maxWidth: '600px',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    width: '50%',
  },
}

type DecisionModalProps = ModalProps & {
  header?: string
  children?: React.ReactNode
  onResult: (value: boolean) => void
  rejectLabel?: string
  confirmLabel?: string
  confirmButtonProps?: ButtonProps
  content?: JSX.Element
  hideTitle?: boolean
  extraClassNames: ModalProps['extraClassNames'] & { buttonsClassName?: string }
}

export class DecisionModal extends Component<DecisionModalProps> {
  static defaultProps = {
    rejectLabel: 'No',
    confirmLabel: 'Yes',
    extraClassNames: {},
    hideTitle: false,
    content: null,
  }

  handleConfirm = () => {
    this.props.onResult(true)
  }

  handleReject = () => {
    this.props.onResult(false)
  }

  render() {
    const {
      title,
      header,
      children,
      rejectLabel,
      confirmLabel,
      confirmButtonProps,
      isOpen,
      content,
      extraClassNames,
      hideTitle,
    } = this.props

    return (
      <Modal
        {...this.props}
        onClose={this.handleReject}
        contentLabel={title}
        isOpen={isOpen}
        style={decisionStyle}
        title={title}
        extraClassNames={extraClassNames}
        hideHeader={hideTitle}
      >
        {header && (
          <p>
            <strong
              className={
                extraClassNames && extraClassNames.headerClassName
                  ? extraClassNames.headerClassName
                  : ''
              }
            >
              <FormattedMessage id={header} />
            </strong>
          </p>
        )}
        {children && <div className="util-modalChild">{children}</div>}
        {content}
        <div
          className={
            extraClassNames && extraClassNames.buttonsClassName
              ? extraClassNames.buttonsClassName
              : 'util-flex end-xs'
          }
        >
          <Button
            grouped
            label={rejectLabel}
            onClick={this.handleReject}
          />
          <Button
            grouped
            label={confirmLabel}
            onClick={this.handleConfirm}
            {...confirmButtonProps}
          />
        </div>
      </Modal>
    )
  }
}
