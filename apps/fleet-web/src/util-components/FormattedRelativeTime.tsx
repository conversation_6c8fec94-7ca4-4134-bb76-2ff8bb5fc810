import { useEffect, useMemo, useState } from 'react'
import { injectIntl, type IntlShape } from 'react-intl'
import type { Except } from 'type-fest'

import type { ExcludeStrict } from '@fleet-web/types/utils'

/** To be used as a "polyfill" for FormattedRelativeTime https://formatjs.io/docs/react-intl/components#formattedrelativetime
 *  Please DELETE this component once we update react-intl to latest.
 *
 *  Based on react-intl@5.25.0 https://github.com/formatjs/formatjs/blob/main/packages/react-intl/src/components/relative.tsx
 */

// ---------- Declarations from the latest react-intl
type RelativeTimeFormatSingularUnit = ExcludeStrict<
  Intl.RelativeTimeFormatUnit,
  'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'quarters' | 'years'
>

type FormatRelativeTimeOptions = Except<Intl.RelativeTimeFormatOptions, 'localeMatcher'>

// ----------

const MINUTE = 60
const HOUR = 60 * 60
const DAY = 60 * 60 * 24

function selectUnit(seconds: number): RelativeTimeFormatSingularUnit {
  const absValue = Math.abs(seconds)

  if (absValue < MINUTE) {
    return 'second'
  }

  if (absValue < HOUR) {
    return 'minute'
  }

  if (absValue < DAY) {
    return 'hour'
  }

  return 'day'
}

function getDurationInSeconds(unit?: RelativeTimeFormatSingularUnit): number {
  switch (unit) {
    case 'second': {
      return 1
    }
    case 'minute': {
      return MINUTE
    }
    case 'hour': {
      return HOUR
    }
    default: {
      return DAY
    }
  }
}

function valueToSeconds(value?: number, unit?: RelativeTimeFormatSingularUnit): number {
  if (!value) {
    return 0
  }
  switch (unit) {
    case 'second': {
      return value
    }
    case 'minute': {
      return value * MINUTE
    }
    default: {
      return value * HOUR
    }
  }
}

export type FormattedRelativeTimeProps = FormatRelativeTimeOptions & {
  value?: number
  unit?: RelativeTimeFormatSingularUnit
  updateIntervalInSeconds?: number
  children?(value: string): React.ReactElement | null
}

const INCREMENTABLE_UNITS = new Set<RelativeTimeFormatSingularUnit>([
  'second',
  'minute',
  'hour',
])
function canIncrement(unit: RelativeTimeFormatSingularUnit = 'second'): boolean {
  return INCREMENTABLE_UNITS.has(unit)
}

const SimpleFormattedRelativeTime = injectIntl(
  ({
    children,
    value,
    unit,
    intl,
    numeric,
    style,
  }: Except<FormattedRelativeTimeProps, 'updateIntervalInSeconds'> & {
    value: number
    unit: RelativeTimeFormatSingularUnit
    intl: IntlShape
  }) => {
    const formattedRelativeTime = useMemo(
      () =>
        new Intl.RelativeTimeFormat(intl.locale, {
          numeric,
          style,
        }).format(value, unit),
      [intl.locale, numeric, style, unit, value],
    )

    if (typeof children === 'function') {
      return children(formattedRelativeTime)
    }

    return <>{formattedRelativeTime}</>
  },
)

function FormattedRelativeTime({
  value = 0,
  unit = 'second',
  updateIntervalInSeconds,
  ...otherProps
}: FormattedRelativeTimeProps) {
  const [prevUnit, setPrevUnit] = useState<RelativeTimeFormatSingularUnit | undefined>()
  const [prevValue, setPrevValue] = useState<number>(0)
  const [currentValueInSeconds, setCurrentValueInSeconds] = useState<number>(0)
  let updateTimer: number

  if (unit !== prevUnit || value !== prevValue) {
    setPrevValue(value || 0)
    setPrevUnit(unit)
    setCurrentValueInSeconds(canIncrement(unit) ? valueToSeconds(value, unit) : 0)
  }

  // eslint-disable-next-line sonarjs/no-invariant-returns
  useEffect(() => {
    function clearUpdateTimer() {
      clearTimeout(updateTimer)
    }
    clearUpdateTimer()
    // If there's no interval and we cannot increment this unit, do nothing
    if (!updateIntervalInSeconds || !canIncrement(unit)) {
      return clearUpdateTimer
    }
    // Figure out the next interesting time
    const nextValueInSeconds = currentValueInSeconds - updateIntervalInSeconds
    const nextUnit = selectUnit(nextValueInSeconds)
    // We've reached the max auto incrementable unit, don't schedule another update
    if (nextUnit === 'day') {
      return clearUpdateTimer
    }

    const unitDuration = getDurationInSeconds(nextUnit)
    const remainder = nextValueInSeconds % unitDuration
    const prevInterestingValueInSeconds = nextValueInSeconds - remainder
    const nextInterestingValueInSeconds =
      prevInterestingValueInSeconds >= currentValueInSeconds
        ? prevInterestingValueInSeconds - unitDuration
        : prevInterestingValueInSeconds
    const delayInSeconds = Math.abs(
      nextInterestingValueInSeconds - currentValueInSeconds,
    )

    if (currentValueInSeconds !== nextInterestingValueInSeconds) {
      // eslint-disable-next-line react-hooks/react-compiler, react-hooks/exhaustive-deps
      updateTimer = window.setTimeout(
        () => setCurrentValueInSeconds(nextInterestingValueInSeconds),
        delayInSeconds * 1e3,
      )
    }
    return clearUpdateTimer
  }, [currentValueInSeconds, updateIntervalInSeconds, unit])

  let currentValue = value || 0
  let currentUnit = unit

  if (
    canIncrement(unit) &&
    typeof currentValueInSeconds === 'number' &&
    updateIntervalInSeconds
  ) {
    currentUnit = selectUnit(currentValueInSeconds)
    const unitDuration = getDurationInSeconds(currentUnit)
    currentValue = Math.round(currentValueInSeconds / unitDuration)
  }
  return (
    <SimpleFormattedRelativeTime
      value={currentValue}
      unit={currentUnit}
      {...otherProps}
    />
  )
}

export default FormattedRelativeTime
