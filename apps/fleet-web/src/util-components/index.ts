import { useMifleetFormattedNumber } from '@fleet-web/modules/mifleet/shared/utils'

import AdminHeader from './admin-header'
import AdvancedTable from './advanced-table'
import AlertButton from './alert-button'
import AlertsBadge from './alerts-badge'
import Breadcrumbs from './breadcrumbs'
import Button from './button'
import Carousel from './carousel'
import Checkbox from './checkbox'
import ClassWrapper from './class-wrapper'
import colorOptions from './color-options'
import Alert from './custom/Alert'
import ExpandableDiv from './custom/ExpandableDiv'
import Pills from './custom/Pills/pills'
import ToggleTag from './custom/ToggleTag/toggle-tag'
// Dashboard
import RadioButtonGroup from './dashboard/radio-button-group'
import DataBlock from './data-block'
import DatePicker from './date-picker'
import Dropdown from './dropdown'
import DropdownInput from './dropdown-input'
import EditButton from './edit-button'
import ErrorBoundary from './error-boundary'
import FileInput from './file-input'
import FormattedCosts from './formatted-costs'
import FormattedDistance from './formatted-distance'
import FormattedDuration from './formatted-duration'
import FormattedPhone from './formatted-phone'
import FormattedTemperature from './formatted-temperature'
import FormattedTime from './formatted-time'
import FormattedUserDate from './formatted-user-date'
import FormattedUserTime from './formatted-user-time'
import FormattedVolume from './formatted-volume'
import IconButton from './icon-button'
import InputDropdown from './input-dropdown'
import IntlGlobalProvider from './intl-global-provider'
import LoadingBarAnimation from './loading-bar-animation'
import FormattedLatLng from './map/google/formatted-latlng'
import GeoFuellingStationMarker from './map/google/layers/geo-fuelling-station-marker'
import GPSCoordinates from './map/google/layers/gps-coordinates'
import { renderLandmarkMarker } from './map/google/layers/landmark-marker'
import RenderIfInBounds from './map/google/layers/render-if-in-bounds'
import VehicleCluster from './map/google/layers/vehicle-cluster'
import VehicleComponents from './map/google/layers/vehicle-components'
import VehicleMarker from './map/google/layers/vehicle-marker'
// Share Map components
import ContextMenu from './map/shared/context-menu'
import { EventMarker, EventMarkerInfo } from './map/shared/event-marker'
import Hover from './map/shared/hover'
import SectionSubHeader from './mifleet/section-sub-header'
import MinimizableSearchBar from './minimizable-search-bar'
import Modal, { DecisionModal } from './modal'
import OldTimelineBar from './old-timeline-bar'
import PasswordInput from './password-input'
import PersonInfo from './person-info'
import PhoneInput from './phone-input'
import ProgressBar from './progress-bar'
import RadioInput from './radio-input'
import RealTimePicker from './real-time-picker'
import InfoRightPanel from './right-panel'
import SearchBar from './search-bar'
import SectionHeader from './section-header'
import MultiSelect from './selects/multi-select'
import Spinner from './spinner'
import StarRating from './star-rating'
import Stats from './stats'
import StatusBadge from './status-badge'
import StatusBar from './StatusBar'
import Table from './table'
import { TableSensorMarker } from './table-sensor-marker'
import TextInput from './text-input'
import ThreeDots from './three-dots'
import ToggleButton from './toggle-button'
import ToggleDropdown from './toggle-dropdown'
import Tooltip, { withTooltip } from './tooltip'
import vehicleIcons, { vehicleMarkerBorderIcons } from './vehicle-icons'
import VerticalNav from './vertical-nav'

export { default as FormattedNumber } from './FormattedNumber'

export {
  AdminHeader,
  AdvancedTable,
  AlertButton,
  AlertsBadge,
  Breadcrumbs,
  Button,
  Carousel,
  Checkbox,
  ClassWrapper,
  colorOptions,
  DataBlock,
  DatePicker,
  DecisionModal,
  Dropdown,
  EditButton,
  ErrorBoundary,
  FileInput,
  FormattedCosts,
  FormattedDistance,
  FormattedDuration,
  FormattedLatLng,
  FormattedPhone,
  FormattedUserTime,
  FormattedTime,
  FormattedUserDate,
  FormattedTemperature,
  FormattedVolume,
  IconButton,
  InputDropdown,
  IntlGlobalProvider,
  Modal,
  PasswordInput,
  Pills,
  RadioInput,
  SearchBar,
  MinimizableSearchBar,
  SectionHeader,
  TableSensorMarker,
  Spinner,
  StarRating,
  Stats,
  StatusBadge,
  StatusBar,
  Table,
  TextInput,
  OldTimelineBar,
  RealTimePicker,
  ToggleDropdown,
  ToggleTag,
  Tooltip,
  withTooltip,
  PersonInfo,
  vehicleIcons,
  vehicleMarkerBorderIcons,
  VerticalNav,
  SectionSubHeader,
  Alert,
  LoadingBarAnimation,
  MultiSelect,
  PhoneInput,
  ToggleButton,
  InfoRightPanel,
  DropdownInput,
  ProgressBar,
  ThreeDots,
  ExpandableDiv,
  ContextMenu,
  EventMarker,
  EventMarkerInfo,
  Hover,
  GPSCoordinates,
  renderLandmarkMarker,
  GeoFuellingStationMarker,
  RenderIfInBounds,
  VehicleComponents,
  VehicleMarker,
  VehicleCluster,
  useMifleetFormattedNumber,
  // End of Map components
  // Dashboard
  RadioButtonGroup,
}

export * from './dropdown'
export * from './formatted-distance'
export * from './formatted-temperature'
export * from './input-dropdown'
export * from './ctToast'
export * from './ImageWithFallback'
