import { useEffect, useRef, useState } from 'react'
import { isDate } from 'lodash'
import moment from 'moment-timezone'
import styled from 'styled-components'
import type { ReadonlyDeep } from 'type-fest'

import { getIsTime24 } from '@fleet-web/duxs/user'
import { useOnClickOutside } from '@fleet-web/hooks'
import { useTypedSelector } from '@fleet-web/redux-hooks'

import TextInput from './text-input'

type Props = ReadonlyDeep<{
  timePickerKey?: string
  defaultTimePickerValue?: string
  disabled?: boolean
  required?: boolean
  className?: string
  hideSeconds?: boolean
  addPadding?: boolean
  meta?: {
    valid: boolean
    touched: boolean | Record<string, any>
    error: string
  }
  onChange: (date: Date) => void
  dateValue: Date | undefined
  placeholder?: string
  minutesInterval?: number
  onFocus?: () => void
}>

type TimeSection = 'hours' | 'minutes' | 'seconds'

/*
TODO:

 - Add accessability
 - Add support to input the time directly on the "TextInput"

The 'dateValue' prop:
  Receives a Date(1) object
    RealTimePicker - changes the time for Date(1)
  Returns Date(1) with the time updated

*/

type ActiveValues = {
  hours: string
  minutes: string
  seconds: string
  ampm: 'am' | 'pm' | 'none'
}

const RealTimePicker = ({
  // TextInput Props
  disabled = false,
  required,
  meta,
  placeholder = 'Time',
  // Component Props
  onChange,
  addPadding = false,
  onFocus,
  dateValue,
  className,
  hideSeconds,
  minutesInterval = 0,
  defaultTimePickerValue,
  timePickerKey = '',
}: Props) => {
  const isTime24 = useTypedSelector(getIsTime24)

  /* Decide if the DropDown should be opened or not */
  const [isOpen, setIsOpen] = useState(false)

  // Minutes Interval Settings
  const [activeMinutesInterval] = useState(minutesInterval <= 60 ? minutesInterval : 0)

  /* Setting the active values so we don't have to make getDocumentById 3 times on every select */
  const [activeValues, setActiveValues] = useState<ActiveValues | null>(null)

  /* Left padding a String(number) with a leading 0 if the 'number' on as one character - eg: 1 => 01 */
  const leftPad = (value: string, targetLength = 2) => {
    let output = value
    while (output.length < targetLength) {
      output = '0' + output
    }
    return output
  }

  useEffect(() => {
    if (defaultTimePickerValue) {
      const hours = document.getElementById(
        `${timePickerKey}scrollable-hours-${activeValues?.hours}`,
      )
      if (isOpen && hours) {
        hours.scrollIntoView(true)
      }
    }
  }, [isOpen, activeValues, defaultTimePickerValue, timePickerKey])

  useEffect(() => {
    if (dateValue && isDate(dateValue)) {
      setActiveValues({
        hours: isTime24
          ? leftPad(dateValue.getHours().toString())
          : leftPad(
              dateValue.getHours() % 12 === 0 ? '12' : `${dateValue.getHours() % 12}`,
            ),
        minutes: leftPad(dateValue.getMinutes().toString()),
        seconds: leftPad(dateValue.getSeconds().toString()),
        // eslint-disable-next-line no-nested-ternary
        ampm: isTime24 ? 'none' : dateValue.getHours() < 12 ? 'am' : 'pm',
      })
    }
  }, [dateValue, isTime24])

  useEffect(() => {
    if (isOpen && activeValues) {
      const hours = document.getElementById(
        `${timePickerKey}scrollable-hours-${activeValues.hours}`,
      )
      const minutes = document.getElementById(
        `${timePickerKey}scrollable-minutes-${activeValues.minutes}`,
      )
      const seconds = document.getElementById(
        `${timePickerKey}scrollable-seconds-${activeValues.seconds}`,
      )
      if (hours) {
        hours.parentElement?.scrollTo(0, hours.offsetTop)
      }

      if (minutes) {
        minutes.parentElement?.scrollTo(0, minutes.offsetTop)
      }

      if (seconds) {
        seconds.parentElement?.scrollTo(0, seconds.offsetTop)
      }
    }
  }, [activeValues, isOpen, timePickerKey])

  /* Checking time change for each section: Hours, Minutes and Seconds */
  const onTimeSelection = (section: TimeSection, sectionValue: string) => {
    if (!dateValue || activeValues === null) {
      return
    }
    const newDate = new Date(dateValue)

    switch (section) {
      case 'hours': {
        if (activeValues.ampm === 'am' && Number(sectionValue) >= 12) {
          newDate.setHours(Number(sectionValue) % 12)
        } else if (activeValues.ampm === 'pm' && Number(sectionValue) < 12) {
          newDate.setHours(Number(sectionValue) + 12)
        } else {
          newDate.setHours(Number(sectionValue))
        }
        break
      }
      case 'minutes': {
        newDate.setMinutes(Number(sectionValue))
        break
      }
      case 'seconds': {
        newDate.setSeconds(Number(sectionValue))
        break
      }
    }

    setActiveValues({ ...activeValues, [section]: leftPad(sectionValue) })
    return onChange(newDate)
  }

  /* Rendering the list for each section based on time format - 12/24 */
  const renderTimeSection = (section: TimeSection) => {
    const list: Array<string> = []

    let inc = section === 'hours' && !isTime24 ? 1 : 0
    // eslint-disable-next-line no-nested-ternary
    const iterator = section === 'hours' ? (isTime24 ? 24 : 13) : 60
    while (inc < iterator) {
      if (section === 'minutes' && activeMinutesInterval) {
        if (inc % activeMinutesInterval === 0) {
          list.push(leftPad(inc.toString()))
        }
      } else {
        list.push(leftPad(inc.toString()))
      }
      inc++
    }

    return (
      <StyledTimeList id={`${section}-scrollable`}>
        {list.map((o) => (
          <StyledTimeBlock
            onClick={() => onTimeSelection(section, o)}
            key={o}
            id={`${timePickerKey}scrollable-${section}-${o}`}
            isActive={activeValues?.[section] === o}
          >
            {o}
          </StyledTimeBlock>
        ))}
      </StyledTimeList>
    )
  }

  /* Validate the text to display also based on time format - 12/24 */
  const displayTextValue = () => {
    if (!dateValue || !isDate(dateValue)) {
      return ''
    }

    let timeFormat = isTime24 ? 'HH:mm' : 'hh:mm'
    if (!hideSeconds) {
      timeFormat += ':ss'
    }
    if (!isTime24) {
      timeFormat += ' a'
    }

    let convertDate = moment(dateValue).format()
    if (
      activeMinutesInterval &&
      moment(dateValue).minutes() % activeMinutesInterval !== 0
    ) {
      convertDate = moment(dateValue).set('minute', 0).format()
    }

    return moment(convertDate).format(timeFormat)
  }

  /* open/close the DropDown and scroll the selected values to the top */
  const toggleDropdown = () => {
    if (!dateValue || !isDate(dateValue)) {
      let newDate = new Date()
      if (defaultTimePickerValue) {
        newDate = new Date(defaultTimePickerValue)
      }

      if (minutesInterval && !defaultTimePickerValue) {
        newDate.setMinutes(0)
        newDate.setSeconds(0)
      }

      setActiveValues({
        hours: leftPad(newDate.getHours().toString()),
        minutes: leftPad(newDate.getMinutes().toString()),
        seconds: leftPad(newDate.getSeconds().toString()),
        // eslint-disable-next-line no-nested-ternary
        ampm: isTime24 ? 'none' : newDate.getHours() < 12 ? 'am' : 'pm',
      })
      onChange(newDate)
    }

    setIsOpen((prev) => !prev)

    if (onFocus) onFocus()
  }

  const renderAmPmSection = () => {
    const list = ['am', 'pm'] as const

    const onSelectOption = (sectionValue: (typeof list)[number]) => {
      if (!dateValue) {
        return
      }

      const newDate = new Date(dateValue.valueOf())

      const newDateHoursIn24Format = newDate.getHours()
      if (sectionValue === 'am' && newDateHoursIn24Format >= 12) {
        newDate.setHours(newDateHoursIn24Format % 12)
      } else if (sectionValue === 'pm' && newDateHoursIn24Format < 12) {
        newDate.setHours(newDateHoursIn24Format + 12)
      }

      setActiveValues((prev) =>
        prev === null ? null : { ...prev, ampm: sectionValue },
      )
      onChange(newDate)
    }

    return (
      <StyledTimeList>
        {list.map((o) => (
          <StyledTimeBlock
            onClick={() => onSelectOption(o)}
            key={o}
            isActive={activeValues?.ampm === o}
          >
            {o}
          </StyledTimeBlock>
        ))}
      </StyledTimeList>
    )
  }

  /* OnClickOutside (of the opened dropdown) handler */
  const ref = useRef<HTMLDivElement>(null)
  useOnClickOutside(ref, () => setIsOpen(false))

  return (
    <TimePickerWrapper
      disabled={disabled}
      addPadding={addPadding}
      className={className}
      ref={ref}
    >
      <TextInput
        readOnly
        placeholder={placeholder}
        disabled={disabled}
        value={displayTextValue()}
        iconName="clock"
        meta={meta}
        required={required}
      />
      {!disabled && <StyledClickHandler onClick={() => toggleDropdown()} />}
      {dateValue && isDate(dateValue) && (
        <StyledDropDown isOpen={isOpen}>
          {renderTimeSection('hours')}
          {renderTimeSection('minutes')}
          {!hideSeconds && renderTimeSection('seconds')}
          {!isTime24 && renderAmPmSection()}
        </StyledDropDown>
      )}
    </TimePickerWrapper>
  )
}

export default RealTimePicker

const TimePickerWrapper = styled.div<{
  disabled: boolean
  addPadding: boolean
}>`
  box-sizing: border-box;
  position: relative;
  padding: ${(props) => (props.addPadding ? '0 0.5rem !important' : '0 !important')};
  width: 100%;
  cursor: ${(props) => (props.disabled ? 'default' : 'pointer')};
`

const StyledClickHandler = styled.div`
  position: absolute;
  width: 100%;
  height: 38px;
  top: 0;
`

const StyledDropDown = styled.div<{ isOpen: boolean }>`
  background-color: white;
  display: flex;
  position: absolute;
  width: 100%;
  height: ${(props) => (props.isOpen ? '192px' : '0px')};
  top: 36px;
  border-left: ${(props) => (props.isOpen ? '1px solid #cccccc' : 'none')};
  border-right: ${(props) => (props.isOpen ? '1px solid #cccccc' : 'none')};
  border-bottom: ${(props) => (props.isOpen ? '1px solid #cccccc' : 'none')};
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  z-index: 1;
`

const StyledTimeList = styled.div`
  width: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;

  ::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  -ms-overflow-style: none; /* IE / Edge */

  &:not(:last-child) {
    border-right: 1px solid #cccccc;
  }

  &:hover {
    ::-webkit-scrollbar {
      display: block; /* Chrome, Safari and Opera */
    }
    -ms-overflow-style: auto; /* IE / Edge */
  }
`

const StyledTimeBlock = styled.div<{ isActive?: boolean }>`
  height: 32px;
  width: 100%;
  padding: 5px;
  cursor: pointer;
  background-color: ${(props) =>
    props.isActive ? 'var(--styleInputfieldColourActive)' : 'transparent'};
  color: ${(props) => (props.isActive ? 'white' : '#333333')};

  &:hover {
    background-color: ${(props) =>
      props.isActive
        ? 'var(--styleInputfieldColourActive)'
        : 'var(--styleInputfieldColourHover)'};
    opacity: ${(props) => (props.isActive ? '0.8' : '1')};
  }
`
