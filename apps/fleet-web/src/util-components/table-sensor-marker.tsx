import {
  styled as MUIstyled,
  Tooltip,
  tooltipClasses,
  Typography,
  type TooltipProps,
} from '@karoo-ui/core'
import styled from 'styled-components'
import type { Except } from 'type-fest'

import type { TerminalEventTypeCode } from '@fleet-web/api/types'
import { useVehiclesGroupedEventTypesQuery } from '@fleet-web/modules/api/useEventTypes'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from '@fleet-web/util-functions/security-utils'

import { mapActionIconToSvgString } from './action-icon-matcher'

const Marker = styled.div<{ table: boolean }>`
  height: auto;
  margin: auto;
  position: relative;
  transform: translateY(${(props) => (props.table ? '0px' : '-20px')});
  width: 30px;
  z-index: 10;

  svg {
    height: inherit;
  }
`

const MarkerActionCount = styled.div`
  position: absolute;
  right: -18px;
  top: -5px;
  width: 23px;
  height: 23px;
  border-radius: 5px;
  background-color: ${(props) => props.theme.colors.styleButtonDefaultColourActive};
  color: white;
  z-index: 10;
`

type TableSensorMarkerProps = {
  icon: TerminalEventTypeCode
  fallbackDescription: string
}

export function TableSensorMarker({
  icon,
  fallbackDescription = '',
}: TableSensorMarkerProps) {
  const vehiclesGroupedEventTypesQuery = useVehiclesGroupedEventTypesQuery()

  const isGroupAction =
    typeof fallbackDescription === 'string'
      ? fallbackDescription.split(';').length > 1
      : false

  const markerIcon = mapActionIconToSvgString(icon, {
    variant: 'square',
  })

  return (
    <div className="SensorMarker SensorMarker-table">
      <SensorMarkerInfoTooltip
        isGroupAction={isGroupAction}
        title={
          isGroupAction
            ? fallbackDescription
            : (vehiclesGroupedEventTypesQuery.data?.getEventLabel({
                eventType: icon,
                eventTypeDescription: fallbackDescription,
              }) ?? '')
        }
      >
        <span>
          {markerIcon ? (
            <Marker
              table
              {...makeSanitizedInnerHtmlProp({ dirtyHtml: markerIcon })}
            />
          ) : null}
          {isGroupAction && (
            <MarkerActionCount>
              {fallbackDescription.split(';').length}
            </MarkerActionCount>
          )}
        </span>
      </SensorMarkerInfoTooltip>
    </div>
  )
}

const SensorMarkerInfoTooltip = MUIstyled(
  ({
    isGroupAction,
    title,
    className,
    children,
    ...props
  }: Except<TooltipProps, 'arrow' | 'placement' | 'slotProps' | 'title'> & {
    title: string
    isGroupAction: boolean
  }) => (
    <Tooltip
      {...props}
      arrow={false}
      placement="right"
      title={
        isGroupAction ? (
          title
            .split(';')
            .map((message) => (
              <Typography key={message}>
                {ctIntl.formatMessage({ id: message })}
              </Typography>
            ))
        ) : (
          <Typography>{ctIntl.formatMessage({ id: title })}</Typography>
        )
      }
      classes={{ popper: className }}
      slotProps={{
        popper: {
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [7, -30],
              },
            },
          ],
        },
      }}
    >
      {children}
    </Tooltip>
  ),
)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.black,
    paddingRight: theme.spacing(1.5),
    paddingLeft: theme.spacing(1.5),
  },
}))
