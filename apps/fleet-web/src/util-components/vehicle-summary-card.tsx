import { match } from 'ts-pattern'

import { VehicleType } from '@fleet-web/api/types'
import type { VehicleSpeedSource } from '@fleet-web/api/vehicles/types'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import FormattedDistance from '@fleet-web/util-components/formatted-distance'
import VehicleIconCircle from '@fleet-web/util-components/vehicle-icon-circle'

import StarRating from './star-rating'

type Props = {
  vehicle: Record<string, FixMeAny> & { speedSource: VehicleSpeedSource }
  statusClassName: string | null
}

export const VehicleSummaryCard = ({ vehicle, statusClassName }: Props) => (
  <div className={`VehicleSummaryCard ${statusClassName || vehicle.statusClassName}`}>
    <VehicleIconCircle
      className={`VehicleSummaryCard-icon ${
        statusClassName || vehicle.statusClassName
      } ${vehicle.isWifi && 'VehicleSummaryCard-icon-wifi'}`}
      type={vehicle.isWifi ? VehicleType.WifiUnit : vehicle.type}
      iconColor={vehicle.iconColor}
      statusClassName={vehicle.statusClassName}
      carpoolStatus={vehicle.carpoolStatus}
    />
    <div className="VehicleSummaryCard-details">
      {vehicle.name}
      {vehicle.name !== vehicle.registration && vehicle.registration
        ? ` (${vehicle.registration})`
        : ''}
      <span className="VehicleSummaryCard-description">
        {[vehicle.make, vehicle.model]
          .filter((a) => a && a !== 'Validation removed')
          .join(' ')}
      </span>
      <span className="VehicleSummaryCard-description">
        {[vehicle.year, vehicle.color]
          .filter((a) => a && a !== 'Validation removed')
          .join(' ')}
      </span>
      {!!vehicle.speedSource && (
        <span className="VehicleSummaryCard-description">
          {`${ctIntl.formatMessage(
            { id: 'speedSource' },
            {
              values: {
                speedSource: match(vehicle.speedSource)
                  .with('CAN', () => ctIntl.formatMessage({ id: 'CAN' }))
                  .with('GPS', () => 'GPS')
                  .exhaustive(),
              },
            },
          )}: `}
          <FormattedDistance
            value={vehicle.speed}
            perTime
            round
          />
        </span>
      )}
      <StarRating
        rating={vehicle.rating}
        color={'rgb(243,188,97)'}
      />
    </div>
  </div>
)
