import { random, range, sortBy } from 'lodash'
import { arrayOf, bool, number, oneOfType, shape, string } from 'prop-types'

import ArrowedTooltip from '@fleet-web/components/_popups/Tooltip/Arrowed'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import FormattedUserDate from '@fleet-web/util-components/formatted-user-date'
import FormattedUserTime from '@fleet-web/util-components/formatted-user-time'
import { daysApart, TIME } from '@fleet-web/util-functions/time-utils'

const Ticks = ({ width }) => (
  <svg
    width={width}
    xmlns="http://www.w3.org/2000/svg"
    className="TimelineBar-ticks"
  >
    <line
      x1="0%"
      y1="0"
      x2="0%"
      y2="100%"
      strokeWidth="1"
    />
    <line
      x1="25%"
      y1="10%"
      x2="25%"
      y2="90%"
      strokeWidth="1"
    />
    <line
      x1="50%"
      y1="0"
      x2="50%"
      y2="100%"
      strokeWidth="1"
    />
    <line
      x1="75%"
      y1="10%"
      x2="75%"
      y2="90%"
      strokeWidth="1"
    />
    <line
      x1="100%"
      y1="0"
      x2="100%"
      y2="100%"
      strokeWidth="1"
    />
  </svg>
)

Ticks.propTypes = {
  width: oneOfType([number, string]).isRequired,
}

const getRandomPositionsFromArray = (limit, length) => {
  let taken = [] // eslint-disable-line prefer-const
  let hasSpace = 2

  while (hasSpace < limit) {
    const x = random(1, length - 1)

    if (!taken.includes(x)) {
      taken.push(x)
      hasSpace++
    }
  }

  return [0, ...sortBy(taken), length - 1]
}

const OldTimelineBar = ({
  events,
  className,
  width: customWidth,
  height,
  ticks,
  dateRangeWithServerTZ,
  days,
  times,
  numberOfTimes,
  limitDaysRange,
  eventTooltip,
}) => {
  const width = typeof customWidth === 'string' ? customWidth : customWidth + 'px'
  let dayArr
  let timeArr

  if (dateRangeWithServerTZ) {
    const startTime = new Date(dateRangeWithServerTZ.from).getTime()
    const endTime = new Date(dateRangeWithServerTZ.to).getTime()
    const interval = (endTime - startTime) / (numberOfTimes - 1)

    if (days) {
      const totalDays =
        daysApart(dateRangeWithServerTZ.from, dateRangeWithServerTZ.to) + 1
      const rangeArray = range(totalDays)
      const positions = getRandomPositionsFromArray(
        rangeArray.length - 1,
        limitDaysRange,
      )

      dayArr = rangeArray.map((d) => {
        if (limitDaysRange && rangeArray.length > limitDaysRange) {
          if (positions.includes(d)) {
            return (
              <FormattedUserDate
                key={d}
                value={startTime + d * TIME.HOURS_24}
                month="numeric"
                day="numeric"
                ignoreTZ
              />
            )
          }

          return null
        }

        return (
          <FormattedUserDate
            key={d}
            value={startTime + d * TIME.HOURS_24}
            month="numeric"
            day="numeric"
            ignoreTZ
          />
        )
      })

      if (totalDays === 1)
        dayArr.unshift(
          <FormattedUserDate
            key={'first'}
            value={startTime}
            month="numeric"
            day="numeric"
            ignoreTZ
          />,
        )
    }

    if (times) {
      timeArr = range(numberOfTimes).map((t) => (
        <FormattedUserTime
          key={t}
          value={startTime + t * interval}
          ignoreTZ
        />
      ))
    }
  }

  const renderFilledSection = (event, key) => {
    const left = Math.ceil((event.pctLeftAbsolute || event.pctLeft) * 2) / 2 + '%'
    const width = Math.ceil((event.pctWidthAbsolute || event.pctWidth) * 2) / 2 + '%'

    const eventClassName =
      event.event ||
      event.statusClass ||
      (event.altClassName === 'pc' || event.altClassName === 'ym'
        ? event.altClassName
        : event.statusClassName)

    return (
      <div
        className={`TimelineBar-section ${eventClassName || 'driving'}`}
        key={key}
        style={{
          left,
          width,
          height: height ? height - 2 : undefined,
          backgroundColor: event.backgroundColor,
        }}
      />
    )
  }

  return (
    <div
      className={'TimelineBar ' + className}
      style={{ width, height: height + 'px' }}
    >
      {ticks && <Ticks width={width} />}
      {dayArr && <div className="TimelineBar-days row">{dayArr}</div>}
      <div
        className="TimelineBar-bar"
        style={{ height: height - 8 + 'px' }}
      >
        {events.map((event) =>
          eventTooltip ? (
            <ArrowedTooltip
              // eslint-disable-next-line sonarjs/no-uniq-key
              key={(event.id || event.pctLeft) + Math.random()}
              placement="bottom"
              label={ctIntl.formatMessage({ id: event.event })}
            >
              {renderFilledSection(event)}
            </ArrowedTooltip>
          ) : (
            renderFilledSection(event, (event.id || event.pctLeft) + Math.random())
          ),
        )}
      </div>
      {timeArr && <div className="TimelineBar-times row">{timeArr}</div>}
    </div>
  )
}

OldTimelineBar.propTypes = {
  events: arrayOf(
    shape({
      id: string,
      pctLeft: number.isRequired,
      pctWidth: number.isRequired,
    }),
  ),
  width: oneOfType([string, number]),
  height: number,
  className: string,
  ticks: bool,
  dateRangeWithServerTZ: shape({}),
  days: bool,
  times: bool,
  numberOfTimes: number,
  limitDaysRange: number,
  eventTooltip: bool,
}

OldTimelineBar.defaultProps = {
  width: '100%',
  events: [],
  className: '',
  ticks: true,
  height: 14,
  dateRangeWithServerTZ: null,
  days: false,
  times: false,
  numberOfTimes: 8,
  limitDaysRange: 5,
  eventTooltip: false,
}

export default OldTimelineBar
