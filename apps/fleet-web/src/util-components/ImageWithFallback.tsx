import { useState } from 'react'
import { LRUCache } from '@karoo/utils'
import type { Except } from 'type-fest'

import useEffectExceptOnMount from '@fleet-web/hooks/useEffectExceptOnMount'
import { isNilOrTrimmedEmptyString } from '@fleet-web/util-functions/string-utils'

export type ImageWithFallbackProps = {
  fallbackSrc: string
  src: string | null | undefined
} & Except<
  React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>,
  'src'
>

const erroredImgSrcCache = new LRUCache<string, string>(100)

export const ImageWithFallback = ({
  fallbackSrc,
  src: srcProp,
  ...imgProps
}: ImageWithFallbackProps) => {
  const src =
    isNilOrTrimmedEmptyString(srcProp) || erroredImgSrcCache.has(srcProp)
      ? fallbackSrc
      : srcProp
  const [currentSrc, setCurrentSrc] = useState(src)

  useEffectExceptOnMount(() => {
    setCurrentSrc(src)
  }, [src])

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const imgEl = e.target as HTMLImageElement

    if (imgEl.src === fallbackSrc) {
      // fallback also failed to fetch, do nothing
      return
    }
    erroredImgSrcCache.set(currentSrc, currentSrc)
    setCurrentSrc(fallbackSrc)
  }

  return (
    <img
      src={currentSrc}
      onError={handleError}
      {...imgProps}
    />
  )
}
