import type { ComponentProps } from 'react'
import { isNil } from 'lodash'
import type { MomentInput } from 'moment-timezone'
import { FormattedTime } from 'react-intl'
import { connect } from 'react-redux'
import type { Except } from 'type-fest'

import { getFormattedTimeWithTZ, getIsTime24 } from '@fleet-web/duxs/user'
import { getUserTimeZone } from '@fleet-web/duxs/user-sensitive-selectors'
import type { AppState } from '@fleet-web/root-reducer'

type Props = {
  timeZone?: string
  extraClassName?: string
  /**  determines if timezone is handled in backend or frontend. In the future we want FormattedTime to be removed so that timezones are handled exclusively in the backend.*/
  ignoreTZ?: boolean
  // dateTime value in milliseconds, valid dateTime string or JS Date object
  value: string | number | Date
  tzOffset?: number
} & ReturnType<typeof mapStateToProps> &
  Except<ComponentProps<typeof FormattedTime>, 'timeZone' | 'hour12' | 'value'>

const FormattedUserTime = ({
  userTimeZone,
  timeZone,
  isTime24 = true,
  extraClassName = '',
  ignoreTZ = false,
  value,
  doGetFormattedTimeWithTZ,
  tzOffset,
  ...rest
}: Props) => (
  <span className={'util-uppercase ' + extraClassName}>
    {ignoreTZ ? (
      doGetFormattedTimeWithTZ(value, tzOffset)
    ) : (
      <FormattedTime
        timeZone={isNil(timeZone) ? userTimeZone.ianaName : timeZone}
        hour12={!isTime24}
        value={value}
        {...rest}
      />
    )}
  </span>
)

const mapStateToProps = (state: AppState) => ({
  userTimeZone: getUserTimeZone(state),
  isTime24: getIsTime24(state),
  doGetFormattedTimeWithTZ: (dateTime: MomentInput, tzOffset?: number) =>
    getFormattedTimeWithTZ(state, dateTime, tzOffset),
})

export default connect(mapStateToProps)(FormattedUserTime)
