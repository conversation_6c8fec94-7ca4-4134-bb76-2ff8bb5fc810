import { isValidElement, memo, type ReactNode } from 'react'
import { get, isEmpty, isNil, toString } from 'lodash'
import { connect } from 'react-redux'

import Icon from '@fleet-web/components/Icon'
import { getTooltip, setTooltip, type TooltipDirection } from '@fleet-web/duxs/tooltip'
import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import ClassWrapper from './class-wrapper'

const PAD = 20

export type WithTooltipProps = {
  // Props
  hideTooltip?: boolean
  tooltipMessage?: ReactNode | string
  tooltipDirection?: TooltipDirection
}

export type WithTooltipOptions = {
  messageTransform?: (...args: Array<FixMeAny>) => FixMeAny
  direction?: TooltipDirection
  hideByDefault?: boolean
  withWrapper?: boolean
}
/** @deprecated Use ArrowedTooltip instead. Check out storybook for some examples  */
export const withTooltip =
  (
    messageOrAccessor: string | JSX.Element | null,
    {
      messageTransform,
      direction: defaultDirection,
      hideByDefault = false,
      withWrapper = true,
    }: WithTooltipOptions = {},
  ) =>
  (WrappedComponent: FixMeAny) => {
    function WithTooltip({
      // Props
      hideTooltip = false,
      tooltipMessage = '',
      tooltipDirection,
      // Actions
      setTooltip,
      ...wrappedComponentProps
    }: WithTooltipProps & typeof actionCreators) {
      const isComponent = isValidElement(messageOrAccessor)
      let elementWithTooltip: HTMLElement

      const getRef = (elementWithTooltipRef: HTMLElement) => {
        // eslint-disable-next-line react-hooks/react-compiler
        elementWithTooltip = elementWithTooltipRef
      }

      const getMessageGeneratorWithResolvedMessage = () => {
        if (hideTooltip || hideByDefault) {
          return
        }

        if (isComponent) {
          return () => messageOrAccessor
        }

        let message: string | React.ReactNode = messageOrAccessor // Fallback

        const valueFromAccessor = isComponent
          ? undefined
          : get(wrappedComponentProps, messageOrAccessor as string)

        if (!isEmpty(tooltipMessage)) {
          message = tooltipMessage
        } else if (!isNil(valueFromAccessor)) {
          message = ctIntl.formatMessage({ id: valueFromAccessor })
        }

        if (messageTransform) {
          message = messageTransform(message)
        }

        return () => (isValidElement(message) ? message : toString(message))
      }

      const handleMouseEnter = () => {
        if (
          !elementWithTooltip || // No ref yet
          hideTooltip ||
          hideByDefault
        ) {
          return
        }

        // Make sure we should show
        const messageGenerator = getMessageGeneratorWithResolvedMessage()
        if (!isNil(messageGenerator) && isEmpty(messageGenerator())) {
          return
        }

        // Get location and dispatch
        const location = elementWithTooltip.getBoundingClientRect()
        setTooltip(
          location,
          messageGenerator,
          tooltipDirection || defaultDirection,
          withWrapper,
        )
      }

      const handleMouseLeave = () => {
        if (!isComponent) {
          setTooltip(null, undefined, undefined, undefined)
        }
      }

      return (
        <ClassWrapper
          getRef={getRef}
          eventListeners={{
            onmouseenter: handleMouseEnter,
            onmouseover: handleMouseEnter,
            onmouseleave: handleMouseLeave,
            onmouseout: handleMouseLeave,
            onclick: handleMouseLeave, // Prevents lingering tooltip if navigating away
          }}
        >
          <WrappedComponent {...wrappedComponentProps} />
        </ClassWrapper>
      )
    }

    const actionCreators = {
      // API
      setTooltip,
    }

    return memo(connect(null, actionCreators)(WithTooltip))
  }

type TooltipProps = ReturnType<typeof mapStateToProps>

// Constants
const tMaxWidth = 100
const tMaxHeight = 40
const halfTMaxWidth = tMaxWidth / 2

/** @deprecated Use ArrowedTooltip instead. Check out storybook for some examples  */
const Tooltip = ({ tooltip }: TooltipProps) => {
  if (isEmpty(tooltip) || !tooltip) {
    return null
  }

  // Get wrapped element dimensions
  const {
    location: { left: l, right: r, top: t, bottom: b, width: cWidth, height: cHeight },
    direction: directionWithPriority,
    messageGenerator,
    withWrapper,
  } = tooltip
  const halfCWidth = cWidth / 2
  const halfCHeight = cHeight / 2

  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  // Check if there is space to the sides for up/down placement
  const hasSpaceToSides =
    r - halfCWidth + halfTMaxWidth + PAD <= windowWidth &&
    l + halfCWidth - halfTMaxWidth - PAD >= 0

  // Check if there is space above and below
  const canDown = hasSpaceToSides && b + tMaxHeight + PAD <= windowHeight
  const canUp = hasSpaceToSides && t - tMaxHeight - PAD >= 0

  // Check if there is space to place tooltip on the sides
  const canRight = r + tMaxWidth + PAD <= windowWidth
  const canLeft = l - tMaxWidth - PAD >= 0

  let direction

  // Set direction with priority if possible
  if (directionWithPriority) {
    if (directionWithPriority === 'up' && canUp) {
      direction = 'up'
    } else if (directionWithPriority === 'down' && canDown) {
      direction = 'down'
    } else if (directionWithPriority === 'right' && canRight) {
      direction = 'right'
    } else if (directionWithPriority === 'left' && canLeft) {
      direction = 'left'
    }
  }

  if (!direction) {
    if (canDown) {
      direction = 'down'
    } else if (canUp) {
      direction = 'up'
    } else if (canRight) {
      direction = 'right'
    } else if (canLeft) {
      direction = 'left'
    } else {
      direction = 'down'
    }
  }

  let top
  let left
  switch (direction) {
    case 'up': {
      top = t - PAD
      left = l + halfCWidth
      break
    }
    case 'right': {
      top = t + halfCHeight
      left = r + PAD
      break
    }
    case 'left': {
      top = t + halfCHeight
      left = l - PAD
      break
    }
    default: {
      top = b + PAD
      left = l + halfCWidth
      break
    }
  }

  const tooltipMessage = messageGenerator()
  const renderedTooltipMessage = isValidElement(tooltipMessage)
    ? tooltipMessage
    : ctIntl.formatMessage({ id: tooltipMessage })

  return (
    <div
      style={withWrapper ? { top: top - 10, left } : { top: top - 25, left }}
      className={
        withWrapper
          ? `Tooltip ${tooltip ? 'Tooltip--' + direction : 'is-hidden'}`
          : `Tooltip-customize ${tooltip ? 'Tooltip--' + direction : 'is-hidden'}`
      }
    >
      {renderedTooltipMessage}
      <Icon
        icon="caret-up"
        className={
          withWrapper
            ? `Tooltip-arrow Tooltip-arrow--${direction} `
            : `Tooltip-customize-arrow Tooltip-arrow--${direction}`
        }
      />
    </div>
  )
}

const mapStateToProps = (state: AppState) => ({
  tooltip: getTooltip(state),
})

export default connect(mapStateToProps)(Tooltip)
