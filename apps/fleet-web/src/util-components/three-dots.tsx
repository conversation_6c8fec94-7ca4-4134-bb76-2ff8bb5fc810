import { useRef, useState } from 'react'
import type { IconName } from '@fortawesome/fontawesome-svg-core'
import styled from 'styled-components'

import MoreVerticalIconButton from '@fleet-web/components/_buttons/MoreIconButton/MoreVerticalIconButton'
import Icon from '@fleet-web/components/Icon'
import { useOnClickOutside } from '@fleet-web/hooks'
import { variables } from '@fleet-web/shared/components/styled/global-styles'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

export type Option = {
  key: string
  icon: IconName
  onClick: () => void
  tooltipMessage: string
}

type Props = {
  options: Array<Option>
  className?: string
}

const ThreeDots = ({ options, className, ...rest }: Props) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const ref = useRef<HTMLDivElement>(null)
  useOnClickOutside(ref, () => {
    setIsExpanded(false)
  })

  const handleExpand = () => {
    if (!isExpanded) {
      setIsExpanded(true)
    }
  }

  const handleClickOption = (option: Option) => {
    setIsExpanded(false)
    option.onClick()
  }

  return (
    <Container
      {...rest}
      className={className}
      onClick={handleExpand}
    >
      <MoreVerticalIconButton />
      {isExpanded && (
        <OptionsContainer ref={ref as FixMeAny}>
          {options.map((option: Option) => (
            <OptionItem
              key={option.key}
              onClick={() => handleClickOption(option)}
            >
              <StyledOptionIcon icon={option.icon} />
              <OptionLabel>
                {ctIntl.formatMessage({ id: option.tooltipMessage })}
              </OptionLabel>
            </OptionItem>
          ))}
        </OptionsContainer>
      )}
    </Container>
  )
}

export default ThreeDots

const Container = styled.div`
  position: relative;
  cursor: pointer;
`

const StyledOptionIcon = styled(Icon)`
  transition: all 0.3s ease-in-out;
  font-size: 12px;
  color: #333333;
`

const OptionLabel = styled.span`
  font-size: 12px;
  margin-left: 8px;
  font-family: Roboto, sans-serif;
  color: #666666;
`

const OptionsContainer = styled.div`
  position: absolute;
  top: 10px;
  right: 15px;
  width: 190px;
  z-index: 15;
  background-color: ${variables.white};
  border-radius: 4px;
  border: 1px solid #cecece;
`

const OptionItem = styled.div`
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 28px;
  border-bottom: 1px solid #cecece;
  padding: 0 10px 0 20px;
  transition: background-color 0.3s ease-in-out;

  &:last-child {
    border-bottom: 0;
  }

  &:hover {
    background-color: #eeeeee;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 95%;
    border-right: 1px solid ${variables.white};
  }

  &:first-child {
    &::after {
      border-right: 0;
    }
  }
`
