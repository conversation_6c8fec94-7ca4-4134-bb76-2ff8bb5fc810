import { But<PERSON>, Stack, Typography } from '@karoo-ui/core'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import { Link, useHistory } from 'react-router-dom'

import { VehicleType, type VehicleId } from '@fleet-web/api/types'
import { getListVehiclesPermission } from '@fleet-web/duxs/user'
import DriverWithChip from '@fleet-web/modules/app/components/driverChip/DriverWithChip'
import { getVehicleDetailsModalMainPath } from '@fleet-web/modules/app/GlobalModals/VehicleDetails/utils'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import { GA4 } from '@fleet-web/shared/google-analytics4'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'
import StarRating from '@fleet-web/util-components/star-rating'

import ReVehicleIconCircle from './re-vehicle-icon-circle'

type Props = {
  vehicle: Record<string, FixMeAny> & { id: VehicleId }
  btnElement?: React.ReactNode
}

const ReVehicleSummaryCard = ({ vehicle }: Props) => {
  const history = useHistory()
  const listVehiclesPermission = useTypedSelector(getListVehiclesPermission)

  return (
    <Stack
      direction="row"
      alignItems="center"
      gap={2}
    >
      <ReVehicleIconCircle
        statusClassName={vehicle.statusClassName}
        description={ctIntl.formatMessage({ id: vehicle.description })}
        type={vehicle.isWifi ? VehicleType.WifiUnit : vehicle.type}
        carpoolStatus={vehicle.carpoolStatus}
      />
      <Stack gap={0.5}>
        <Typography
          variant="subtitle2"
          style={{ wordBreak: 'break-word' }}
        >
          {vehicle.name}
          {vehicle.name !== vehicle.registration && vehicle.registration
            ? ` (${vehicle.registration})`
            : ''}
        </Typography>
        <StarRating
          rating={vehicle.rating}
          color={'rgb(243,188,97)'}
        />
        <Typography variant="caption">
          {`${[vehicle.make, vehicle.model]
            .filter((a) => a && a !== 'Validation removed')
            .join(' ')} ${[vehicle.year, vehicle.color]
            .filter((a) => a && a !== 'Validation removed')
            .join(' ')}`}
        </Typography>
        <Stack gap={0.5}>
          <DriverWithChip
            driverName={{
              name: vehicle.driverName.name,
              status: vehicle.driverName.status,
            }}
            linkingMethod={{ type: vehicle.driverName.linkage_type_enum }}
            picture={vehicle.driverName.logo_image_base64}
            driverId={vehicle.driverName.client_driver_id}
          />
          <Link
            to={getVehicleDetailsModalMainPath(history.location, vehicle.id)}
            onClick={(e) => {
              GA4.event({
                category: 'Vehicle',
                action: 'Vehicle Details - Vehicle Card Click',
              })

              if (!listVehiclesPermission) {
                e.preventDefault()
              }
            }}
          >
            <Button
              size="small"
              endIcon={<ChevronRightIcon />}
              sx={{ p: 0 }}
            >
              {ctIntl.formatMessage({ id: 'Go to profile' })}
            </Button>
          </Link>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default ReVehicleSummaryCard
