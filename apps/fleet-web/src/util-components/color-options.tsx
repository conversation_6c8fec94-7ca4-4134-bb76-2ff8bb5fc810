import { FormattedMessage } from 'react-intl'

import { colors } from '@fleet-web/api/colors'
import { capitalize } from '@fleet-web/util-functions/string-utils'

export default colors.map((color) => ({
  name: capitalize(color.label),
  value: color.hex,
  Component: () => (
    <div key={color.hex}>
      <div
        className="util-inlineCircle"
        style={{
          backgroundColor: color.hex,
        }}
      />
      <FormattedMessage
        id={capitalize(color.label)}
        defaultMessage={capitalize(color.label)}
      />
    </div>
  ),
}))
