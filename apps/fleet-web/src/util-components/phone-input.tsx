import { useState } from 'react'
import type * as React from 'react'
import { startCase } from 'lodash'
import type { TextFieldProps } from '@karoo-ui/core'
import classnames from 'classnames'
import Select, {
  components,
  type IndicatorProps,
  type OptionProps,
  type Styles as ReactSelectStyles,
  type SingleValueProps,
} from 'react-select'
import styled from 'styled-components'
import type { Except } from 'type-fest'

import Icon from '@fleet-web/components/Icon'
import { getInputFieldColors, getISO3166Alpha2CountryCode } from '@fleet-web/duxs/user'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import useCountryCodes from '@fleet-web/shared/react-hook-form/PhoneNumberInput/useCountryCodes'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import TextInput from './text-input'

type Props = {
  value: string | undefined
  onChange: (
    value: string | undefined,
    countryCodePrefix: `+${string}` | undefined,
    country: PhoneInputCountryPhoneCode,
  ) => void
  hideCountry?: boolean
  defaultCountryCode: string | null
  meta: { error?: any; touched?: boolean | boolean[]; valid?: boolean }
} & Except<TextFieldProps, 'onChange'>

export type PhoneInputCountryPhoneCode = ReturnType<
  typeof useCountryCodes
>['allCountries'][number]

const PhoneInput = (props: Props) => {
  const inputFieldColors = useTypedSelector(getInputFieldColors)
  const defaultCountryCode = useTypedSelector(getISO3166Alpha2CountryCode)
  const buildCountryCode = useCountryCodes()

  const [isFocus, setIsFocus] = useState<boolean>(false)
  const [phoneNumber, setPhoneNumber] = useState<{
    countryCode: string
    number: string | undefined
  }>({ countryCode: defaultCountryCode, number: undefined })

  const handleInputChange = (value: string | undefined) => {
    setPhoneNumber((prev) => ({ ...prev, number: value }))
    const country = buildCountryCode.byCountryCode[phoneNumber.countryCode]
    if (props.onChange) {
      props.onChange(`+${country.prefix}${value ?? ''}`, `+${country.prefix}`, country)
    }
  }

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocus(true)
    if (props.onFocus) {
      props.onFocus(event)
    }
  }

  const handleOnBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocus(false)

    if (props.onBlur) {
      props.onBlur(event)
    }
  }

  /**
   * event for selection of the country code
   */
  const handleOnCodeChange = (res: PhoneInputCountryPhoneCode) => {
    setPhoneNumber((prev) => ({ ...prev, countryCode: res.value }))
    const country = buildCountryCode.byCountryCode[phoneNumber.countryCode]

    if (props.onChange) {
      props.onChange(
        `+${res.prefix}${phoneNumber.number ?? ''}`,
        `+${country.prefix}`,

        country,
      )
    }
  }

  const style: ReactSelectStyles = {
    container: (base) => ({ ...base, borderRight: '1px solid #ddd' }),
    input: (base) => ({
      ...base,
      border: 'none',
      borderRadius: 0,
    }),
    control: (base) => ({
      ...base,
      border: 'none',
      borderRadius: 0,
      outline: 'none',
      borderColor: 'transparent',
      boxShadow: 'none',
      '&:hover': {},
      '&:isFocused': {
        outline: 'none',
      },
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    singleValue: (base) => ({
      ...base,
      display: 'flex',
      alignItems: 'center',
    }),
    menu: (base: any) => ({
      ...base,
      textAlign: 'left',
      width: '287px',
    }),
    menuList: (base) => ({
      ...base,
      maxHeight: '160px',
    }),
    option: (base, state) => ({
      ...base,
      display: 'flex',
      alignItems: 'center',
      color: state.isSelected && 'black',
      backgroundColor: state.isFocused && inputFieldColors.styleInputfieldColourHover,
    }),
  }

  const { hideCountry } = props

  return (
    <CountrySelectContainer
      isFocus={isFocus || Boolean(phoneNumber.number)}
      hideCountry={Boolean(hideCountry)}
      className={classnames({
        'is-notvalid': !props.meta.valid,
      })}
    >
      {!hideCountry && (
        <Select
          value={
            buildCountryCode.byCountryCode[
              phoneNumber.countryCode
            ] as PhoneInputCountryPhoneCode
          }
          options={buildCountryCode.allCountries as Array<PhoneInputCountryPhoneCode>}
          styles={style}
          onChange={(value) => {
            handleOnCodeChange(value as PhoneInputCountryPhoneCode)
          }}
          isDisabled={props.disabled}
          components={{ DropdownIndicator, Option, SingleValue }}
        />
      )}
      <TextInput
        required={props.required}
        value={phoneNumber.number}
        onFocus={handleFocus}
        onBlur={handleOnBlur}
        onChange={(e) => {
          const value = e.target.value

          if (value.length === 0) {
            handleInputChange(undefined)
          } else if (/^\d+$/.test(e.target.value)) {
            handleInputChange(value)
          }
        }}
        placeholder={ctIntl.formatMessage({ id: startCase(props.name) })}
      />

      {props.meta.error && (
        <span className="error">{ctIntl.formatMessage({ id: props.meta.error })}</span>
      )}
    </CountrySelectContainer>
  )
}

export default PhoneInput

const CountrySelectContainer = styled.div<{ isFocus: boolean; hideCountry: boolean }>`
  height: 100%;
  display: grid;

  grid-template-columns: ${({ hideCountry }) =>
    hideCountry ? '100%' : '84px calc(100% - 84px)'};
  border: 1px solid #ddd;
  border-radius: 3px;
  position: relative;
  box-sizing: border-box;

  &.is-notvalid {
    border: 1px solid #ce5239;
    box-shadow: 0 1px 5px -3px rgba(0, 0, 0, 0.5);
  }

  .TextInput {
    position: relative;
    box-sizing: border-box;
    border: none;

    &-input {
      border: none;
      outline: none;
      font-size: 1rem;
      padding: ${({ isFocus }) => (isFocus ? '10px' : '0px')} 10px 0 10px;
      min-height: 38px;
      box-shadow: none !important;
      border-radius: ${({ hideCountry }) => (hideCountry ? '4px' : '0 !important')};
      width: 100%;
      height: 100%;

      &:hover {
      }
    }

    .is-required {
      color: #ce5239;
      position: absolute;
      line-height: 18px;
      font-size: 12px;
      left: 4px;
    }
  }

  .error {
    position: absolute;
    font-size: 10px;
    text-transform: uppercase;
    color: #ce5239;
    bottom: -15px;
    left: 10px;
  }

  img {
    height: 15px;
    display: block;
    width: 100%;
  }
`

const PhoneInputCountryIcon = styled.div`
  border: 1px solid #cccccc;
  width: calc(1em * 1.5);
  border-radius: 1px;
`

const DropdownIndicator = (props: IndicatorProps<PhoneInputCountryPhoneCode>) => (
  <components.DropdownIndicator {...props}>
    <Icon
      icon="caret-down"
      className={`InputDropdown-custom-arrow`}
    />
  </components.DropdownIndicator>
)

const SingleValue = (props: SingleValueProps<PhoneInputCountryPhoneCode>) => (
  <components.SingleValue {...props}>
    {'+' + props.data.prefix.replace(/[()]/g, '')}
  </components.SingleValue>
)

const imgStyleOption = {
  marginRight: '8px',
}

const Option = (
  props: OptionProps<PhoneInputCountryPhoneCode> & Record<string, FixMeAny>,
) => {
  const flagUrl =
    // eslint-disable-next-line sonarjs/no-clear-text-protocols
    'http://catamphetamine.gitlab.io/country-flag-icons/3x2/' + props.value + '.svg'
  return (
    <components.Option {...props}>
      <PhoneInputCountryIcon style={imgStyleOption}>
        <img
          src={flagUrl}
          alt={props.label}
        />
      </PhoneInputCountryIcon>

      {props.label}
    </components.Option>
  )
}
