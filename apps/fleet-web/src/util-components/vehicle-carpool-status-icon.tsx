import { memo } from 'react'
import AccessTimeFilled from '@mui/icons-material/AccessTimeFilled'
import CheckCircle from '@mui/icons-material/CheckCircle'
import ConstructionIcon from '@mui/icons-material/Construction'
import LoopOutlined from '@mui/icons-material/LoopOutlined'
import RemoveCircle from '@mui/icons-material/RemoveCircle'
import { match, P } from 'ts-pattern'

import type { FetchVehicles } from '@fleet-web/api/vehicles/types'
import { BookingStatusMapping } from '@fleet-web/modules/carpool/utils/constants'

type Props = {
  statusClassName: FetchVehicles.Vehicle['statusClassName'] | undefined
  carpoolStatus: FetchVehicles.Vehicle['carpool_status'] | undefined
}

const VehicleCarpoolStatusIcon = ({ statusClassName, carpoolStatus }: Props) => {
  const getCarpoolStatusIcon = () => {
    if (statusClassName === 'maintenance') {
      return <ConstructionIcon sx={{ fontSize: 16, color: '#f44336' }} />
    }

    return match(carpoolStatus)
      .with(null, () => null)
      .with(undefined, () => null)
      .with(P.union(...BookingStatusMapping.free), () => (
        <RemoveCircle sx={{ fontSize: 16, color: '#BDBDBD' }} />
      ))
      .with(P.union(...BookingStatusMapping.requested), () => (
        <AccessTimeFilled sx={{ fontSize: 16, color: '#F57C00' }} />
      ))
      .with(P.union(...BookingStatusMapping.active), () => (
        <LoopOutlined sx={{ fontSize: 16, color: '#2196F3' }} />
      ))
      .with(P.union(...BookingStatusMapping.issued), () => (
        <CheckCircle sx={{ fontSize: 16, color: '#ACAF50' }} />
      ))
      .otherwise(() => null)
  }

  return <>{getCarpoolStatusIcon()}</>
}

export default memo(VehicleCarpoolStatusIcon)
