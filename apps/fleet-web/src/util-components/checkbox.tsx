import type * as React from 'react'
import cx from 'classnames'

import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type OnChange = (
  checked: boolean,
  name: string,
  event:
    | React.ChangeEvent<HTMLInputElement>
    | React.MouseEvent<HTMLLabelElement, MouseEvent>,
) => void

type Props = {
  value?: FixMeAny
  name?: string
  label?: string
  id?: string
  placeholder?: string
  className?: string
  extraClassNames?: {
    labelClassNames?: string
    inputClassNames?: string
    containerClassNames?: string
  }
  input?: {
    value?: FixMeAny
    onChange?: OnChange
    name?: string
  }
  onChange?: OnChange
  disabled?: boolean
  small?: boolean
  indeterminate?: boolean
}

function Checkbox({
  // Same as containerClassNames. Makes using styled components easier and cleaner
  className = '',
  extraClassNames,
  label = '',
  id = '',
  name = '',
  value = '',
  placeholder = '',
  input = {},
  onChange,
  disabled = false,
  small = false,
  indeterminate = false,
}: Props) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const valueOnChange = input.onChange || onChange
    if (valueOnChange) {
      valueOnChange(e.currentTarget.checked, name, e)
    }
  }

  const handleLabelClick = (e: React.MouseEvent<HTMLLabelElement, MouseEvent>) => {
    e.preventDefault()
    const valueOnChange = input.onChange || onChange
    if (!valueOnChange) return

    const inputValue = input.name ? input.value : value
    valueOnChange(!inputValue, name, e)
  }

  const checked = input.value === undefined ? value : input.value

  const {
    containerClassNames = '',
    inputClassNames = '',
    labelClassNames = '',
  } = extraClassNames ?? {}

  return (
    <div
      className={cx({
        Checkbox: true,
        [className]: className,
        [containerClassNames]: containerClassNames,
        'Checkbox--small': small,
        'is-indeterminate': indeterminate,
      })}
    >
      {placeholder && (
        <span
          className="Checkbox-placeholder"
          onClick={handleLabelClick}
        >
          {placeholder}
        </span>
      )}
      <input
        name={name}
        type="checkbox"
        value={value}
        id={id}
        checked={indeterminate || checked}
        className={`Checkbox-input ${inputClassNames}`}
        onChange={handleChange}
        disabled={disabled}
      />
      <label
        htmlFor={name}
        className={`Checkbox-box`}
        onClick={handleLabelClick}
      />
      {label ? (
        <span
          className={`Checkbox-label ${labelClassNames}`}
          onClick={handleLabelClick}
        >
          {ctIntl.formatMessage({ id: label })}
        </span>
      ) : null}
    </div>
  )
}

export default Checkbox
