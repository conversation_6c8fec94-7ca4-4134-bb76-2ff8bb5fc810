import { Component } from 'react'
import { bool, func, shape, string } from 'prop-types'

import CloseEye from '@fleet-web/components/Icon/Eye/Close'
import OpenEye from '@fleet-web/components/Icon/Eye/Open'

import TextInput from './text-input'

class PasswordInput extends Component {
  constructor(props) {
    super(props)
    this.state = {
      hasFocused: false,
      showingPassword: false,
    }
  }

  handleFocus = () => {
    const onChange = this.props.input.onChange || this.props.onChange
    const value = this.props.input.value || this.props.value
    if (!value && !this.state.hasFocused) {
      this.setState({ hasFocused: true })
      onChange('', this.props.id)
    }
  }

  handleIconClick = () => {
    this.setState({ showingPassword: !this.state.showingPassword })
  }

  render() {
    const value = this.props.input.value || this.props.value
    return (
      <TextInput
        {...this.props}
        type={this.state.showingPassword ? 'text' : 'password'}
        input={{
          ...this.props.input,
          value,
          onFocus: this.handleFocus,
        }}
        {...(this.props.enableShowingPassword && {
          icon:
            // eslint-disable-next-line no-nested-ternary
            this.props.disabled ? undefined : this.state.showingPassword ? (
              <CloseEye />
            ) : (
              <OpenEye />
            ),
          onIconClick: this.handleIconClick,
        })}
      />
    )
  }
}

PasswordInput.propTypes = {
  autoComplete: string,
  input: shape({}),
  onChange: func,
  value: string,
  disabled: bool,
  id: string,
  enableShowingPassword: bool,
}

PasswordInput.defaultProps = {
  autoComplete: null,
  input: {},
  onChange: null,
  value: '',
  disabled: false,
  id: null,
  enableShowingPassword: true,
}

export default PasswordInput
