import { isValidElement, useState, type ComponentProps } from 'react'
import type * as React from 'react'
import { isFunction, isNil } from 'lodash'
import classNames from 'classnames'
import styled from 'styled-components'

import Icon from '@fleet-web/components/Icon'
import type { FixMeAny } from '@fleet-web/types'

import { ctIntl } from './ctIntl'

type Props = {
  // Configuration
  type?: string
  placeholder?: React.ReactElement | (() => FixMeAny) | string | null
  placeholderExtra?: string | null
  iconName?: ComponentProps<typeof Icon>['icon']
  icon?: React.ReactElement
  onIconClick?: (...args: Array<FixMeAny>) => void
  className?: string
  extraClassNames?: {
    containerClassNames?: string
    inputClassNames?: string
    placeholderClassNames?: string
    errorClassNames?: string
    iconClassNames?: string
  }
  focus?: boolean
  forceOriginalValue?: boolean
  maxLength?: number
  hint?: string
  isOpen?: boolean
  disabled?: boolean
  required?: boolean
  readOnly?: boolean
  id?: string
  simpleClassName?: string
  name?: string
  defaultValue?: FixMeAny
  autoComplete?: string
  // Input
  input?: {
    value?: FixMeAny
    onChange?: (...args: Array<FixMeAny>) => void
  }
  value?: FixMeAny
  min?: string | number
  pattern?: string
  onInput?: (...args: Array<FixMeAny>) => void
  textArea?: boolean
  large?: boolean
  getRef?: (...args: Array<FixMeAny>) => void
  inputStyle?: React.CSSProperties
  // HOC
  meta?: {
    valid?: FixMeAny
    touched?: boolean | Array<boolean> | Record<string, boolean>
    error?: FixMeAny
  }
  showInputLength?: boolean
  step?: number

  onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>
  onFocus?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>
  onKeyDown?: React.KeyboardEventHandler<HTMLTextAreaElement | HTMLInputElement>
  onClick?: React.MouseEventHandler<HTMLTextAreaElement | HTMLInputElement>
}

// zeros are considered valid
const getValidInputValue = (value: FixMeAny) =>
  value || value === 0 ? value : undefined

function TextInput({
  focus = false,
  forceOriginalValue = false,
  type = 'text',
  icon,
  iconName,
  onIconClick,
  disabled = false,
  meta = {
    valid: true,
    touched: false,
    error: '',
  },
  className = '', // The containerClassNames prop should be deprecated and className prop should be used instead
  extraClassNames = {
    containerClassNames: '',
    inputClassNames: '',
    placeholderClassNames: '',
    errorClassNames: '',
    iconClassNames: '',
  },
  simpleClassName = '',
  input = {},
  defaultValue = '',
  value = '',
  onBlur = () => {},
  onKeyDown = undefined,
  onFocus = () => {},
  onClick = () => {},
  onChange,
  onInput,
  readOnly = false,
  maxLength,
  hint,
  required = false,
  textArea = false,
  large = false,
  min,
  id,
  placeholder = null,
  placeholderExtra = null,
  getRef = () => {},
  name = '',
  pattern,
  autoComplete,
  inputStyle,
  showInputLength = false,
  step = undefined,
}: Props) {
  const [isFocused, setIsFocused] = useState(false)
  const [originalInputValue, setOriginalInputValue] = useState<boolean | FixMeAny>(
    false,
  )

  const getDisplayValue = () =>
    getValidInputValue(input.value) ?? getValidInputValue(value) ?? defaultValue

  const getInputValue = () => {
    // OnChange return undefined from text-input
    // OnChange return false from input-dropdown
    const displayValue = getDisplayValue()
    // eslint-disable-next-line no-nested-ternary
    return isNil(originalInputValue)
      ? forceOriginalValue
        ? displayValue
        : null
      : displayValue
  }

  const onInputChange: Props['onChange'] = (e) => {
    const changeHandler = input.onChange || onChange || onInput

    if (changeHandler) {
      if (!originalInputValue) {
        setOriginalInputValue(changeHandler(e))
      }

      changeHandler(e)
    }
  }

  const handleOnFocus: Props['onFocus'] = (e) => {
    setIsFocused(true)
    onFocus(e)
  }

  const handleOnBlur: Props['onBlur'] = (e) => {
    setIsFocused(false)
    onBlur(e)
  }

  const { valid, touched, error } = meta
  const {
    containerClassNames = '',
    inputClassNames = '',
    placeholderClassNames = '',
    errorClassNames = '',
    iconClassNames = '',
  } = extraClassNames

  const InputElement = textArea ? 'textarea' : 'input'

  const displayedValue = getInputValue()

  let complexClassName = classNames(inputClassNames, 'TextInput-input', {
    'is-withValue': Boolean(getValidInputValue(displayedValue)),
    'TextInput-textArea': textArea && !large,
    'TextInput-textArea-large': textArea && large,
    'without-placeholder': !placeholder && !placeholderExtra,
  })

  if (simpleClassName !== '') {
    complexClassName = simpleClassName
  }

  const isEmptyString = (values: Array<FixMeAny>) =>
    values.every((item) => item === '' || isNil(item))

  const isForceNumber = type === 'forceNumber'

  const proxyType = isForceNumber ? 'number' : type

  const localOnKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement | HTMLInputElement>,
  ) => {
    if (onKeyDown) {
      onKeyDown(e)
    }

    if (isForceNumber && e.key !== 'Backspace') {
      // eslint-disable-next-line sonarjs/slow-regex, @typescript-eslint/no-unused-expressions
      !/^\d*\.?\d*$/gi.test(e.key) && e.preventDefault()
    }
  }

  return (
    <div
      className={`${containerClassNames} ${className} TextInput ${
        touched && !valid ? 'is-invalid' : ''
      } ${textArea ? 'TextInput-textArea-container' : ''}`}
    >
      <InputElement
        ref={(input: FixMeAny) => {
          getRef(input)
        }}
        autoFocus={focus}
        className={complexClassName}
        value={displayedValue}
        min={min}
        type={proxyType}
        id={id}
        disabled={disabled}
        onChange={onInputChange as FixMeAny}
        onBlur={handleOnBlur as FixMeAny}
        onFocus={handleOnFocus as FixMeAny}
        onKeyDown={localOnKeyDown as FixMeAny}
        readOnly={readOnly}
        maxLength={maxLength}
        autoComplete={autoComplete}
        name={name}
        pattern={pattern}
        style={inputStyle}
        onClick={onClick as FixMeAny}
        step={step}
      />

      {placeholder && (
        <div
          className={`${placeholderClassNames} TextInput-placeholder ${
            !isEmptyString([input.value, displayedValue, value]) ? 'is-withValue' : ''
          }`}
        >
          {/* eslint-disable-next-line no-nested-ternary */}
          {isValidElement(placeholder)
            ? placeholder
            : isFunction(placeholder)
              ? placeholder()
              : ctIntl.formatMessage({ id: placeholder as string })}
          {placeholderExtra}
        </div>
      )}
      {required && <div className="TextInput-required">*</div>}
      {iconName && (
        <Icon
          icon={iconName}
          className={`TextInput-faIcon ${iconClassNames} ${
            isFocused ? 'TextInput-faIcon--focused' : ''
          }`}
          onClick={onIconClick}
        />
      )}
      {icon && (
        <span
          className="TextInput-faIcon"
          onClick={onIconClick}
        >
          {icon}
        </span>
      )}
      <div className={`TextInput-${error ? 'error' : 'hint'} ${errorClassNames}`}>
        {/* eslint-disable-next-line no-nested-ternary */}
        {touched && error
          ? ctIntl.formatMessage({ id: error })
          : hint
            ? ctIntl.formatMessage({ id: hint })
            : ''}
      </div>

      {showInputLength && (
        <StyledTextLength>
          {displayedValue.length}
          <span>/</span>
          {maxLength}
        </StyledTextLength>
      )}
    </div>
  )
}

export default TextInput

const StyledTextLength = styled.span`
  background: white;
  border-radius: 4px;
  bottom: -6px;
  font-size: 12px;
  padding: 0px 2px;
  position: absolute;
  right: 8px;
`
