import { useEffect, useMemo, useRef } from 'react'
import * as React from 'react'
import { find, isNil } from 'lodash'
import zenscroll from 'zenscroll'

import { useActiveElement } from '@fleet-web/hooks'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

// Array with items ids
const getItemsIds = (items: Array<Item>) =>
  items.reduce((itemsIds, item) => {
    itemsIds.push(item.id)
    if (item.subItems !== undefined) {
      item.subItems.map((subItem) => itemsIds.push(subItem.id))
    }

    return itemsIds
  }, [] as Array<string>)

type Item = {
  label: string
  id: string
  handleClick?: (...args: Array<FixMeAny>) => FixMeAny
  emitClick?: boolean
  subItems?: Array<{
    label: string
    id: string
    component?: JSX.Element
    handleClick?: (...args: Array<FixMeAny>) => FixMeAny
  }>
}

type Props = {
  items: Array<Item>
  scrollOffset?: number
  scrollElementId?: string
}

function VerticalNav({
  items,
  /*
    This is the NavBar height (104px) +
    AdminHeader height (56px) +
    20px to give extra margin
  */
  scrollOffset = 180,
  scrollElementId = 'content-container',
}: Props) {
  const root = useRef<HTMLElement | null>()
  const zenScroller = useRef<typeof zenscroll>()
  const itemsIds = useMemo(() => getItemsIds(items), [items])

  const [activeElement, setActiveElement] = useActiveElement(itemsIds, {
    offsetTop: scrollOffset,
    scrollElementId,
  })

  useEffect(() => {
    root.current = document.getElementById(scrollElementId)
    if (root.current === null) {
      throw new Error(`${scrollElementId} element does not exist`)
    }

    zenScroller.current = zenscroll.createScroller(root.current, 300, scrollOffset)
  }, [activeElement, scrollElementId, scrollOffset])

  const handleClick = (e: React.MouseEvent<HTMLSpanElement>) => {
    const { target } = e.currentTarget.dataset
    if (target === undefined) {
      throw new Error(
        'This click handler was fired on an element without the data-target attribute',
      )
    }

    const targetItem = find(
      items.reduce((previous, current) => {
        const currentItems = [current]
        if (current.subItems) {
          for (const item of current.subItems) {
            currentItems.push(item)
          }
        }
        return [...previous, ...currentItems]
      }, [] as Array<Item>),
      { id: target },
    )
    const targetDiv = document.getElementById(target)
    const hasScroll = isNil(root.current)
      ? false
      : root.current.scrollHeight > root.current.offsetHeight

    if (targetItem && targetItem.emitClick && targetItem.handleClick) {
      targetItem.handleClick()
    }

    /*
      Make sure the targetDiv is not inside a div with position: relative;!
      zenscroll doesn't work well with position: relative; containers.
      Open issue on GitHub: https://github.com/zengabor/zenscroll/issues/42
    */
    if (targetDiv && hasScroll && zenScroller.current) {
      zenScroller.current.to(targetDiv)
    }

    // Force active element
    if (!hasScroll) {
      setActiveElement(target)
    }
  }

  return (
    <div className="VerticalNav">
      <ul className="VerticalNav-inner">
        {items.map((item) => (
          <li key={item.label}>
            <span
              className={`VerticalNav-item${
                activeElement === item.id ? ' is-active' : ''
              }`}
              data-target={item.id}
              onClick={handleClick}
            >
              <a
                href={'#' + item.id}
                className="VerticalNav-link util-textEllipsis"
              >
                {ctIntl.formatMessage({ id: item.label })}
              </a>
            </span>
            {item.subItems ? (
              <ul
                key={item.label}
                className="VerticalNav-subList"
              >
                {item.subItems.map((subItem) => (
                  <li
                    key={subItem.label}
                    data-target={subItem.id}
                    className={`VerticalNav-subList-item${
                      activeElement === subItem.id ? ' is-active' : ''
                    }`}
                    onClick={handleClick}
                  >
                    <a
                      href={'#' + subItem.id}
                      className="VerticalNav-link"
                    >
                      {ctIntl.formatMessage({ id: subItem.label })}
                    </a>
                  </li>
                ))}
              </ul>
            ) : null}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default React.memo(VerticalNav)
