import { connect } from 'react-redux'

import { getDriver } from '@fleet-web/duxs/drivers'
import { getGeofence } from '@fleet-web/duxs/geofences'
import { getVehicle } from '@fleet-web/duxs/vehicles'
import type { AppState } from '@fleet-web/root-reducer'
import type { FixMeAny } from '@fleet-web/types'

type OwnProps = {
  id: FixMeAny
  type: 'vehicle' | 'driver' | 'geofence'
  property?: string
  className?: string
  defaultMessage?: string
}

type Props = OwnProps & ReturnType<typeof mapStateToProps>

const Lookup = ({ value = '', defaultMessage, className }: Props) =>
  value || defaultMessage ? (
    <span className={className}>{value || defaultMessage}</span>
  ) : null

function mapStateToProps(state: AppState, ownProps: OwnProps) {
  const { type, property = 'name', id } = ownProps
  let item

  switch (type) {
    case 'vehicle': {
      item = getVehicle(state, id)
      break
    }
    case 'driver': {
      item = getDriver(state, id)
      break
    }
    case 'geofence': {
      item = getGeofence(state, id)
      break
    }
    default: {
      item = null
    }
  }

  return {
    // eslint-disable-next-line no-nested-ternary
    value: item ? (property === null ? item : (item as FixMeAny)[property]) : null,
  }
}

export default connect(mapStateToProps)(Lookup)
