import { Fragment, type ReactNode } from 'react'
import { Box, Tooltip, type BoxProps } from '@karoo-ui/core'

import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

type Props = {
  data: Array<{
    key: string
    value: string | number
  }>
  divider?: ReactNode
  className?: string
  boxClassName?: {
    self: string
    value: string
    label: string
  }
  reversed?: boolean
  tooltipLabel?: string
  boxLabelProps?: BoxProps
  boxValueProps?: BoxProps
}

const Stats = ({
  boxClassName = { self: '', value: '', label: '' },
  data,
  className = '',
  reversed = false,
  tooltipLabel,
  boxLabelProps,
  boxValueProps,
  divider,
}: Props) => {
  const boxChildren = (key: string, value?: FixMeAny) => {
    const formattedMessage = ctIntl.formatMessage(
      {
        id: key,
        defaultMessage: key,
      },
      { values: { count: value } },
    )
    return [
      <Box
        key={key}
        className={`Stats-label ${boxClassName.label}`}
        title={formattedMessage}
        {...boxLabelProps}
      >
        {formattedMessage}
      </Box>,
      <Box
        key={value}
        className={`Stats-value ${boxClassName.value}`}
        title={value}
        {...boxValueProps}
      >
        {value}
      </Box>,
    ]
  }

  const items = data.map(({ key, value }, idx) => (
    <Fragment key={key}>
      <div className={`Stats-box ${boxClassName.self}`}>
        {reversed ? boxChildren(key, value).reverse() : boxChildren(key, value)}
        {idx > 0 && <div className="Stats-spacing" />}
      </div>
      {data.length > 0 && idx !== data.length - 1 && divider}
    </Fragment>
  ))

  return (
    <Tooltip
      arrow
      title={tooltipLabel ? ctIntl.formatMessage({ id: tooltipLabel }) : undefined}
    >
      <div className={`Stats ${className}`}>{items}</div>
    </Tooltip>
  )
}

export default Stats
