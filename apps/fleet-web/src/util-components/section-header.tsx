import type * as React from 'react'
import { isNil } from 'lodash'
import styled from 'styled-components'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

import Button, { type ButtonWrapperProps } from './button'

export type Props = {
  buttons?: JSX.Element | null
  className?: string
  iconButton?: Array<ButtonWrapperProps> | null
  id?: string
  label: string
  small?: boolean
  sub?: boolean
  subLabel?: string
  requiredMessage?: boolean
  icon?: React.ReactNode
}

const SectionHeader = ({
  buttons = null,
  subLabel = '',
  sub = false,
  small = false,
  id = '',
  requiredMessage = false,
  className = '',
  iconButton = null,
  label,
  icon = null,
}: Props) => (
  <div
    className={`SectionHeader ${
      // eslint-disable-next-line no-nested-ternary
      sub
        ? 'SectionHeader--sub'
        : small
          ? 'SectionHeader--small'
          : 'SectionHeader--section'
    }
    ${className}`}
    id={id}
  >
    <SectionHeaderContainer>
      {icon}
      {ctIntl.formatMessage({ id: label })}
      {subLabel ? (
        <span className="SectionHeader-subLabel">
          {ctIntl.formatMessage({ id: subLabel })}
        </span>
      ) : null}
      {requiredMessage ? (
        <span
          className={`SectionHeader-requiredMessage ${
            iconButton ? 'SectionHeader-requiredMessage-iconButtonExist' : ''
          }`}
        >
          <span className="SectionHeader-asterisk">*</span>
          {ctIntl.formatMessage({ id: 'Required Fields' })}
        </span>
      ) : null}
    </SectionHeaderContainer>

    <div className="SectionHeader-iconButton">
      {/* eslint-disable-next-line no-nested-ternary */}
      {!isNil(buttons) ? (
        buttons
      ) : !isNil(iconButton) ? (
        <span>
          {iconButton.map((ic) => (
            <Button
              {...ic}
              key={ic.id}
            />
          ))}
        </span>
      ) : (
        <span />
      )}
    </div>
  </div>
)

export default SectionHeader

const SectionHeaderContainer = styled.span`
  flex: 1;
`
