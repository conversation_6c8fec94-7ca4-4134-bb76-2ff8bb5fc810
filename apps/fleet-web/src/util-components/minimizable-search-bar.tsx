import { useEffect, useRef, useState } from 'react'
import { isNil } from 'lodash'
import styled from 'styled-components'

import { variables } from '@fleet-web/shared/components/styled/global-styles'
import type { FixMeAny } from '@fleet-web/types'

import TextInput from './text-input'

export type Props = {
  id?: string
  className?: string
  focus?: boolean
  placeholder?: string
  forceOriginalValue?: boolean
  onBlur?: (...args: Array<FixMeAny>) => FixMeAny
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClick?: (...args: Array<FixMeAny>) => FixMeAny
  value?: string
  alwaysOpen?: boolean
  meta?: {
    valid?: boolean
    touched?: boolean | Array<boolean> | Record<string, boolean>
    error?: string
  }
}

const MinimizableSearchBar = ({
  id = '',
  className = '',
  focus = false,
  placeholder = 'Search',
  forceOriginalValue = false,
  onBlur,
  onChange,
  onClick,
  value,
  alwaysOpen,
  meta = {
    valid: true,
    touched: false,
    error: '',
  },
}: Props) => {
  const [isOpen, setIsOpen] = useState(alwaysOpen || false)
  const searchRef = useRef(null)

  useEffect(() => {
    if (value && forceOriginalValue) {
      setIsOpen(true)
    }
  }, [value, forceOriginalValue])

  useEffect(() => {
    if (alwaysOpen) {
      setIsOpen(true)
    }
  }, [alwaysOpen])

  const setOpenAndFocus = (isOpen: boolean, searchRef: FixMeAny) => {
    if (!isOpen) {
      setIsOpen(true)
      searchRef.current.focus()
    } else {
      setIsOpen(false)
      searchRef.current.blur()
    }
  }

  return (
    <>
      <StyledTextInput
        id={id}
        onBlur={(event) => {
          if (!alwaysOpen) {
            event.currentTarget.blur()
            if (isOpen && !value) {
              setIsOpen(false)
            }
          }
          if (onBlur) {
            onBlur()
          }
        }}
        onClick={() => {
          if (!alwaysOpen && !value && !isNil(searchRef)) {
            setOpenAndFocus(isOpen, searchRef)
          }
          if (onClick) {
            onClick()
          }
        }}
        onIconClick={() => {
          if (!alwaysOpen && !value && !isNil(searchRef)) {
            setOpenAndFocus(isOpen, searchRef)
          }
          if (onClick) {
            onClick()
          }
        }}
        focus={focus}
        input={{
          onChange: onChange,
          value: value,
        }}
        placeholder={placeholder}
        iconName="search"
        forceOriginalValue={forceOriginalValue}
        isOpen={isOpen}
        meta={meta}
        getRef={(ref) => {
          searchRef.current = ref
        }}
        extraClassNames={{
          containerClassNames: `SearchBar ${className}`,
        }}
      />
    </>
  )
}

export default MinimizableSearchBar

type StyleProps = {
  isOpen: boolean
}

const StyledTextInput = styled(TextInput)`
  min-width: 37px;
  transition: all 0.5s ease;
  max-width: ${(props: StyleProps) =>
    props.isOpen ? variables.searchBarMaxWidth : variables.minimizedSearchBarMaxWidth};

  :hover {
    cursor: ${(props: StyleProps) => (props.isOpen ? 'text' : 'pointer')};
  }
  .TextInput-placeholder {
    display: ${(props: StyleProps) => (props.isOpen ? 'block' : 'none')};
  }
  .TextInput-input {
    border-radius: ${(props: StyleProps) => (props.isOpen ? '3px' : '8px')};
    :hover {
      cursor: ${(props: StyleProps) => (props.isOpen ? 'text' : 'pointer')};
    }
  }
  .TextInput-faIcon {
    font-size: 16px;
    right: ${(props: StyleProps) => (props.isOpen ? '15px' : '6px')};
  }
`
