import { VehicleType } from '@fleet-web/api/types'

export const vehicleMarkerBorderIcons = {
  standard: require('../../assets/vehicles/bearing-border.svg'),
  dashed: require('../../assets/vehicles/bearing-border-dashed.svg'),
}

const vehicleIcons = {
  [VehicleType.Default]: require('../../assets/vehicles/sedan-car.svg'),
  [VehicleType.Motorbike]: require('../../assets/vehicles/motorbike.svg'),
  [VehicleType.SmallCar]: require('../../assets/vehicles/small-car.svg'),
  [VehicleType.SedanCar]: require('../../assets/vehicles/sedan-car.svg'),
  [VehicleType.FourByFour]: require('../../assets/vehicles/4x4.svg'),
  [VehicleType.Van]: require('../../assets/vehicles/van.svg'),
  [VehicleType.SmallTruck]: require('../../assets/vehicles/small-truck.svg'),
  [VehicleType.LargeTruck]: require('../../assets/vehicles/large-truck.svg'),
  [VehicleType.SmallMachine]: require('../../assets/vehicles/small-machine.svg'),
  [VehicleType.LargeMachine]: require('../../assets/vehicles/large-machine.svg'),
  [VehicleType.Bus]: require('../../assets/vehicles/bus.svg'),
  [VehicleType.GolfCart]: require('../../assets/vehicles/golf-car.svg'),
  [VehicleType.TruckConcretePump]: require('../../assets/vehicles/truck-concrete-pump.svg'),
  [VehicleType.TruckMixer]: require('../../assets/vehicles/truck-mixer.svg'),
  [VehicleType.MobileCrane]: require('../../assets/vehicles/mobile-crane.svg'),
  [VehicleType.Boat]: require('../../assets/vehicles/boat.svg'),
  [VehicleType.Generator]: require('../../assets/vehicles/generator.svg'),
  [VehicleType.Crane]: require('../../assets/vehicles/crane.svg'),
  [VehicleType.StaticPump]: require('../../assets/vehicles/static-pump.svg'),
  [VehicleType.Trailer]: require('../../assets/vehicles/trailer.svg'),
  [VehicleType.WifiUnit]: require('../../assets/vehicles/wifi.svg'),
  [VehicleType.PickupTruck]: require('../../assets/vehicles/pickup-truck.svg'),
  [VehicleType.Ambulance]: require('../../assets/vehicles/ambulance.svg'),
  [VehicleType.WaterTruck]: require('../../assets/vehicles/water-truck.svg'),
  [VehicleType.FireTruck]: require('../../assets/vehicles/fire-truck.svg'),
  [VehicleType.Backhoe]: require('../../assets/vehicles/backhoe.svg'),
  [VehicleType.DumpTruck]: require('../../assets/vehicles/dump-truck.svg'),
  [VehicleType.Forklift]: require('../../assets/vehicles/forklift.svg'),
  [VehicleType.Grader]: require('../../assets/vehicles/grader.svg'),
  [VehicleType.Loader]: require('../../assets/vehicles/loader.svg'),
  [VehicleType.RoadRoller]: require('../../assets/vehicles/road-roller.svg'),
  [VehicleType.Tractor]: require('../../assets/vehicles/tractor.svg'),
  [VehicleType.TowTruck]: require('../../assets/vehicles/tow-truck.svg'),
  [VehicleType.Lorry]: require('../../assets/vehicles/lorry.svg'),
  [VehicleType.LorryCrane]: require('../../assets/vehicles/lorry-crane.svg'),
  [VehicleType.Uncharacterized]: require('../../assets/vehicles/uncharacterized.svg'),
  [VehicleType.Patrol]: require('../../assets/vehicles/patrol.svg'),
  [VehicleType.PrisonerTransport]: require('../../assets/vehicles/prisoner-transport.svg'),
  [VehicleType.BulletProof]: require('../../assets/vehicles/bulletproof.svg'),
  [VehicleType.Jetski]: require('../../assets/vehicles/jetski.svg'),
  [VehicleType.RailwayCar]: require('../../assets/vehicles/railway-car.svg'),
  [VehicleType.DrillingRig]: require('../../assets/vehicles/drilling-rig.svg'),
  [VehicleType.RTXMachine]: require('../../assets/vehicles/rtx-machine.svg'),
  [VehicleType.SkidSteer]: require('../../assets/vehicles/skid-steer.svg'),
  [VehicleType.Escavator]: require('../../assets/vehicles/escavator.svg'),

  /* ----- BEFORE YOU ADD MORE ICONS ------ */
  // You will have to add a class="cls-1" on the icon svg file (see lorry-crane.svg as an example).
  // This will guarantee that modules/features that customize the icon color (through that class name) can apply the desired color correctly.
} satisfies Record<VehicleType, string>

export default vehicleIcons
