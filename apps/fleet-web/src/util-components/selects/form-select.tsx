import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from '@karoo-ui/core'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

export default function FormSelection<T extends string | number>({
  testId,
  label,
  value,
  disabled,
  onChange,
  options,
  errorMsg,
  helperText,
}: {
  testId: string
  label: string
  value: T
  disabled?: boolean
  onChange: (value: T) => void
  options: ReadonlyArray<{
    label: string
    value: T
  }>
  helperText?: string
  errorMsg?: string
}) {
  const finalHelpText = errorMsg ?? helperText
  return (
    <FormControl>
      <InputLabel id={testId}>{ctIntl.formatMessage({ id: label })}</InputLabel>
      <Select
        data-testid={testId}
        labelId={testId}
        value={value}
        disabled={disabled ?? false}
        label={ctIntl.formatMessage({ id: label })}
        onChange={(e) => onChange(e.target.value as typeof value)}
        size="small"
        error={!!errorMsg}
        sx={{ width: '180px' }}
      >
        {options.map((option) => (
          <MenuItem
            key={option.value}
            value={option.value}
          >
            {ctIntl.formatMessage({ id: option.label })}
          </MenuItem>
        ))}
      </Select>
      {finalHelpText && (
        <FormHelperText error={!!errorMsg}>
          {ctIntl.formatMessage({ id: finalHelpText })}
        </FormHelperText>
      )}
    </FormControl>
  )
}
