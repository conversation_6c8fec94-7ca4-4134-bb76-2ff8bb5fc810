import type { Styles } from 'react-select'

import type { getInputFieldColors } from '@fleet-web/duxs/user'
import type { FixMeAny } from '@fleet-web/types'

export const getDefaultStyles = (
  inputColors: ReturnType<typeof getInputFieldColors>,
  extraValueContainerStyles?: FixMeAny,
): Partial<Styles> => ({
  container: (base) => ({
    ...base,
    borderColor: inputColors.styleInputfieldColourActive,
    minWidth: '240px',
  }),
  control: (base) => ({
    ...base,
    '&:hover': { borderColor: inputColors.styleInputfieldColourHover },
    border: '1px solid #ccc',
    boxShadow: 'inset 0 1px 5px -3px rgba(0,0,0,.5)',
  }),
  menu: (base) => ({
    ...base,
    borderColor: inputColors.styleInputfieldColourActive,
    zIndex: 10,
  }),
  groupHeading: (base) => ({
    ...base,
    fontSize: '16px',
    color: '#333',
    fontWeight: 'bold',
    textTransform: 'capitalize',
  }),
  valueContainer: (base) => ({
    ...base,
    ...extraValueContainerStyles,
  }),
  option: (base, state) => {
    const baseOptionStyle: React.CSSProperties = {
      ...base,
      textAlign: 'left',
      borderColor: inputColors.styleInputfieldColourActive,
      backgroundColor: state.isFocused && inputColors.styleInputfieldColourHover,
    }

    if (state.isSelected) {
      return {
        ...baseOptionStyle,
        backgroundColor: 'white',
        color: 'inherit',
        '&:hover': { backgroundColor: '#DEEBFF' },
      }
    }

    return baseOptionStyle
  },
  placeholder: (base) => ({
    ...base,
    color: 'hsla(0,0%,40%,.7)',
    textAlign: 'left',
  }),
})
