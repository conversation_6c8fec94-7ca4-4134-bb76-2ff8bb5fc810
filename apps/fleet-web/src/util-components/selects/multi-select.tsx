import { useCallback } from 'react'
import { connect, type DispatchProp } from 'react-redux'
import type { ActionTypes, OptionsType, Styles } from 'react-select'

import { getInputFieldColors } from '@fleet-web/duxs/user'
import { useTypedSelector } from '@fleet-web/redux-hooks'
import type { FixMeAny } from '@fleet-web/types'
import { ctIntl } from '@fleet-web/util-components/ctIntl'

import SelectAny from '../custom/InputDropdown'
import { getDefaultStyles } from './shared/styles'

const selectAllOption = {
  value: 'all',
  name: 'Select All',
}

const Select = SelectAny as FixMeAny

type Props = {
  value?: OptionsType<any> | string
  defaultValue?: OptionsType<any> | string
  options:
    | OptionsType<any>
    | Array<{
        label: string
        options: Array<OptionsType<any>>
      }>
  multiple?: boolean
  grouped?: boolean
  placeholder?: string
  required?: boolean
  name?: string
  input?: {
    value: string
    onChange: ((...args: Array<FixMeAny>) => FixMeAny) | undefined
  }
  className?: string
  extraClassNames?: { containerClassNames: string }
  extraValueContainerStyles?: { overflow?: string; maxHeight?: string }
  onChange?: (...args: Array<FixMeAny>) => FixMeAny
  formatOptionLabel?: (...args: Array<FixMeAny>) => FixMeAny
  hideSelectedOptions?: boolean
  closeMenuOnSelect?: boolean
  meta?: {
    valid?: FixMeAny
    touched?: boolean | Array<boolean> | Record<string, boolean>
    error?: string
  }
  styles?: Partial<Styles>
  isDisabled?: boolean
  hasAllOption?: boolean
} & DispatchProp

const MultiSelect = ({
  defaultValue,
  options,
  multiple = true,
  grouped = false,
  placeholder = '',
  required,
  input = { value: '', onChange: undefined },
  className = '',
  extraClassNames = { containerClassNames: '' },
  isDisabled = false,
  onChange = () => {},
  formatOptionLabel = (data: FixMeAny) => data.label,
  meta = {
    valid: true,
    touched: false,
    error: '',
  },
  hasAllOption = false,
  ...props
}: Props) => {
  const inputFieldColors = useTypedSelector(getInputFieldColors)

  const handleSelectedOption = (
    value: string,
    {
      action,
      option,
    }: {
      action: ActionTypes
      option: Record<string, any> | undefined
    },
  ) => {
    const changeHandler = input.onChange || onChange
    if (option?.value === selectAllOption.value) {
      changeHandler(
        options.map((i) => ({
          ...i,
          label: i.name,
        })),
        action,
      )
    } else {
      changeHandler(value, action)
    }
  }

  const getOptions = useCallback(
    () => (hasAllOption ? [selectAllOption, ...options] : options),
    [hasAllOption, options],
  )

  const { valid, touched, error } = meta

  return (
    <div className="InputDropdown-container">
      {options.length > 0 ? (
        <Select
          multiple={multiple}
          grouped={grouped}
          placeholder={placeholder}
          required={required}
          defaultValue={input.value || defaultValue || ''}
          options={getOptions()}
          formatOptionLabel={formatOptionLabel}
          className={className || extraClassNames.containerClassNames}
          onChange={handleSelectedOption}
          isDisabled={isDisabled}
          styles={
            props.styles ||
            getDefaultStyles(inputFieldColors, props.extraValueContainerStyles)
          }
          isSearchable
          {...props}
        />
      ) : (
        <p>No Options Available</p>
      )}
      <div className={'TextInput-error'}>
        {!valid && touched && error ? ctIntl.formatMessage({ id: error }) : ''}
      </div>
    </div>
  )
}

export default connect()(MultiSelect)
