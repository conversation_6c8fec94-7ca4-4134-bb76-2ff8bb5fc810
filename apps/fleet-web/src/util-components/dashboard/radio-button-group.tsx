import styled from 'styled-components'

import Button from '@fleet-web/util-components/button-storeless'

type Option = {
  label: string
  value: string
  onClick: () => void
}

type Props = {
  options: Array<Option>
  activeOption: string
}

const RadioButtonGroup = ({ options, activeOption }: Props) => (
  <Container>
    {options.map((option) => (
      <RadioButton
        $isActive={option.value === activeOption}
        small
        key={option.value}
        label={option.label}
        onClick={option.onClick ? option.onClick : () => {}}
      />
    ))}
  </Container>
)

export default RadioButtonGroup

const Container = styled.div`
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
`

const RadioButton = styled(Button)<{ $isActive: boolean }>`
  &&& {
    background: ${(props) =>
      props.$isActive
        ? props.theme.colors.styleButtonDefaultColourActive
        : props.theme.colors.styleButtonDefaultColourStandard};
    border: 1px solid
      ${(props) =>
        props.$isActive
          ? props.theme.colors.styleButtonDefaultColourActive
          : '#CCCCCC'};
    color: ${(props) =>
      props.$isActive
        ? props.theme.colors.styleButtonDefaultTextColourActive
        : props.theme.colors.styleButtonDefaultTextColourStandard};
    outline: none;
    border-radius: 0;

    &:first-of-type {
      border-radius: 4px 0 0 4px;
    }

    &:last-of-type {
      border-radius: 0 4px 4px 0;
    }

    &:not([disabled]) {
      &:focus,
      &:hover {
        background: ${(props) =>
          props.$isActive
            ? props.theme.colors.styleButtonDefaultColourActive
            : props.theme.colors.styleButtonDefaultColourHover};
        color: ${(props) =>
          props.$isActive
            ? props.theme.colors.styleButtonDefaultTextColourActive
            : props.theme.colors.styleButtonDefaultTextColourHover};
        border: 1px solid
          ${(props) =>
            props.$isActive
              ? props.theme.colors.styleButtonDefaultColourActive
              : props.theme.colors.styleButtonDefaultTextColourHover};
      }

      &:active {
        background: ${(props) =>
          props.$isActive
            ? 'initial'
            : props.theme.colors.styleButtonDefaultColourActive};
        border-color: ${(props) =>
          props.$isActive
            ? 'initial'
            : props.theme.colors.styleButtonDefaultTextColourActive};
        box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
        color: ${(props) =>
          props.$isActive
            ? 'initial'
            : props.theme.colors.styleButtonDefaultTextColourActive};
      }
    }
  }
`
