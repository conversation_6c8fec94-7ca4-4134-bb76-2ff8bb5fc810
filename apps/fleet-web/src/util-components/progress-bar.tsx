import { useEffect, useState } from 'react'
import { FormattedMessage } from 'react-intl'
import styled from 'styled-components'

import { variables } from '@fleet-web/shared/components/styled/global-styles'

type Props = {
  width?: number
  height?: number
  duration: number
  onLoadingComplete: () => void
}

const ProgressBar = ({
  width = 255,
  height = 8,
  duration,
  onLoadingComplete,
}: Props) => {
  const [percentage, setPercentage] = useState(0)

  useEffect(() => {
    if (percentage < 100) {
      window.setTimeout(() => setPercentage(percentage + 1), duration / 100)
    } else {
      onLoadingComplete()
    }
  }, [duration, onLoadingComplete, percentage])

  return (
    <Container>
      <ProgressBarWrapper
        width={width}
        height={height}
      >
        <ProgressBarTopLayer duration={duration} />
        <ProgressBarBottomLayer />
      </ProgressBarWrapper>
      <LoadingLabel>
        <FormattedMessage
          id="dashboard.loading"
          values={{
            percentage: `${percentage}%`,
          }}
        />
      </LoadingLabel>
    </Container>
  )
}

export default ProgressBar

const Container = styled.div`
  text-align: center;
`

const ProgressBarWrapper = styled.div<{ width: number; height: number }>`
  position: relative;
  width: ${({ width }) => `${width}px`};
  height: ${({ height }) => `${height}px`};
  margin: 0 auto 9px;
  border-radius: ${({ height }) => `${height / 2}px`};
  overflow: hidden;
`

const ProgressBarTopLayer = styled.div<{ duration: number }>`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  height: 100%;
  width: 100%;
  background-color: ${(props) => props.theme.colors.styleButtonDefaultColourActive};
  animation: ${({ duration }) => `loading ${duration / 1000}s ease-in-out 1`};
`

const ProgressBarBottomLayer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-color: ${variables.lightGray};
`

const LoadingLabel = styled.p`
  font-size: 12px;
  font-weight: 600;
`
