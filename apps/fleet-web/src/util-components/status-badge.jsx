import { shape, string } from 'prop-types'
import { FormattedMessage } from 'react-intl'

import Icon from '@fleet-web/components/Icon'
import { STATUS_TO_MESSAGE } from '@fleet-web/util-functions'

const StatusBadge = ({ statusClassName, statusMessage, iconName, inlineStyle }) => (
  <div
    className={`StatusBadge ${statusClassName}`}
    style={inlineStyle}
  >
    {iconName ? (
      <Icon
        icon={iconName}
        className="StatusBadge-icon"
      />
    ) : null}
    {statusMessage !== null || statusClassName !== null ? (
      <FormattedMessage
        id={statusMessage ? statusMessage : STATUS_TO_MESSAGE[statusClassName]}
      />
    ) : (
      <div>{statusMessage}</div>
    )}
  </div>
)

StatusBadge.propTypes = {
  statusClassName: string.isRequired,
  statusMessage: string,
  iconName: string,
  inlineStyle: shape({}),
}

StatusBadge.defaultProps = {
  iconName: '',
  statusMessage: null,
  inlineStyle: {},
}

export default StatusBadge
