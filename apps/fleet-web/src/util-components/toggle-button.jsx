import { bool, func, shape, string } from 'prop-types'
import styled, { css } from 'styled-components'

import { ctIntl } from '@fleet-web/util-components/ctIntl'

const StyledToggleButton = styled.div.attrs(({ style, value }) => ({
  buttonColor: value ? '#b5d55e' : '#999',
  circle: {
    position: value ? { right: '1px' } : { left: '1px' },
    size: `${style.height - 2 || '17'}px`,
  },
  fontSize: style.fontSize || '10px',
  height: `${style.height || '19'}px`,
  padding: value
    ? `4px ${style.height + 5 || '24'}px 4px 12px`
    : `4px 12px 4px ${style.height + 5 || '24'}px`,
}))`
  align-items: center;
  background-color: ${(props) =>
    props.disabled ? 'rgba(102,102,102, 0.4)' : props.buttonColor};
  border-radius: 30px;
  box-sizing: border-box;
  color: #fff;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  display: inline-flex;
  font-size: ${(props) => props.fontSize};
  font-weight: bold;
  height: ${(props) => props.height};
  margin: auto 0;
  min-width: 55px;
  ${(props) => (props.expandWidth ? '' : 'max-width: 55px;')} position: relative;
  justify-content: center;
  text-align: center;
  text-transform: uppercase;
  user-select: none;
  vertical-align: middle;

  ${(props) =>
    props.editable
      ? css`
          padding: ${(props) => props.padding};
          ::before {
            background: #fff;
            border-radius: 50%;
            content: '';
            height: ${(props) => props.circle.size};
            position: absolute;
            top: 1px;
            width: ${(props) => props.circle.size};

            ${(props) => props.circle.position};
          }
        `
      : css`
          pointer-events: none;
        `};
`

const ToggleButton = (props) => (
  <StyledToggleButton
    {...props}
    className={props.className}
  >
    <span>
      {ctIntl.formatMessage({
        id: props.value ? props.label.on : props.label.off,
      })}
    </span>
  </StyledToggleButton>
)

ToggleButton.propTypes = {
  value: bool.isRequired,
  disabled: bool,
  editable: bool,
  className: string,
  label: shape({
    on: string,
    off: string,
  }),
  onClick: func,
  style: shape({}),
  expandWidth: bool,
}

ToggleButton.defaultProps = {
  className: undefined,
  label: {
    on: 'On',
    off: 'Off',
  },
  onClick: () => {},
  style: {},
  disabled: false,
  editable: true,
  expandWidth: true,
}

export default ToggleButton
