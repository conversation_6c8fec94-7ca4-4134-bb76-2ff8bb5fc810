// eslint-disable @typescript-eslint/no-require-imports
const rspack = require('@rspack/core')
const z = require('zod')
const { join, resolve } = require('node:path')
const {
  esmNodeModulesToTranspileDirPaths,
  checkForCommonJSSync,
} = require('./search-esm-node-modules.cjs')
const localPackageConfig = require('./package.json')

/**
 *
 * @param {string} fullPath
 */
function getPkgNameFromFullPath(fullPath) {
  const nodeModules = 'node_modules/'
  const nodeModulesPosition = fullPath.indexOf(nodeModules)

  if (nodeModulesPosition !== -1) {
    // Get the position after 'node_modules/'
    const startPosition = nodeModulesPosition + nodeModules.length
    const endPosition = fullPath.indexOf('node_modules/', startPosition)

    if (endPosition !== -1) {
      // Slice the string from startPosition to endPosition
      return fullPath.slice(startPosition, endPosition)
    }
    // If there is no slash after 'node_modules/', return the rest of the string
    return fullPath.slice(startPosition)
  } else {
    // If 'node_modules' is not found, return the original path or handle it as needed
    return fullPath
  }
}

const hasLoggedSet = new Set()
const consoleLogOnce = (msg) => {
  if (!hasLoggedSet.has(msg)) {
    // eslint-disable-next-line no-console
    console.log(msg)
    hasLoggedSet.add(msg)
  }
}

const getModuleJSAndTSRulesParams = z.object({
  useReactRefresh: z.boolean(),
  transpileNodeModules: z.boolean(),
})

// oxlint-disable-next-line no-array-reduce
const dependenciesResolveAlias = Object.keys(localPackageConfig.dependencies).reduce(
  (acc, key) => {
    acc[key] = resolve(__dirname, `node_modules/${key}`)
    return acc
  },
  {},
)

module.exports = {
  // Contains isolated configs so they can be used more freely
  plugins: {
    // https://webpack.js.org/plugins/ignore-plugin/#example-of-ignoring-moment-locales
    ignoreMomentLocalesPlugin: new rspack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
  },
  resolveJSAndTS: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
    alias: {
      /** Webpack v5 does not bundle Nodejs core lib anymore hence we need to polyfill the modules we use.
       *  Please, REMOVE if we don't use them anymore (builds should succeed) */
      vm: 'vm-browserify',
      stream: 'stream-browserify',
      /** ------------------------ */
      // This is a temporary solution. Ideally we should not have to specify aliases both here and in tsconfig
      assets: join(__dirname, 'assets/'),
      '@fleet-web': join(__dirname, 'src/'),
      '@karoo-ui/core-rhf': join(__dirname, 'src/shared/react-hook-form/index.ts'),
      '@karoo-ui/core-tanstack-form': join(
        __dirname,
        'src/shared/tanstack-form/index.ts',
      ),
      '@karoo-ui/core': resolve(__dirname, '../../libs/karoo-ui/core/dist/index.js'),
      '@karoo/utils': resolve(__dirname, '../../libs/karoo-utils/dist/index.js'),
      ...dependenciesResolveAlias,
    },
  },
  /**
   *
   * @param {z.infer<typeof getModuleJSAndTSRulesParams>} params
   * @returns
   */
  getModuleJSAndTSRules: (params) => {
    const { useReactRefresh, transpileNodeModules } =
      getModuleJSAndTSRulesParams.parse(params)

    /** @type {import('@rspack/core').SwcLoaderEnvConfig} */
    const swcEnv = {
      // See https://www.notion.so/Minimal-Supported-Software-2e2ec59ad71a4437b6408f3cd820a625 for more info on browser support
      targets: 'defaults and fully supports es6-module', // Should be kept in sync with the browserslist file
      coreJs: '3.37',
      mode: 'usage',
    }

    const swcLoaderConfig = {
      loader: 'builtin:swc-loader',
      /** @type {import('@rspack/core').SwcLoaderOptions} */
      options: {
        env: swcEnv,
        jsc: {
          parser: {
            syntax: 'typescript',
            tsx: true,
            dynamicImport: true,
          },
          transform: {
            react: {
              runtime: 'automatic',
              refresh: useReactRefresh,
              development: useReactRefresh,
            },
          },
        },
      },
    }

    return [
      // Transpile node_modules
      transpileNodeModules && {
        test: /\.(js|mjs)$/,
        // Only transpile the packages that may have more recent features that need to be compiled. Otherwise, no blockers.
        include: (filePath) => {
          if (!/node_modules/.test(filePath)) {
            return false
          }

          const dep = esmNodeModulesToTranspileDirPaths.some((dirPath) =>
            filePath.startsWith(dirPath),
          )
          if (!dep) {
            return false
          }

          return checkForCommonJSSync(filePath) === false
        },
        /** @type {import('@rspack/core').RuleSetUse} */
        use: (info) => {
          consoleLogOnce(
            `Transpiling package [${getPkgNameFromFullPath(info.realResource)}]`,
          )
          return [
            {
              loader: 'builtin:swc-loader',
              /** @type {import('@rspack/core').SwcLoaderOptions} */
              options: {
                env: swcEnv,
                jsc: { parser: { syntax: 'ecmascript' } },
              },
            },
          ]
        },
      },
      {
        test: /\.(js|ts|jsx)$/,
        include: resolve(__dirname, 'src'),
        /** @type {import('@rspack/core').RuleSetUse} */
        use: [swcLoaderConfig],
      },
      {
        test: /\.tsx$/,
        include: resolve(__dirname, 'src'),
        /** @type {import('@rspack/core').RuleSetUse} */
        use: (info) => {
          // Configuration based on https://github.com/rspack-contrib/rspack-examples/tree/main/rspack/react-compiler-babel-ts

          // Do not run react compiler on these places since they tend to often violate the react rules at the moment and/or are deprecated.
          if (
            !info.realResource.includes('/modules/privacy') &&
            !info.realResource.includes('/modules/delivery/') &&
            // !info.realResource.includes('/modules/deliveryRevamp/') && // deliveryRevamp should use compiler but for now it is using some components from the old delivery that violate react rules
            !info.realResource.includes('/modules/mifleet')
          ) {
            return [
              swcLoaderConfig,
              {
                loader: 'babel-loader',
                options: {
                  plugins: [
                    [
                      'babel-plugin-react-compiler',
                      {
                        target: '18', // default is 19
                        // panicThreshold: 'critical_errors',
                        environment: {
                          // Important to not have "access property of null/undefined". Read docs for more info
                          // Caused issues when doing complex comparisons of useQuery "status" property on a useEffect hook, for instance.
                          enableTreatFunctionDepsAsConditional: true,
                          // This is needed for react-hook-form@7.53.0 currently. Does not play by the react rules in a lot of places :(
                          // Should keep an eye on it and remove this "fix" when they fix it.
                          enableAssumeHooksFollowRulesOfReact: false,
                        },
                      },
                    ], // must run first!
                    ['@babel/plugin-syntax-typescript', { isTSX: true }],
                  ],
                },
              },
            ]
          }

          return [swcLoaderConfig]
        },
      },
    ]
  },
}
