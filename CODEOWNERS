[core-team] @gustavo.cerqueira @joseph.feng @rodrigo.faria @mario.bento @tiago.sousa @ricardo.pimentel @huang.chuhang @joao.santos @jose.coelho @luis.sahagun @chenthuran.s @pedro.ventura
/apps/fleet-web/src/modules/vision/
/apps/fleet-web/src/modules/live-vision/
/apps/fleet-web/src/modules/admin/
/apps/fleet-web/src/modules/alert-center/
/apps/fleet-web/src/modules/app/
/apps/fleet-web/src/modules/carpool/
/apps/fleet-web/src/modules/coaching/
/apps/fleet-web/src/modules/dashboard/
/apps/fleet-web/src/modules/detail/
/apps/fleet-web/src/modules/engine/
/apps/fleet-web/src/modules/forms/
/apps/fleet-web/src/modules/groups/
/apps/fleet-web/src/modules/help/
/apps/fleet-web/src/modules/know-the-driver/
/apps/fleet-web/src/modules/lists/
/apps/fleet-web/src/modules/maintenance/
/apps/fleet-web/src/modules/map-view/
/apps/fleet-web/src/modules/reports/
/apps/fleet-web/src/modules/ruc/
/apps/fleet-web/src/modules/settings/

[tacho-team] @tiago.sousa @ricardo.pimentel @rodrigo.faria @joseph.feng @mario.bento @joao.santos @jose.coelho @luis.sahagun @chenthuran.s @pedro.ventura @gustavo.cerqueira
/apps/fleet-web/src/modules/tachograph/
/apps/fleet-web/src/modules/map-view/DriversMapView/


[mifleet-team] @mario.bento @fahimeh.ghorbani @rodrigo.faria @tiago.sousa @gustavo.cerqueira @joseph.feng @joao.santos @jose.coelho @luis.sahagun @chenthuran.s @pedro.ventura
/apps/fleet-web/src/modules/mifleet/
/apps/fleet-web/src/duxs/mifleet/
/apps/fleet-web/src/sagas/mifleet/
/apps/fleet-web/src/api/mifleet/

[delivery-team] @tiago.sousa @ricardo.pimentel @adi.nugroho @huang.chuhang @rodrigo.faria @mario.bento @joseph.feng @joao.santos @jose.coelho @luis.sahagun @chenthuran.s @pedro.ventura
/apps/fleet-web/src/modules/deliveryRevamp/
/apps/fleet-web/src/modules/delivery

[libs] @tiago.sousa @joseph.feng @rodrigo.faria @mario.bento @ricardo.pimentel @luis.sahagun @chenthuran.s @pedro.ventura
/libs/

[cartrack-translation-tool-frontend] @samir.carvalho @jonnas.costa @rodrigo.faria @mario.bento @tiago.sousa @ricardo.pimentel @pedro.ventura @chenthuran.s
/apps/cartrack-translation-tool/frontend/

[cartrack-translation-tool-backend] @samir.carvalho @rodrigo.faria @jonnas.costa @pedro.ventura @chenthuran.s
/apps/cartrack-translation-tool/backend/

[cartrack-translation-tool-configs] @samir.carvalho @rodrigo.faria @jonnas.costa @pedro.ventura @chenthuran.s
/apps/cartrack-translation-tool/**/*.yml
/apps/cartrack-translation-tool/**/*.yaml
/apps/cartrack-translation-tool/**/.dockerignore


[configuration] @rodrigo.faria @tiago.sousa @joseph.feng @mario.bento @gustavo.cerqueira @ricardo.pimentel @luis.sahagun @chenthuran.s @pedro.ventura
package.json
pnpm-lock.yaml
.oxlintrc.json
.prettierrc.js
.prettierignore
*.eslint.config.js
*.tsconfig.*.json
*.config.js
*.config.ts
