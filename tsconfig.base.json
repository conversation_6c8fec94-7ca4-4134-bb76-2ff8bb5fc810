{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "bundler", "emitDecoratorMetadata": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "target": "ES2022", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@karoo-ui/core": ["dist/libs/karoo-ui/core"], "@karoo-ui/core-rhf": ["apps/fleet-web/src/shared/react-hook-form/index.ts"], "@karoo-ui/core-tanstack-form": ["apps/fleet-web/src/shared/tanstack-form/index.ts"], "@karoo-ui/core/themeAugmentation": ["libs/karoo-ui/core/src/typings/themeAugmentation.d.ts"], "@karoo/utils": ["dist/libs/karoo-utils"], "api/*": ["apps/fleet-web/src/api/*"], "assets/*": ["apps/fleet-web/assets/*"], "cartrack-ui-kit": ["apps/fleet-web/src/util-components"], "cartrack-utils": ["apps/fleet-web/src/util-functions"], "duxs/*": ["apps/fleet-web/src/duxs/*"], "src/*": ["apps/fleet-web/src/*"]}}, "exclude": ["**/node_modules", "tmp", "**/.*/"]}